<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hongru.mapper.businessOps.PayConditionsMapper">
    <sql id="payConditions_sql">
		pc.[流水号] AS id,pc.[付款条件] AS payCondition,pc.[付款条件名] AS payConditionName,pc.[备注] AS remark,
		pc.[创建人姓名] AS creatorName,pc.[创建时间] AS createdTime,
		pc.[更新人姓名] AS updaterName,pc.[更新时间] AS updatedTime
	</sql>

	<insert id="insertPayConditions" parameterType="com.hongru.entity.businessOps.PayConditions">
		INSERT INTO [businessOps].[dbo].[付款条件表]
		(
		[付款条件],
		[付款条件名],
		[备注],
		[创建人姓名],
		[创建时间]
		)VALUES(
		#{payConditions.payCondition},
		#{payConditions.payConditionName},
		#{payConditions.remark},
		#{payConditions.creatorName},
		#{payConditions.createdTime}
		)
	</insert>

	<select id="selectPayConditionsById" resultType="com.hongru.entity.businessOps.PayConditions">
		SELECT
		<include refid="payConditions_sql"/>
		FROM [businessOps].[dbo].[付款条件表] pc
		<where>
			<if test="id != null">
				AND pc.[流水号] = #{id}
			</if>
		</where>
	</select>

	<select id="listPayConditions" resultType="com.hongru.entity.businessOps.PayConditions">
		SELECT
		<include refid="payConditions_sql"/>
		FROM [businessOps].[dbo].[付款条件表] pc
		<where>
			<if test="payCondition != null and payCondition != ''">
				AND pc.[付款条件] = #{payCondition}
			</if>
			<if test="payConditionName != null and payConditionName != 0">
				AND pc.[付款条件名] = #{payConditionName}
			</if>
		</where>
		ORDER BY pc.[付款条件]
	</select>

	<update id="updatePayConditions">
		UPDATE [businessOps].[dbo].[付款条件表]
		<set>
			<if test="payConditions.payCondition != null and payConditions.payCondition != ''">
				[付款条件] = #{payConditions.payCondition},
			</if>
			<if test="payConditions.payConditionName != null and payConditions.payConditionName != ''">
				[付款条件名] = #{payConditions.payConditionName},
			</if>
			<if test="payConditions.remark != null">
				[备注] = #{payConditions.remark},
			</if>
			<if test="payConditions.updaterName != null and payConditions.updaterName != ''">
				[更新人姓名] = #{payConditions.updaterName},
			</if>
			<if test="payConditions.updatedTime != null">
				[更新时间] = #{payConditions.updatedTime},
			</if>
		</set>
		WHERE [流水号] = #{payConditions.id}
	</update>
	
	<delete id="deletePayConditions">
		DELETE [businessOps].[dbo].[付款条件表] WHERE [流水号] = #{id}
	</delete>
</mapper>