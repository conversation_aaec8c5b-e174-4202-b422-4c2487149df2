<%@ page language="java" import="java.util.*"
         contentType="text/html; charset=utf-8"%>
<%@ include file="/WEB-INF/layouts/base.jsp"%>
<html>
<head>
    <title>订单数据预览</title>
    <style>
        .excel-preview-container {
            width: 100%;
            overflow-x: auto;
            margin-top: 10px;
            position: relative;
        }
        .excel-table {
            width: 100%;
            border-collapse: collapse;
            text-align: center;
        }
        .excel-table th, .excel-table td {
            border: 1px solid #ddd;
            padding: 6px;
            text-align: center;
            font-size: 12px;
        }
        .excel-table tr:nth-child(even) {
            background-color: #f2f2f2;
        }
        .excel-table th {
            padding-top: 10px;
            padding-bottom: 10px;
            background-color: #4CAF50;
            color: white;
        }
        .excel-title {
            text-align: center;
            font-size: 18px;
            font-weight: bold;
            margin: 15px 0;
        }
        .excel-info {
            position: absolute;
            top: 0;
            right: 15px;
            font-size: 12px;
            text-align: left;
            display: flex;
            flex-direction: column;
            line-height: 1.4;
        }
        .excel-info span {
            display: block;
            margin-bottom: 3px;
        }
        .thick-bottom-border {
            border-bottom: 2px solid #000 !important;
        }
        .thick-top-border {
            border-top: 2px solid #000 !important;
        }
        .print-options {
            margin: 10px 0;
            padding: 10px;
            background-color: #f8f8f8;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .print-options label {
            margin-right: 15px;
            cursor: pointer;
        }

        /* 打印样式 */
        @media print {
            body * {
                visibility: hidden;
            }
            body {
                margin: 0;
                padding: 0;
            }
            .print-options {
                display: none;
            }
            #excelPreviewContainer, #excelPreviewContainer * {
                visibility: visible;
            }
            #excelPreviewContainer {
                position: absolute;
                left: 0;
                top: 0;
                width: 100%;
                page-break-after: avoid;
            }
            .excel-info {
                position: absolute;
                top: 0;
                right: 15px;
                z-index: 10;
                background-color: #fff;
                padding: 5px;
                font-size: 10pt;
                text-align: right;
            }
            .excel-info span {
                display: block;
                margin-bottom: 3px;
            }
            .excel-title {
                margin-top: 30px !important;
                margin-bottom: 20px !important;
                font-size: 14pt;
            }
            .excel-header-container {
                position: relative;
                padding-top: 35px !important;
                margin-bottom: 40px !important;
            }
            .excel-table {
                font-size: 9pt;
                width: 100%;
                page-break-inside: avoid;
            }
            .excel-table th, .excel-table td {
                padding: 3px;
            }
            .btn-print {
                display: none;
            }
            .ibox-title, .ibox-content {
                padding: 0 !important;
                margin: 0 !important;
                border: none !important;
            }
            .ibox {
                margin: 0 !important;
                border: none !important;
            }
            .thick-bottom-border {
                border-bottom: 2px solid #000 !important;
            }
            .thick-top-border {
                border-top: 2px solid #000 !important;
            }
        }
    </style>
</head>
<body class="gray-bg">
<div class="ibox float-e-margins">
    <div class="ibox-title">
        <h5>订单数据预览</h5>
        <div class="ibox-tools">
            <a class="btn btn-primary btn-xs btn-print" style="float: right;" id="printExcel">
                <i class="layui-icon layui-icon-print"></i> 打印Excel
            </a>
        </div>
    </div>
    <div class="ibox-content">
        <!-- 添加隐藏的表单，用于传递查询参数 -->
        <form id="formSearch" class="layui-form" method="post" action="" style="display: none;">
            <input type="hidden" name="orderType" value="0">
            <!-- 其他可能需要的参数字段 -->
            <input type="hidden" name="page" value="1">
            <input type="hidden" name="limit" value="1000">
        </form>

        <div class="print-options">
            <label><input type="radio" name="printOrientation" value="portrait"> 纵向打印</label>
            <label><input type="radio" name="printOrientation" value="landscape" checked> 横向打印</label>
            <label><input type="checkbox" id="fitToOnePage" checked> 适应单页</label>
        </div>
        <div class="excel-preview-container" id="excelPreviewContainer">
            <!-- Excel预览将在这里加载 -->
            <div style="text-align: center; padding-top: 100px;">
                <i class="layui-icon layui-icon-loading layui-anim layui-anim-rotate layui-anim-loop" style="font-size: 30px;"></i>
                <p>正在加载Excel预览，请稍候...</p>
            </div>
        </div>
    </div>
</div>
<myfooter>
    <script>
        $(function() {
            // 加载Excel预览
            loadExcelPreview();

            // 打印按钮点击事件
            $("#printExcel").click(function() {
                printExcelContent();
            });
        });

        // 打印Excel内容
        function printExcelContent() {
            // 获取打印方向选项
            var orientation = $('input[name="printOrientation"]:checked').val();
            var fitToOnePage = $('#fitToOnePage').is(':checked');

            // 设置打印选项
            var style = document.createElement('style');

            // 根据用户选择设置打印方向和其他属性
            var pageSetup = '@page { size: ' + orientation + '; ';
            pageSetup += 'margin: 10mm; }';

            if (fitToOnePage) {
                pageSetup += ' .excel-table { font-size: 8pt; } .excel-table th, .excel-table td { padding: 2px; }';
            }

            style.innerHTML = pageSetup;
            document.head.appendChild(style);

            // 打印
            window.print();

            // 打印后移除样式
            document.head.removeChild(style);
        }

        // 加载Excel预览
        var formData = {};
        function loadExcelPreview() {
            layui.use(['laydate', 'layer', 'table', 'element','form'], function(){
                var laydate = layui.laydate //日期
                    ,layer = layui.layer //弹层
                    ,table = layui.table //表格
                    ,element = layui.element //元素操作
                var form = layui.form;
                
                // 获取URL参数，如果有的话
                var urlParams = new URLSearchParams(window.location.search);
                formData = $("#formSearch").serializeJsonObject() || {};

                // 尝试从父页面获取查询参数
                try {
                    if (window.parent && window.parent.$("#formSearch").length) {
                        var parentFormData = window.parent.$("#formSearch").serializeJsonObject();
                        if (parentFormData) {
                            // 合并父页面的查询参数
                            formData = $.extend(formData, parentFormData);
                        }
                    }
                } catch (e) {
                    console.error('获取父页面参数失败:', e);
                }

                formData = {
                    page: 1,
                    limit: 1000,
                    planStatus: "1",
                    outboundStartDate: formData.outboundStartDate,
                    outboundEndDate: null
                }

                // 出库开始日期必选
                if (!formData.outboundStartDate) {
                    layer.alert('请选择出库开始日期', {icon: 2});
                    return;
                }

                // 计算出库结束日期 (查询一周内被确认的订单)
                var outboundEndDate = new Date(formData.outboundStartDate);
                outboundEndDate.setDate(outboundEndDate.getDate() + 6);
                formData.outboundEndDate = outboundEndDate.toISOString().split('T')[0];

                console.log('发送预览请求参数:', formData);

                $.ajax({
                    type: 'post',
                    url: baselocation + '/order/orderEntry/previewData',
                    data: formData,
                    dataType: 'json',
                    success: function(result) {
                        if (result.code == 1 && result.data) {
                            var data = result.data;
                            renderExcelTable(data);
                        } else {
                            layer.alert(result.message || '加载预览失败', {icon: 2}, function(index) {
                                layer.close(index);
                                closeAll();
                            });
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error('请求错误:', error);
                        layer.alert('服务器错误，请稍后重试', {icon: 2});
                    }
                });
            });
        }

        // 渲染Excel表格
        function renderExcelTable(data) {
            var html = '';
            
            // 添加表格标题和记录信息区域
            html += '<div style="position: relative; margin-bottom: 30px; padding-top: 10px;" class="excel-header-container">';
            
            // 右上角添加记录号等信息
            html += '<div class="excel-info">';
            html += '<span>记录号：' + data.recordNo + '</span>';
            html += '<span>修改号：' + data.modifyNo + '</span>';
            html += '<span>文件号：' + data.fileNo + '</span>';
            html += '</div>';
            
            // 将日期格式从yyyy-MM-dd转换为yyyy/MM/dd
            var startDate = formData.outboundStartDate.replace(/-/g, '/');
            var endDate = formData.outboundEndDate.replace(/-/g, '/');
            var titleDate = startDate + '-' + endDate;
            
            // 标题居中
            html += '<div class="excel-title">周出库计划（WIN-W）('+titleDate+'）</div>';
            html += '</div>';

            // 表格内容
            html += '<table class="excel-table">';
            html += '<thead><tr>';
            for (var i = 0; i < data.headers.length; i++) {
                html += '<th>' + data.headers[i] + '</th>';
            }
            html += '</tr></thead>';

            html += '<tbody>';
            
            // 找到客户列的索引
            var customerColumnIndex = 0; // 默认值，如果找不到"客户"列就使用第一列
            for (var i = 0; i < data.headers.length; i++) {
                if (data.headers[i] === "客户") {
                    customerColumnIndex = i;
                    break;
                }
            }
            
            for (var i = 0; i < data.rows.length; i++) {
                var rowClass = '';
                // 修改判断逻辑：在客户变更的当前行添加上边框（而不是前一行的下边框）
                if (i > 0 && data.rows[i][customerColumnIndex] !== data.rows[i-1][customerColumnIndex]) {
                    rowClass = ' class="thick-top-border"';
                }
                html += '<tr' + rowClass + '>';
                for (var j = 0; j < data.rows[i].length; j++) {
                    // 处理null值，显示为空字符串
                    var cellValue = data.rows[i][j];
                    if (cellValue === null || cellValue === 'null' || cellValue === undefined) {
                        cellValue = '';
                    }
                    html += '<td>' + cellValue + '</td>';
                }
                html += '</tr>';
            }
            html += '</tbody>';
            html += '</table>';

            $("#excelPreviewContainer").html(html);
        }

         /**
         * 关闭页面
         */
         function closeAll() {
            var index = parent.layer.getFrameIndex(window.name);
            parent.layer.close(index);
        }
    </script>
</myfooter>
</body>
</html>