<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hongru.mapper.businessOps.WareHouseMapper">
    <sql id="wareHouse_sql">
		pc.[流水号] AS id,pc.[仓库代码] AS wareHouseCode,pc.[仓库名] AS wareHouseName,pc.[仓库场所区分] AS wareHousePlaceDiff,
		pc.[仓库区分] AS wareHouseDiff,pc.[创建人姓名] AS creatorName,pc.[创建时间] AS createdTime,
		pc.[更新人姓名] AS updaterName,pc.[更新时间] AS updatedTime
	</sql>

	<insert id="insertWareHouse" parameterType="com.hongru.entity.businessOps.WareHouse">
		INSERT INTO [businessOps].[dbo].[仓库表]
		(
		[仓库代码],
		[仓库名],
		[仓库场所区分],
		[仓库区分],
		[创建人姓名],
		[创建时间]
		)VALUES(
		#{wareHouse.wareHouseCode},
		#{wareHouse.wareHouseName},
		#{wareHouse.wareHousePlaceDiff},
		#{wareHouse.wareHouseDiff},
		#{wareHouse.creatorName},
		#{wareHouse.createdTime}
		)
	</insert>

	<select id="selectWareHouseById" resultType="com.hongru.entity.businessOps.WareHouse">
		SELECT
		<include refid="wareHouse_sql"/>
		FROM [businessOps].[dbo].[仓库表] pc
		<where>
			<if test="id != null">
				AND pc.[流水号] = #{id}
			</if>
		</where>
	</select>

	<select id="listWareHouse" resultType="com.hongru.entity.businessOps.WareHouse">
		SELECT
		<include refid="wareHouse_sql"/>
		FROM [businessOps].[dbo].[仓库表] pc
		<where>
			<if test="wareHouseCode != null and wareHouseCode != ''">
				AND pc.[仓库代码] = #{wareHouseCode}
			</if>
			<if test="wareHouseName != null and wareHouseName != ''">
				AND pc.[仓库名] = #{wareHouseName}
			</if>
		</where>
		ORDER BY pc.[仓库代码]
	</select>

	<update id="updateWareHouse">
		UPDATE [businessOps].[dbo].[仓库表]
		<set>
			<if test="wareHouse.wareHouseCode != null and wareHouse.wareHouseCode != ''">
				[仓库代码] = #{wareHouse.wareHouseCode},
			</if>
			<if test="wareHouse.wareHouseName != null and wareHouse.wareHouseName != ''">
				[仓库名] = #{wareHouse.wareHouseName},
			</if>
			<if test="wareHouse.wareHousePlaceDiff != null and wareHouse.wareHousePlaceDiff != ''">
				[仓库场所区分] = #{wareHouse.wareHouseName},
			</if>
			<if test="wareHouse.wareHouseDiff != null and wareHouse.wareHouseDiff != ''">
				[仓库区分] = #{wareHouse.wareHouseDiff},
			</if>
			<if test="wareHouse.updaterName != null and wareHouse.updaterName != ''">
				[更新人姓名] = #{wareHouse.updaterName},
			</if>
			<if test="wareHouse.updatedTime != null">
				[更新时间] = #{wareHouse.updatedTime},
			</if>
		</set>
		WHERE [流水号] = #{wareHouse.id}
	</update>
	
	<delete id="deleteWareHouse">
		DELETE [businessOps].[dbo].[仓库表] WHERE [流水号] = #{id}
	</delete>
</mapper>