package com.hongru.service.impl.businessOps;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import com.hongru.entity.businessOps.PaymentAmount;
import com.hongru.entity.businessOps.PaymentAmountDetail;
import com.hongru.entity.businessOps.CopperContract;
import com.hongru.mapper.businessOps.PaymentAmountMapper;
import com.hongru.mapper.businessOps.PaymentAmountDetailMapper;
import com.hongru.mapper.businessOps.SalesAmountMapper;
import com.hongru.pojo.dto.PaymentRequestLoginListDTO;
import com.hongru.pojo.dto.PaymentRequestExportDTO;
import com.hongru.service.businessOps.IPaymentRequestLoginService;
import com.hongru.service.businessOps.ISalesCommonService;
import com.hongru.service.sumitomo.ISumitomoService;
import com.hongru.support.page.PageInfo;
import com.hongru.common.util.DateUtils;
import com.hongru.common.util.JapaneseFiscalYearUtils;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Map;

@Service
public class PaymentRequestLoginServiceImpl implements IPaymentRequestLoginService {

    @Autowired
    private PaymentAmountMapper paymentAmountMapper;

    @Autowired
    private PaymentAmountDetailMapper paymentAmountDetailMapper;

    @Autowired
    private SalesAmountMapper salesAmountMapper;

    @Autowired
    private ISalesCommonService salesCommonService;

    @Autowired
    private ISumitomoService sumitomoService;

    @Override
    public List<PaymentRequestLoginListDTO> listPaymentRequestLoginByPage(PageInfo pageInfo, String customerCode,
            String paymentRequestDate) throws Exception {
        return paymentAmountMapper.listPaymentRequestLoginByPage(pageInfo, customerCode, paymentRequestDate);
    }

    @Override
    public Integer listPaymentRequestLoginByPageCount(String customerCode, String paymentRequestDate) throws Exception {
        return paymentAmountMapper.listPaymentRequestLoginByPageCount(customerCode, paymentRequestDate);
    }

    @Override
    public List<PaymentRequestLoginListDTO> listPaymentRequestLoginByPageWithUser(PageInfo pageInfo,
            String customerCode, String paymentRequestDate, String creatorName) throws Exception {
        return paymentAmountMapper.listPaymentRequestLoginByPageWithUser(pageInfo, customerCode, paymentRequestDate,
                creatorName);
    }

    @Override
    public Integer listPaymentRequestLoginByPageCountWithUser(String customerCode, String paymentRequestDate,
            String creatorName) throws Exception {
        return paymentAmountMapper.listPaymentRequestLoginByPageCountWithUser(customerCode, paymentRequestDate,
                creatorName);
    }

    @Override
    public PaymentAmount getPaymentAmountByNo(String paymentRequestNo) throws Exception {
        return paymentAmountMapper.selectByPaymentRequestNo(paymentRequestNo);
    }

    @Override
    public List<PaymentAmountDetail> getPaymentAmountDetailsByNo(String paymentRequestNo) throws Exception {
        return paymentAmountDetailMapper.selectByPaymentRequestNo(paymentRequestNo);
    }

    @Override
    @Transactional
    public int savePaymentRequestLoginData(PaymentAmount paymentAmount, List<PaymentAmountDetail> paymentAmountDetails,
            String userName) throws Exception {
        // 生成支付请求NO
        String paymentRequestNo = generatePaymentRequestNo();
        paymentAmount.setPaymentRequestNo(paymentRequestNo);

        // 设置创建信息
        String currentTime = DateUtils.convertIntToDateString(DateUtils.getCurrentTime(), "yyyy-MM-dd HH:mm:ss");
        paymentAmount.setCreatorName(userName);
        paymentAmount.setCreatedTime(currentTime);
        paymentAmount.setUpdaterName(userName);
        paymentAmount.setUpdatedTime(currentTime);

        // 设置签约方代码为客户代码（根据需求变更）
        paymentAmount.setContractorCode(paymentAmount.getCustomerCode());

        // 计算支付金额（所有明细的请求金额之和）
        BigDecimal totalPaymentAmount = BigDecimal.ZERO;
        if (paymentAmountDetails != null && !paymentAmountDetails.isEmpty()) {
            for (PaymentAmountDetail detail : paymentAmountDetails) {
                if (detail.getRequestAmount() != null) {
                    totalPaymentAmount = totalPaymentAmount.add(detail.getRequestAmount());
                }
            }
        }
        paymentAmount.setPaymentAmount(totalPaymentAmount);

        // 验证必填字段（Controller层已验证，这里作为双重保险）
        if (paymentAmount.getPaymentRequestDate() == null || paymentAmount.getPaymentRequestDate().trim().isEmpty()) {
            throw new Exception("支付请求日期不能为空");
        }
        if (paymentAmount.getPaymentDate() == null || paymentAmount.getPaymentDate().trim().isEmpty()) {
            throw new Exception("支付日期不能为空");
        }
        if (paymentAmount.getCustomerCode() == null || paymentAmount.getCustomerCode().trim().isEmpty()) {
            throw new Exception("需求方不能为空");
        }

        // 插入支付额主表
        int result = paymentAmountMapper.insert(paymentAmount);

        if (result > 0 && paymentAmountDetails != null && !paymentAmountDetails.isEmpty()) {
            // 设置明细数据的支付请求NO和创建信息
            for (PaymentAmountDetail detail : paymentAmountDetails) {
                detail.setPaymentRequestNo(paymentRequestNo);
                detail.setCreatorName(userName);
                detail.setCreatedTime(currentTime);
                detail.setUpdaterName(userName);
                detail.setUpdatedTime(currentTime);
            }

            // 批量插入明细数据
            paymentAmountDetailMapper.batchInsert(paymentAmountDetails);

            // 更新销售额明细表的支付请求确认状态（只更新选中的明细）
            for (PaymentAmountDetail detail : paymentAmountDetails) {
                if (detail.getSalesAmountSeq() != null && !detail.getSalesAmountSeq().isEmpty()) {
                    // 使用明细ID精确更新，只更新选中的具体明细行
                    salesAmountMapper.updatePaymentRequestConfirmByDetail(
                            detail.getSalesAmountSeq(),
                            "1");
                }
            }

            // 更新铜合同表的实际数量和剩余数量（针对预约铜/支给铜）
            updateCopperContractQuantities(paymentAmountDetails);
        }

        return result;
    }

    @Override
    @Transactional
    public int updatePaymentRequestLoginData(PaymentAmount paymentAmount,
            List<PaymentAmountDetail> paymentAmountDetails, String userName) throws Exception {
        // 设置更新信息
        String currentTime = DateUtils.convertIntToDateString(DateUtils.getCurrentTime(), "yyyy-MM-dd HH:mm:ss");
        paymentAmount.setUpdaterName(userName);
        paymentAmount.setUpdatedTime(currentTime);

        // 设置签约方代码为客户代码（根据需求变更）
        paymentAmount.setContractorCode(paymentAmount.getCustomerCode());

        // 更新支付额主表
        int result = paymentAmountMapper.updateById(paymentAmount);

        if (result > 0 && paymentAmountDetails != null && !paymentAmountDetails.isEmpty()) {
            // 设置明细数据的更新信息
            for (PaymentAmountDetail detail : paymentAmountDetails) {
                detail.setUpdaterName(userName);
                detail.setUpdatedTime(currentTime);
            }

            // 批量更新明细数据
            paymentAmountDetailMapper.batchUpdate(paymentAmountDetails);

            // 更新铜合同表的实际数量和剩余数量（针对预约铜/支给铜）
            updateCopperContractQuantities(paymentAmountDetails);
        }

        return result;
    }

    @Override
    @Transactional
    public int deletePaymentRequestLoginData(String paymentRequestNo) throws Exception {
        // 1. 先查询要删除的支付请求相关的所有支付额明细，获取销售额明细ID
        List<PaymentAmountDetail> paymentDetails = paymentAmountDetailMapper.selectByPaymentRequestNo(paymentRequestNo);

        // 2. 恢复相关销售额明细的支付请求确认状态
        if (paymentDetails != null && !paymentDetails.isEmpty()) {
            for (PaymentAmountDetail detail : paymentDetails) {
                if (detail.getSalesAmountSeq() != null && !detail.getSalesAmountSeq().isEmpty()) {
                    // 将销售额明细表中对应记录的支付请求确认状态重置为NULL（恢复为可编辑状态）
                    salesAmountMapper.updatePaymentRequestConfirmByDetail(
                            detail.getSalesAmountSeq(),
                            null); // 设置为null表示未确认，恢复可编辑状态
                    System.out
                            .println("恢复销售额明细状态: 明细ID=" + detail.getSalesAmountSeq() + ", 支付请求NO=" + paymentRequestNo);
                }
            }
        }

        // 3. 删除支付额明细数据
        paymentAmountDetailMapper.deleteByPaymentRequestNo(paymentRequestNo);

        // 4. 删除支付额主表数据
        return paymentAmountMapper.deleteByPaymentRequestNo(paymentRequestNo);
    }

    @Override
    public String generatePaymentRequestNo() throws Exception {
        String dateStr = DateUtils.format(new Date(), "yyMMdd");
        return paymentAmountMapper.generatePaymentRequestNo(dateStr);
    }

    @Override
    public List<PaymentAmountDetail> getSalesDataForPaymentRequest(String customerCode) throws Exception {
        // 这里需要查询销售额表和销售额明细表的数据，转换为支付请求明细数据
        // 根据需求文档，查询条件是客户代码和支付请求确认状态不等于'1'
        return salesAmountMapper.getSalesDataForPaymentRequest(customerCode);
    }

    /**
     * 更新铜合同表的实际数量和剩余数量
     * 根据需求第3点：当铜合同类别为预约铜或支给铜时，需要更新铜合同表
     *
     * @param paymentAmountDetails 支付额明细数据列表
     */
    private void updateCopperContractQuantities(List<PaymentAmountDetail> paymentAmountDetails) {
        if (paymentAmountDetails == null || paymentAmountDetails.isEmpty()) {
            return;
        }

        for (PaymentAmountDetail detail : paymentAmountDetails) {
            try {
                // 检查是否有铜合同NO和铜合同类别
                String copperContractNo = detail.getCopperContractNo();
                String copperContractType = detail.getCopperContractType();
                BigDecimal quantity = detail.getQuantity();

                // 只有预约铜(1)或支给铜(2)才需要更新铜合同表
                if (copperContractNo != null && !copperContractNo.isEmpty() &&
                        ("1".equals(copperContractType) || "2".equals(copperContractType)) &&
                        quantity != null && quantity.compareTo(BigDecimal.ZERO) > 0) {

                    // 查询当前铜合同的签约数量和实际数量
                    // 这里需要根据铜签约NO查询铜合同表获取当前的实际数量和签约数量
                    // 然后计算新的实际数量和剩余数量

                    // 实际数量 = 原实际数量 + 当前出货数量
                    // 剩余数量 = 签约数量 - 新的实际数量

                    // 由于需要查询现有数据，这里调用salesCommonService的方法
                    // 先获取铜合同详情
                    String useMonth = "";
                    if (detail.getArrivalDate() != null && detail.getArrivalDate().length() >= 7) {
                        useMonth = detail.getArrivalDate().substring(0, 7); // 截取年月部分
                    }

                    if (!useMonth.isEmpty()) {
                        // 查询铜合同详情
                        CopperContract copperContract = salesCommonService.getCopperContractDetailWithCondition(
                                copperContractNo,
                                useMonth);

                        if (copperContract != null) {
                            // 计算新的实际数量和剩余数量
                            BigDecimal currentActualQuantity = copperContract.getActualQuantity() != null
                                    ? copperContract.getActualQuantity()
                                    : BigDecimal.ZERO;
                            BigDecimal signedQuantity = copperContract.getSignedQuantity() != null
                                    ? copperContract.getSignedQuantity()
                                    : BigDecimal.ZERO;

                            BigDecimal newActualQuantity = currentActualQuantity.add(quantity);
                            BigDecimal newRemainingQuantity = signedQuantity.subtract(newActualQuantity);

                            // 更新铜合同表
                            salesCommonService.updateCopperContractQuantity(copperContractNo, newActualQuantity,
                                    newRemainingQuantity);

                            System.out.println("更新铜合同表 - 铜签约NO: " + copperContractNo +
                                    ", 新实际数量: " + newActualQuantity +
                                    ", 新剩余数量: " + newRemainingQuantity);
                        }
                    }
                }
            } catch (Exception e) {
                // 记录错误但不影响主流程
                System.err.println("更新铜合同表数量时发生错误: " + e.getMessage());
                e.printStackTrace();
            }
        }
    }

    @Override
    public int updateSalesDetailPaymentRequestConfirm(String salesAmountNo, String detailSeq, String confirmStatus,
            String userName) throws Exception {
        return salesAmountMapper.updatePaymentRequestConfirmByDetail(detailSeq, confirmStatus);
    }

    @Override
    public List<PaymentRequestExportDTO> getPaymentRequestExportData(String customerCode, String paymentRequestDate)
            throws Exception {
        List<PaymentRequestExportDTO> exportList = new ArrayList<>();

        // 获取当前日期作为导出日期（作成日）
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        String currentDate = dateFormat.format(new Date());

        // 查询支付请求列表
        List<PaymentRequestLoginListDTO> paymentRequestList = paymentAmountMapper.listPaymentRequestLoginByPage(
                null, customerCode, paymentRequestDate);

        for (PaymentRequestLoginListDTO paymentRequest : paymentRequestList) {
            // 获取支付请求明细
            List<PaymentAmountDetail> details = paymentAmountDetailMapper.selectByPaymentRequestNo(
                    paymentRequest.getPaymentRequestNo());

            // 获取支付请求主表信息
            PaymentAmount paymentAmount = paymentAmountMapper.selectByPaymentRequestNo(
                    paymentRequest.getPaymentRequestNo());

            for (PaymentAmountDetail detail : details) {
                PaymentRequestExportDTO exportDTO = new PaymentRequestExportDTO();

                // 设置基本信息
                exportDTO.setPaymentRequestNo(paymentAmount.getPaymentRequestNo());
                exportDTO.setPaymentRequestDate(paymentAmount.getPaymentRequestDate());
                exportDTO.setCustomerCode(paymentAmount.getCustomerCode());
                exportDTO.setCustomerAlias(paymentAmount.getCustomerAlias());
                exportDTO.setSettlementCurrency(paymentAmount.getSettlementCurrency());
                exportDTO.setCreatedTime(paymentAmount.getCreatedTime()); // 设置创建时间
                exportDTO.setExportDate(currentDate); // 设置导出日期（作成日）

                // 设置签约方代码
                exportDTO.setContractorCode(paymentAmount.getContractorCode());

                // 获取客户全称（需求方全称）
                if (paymentAmount.getCustomerCode() != null && !paymentAmount.getCustomerCode().trim().isEmpty()) {
                    try {
                        com.hongru.entity.sumitomo.Customers customer = sumitomoService
                                .getCustomerByCode(paymentAmount.getCustomerCode());
                        if (customer != null && customer.getCustomerName() != null) {
                            exportDTO.setCustomerFullName(customer.getCustomerName());
                        }
                    } catch (Exception e) {
                        System.err
                                .println("获取客户全称失败，客户代码：" + paymentAmount.getCustomerCode() + "，错误：" + e.getMessage());
                    }
                }

                // 获取签约方全称（签约方代码也关联客户表）
                if (paymentAmount.getContractorCode() != null && !paymentAmount.getContractorCode().trim().isEmpty()) {
                    try {
                        com.hongru.entity.sumitomo.Customers contractor = sumitomoService
                                .getCustomerByCode(paymentAmount.getContractorCode());
                        if (contractor != null && contractor.getCustomerName() != null) {
                            exportDTO.setContractorFullName(contractor.getCustomerName());
                        }
                    } catch (Exception e) {
                        System.err.println(
                                "获取签约方全称失败，签约方代码：" + paymentAmount.getContractorCode() + "，错误：" + e.getMessage());
                    }
                }

                // 设置明细信息
                exportDTO.setShipmentDate(detail.getShipmentDate());
                exportDTO.setProductCode(detail.getProductCode());
                exportDTO.setQuantity(formatQuantity(detail.getQuantity())); // 格式化数量（3位小数）

                // 根据产品代码查询sumitomo库的产品表获取客户部品编码
                if (detail.getProductCode() != null && !detail.getProductCode().trim().isEmpty()) {
                    try {
                        com.hongru.entity.sumitomo.Product productInfo = sumitomoService
                                .getProductInfo(detail.getProductCode(), "0");
                        if (productInfo != null && productInfo.getPartNo() != null) {
                            exportDTO.setCustomerPartCode(productInfo.getPartNo());
                        }
                    } catch (Exception e) {
                        // 如果查询失败，记录日志但不影响其他数据
                        System.out.println("查询产品部品编号失败，产品代码：" + detail.getProductCode() + "，错误：" + e.getMessage());
                    }
                }
                exportDTO.setArrivalDate(detail.getArrivalDate());
                exportDTO.setCustomerOrderNo(detail.getCustomerOrderNo());
                exportDTO.setDetailRemark(detail.getDetailRemark());
                exportDTO.setZeroBase(detail.getZeroBase());
                exportDTO.setConvertedCopperPrice(formatCopperPrice(detail.getConvertedCopperPrice())); // 格式化换算后铜单价（2位小数）
                exportDTO.setSalesUnitPrice(formatUnitPrice(detail.getPaymentUnitPrice())); // 格式化销售单价（2位小数）
                exportDTO.setSalesAmount(formatAmount(detail.getRequestAmount())); // 格式化销售金额（2位小数）

                // 计算单价、金额、税额等
                if (detail.getRequestAmount() != null && detail.getQuantity() != null &&
                        detail.getQuantity().compareTo(BigDecimal.ZERO) > 0) {
                    BigDecimal unitPrice = detail.getRequestAmount().divide(detail.getQuantity(), 2,
                            BigDecimal.ROUND_HALF_UP);
                    exportDTO.setUnitPrice(formatUnitPrice(unitPrice)); // 格式化单价（2位小数）
                }
                exportDTO.setAmount(formatAmount(detail.getRequestAmount())); // 格式化金额（2位小数）

                // 计算税额 - 直接使用SQL查询返回的税率数据
                if (detail.getRequestAmount() != null) {
                    BigDecimal taxRate = null;

                    // 优先尝试解析taxRate字段（SQL查询已经返回了正确的税率值）
                    if (paymentAmount.getTaxRate() != null && !paymentAmount.getTaxRate().trim().isEmpty()) {
                        try {
                            taxRate = new BigDecimal(paymentAmount.getTaxRate());
                            System.out.println("导出使用SQL返回的税率 - 税代码: " + paymentAmount.getTaxCode() +
                                    ", taxRate字段: " + paymentAmount.getTaxRate() + ", 解析后税率: " + taxRate);
                        } catch (NumberFormatException e) {
                            System.err.println("税率字段解析失败: " + paymentAmount.getTaxRate() + ", 错误: " + e.getMessage());
                        }
                    }

                    // 如果taxRate字段解析失败，通过税代码查询（备用方案）
                    if (taxRate == null && paymentAmount.getTaxCode() != null) {
                        try {
                            System.out.println("备用方案：通过税代码查询税率 - 税代码: " + paymentAmount.getTaxCode());
                            com.hongru.entity.businessOps.TaxRateCalculation taxRateCalc = salesCommonService
                                    .getTaxRateByTaxCode(paymentAmount.getTaxCode());
                            if (taxRateCalc != null && taxRateCalc.getTaxRate() != null) {
                                taxRate = taxRateCalc.getTaxRate();
                                System.out.println("备用方案成功 - 查询到税率: " + taxRate);
                            } else {
                                System.err.println("备用方案失败 - 找不到税代码 " + paymentAmount.getTaxCode() + " 对应的税率数据");
                            }
                        } catch (Exception e) {
                            System.err
                                    .println("备用方案异常 - 税代码: " + paymentAmount.getTaxCode() + ", 错误: " + e.getMessage());
                        }
                    }

                    // 计算税额
                    if (taxRate != null && taxRate.compareTo(BigDecimal.ZERO) > 0) {
                        // 将税率转换为百分比形式进行计算：税额 = 金额 × (税率/100)
                        BigDecimal taxRatePercent = taxRate.divide(new BigDecimal("100"));
                        BigDecimal tax = detail.getRequestAmount().multiply(taxRatePercent);
                        exportDTO.setTax(formatTax(tax)); // 格式化税额（2位小数）
                        exportDTO.setTaxRate(taxRate);
                        exportDTO.setTotalPriceWithTax(formatAmount(detail.getRequestAmount().add(tax))); // 格式化含税总额（2位小数）
                        System.out.println("导出税额计算成功 - 税率: " + taxRate + ", 金额: " + detail.getRequestAmount() +
                                ", 税额: " + tax + ", 含税总额: " + detail.getRequestAmount().add(tax));
                    } else {
                        // 没有找到税率数据或税率为0，税额为0
                        exportDTO.setTax(formatTax(BigDecimal.ZERO)); // 格式化税额（2位小数）
                        exportDTO.setTaxRate(BigDecimal.ZERO);
                        exportDTO.setTotalPriceWithTax(formatAmount(detail.getRequestAmount())); // 格式化含税总额（2位小数）
                        System.out.println("税额设为0 - 税代码: " + paymentAmount.getTaxCode() +
                                ", taxRate字段: " + paymentAmount.getTaxRate() + ", 解析后税率: " + taxRate);
                    }
                } else {
                    // 没有金额，税额为0
                    exportDTO.setTax(BigDecimal.ZERO);
                    exportDTO.setTaxRate(BigDecimal.ZERO);
                    exportDTO.setTotalPriceWithTax(BigDecimal.ZERO);
                    System.out.println("没有金额数据，税额设为0");
                }

                exportList.add(exportDTO);
            }
        }

        return exportList;
    }

    @Override
    public List<PaymentRequestExportDTO> getPaymentRequestExportDataByNo(String paymentRequestNo) throws Exception {
        List<PaymentRequestExportDTO> exportList = new ArrayList<>();

        // 获取当前日期作为导出日期（作成日）
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        String currentDate = dateFormat.format(new Date());

        // 获取支付请求主表信息
        PaymentAmount paymentAmount = paymentAmountMapper.selectByPaymentRequestNo(paymentRequestNo);
        if (paymentAmount == null) {
            return exportList;
        }

        // 获取支付请求明细
        List<PaymentAmountDetail> details = paymentAmountDetailMapper.selectByPaymentRequestNo(paymentRequestNo);

        for (PaymentAmountDetail detail : details) {
            PaymentRequestExportDTO exportDTO = new PaymentRequestExportDTO();

            // 设置基本信息
            exportDTO.setPaymentRequestNo(paymentAmount.getPaymentRequestNo());
            exportDTO.setPaymentRequestDate(paymentAmount.getPaymentRequestDate());
            exportDTO.setCustomerCode(paymentAmount.getCustomerCode());
            exportDTO.setCustomerAlias(paymentAmount.getCustomerAlias());
            exportDTO.setSettlementCurrency(paymentAmount.getSettlementCurrency());
            exportDTO.setCreatedTime(paymentAmount.getCreatedTime()); // 设置创建时间
            exportDTO.setExportDate(currentDate); // 设置导出日期（作成日）

            // 设置签约方代码
            exportDTO.setContractorCode(paymentAmount.getContractorCode());

            // 获取客户全称（需求方全称）
            if (paymentAmount.getCustomerCode() != null && !paymentAmount.getCustomerCode().trim().isEmpty()) {
                try {
                    com.hongru.entity.sumitomo.Customers customer = sumitomoService
                            .getCustomerByCode(paymentAmount.getCustomerCode());
                    if (customer != null && customer.getCustomerName() != null) {
                        exportDTO.setCustomerFullName(customer.getCustomerName());
                    }
                } catch (Exception e) {
                    System.err
                            .println("单个导出获取客户全称失败，客户代码：" + paymentAmount.getCustomerCode() + "，错误：" + e.getMessage());
                }
            }

            // 获取签约方全称（签约方代码也关联客户表）
            if (paymentAmount.getContractorCode() != null && !paymentAmount.getContractorCode().trim().isEmpty()) {
                try {
                    com.hongru.entity.sumitomo.Customers contractor = sumitomoService
                            .getCustomerByCode(paymentAmount.getContractorCode());
                    if (contractor != null && contractor.getCustomerName() != null) {
                        exportDTO.setContractorFullName(contractor.getCustomerName());
                    }
                } catch (Exception e) {
                    System.err.println(
                            "单个导出获取签约方全称失败，签约方代码：" + paymentAmount.getContractorCode() + "，错误：" + e.getMessage());
                }
            }

            // 设置明细信息
            exportDTO.setShipmentDate(detail.getShipmentDate());
            exportDTO.setProductCode(detail.getProductCode());
            exportDTO.setQuantity(formatQuantity(detail.getQuantity())); // 格式化数量（3位小数）

            // 根据产品代码查询sumitomo库的产品表获取客户部品编码
            if (detail.getProductCode() != null && !detail.getProductCode().trim().isEmpty()) {
                try {
                    com.hongru.entity.sumitomo.Product productInfo = sumitomoService
                            .getProductInfo(detail.getProductCode(), "0");
                    if (productInfo != null && productInfo.getPartNo() != null) {
                        exportDTO.setCustomerPartCode(productInfo.getPartNo());
                    }
                } catch (Exception e) {
                    // 如果查询失败，记录日志但不影响其他数据
                    System.out.println("查询产品部品编号失败，产品代码：" + detail.getProductCode() + "，错误：" + e.getMessage());
                }
            }
            exportDTO.setArrivalDate(detail.getArrivalDate());
            exportDTO.setCustomerOrderNo(detail.getCustomerOrderNo());
            exportDTO.setDetailRemark(detail.getDetailRemark());
            exportDTO.setZeroBase(detail.getZeroBase());
            exportDTO.setConvertedCopperPrice(formatCopperPrice(detail.getConvertedCopperPrice())); // 格式化换算后铜单价（2位小数）
            exportDTO.setSalesUnitPrice(formatUnitPrice(detail.getPaymentUnitPrice())); // 格式化销售单价（2位小数）
            exportDTO.setSalesAmount(formatAmount(detail.getRequestAmount())); // 格式化销售金额（2位小数）

            // 计算单价、金额、税额等
            if (detail.getRequestAmount() != null && detail.getQuantity() != null &&
                    detail.getQuantity().compareTo(BigDecimal.ZERO) > 0) {
                BigDecimal unitPrice = detail.getRequestAmount().divide(detail.getQuantity(), 2,
                        BigDecimal.ROUND_HALF_UP);
                exportDTO.setUnitPrice(formatUnitPrice(unitPrice)); // 格式化单价（2位小数）
            }
            exportDTO.setAmount(formatAmount(detail.getRequestAmount())); // 格式化金额（2位小数）

            // 计算税额 - 直接使用SQL查询返回的税率数据
            if (detail.getRequestAmount() != null) {
                BigDecimal taxRate = null;

                // 优先尝试解析taxRate字段（SQL查询已经返回了正确的税率值）
                if (paymentAmount.getTaxRate() != null && !paymentAmount.getTaxRate().trim().isEmpty()) {
                    try {
                        taxRate = new BigDecimal(paymentAmount.getTaxRate());
                        System.out.println("单个导出使用SQL返回的税率 - 税代码: " + paymentAmount.getTaxCode() +
                                ", taxRate字段: " + paymentAmount.getTaxRate() + ", 解析后税率: " + taxRate);
                    } catch (NumberFormatException e) {
                        System.err.println("单个导出税率字段解析失败: " + paymentAmount.getTaxRate() + ", 错误: " + e.getMessage());
                    }
                }

                // 如果taxRate字段解析失败，通过税代码查询（备用方案）
                if (taxRate == null && paymentAmount.getTaxCode() != null) {
                    try {
                        System.out.println("单个导出备用方案：通过税代码查询税率 - 税代码: " + paymentAmount.getTaxCode());
                        com.hongru.entity.businessOps.TaxRateCalculation taxRateCalc = salesCommonService
                                .getTaxRateByTaxCode(paymentAmount.getTaxCode());
                        if (taxRateCalc != null && taxRateCalc.getTaxRate() != null) {
                            taxRate = taxRateCalc.getTaxRate();
                            System.out.println("单个导出备用方案成功 - 查询到税率: " + taxRate);
                        } else {
                            System.err.println("单个导出备用方案失败 - 找不到税代码 " + paymentAmount.getTaxCode() + " 对应的税率数据");
                        }
                    } catch (Exception e) {
                        System.err
                                .println("单个导出备用方案异常 - 税代码: " + paymentAmount.getTaxCode() + ", 错误: " + e.getMessage());
                    }
                }

                // 计算税额
                if (taxRate != null && taxRate.compareTo(BigDecimal.ZERO) > 0) {
                    // 将税率转换为百分比形式进行计算：税额 = 金额 × (税率/100)
                    BigDecimal taxRatePercent = taxRate.divide(new BigDecimal("100"));
                    BigDecimal tax = detail.getRequestAmount().multiply(taxRatePercent);
                    exportDTO.setTax(formatTax(tax)); // 格式化税额（2位小数）
                    exportDTO.setTaxRate(taxRate);
                    exportDTO.setTotalPriceWithTax(formatAmount(detail.getRequestAmount().add(tax))); // 格式化含税总额（2位小数）
                    System.out.println("单个导出税额计算成功 - 税率: " + taxRate + ", 金额: " + detail.getRequestAmount() +
                            ", 税额: " + formatTax(tax) + ", 含税总额: " + formatAmount(detail.getRequestAmount().add(tax)));
                } else {
                    // 没有找到税率数据或税率为0，税额为0
                    exportDTO.setTax(formatTax(BigDecimal.ZERO)); // 格式化税额（2位小数）
                    exportDTO.setTaxRate(BigDecimal.ZERO);
                    exportDTO.setTotalPriceWithTax(formatAmount(detail.getRequestAmount())); // 格式化含税总额（2位小数）
                    System.out.println("单个导出税额设为0 - 税代码: " + paymentAmount.getTaxCode() +
                            ", taxRate字段: " + paymentAmount.getTaxRate() + ", 解析后税率: " + taxRate);
                }
            } else {
                // 没有金额，税额为0
                exportDTO.setTax(formatTax(BigDecimal.ZERO)); // 格式化税额（2位小数）
                exportDTO.setTaxRate(BigDecimal.ZERO);
                exportDTO.setTotalPriceWithTax(formatAmount(BigDecimal.ZERO)); // 格式化含税总额（2位小数）
                System.out.println("单个导出没有金额数据，税额设为0");
            }

            exportList.add(exportDTO);
        }

        return exportList;
    }

    /**
     * 格式化数量字段（保留3位小数）
     */
    private BigDecimal formatQuantity(BigDecimal value) {
        if (value == null) {
            return null;
        }
        return value.setScale(3, RoundingMode.HALF_UP);
    }

    /**
     * 格式化单价字段（保留2位小数）
     */
    private BigDecimal formatUnitPrice(BigDecimal value) {
        if (value == null) {
            return null;
        }
        return value.setScale(2, RoundingMode.HALF_UP);
    }

    /**
     * 格式化金额字段（保留2位小数）
     */
    private BigDecimal formatAmount(BigDecimal value) {
        if (value == null) {
            return null;
        }
        return value.setScale(2, RoundingMode.HALF_UP);
    }

    /**
     * 格式化税额字段（保留2位小数）
     */
    private BigDecimal formatTax(BigDecimal value) {
        if (value == null) {
            return null;
        }
        return value.setScale(2, RoundingMode.HALF_UP);
    }

    /**
     * 格式化铜单价字段（保留2位小数）
     */
    private BigDecimal formatCopperPrice(BigDecimal value) {
        if (value == null) {
            return null;
        }
        return value.setScale(2, RoundingMode.HALF_UP);
    }

    @Override
    public List<Map<String, Object>> getDetailsByShipmentNo(String shipmentNo) throws Exception {
        return salesAmountMapper.getDetailsByShipmentNo(shipmentNo);
    }
}
