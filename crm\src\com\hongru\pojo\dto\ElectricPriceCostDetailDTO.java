package com.hongru.pojo.dto;

import com.hongru.entity.businessOps.ElectricPriceCostDetail;
import com.hongru.support.page.PageInfo;

import java.util.List;

public class ElectricPriceCostDetailDTO {
    private PageInfo pageInfo;

    private List<ElectricPriceCostDetail> electricPriceCostDetailList;

    public ElectricPriceCostDetailDTO(PageInfo pageInfo, List<ElectricPriceCostDetail> electricPriceCostDetailList) {
        super();
        this.pageInfo = pageInfo;
        this.electricPriceCostDetailList = electricPriceCostDetailList;
    }

    public PageInfo getPageInfo() {
        return pageInfo;
    }

    public void setPageInfo(PageInfo pageInfo) {
        this.pageInfo = pageInfo;
    }

    public List<ElectricPriceCostDetail> getElectricPriceCostDetailList() {
        return electricPriceCostDetailList;
    }

    public void setElectricPriceCostDetailList(List<ElectricPriceCostDetail> electricPriceCostDetailList) {
        this.electricPriceCostDetailList = electricPriceCostDetailList;
    }
}
