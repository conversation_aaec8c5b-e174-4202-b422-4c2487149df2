<%@ page language="java" import="java.util.*"
         contentType="text/html; charset=utf-8"%>
<%@ include file="/WEB-INF/layouts/base.jsp"%>
<html>
<head>
    <title>出库计划详情列表</title>
    <style>
        /*设置数据表表头字体*/
        .layui-table th .layui-table-cell{
            text-align: center;
        }
    </style>
</head>
<body class="gray-bg">
    <form class="layui-form hr-form-add" action="javascript:void(0);" method="post" id="submitForm" onsubmit="return false;">
        <div class="layui-fluid hr-layui-fluid">
            <div class="layui-card">
                <div class="layui-card-body">
                    <div class="layui-form-item layui-row">
                        <div class="layui-card">
                            <div class="layui-card-body">
                                <div class="layui-form-item layui-row">
                                    <div class="layui-inline layui-col-md12">
                                        <div>
                                            <table class="layui-table">
                                                <thead>
                                                    <tr>
                                                        <th>客户</th>
                                                        <th>接单号</th>
                                                        <th>订单序号</th>
                                                        <th>接单种类</th>
                                                        <th>条码</th>
                                                        <th>产品代码</th>
                                                        <th>订单数量(Kg)</th>
                                                        <th>出库日期</th>
                                                        <th>到货日期</th>
                                                    </tr>
                                                </thead>
                                                <tbody id="subOrderTableBody">
                                                    <c:if test="${not empty orderDetailsList}">
                                                        <c:forEach items="${orderDetailsList}" var="orderDetail">
                                                            <tr>
                                                                <td>${orderDetail.demandCustomerAlias}</td>
                                                                <td>${orderDetail.orderNo}</td>
                                                                <td>${orderDetail.orderSerialNum}</td>
                                                                <td>${orderDetail.orderType}</td>
                                                                <td>${orderDetail.barcode}</td>
                                                                <td>${orderDetail.modelNumber}</td>
                                                                <td>${orderDetail.orderQuantity}</td>
                                                                <td>${orderDetail.outboundDate}</td>
                                                                <td>${orderDetail.arrivalDate}</td>
                                                            </tr>
                                                        </c:forEach>
                                                    </c:if>
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="layui-form-item layui-row">
                        <div class="layui-inline layui-col-md12">
                            <div style="text-align: center;">
                                <button type="button" class="layui-btn layui-btn-normal" onclick="closeAll();">
                                    <i class="layui-icon layui-icon-return"></i> 返回
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>
    <myfooter>
        <script>
            /**
             * 关闭页面
             */
            function closeAll() {
                var index = parent.layer.getFrameIndex(window.name);
                parent.layer.close(index);
            }
        </script>
    </myfooter>
</body>
</html>
