<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hongru.mapper.businessOps.PaymentAmountDetailMapper">
  <sql id="paymentAmountDetail_sql">
    pad.[支付额明细ID] AS id, pad.[支付请求NO] AS paymentRequestNo, pad.[销售额NO] AS salesAmountNo,
    pad.[销售额NO序号] AS salesAmountSeq, pad.[条码] AS barcode, pad.[产品代码] AS productCode,
    pad.[出货日] AS shipmentDate, pad.[到货日] AS arrivalDate, pad.[客户订单号] AS customerOrderNo,
    pad.[明细备注] AS detailRemark, pad.[铜合同类别] AS copperContractType, pad.[铜合同NO] AS copperContractNo,
    pad.[铜条件] AS copperCondition, pad.[换算率] AS conversionRate, pad.[数量] AS quantity,
    pad.[铜base] AS copperBase, pad.[升水] AS premium, pad.[换算后铜单价] AS convertedCopperPrice,
    pad.[零基础] AS zeroBase, pad.[支付单价] AS paymentUnitPrice, pad.[请求金额] AS requestAmount,
    pad.[产品中分类] AS productMiddleCategory,
    pad.[出货单No.] AS deliveryNoteNo, pad.[送货单序号] AS deliveryNoteSeq, pad.[线盘] AS wireDrum,
    pad.[出货计划番号] AS shipmentPlanNo,
    pa.[客户代码] AS customerCode, cc.[货币] AS copperCurrency,
    pad.[创建人] AS creatorName, pad.[创建时间] AS createdTime,
    pad.[更新人] AS updaterName, pad.[更新时间] AS updatedTime
  </sql>

  <!-- 根据支付请求NO查询明细列表 -->
  <select id="selectByPaymentRequestNo" resultType="com.hongru.entity.businessOps.PaymentAmountDetail">
    SELECT <include refid="paymentAmountDetail_sql"/>
    FROM [支付额明细表] pad
    LEFT JOIN [支付额表] pa ON pad.[支付请求NO] = pa.[支付请求NO]
    LEFT JOIN [铜条件表] cc ON pad.[铜条件] COLLATE Chinese_PRC_CI_AS = cc.[铜条件] COLLATE Chinese_PRC_CI_AS
    WHERE pad.[支付请求NO] = #{paymentRequestNo}
    ORDER BY pad.[支付额明细ID]
  </select>

  <!-- 根据支付请求NO删除明细数据 -->
  <delete id="deleteByPaymentRequestNo">
    DELETE FROM [支付额明细表] WHERE [支付请求NO] = #{paymentRequestNo}
  </delete>

  <!-- 批量插入明细数据 -->
  <insert id="batchInsert">
    INSERT INTO [支付额明细表] (
      [支付请求NO], [销售额NO], [销售额NO序号], [条码], [产品代码], [出货日], [到货日],
      [客户订单号], [明细备注], [铜合同类别], [铜合同NO], [铜条件], [换算率], [数量],
      [铜base], [升水], [换算后铜单价], [零基础], [支付单价], [请求金额], [产品中分类],
      [出货单No.], [送货单序号], [线盘], [出货计划番号],
      [创建人], [创建时间], [更新人], [更新时间]
    ) VALUES
    <foreach collection="details" item="detail" separator=",">
      (
        #{detail.paymentRequestNo}, #{detail.salesAmountNo}, #{detail.salesAmountSeq},
        #{detail.barcode}, #{detail.productCode}, #{detail.shipmentDate}, #{detail.arrivalDate},
        #{detail.customerOrderNo}, #{detail.detailRemark}, #{detail.copperContractType},
        #{detail.copperContractNo}, #{detail.copperCondition}, #{detail.conversionRate},
        #{detail.quantity}, #{detail.copperBase}, #{detail.premium}, #{detail.convertedCopperPrice},
        #{detail.zeroBase}, #{detail.paymentUnitPrice}, #{detail.requestAmount},
        #{detail.productMiddleCategory}, #{detail.deliveryNoteNo}, #{detail.deliveryNoteSeq},
        #{detail.wireDrum}, #{detail.shipmentPlanNo},
        #{detail.creatorName}, #{detail.createdTime}, #{detail.updaterName}, #{detail.updatedTime}
      )
    </foreach>
  </insert>

  <!-- 批量更新明细数据 -->
  <update id="batchUpdate">
    <foreach collection="details" item="detail" separator=";">
      UPDATE [支付额明细表] SET
        [明细备注] = #{detail.detailRemark},
        [铜合同类别] = #{detail.copperContractType},
        [铜合同NO] = #{detail.copperContractNo},
        [铜条件] = #{detail.copperCondition},
        [换算率] = #{detail.conversionRate},
        [数量] = #{detail.quantity},
        [铜base] = #{detail.copperBase},
        [升水] = #{detail.premium},
        [换算后铜单价] = #{detail.convertedCopperPrice},
        [零基础] = #{detail.zeroBase},
        [支付单价] = #{detail.paymentUnitPrice},
        [请求金额] = #{detail.requestAmount},
        [出货单No.] = #{detail.deliveryNoteNo},
        [送货单序号] = #{detail.deliveryNoteSeq},
        [线盘] = #{detail.wireDrum},
        [出货计划番号] = #{detail.shipmentPlanNo},
        [更新人] = #{detail.updaterName},
        [更新时间] = #{detail.updatedTime}
      WHERE [支付额明细ID] = #{detail.id}
    </foreach>
  </update>
</mapper>
