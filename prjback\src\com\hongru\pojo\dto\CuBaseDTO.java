package com.hongru.pojo.dto;

import java.math.BigDecimal;

/**
 * Cu Base数据传输对象
 */
public class CuBaseDTO {

    /**
     * 需求方略称
     */
    private String customerAlias;

    /**
     * 标准货币C
     */
    private String settlementCurrency;

    /**
     * 铜单价（不含税・功能货币）
     */
    private BigDecimal copperPrice;

    /**
     * 销售额数
     */
    private BigDecimal salesQuantity;

    /**
     * 销售额金额（不含税）
     */
    private BigDecimal salesAmount;

    /**
     * 铜金额（不含税・功能货币）
     */
    private BigDecimal copperAmount;

    /**
     * 会计年月
     */
    private String accountingYearMonth;

    // Getters and Setters
    public String getCustomerAlias() {
        return customerAlias;
    }

    public void setCustomerAlias(String customerAlias) {
        this.customerAlias = customerAlias;
    }

    public String getSettlementCurrency() {
        return settlementCurrency;
    }

    public void setSettlementCurrency(String settlementCurrency) {
        this.settlementCurrency = settlementCurrency;
    }

    public BigDecimal getCopperPrice() {
        return copperPrice;
    }

    public void setCopperPrice(BigDecimal copperPrice) {
        this.copperPrice = copperPrice;
    }

    public BigDecimal getSalesQuantity() {
        return salesQuantity;
    }

    public void setSalesQuantity(BigDecimal salesQuantity) {
        this.salesQuantity = salesQuantity;
    }

    public BigDecimal getSalesAmount() {
        return salesAmount;
    }

    public void setSalesAmount(BigDecimal salesAmount) {
        this.salesAmount = salesAmount;
    }

    public BigDecimal getCopperAmount() {
        return copperAmount;
    }

    public void setCopperAmount(BigDecimal copperAmount) {
        this.copperAmount = copperAmount;
    }

    public String getAccountingYearMonth() {
        return accountingYearMonth;
    }

    public void setAccountingYearMonth(String accountingYearMonth) {
        this.accountingYearMonth = accountingYearMonth;
    }
}