package com.hongru.service.impl.businessOps;

import com.hongru.common.util.DateUtils;
import com.hongru.common.util.NumberFormatUtil;
import com.hongru.mapper.businessOps.CuBaseMapper;
import com.hongru.mapper.businessOps.SalesAmountMapper;
import com.hongru.pojo.dto.*;
import com.hongru.service.businessOps.ISalesReportService;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class SalesReportServiceImpl implements ISalesReportService {

    @Autowired
    private SalesAmountMapper salesAmountMapper;

    @Autowired
    private CuBaseMapper cuBaseMapper;

    @Override
    public List<DepartmentalSalesRevenueDTO> listDepartmentalSalesRevenue(String startDate, String endDate) {
        return salesAmountMapper.listDepartmentalSalesRevenue(startDate, endDate);
    }

    @Override
    public void exportDepartmentalSalesRevenue(String startDate, String endDate, HttpServletResponse response)
            throws Exception {
        // 查询数据
        List<DepartmentalSalesRevenueDTO> dataList = listDepartmentalSalesRevenue(startDate, endDate);

        if (dataList == null || dataList.isEmpty()) {
            throw new Exception("没有数据可导出");
        }

        // 获取所有产品中分类
        List<String> productCategories = selectDistinctProductMiddleCategory();

        // 创建工作簿
        Workbook workbook = new XSSFWorkbook();
        Sheet sheet = workbook.createSheet("部门销售额");

        // 设置默认行高和列宽
        sheet.setDefaultRowHeight((short) 400);
        for (int i = 0; i < productCategories.size() * 2 + 3; i++) {
            sheet.setColumnWidth(i, 3500);
        }

        // 创建标题样式
        CellStyle titleStyle = workbook.createCellStyle();
        Font titleFont = workbook.createFont();
        titleFont.setBold(true);
        titleFont.setFontHeightInPoints((short) 12);
        titleStyle.setFont(titleFont);
        titleStyle.setAlignment(HorizontalAlignment.CENTER);
        titleStyle.setVerticalAlignment(VerticalAlignment.CENTER);

        // 创建表头样式
        CellStyle headerStyle = workbook.createCellStyle();
        Font headerFont = workbook.createFont();
        headerFont.setBold(true);
        headerStyle.setFont(headerFont);
        headerStyle.setAlignment(HorizontalAlignment.CENTER);
        headerStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        headerStyle.setBorderTop(BorderStyle.THIN);
        headerStyle.setBorderBottom(BorderStyle.THIN);
        headerStyle.setBorderLeft(BorderStyle.THIN);
        headerStyle.setBorderRight(BorderStyle.THIN);
        headerStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
        headerStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);

        // 创建数据样式
        CellStyle dataStyle = workbook.createCellStyle();
        dataStyle.setAlignment(HorizontalAlignment.RIGHT);
        dataStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        dataStyle.setBorderTop(BorderStyle.THIN);
        dataStyle.setBorderBottom(BorderStyle.THIN);
        dataStyle.setBorderLeft(BorderStyle.THIN);
        dataStyle.setBorderRight(BorderStyle.THIN);

        // 创建文本样式
        CellStyle textStyle = workbook.createCellStyle();
        textStyle.setAlignment(HorizontalAlignment.LEFT);
        textStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        textStyle.setBorderTop(BorderStyle.THIN);
        textStyle.setBorderBottom(BorderStyle.THIN);
        textStyle.setBorderLeft(BorderStyle.THIN);
        textStyle.setBorderRight(BorderStyle.THIN);

        // 获取会计年月（从开始日期）
        String yearMonth = "";
        if (startDate != null && !startDate.isEmpty()) {
            try {
                // 处理日期格式，支持多种格式
                if (startDate.contains("-")) {
                    // 如果是 yyyy-MM-dd 格式
                    yearMonth = startDate.substring(0, 7).replace("-", "");
                } else if (startDate.length() >= 6) {
                    // 如果是 yyyyMMdd 格式
                    yearMonth = startDate.substring(0, 6);
                } else {
                    // 如果格式不正确，使用当前日期
                    yearMonth = DateUtils.convertIntToDateString(DateUtils.getCurrentTime(), "yyyyMM");
                }
            } catch (Exception e) {
                // 如果出现异常，使用当前日期
                yearMonth = DateUtils.convertIntToDateString(DateUtils.getCurrentTime(), "yyyyMM");
            }
        } else {
            // 如果没有开始日期，使用当前日期
            yearMonth = DateUtils.convertIntToDateString(DateUtils.getCurrentTime(), "yyyyMM");
        }

        // 创建会计年月行（第一行）
        Row yearMonthRow = sheet.createRow(0);
        Cell yearMonthTitleCell = yearMonthRow.createCell(0);
        yearMonthTitleCell.setCellValue("会计年月");
        // 合并A1、B1
        sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, 1));
        for (int c = 0; c <= 1; c++) {
            Cell cell = yearMonthRow.getCell(c);
            if (cell == null)
                cell = yearMonthRow.createCell(c);
            cell.setCellStyle(headerStyle);
        }
        for (int i = 0; i < productCategories.size(); i++) {
            int col = i * 2 + 2;
            Cell cell = yearMonthRow.createCell(col);
            cell.setCellValue(yearMonth);
            // 合并每个产品分类的2列
            sheet.addMergedRegion(new CellRangeAddress(0, 0, col, col + 1));
            for (int c = col; c <= col + 1; c++) {
                Cell mergedCell = yearMonthRow.getCell(c);
                if (mergedCell == null)
                    mergedCell = yearMonthRow.createCell(c);
                mergedCell.setCellStyle(headerStyle);
            }
        }
        int totalCol = productCategories.size() * 2 + 2;
        Cell totalCell = yearMonthRow.createCell(totalCol);
        totalCell.setCellValue("合计");
        // 合并合计的2列
        sheet.addMergedRegion(new CellRangeAddress(0, 0, totalCol, totalCol + 1));
        for (int c = totalCol; c <= totalCol + 1; c++) {
            Cell mergedCell = yearMonthRow.getCell(c);
            if (mergedCell == null)
                mergedCell = yearMonthRow.createCell(c);
            mergedCell.setCellStyle(headerStyle);
        }

        // 创建品目中分类名行（第二行）
        Row categoryRow = sheet.createRow(1);
        Cell categoryTitleCell = categoryRow.createCell(0);
        categoryTitleCell.setCellValue("品目中分类名");
        // 合并A2、B2
        sheet.addMergedRegion(new CellRangeAddress(1, 1, 0, 1));
        for (int c = 0; c <= 1; c++) {
            Cell cell = categoryRow.getCell(c);
            if (cell == null)
                cell = categoryRow.createCell(c);
            cell.setCellStyle(headerStyle);
        }
        for (int i = 0; i < productCategories.size(); i++) {
            int col = i * 2 + 2;
            Cell cell = categoryRow.createCell(col);
            cell.setCellValue(productCategories.get(i));
            // 合并每个产品分类的2列
            sheet.addMergedRegion(new CellRangeAddress(1, 1, col, col + 1));
            for (int c = col; c <= col + 1; c++) {
                Cell mergedCell = categoryRow.getCell(c);
                if (mergedCell == null)
                    mergedCell = categoryRow.createCell(c);
                mergedCell.setCellStyle(headerStyle);
            }
        }
        Cell totalCategoryCell = categoryRow.createCell(totalCol);
        totalCategoryCell.setCellValue("合计");
        // 合并合计的2列
        sheet.addMergedRegion(new CellRangeAddress(1, 1, totalCol, totalCol + 1));
        for (int c = totalCol; c <= totalCol + 1; c++) {
            Cell mergedCell = categoryRow.getCell(c);
            if (mergedCell == null)
                mergedCell = categoryRow.createCell(c);
            mergedCell.setCellStyle(headerStyle);
        }

        // 创建表头行（第三行）
        Row headerRow = sheet.createRow(2);
        Cell customerLabelCell = headerRow.createCell(0);
        customerLabelCell.setCellValue("需求方略称");
        customerLabelCell.setCellStyle(headerStyle);
        Cell currencyLabelCell = headerRow.createCell(1);
        currencyLabelCell.setCellValue("货币C");
        currencyLabelCell.setCellStyle(headerStyle);
        for (int i = 0; i < productCategories.size(); i++) {
            int col = i * 2 + 2;
            Cell quantityCell = headerRow.createCell(col);
            quantityCell.setCellValue("销售额数");
            quantityCell.setCellStyle(headerStyle);
            Cell amountCell = headerRow.createCell(col + 1);
            amountCell.setCellValue("销售额金额（不含税）");
            amountCell.setCellStyle(headerStyle);
        }
        Cell totalQuantityCell = headerRow.createCell(totalCol);
        totalQuantityCell.setCellValue("销售额数");
        totalQuantityCell.setCellStyle(headerStyle);
        Cell totalAmountCell = headerRow.createCell(totalCol + 1);
        totalAmountCell.setCellValue("销售额金额（不含税）");
        totalAmountCell.setCellStyle(headerStyle);

        // 按客户和货币分组数据
        Map<String, List<DepartmentalSalesRevenueDTO>> groupedData = dataList.stream()
                .collect(Collectors.groupingBy(dto -> dto.getCustomerAlias() + "_" + dto.getSettlementCurrency()));

        // 填充数据行
        int rowIndex = 3;
        for (Map.Entry<String, List<DepartmentalSalesRevenueDTO>> entry : groupedData.entrySet()) {
            String[] keys = entry.getKey().split("_");
            String customerAlias = keys[0];
            String currency = keys.length > 1 ? keys[1] : "";
            List<DepartmentalSalesRevenueDTO> customerData = entry.getValue();

            Row dataRow = sheet.createRow(rowIndex++);

            // 客户简称和货币
            Cell customerCell = dataRow.createCell(0);
            customerCell.setCellValue(customerAlias);
            customerCell.setCellStyle(textStyle);

            Cell currencyCell = dataRow.createCell(1);
            currencyCell.setCellValue(currency);
            currencyCell.setCellStyle(textStyle);

            // 初始化合计
            BigDecimal totalQuantity = BigDecimal.ZERO;
            BigDecimal totalAmount = BigDecimal.ZERO;

            // 填充产品数据
            for (int i = 0; i < productCategories.size(); i++) {
                String category = productCategories.get(i);

                // 查找该客户该产品的数据
                DepartmentalSalesRevenueDTO categoryData = customerData.stream()
                        .filter(dto -> category.equals(dto.getProductMiddleCategory()))
                        .findFirst()
                        .orElse(null);

                Cell quantityCell = dataRow.createCell(i * 2 + 2);
                Cell amountCell = dataRow.createCell(i * 2 + 3);

                if (categoryData != null) {
                    BigDecimal quantity = categoryData.getSalesQuantity();
                    BigDecimal amount = categoryData.getSalesAmount();

                    quantityCell.setCellValue(NumberFormatUtil.getQuantityValue(quantity));
                    amountCell.setCellValue(NumberFormatUtil.getAmountValue(amount));

                    // 累加合计
                    if (quantity != null) {
                        totalQuantity = totalQuantity.add(quantity);
                    }
                    if (amount != null) {
                        totalAmount = totalAmount.add(amount);
                    }
                } else {
                    quantityCell.setCellValue("");
                    amountCell.setCellValue("");
                }

                quantityCell.setCellStyle(dataStyle);
                amountCell.setCellStyle(dataStyle);
            }

            // 填充合计列
            Cell totalQuantityDataCell = dataRow.createCell(totalCol);
            totalQuantityDataCell.setCellValue(NumberFormatUtil.getQuantityValue(totalQuantity));
            totalQuantityDataCell.setCellStyle(dataStyle);

            Cell totalAmountDataCell = dataRow.createCell(totalCol + 1);
            totalAmountDataCell.setCellValue(NumberFormatUtil.getAmountValue(totalAmount));
            totalAmountDataCell.setCellStyle(dataStyle);
        }

        // 创建合计行专用样式（右对齐+灰色底色）
        CellStyle totalDataStyle = workbook.createCellStyle();
        totalDataStyle.setAlignment(HorizontalAlignment.RIGHT);
        totalDataStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        totalDataStyle.setBorderTop(BorderStyle.THIN);
        totalDataStyle.setBorderBottom(BorderStyle.THIN);
        totalDataStyle.setBorderLeft(BorderStyle.THIN);
        totalDataStyle.setBorderRight(BorderStyle.THIN);
        totalDataStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
        totalDataStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);

        // 创建总计行
        Row totalRow = sheet.createRow(rowIndex);
        Cell totalLabelCell = totalRow.createCell(0);
        totalLabelCell.setCellValue("总计");
        totalLabelCell.setCellStyle(headerStyle);
        // 货币C列为空，保持样式一致
        Cell totalCurrencyCell = totalRow.createCell(1);
        totalCurrencyCell.setCellValue("");
        totalCurrencyCell.setCellStyle(headerStyle);

        // 计算每个产品分类的总计
        Map<String, BigDecimal> categoryTotalQuantity = new HashMap<>();
        Map<String, BigDecimal> categoryTotalAmount = new HashMap<>();

        for (DepartmentalSalesRevenueDTO dto : dataList) {
            String category = dto.getProductMiddleCategory();
            BigDecimal quantity = dto.getSalesQuantity();
            BigDecimal amount = dto.getSalesAmount();

            if (category != null) {
                // 累加数量
                if (quantity != null) {
                    categoryTotalQuantity.put(category,
                            categoryTotalQuantity.getOrDefault(category, BigDecimal.ZERO).add(quantity));
                }

                // 累加金额
                if (amount != null) {
                    categoryTotalAmount.put(category,
                            categoryTotalAmount.getOrDefault(category, BigDecimal.ZERO).add(amount));
                }
            }
        }

        // 总计的总计
        BigDecimal grandTotalQuantity = BigDecimal.ZERO;
        BigDecimal grandTotalAmount = BigDecimal.ZERO;

        // 填充产品总计
        for (int i = 0; i < productCategories.size(); i++) {
            String category = productCategories.get(i);

            BigDecimal quantity = categoryTotalQuantity.getOrDefault(category, BigDecimal.ZERO);
            BigDecimal amount = categoryTotalAmount.getOrDefault(category, BigDecimal.ZERO);

            Cell quantityCell = totalRow.createCell(i * 2 + 2);
            quantityCell.setCellValue(quantity.doubleValue());
            quantityCell.setCellStyle(totalDataStyle); // 右对齐+灰色底色

            Cell amountCell = totalRow.createCell(i * 2 + 3);
            amountCell.setCellValue(amount.doubleValue());
            amountCell.setCellStyle(totalDataStyle); // 右对齐+灰色底色

            // 累加总计的总计
            grandTotalQuantity = grandTotalQuantity.add(quantity);
            grandTotalAmount = grandTotalAmount.add(amount);
        }

        // 填充总计的总计
        Cell grandTotalQuantityCell = totalRow.createCell(totalCol);
        grandTotalQuantityCell.setCellValue(grandTotalQuantity.doubleValue());
        grandTotalQuantityCell.setCellStyle(totalDataStyle); // 右对齐+灰色底色

        Cell grandTotalAmountCell = totalRow.createCell(totalCol + 1);
        grandTotalAmountCell.setCellValue(grandTotalAmount.doubleValue());
        grandTotalAmountCell.setCellStyle(totalDataStyle); // 右对齐+灰色底色

        // 设置文件名
        String fileName = "DepartmentalSalesReport_"
                + DateUtils.convertIntToDateString(DateUtils.getCurrentTime(), "yyyyMMddHHmmss")
                + ".xlsx";

        // 设置响应头
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setHeader("Content-Disposition", "attachment;filename=" + fileName);

        // 写入响应
        ServletOutputStream outputStream = response.getOutputStream();
        workbook.write(outputStream);
        outputStream.flush();
        outputStream.close();
        workbook.close();
    }

    @Override
    public List<String> selectDistinctProductMiddleCategory() {
        return salesAmountMapper.selectDistinctProductMiddleCategory();
    }

    @Override
    public List<ShipmentSalesDiffDTO> listShipmentSalesDiff(String startDate, String endDate) {
        return salesAmountMapper.listShipmentSalesDiff(startDate, endDate);
    }

    @Override
    public List<ShipmentSalesDiffDTO> listShipmentSalesDiffByYearMonth(String yearMonth) {
        return salesAmountMapper.listShipmentSalesDiffByYearMonth(yearMonth);
    }

    @Override
    public void exportShipmentSalesDiff(String startDate, String endDate, HttpServletResponse response)
            throws Exception {
        // 查询数据
        List<ShipmentSalesDiffDTO> dataList = listShipmentSalesDiff(startDate, endDate);

        if (dataList == null || dataList.isEmpty()) {
            throw new Exception("没有数据可导出");
        }

        // 获取所有产品中分类
        List<String> productCategories = selectDistinctProductMiddleCategory();

        // 创建工作簿
        Workbook workbook = new XSSFWorkbook();
        Sheet sheet = workbook.createSheet("出货和销售额差");

        // 设置默认行高和列宽
        sheet.setDefaultRowHeight((short) 400);
        for (int i = 0; i < productCategories.size() * 4 + 1; i++) {
            sheet.setColumnWidth(i, 3500);
        }

        // 创建标题样式 - 添加灰底色
        CellStyle titleStyle = workbook.createCellStyle();
        Font titleFont = workbook.createFont();
        titleFont.setBold(true);
        titleFont.setFontHeightInPoints((short) 12);
        titleStyle.setFont(titleFont);
        titleStyle.setAlignment(HorizontalAlignment.CENTER);
        titleStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        titleStyle.setBorderTop(BorderStyle.THIN);
        titleStyle.setBorderBottom(BorderStyle.THIN);
        titleStyle.setBorderLeft(BorderStyle.THIN);
        titleStyle.setBorderRight(BorderStyle.THIN);
        titleStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
        titleStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);

        // 创建表头样式
        CellStyle headerStyle = workbook.createCellStyle();
        Font headerFont = workbook.createFont();
        headerFont.setBold(true);
        headerStyle.setFont(headerFont);
        headerStyle.setAlignment(HorizontalAlignment.CENTER);
        headerStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        headerStyle.setBorderTop(BorderStyle.THIN);
        headerStyle.setBorderBottom(BorderStyle.THIN);
        headerStyle.setBorderLeft(BorderStyle.THIN);
        headerStyle.setBorderRight(BorderStyle.THIN);
        headerStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
        headerStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);

        // 创建数据样式
        CellStyle dataStyle = workbook.createCellStyle();
        dataStyle.setAlignment(HorizontalAlignment.RIGHT);
        dataStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        dataStyle.setBorderTop(BorderStyle.THIN);
        dataStyle.setBorderBottom(BorderStyle.THIN);
        dataStyle.setBorderLeft(BorderStyle.THIN);
        dataStyle.setBorderRight(BorderStyle.THIN);

        // 创建文本样式
        CellStyle textStyle = workbook.createCellStyle();
        textStyle.setAlignment(HorizontalAlignment.LEFT);
        textStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        textStyle.setBorderTop(BorderStyle.THIN);
        textStyle.setBorderBottom(BorderStyle.THIN);
        textStyle.setBorderLeft(BorderStyle.THIN);
        textStyle.setBorderRight(BorderStyle.THIN);

        // 创建表头文本样式（左对齐+灰色底色）
        CellStyle headerTextStyle = workbook.createCellStyle();
        headerTextStyle.setAlignment(HorizontalAlignment.LEFT);
        headerTextStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        headerTextStyle.setBorderTop(BorderStyle.THIN);
        headerTextStyle.setBorderBottom(BorderStyle.THIN);
        headerTextStyle.setBorderLeft(BorderStyle.THIN);
        headerTextStyle.setBorderRight(BorderStyle.THIN);
        headerTextStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
        headerTextStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        headerTextStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
        headerTextStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);

        // 创建会计年月行（第一行）
        Row yearMonthRow = sheet.createRow(0);
        Cell yearMonthTitleCell = yearMonthRow.createCell(0);
        yearMonthTitleCell.setCellValue("会计年月");
        yearMonthTitleCell.setCellStyle(headerStyle);
        // 从数据中获取会计年月（如果有数据）
        String yearMonth = "";
        if (dataList != null && !dataList.isEmpty() && dataList.get(0) != null) {
            // 使用SQL查询结果中的会计年月
            ShipmentSalesDiffDTO firstRecord = dataList.get(0);
            // 提取会计年月，假设格式为yyyy-MM，转换为yyyyMM
            String accountingYearMonth = firstRecord.getAccountingYearMonth();
            if (accountingYearMonth != null && !accountingYearMonth.isEmpty()) {
                yearMonth = accountingYearMonth.replace("-", "");
            } else {
                // 如果为空，使用当前日期
                yearMonth = new SimpleDateFormat("yyyyMM").format(new Date());
            }
        } else {
            // 如果没有数据，使用当前日期
            yearMonth = new SimpleDateFormat("yyyyMM").format(new Date());
        }

        for (int i = 0; i < productCategories.size(); i++) {
            int col = i * 4 + 1;
            Cell cell = yearMonthRow.createCell(col);
            cell.setCellValue(yearMonth);
            // 合并每个产品分类的4列
            sheet.addMergedRegion(new CellRangeAddress(0, 0, col, col + 3));
            for (int c = col; c <= col + 3; c++) {
                Cell mergedCell = yearMonthRow.getCell(c);
                if (mergedCell == null)
                    mergedCell = yearMonthRow.createCell(c);
                mergedCell.setCellStyle(headerStyle);
            }
        }
        int totalCol = productCategories.size() * 4 + 1;
        Cell totalCell = yearMonthRow.createCell(totalCol);
        totalCell.setCellValue("合计");
        // 合并合计的4列
        sheet.addMergedRegion(new CellRangeAddress(0, 0, totalCol, totalCol + 3));
        for (int c = totalCol; c <= totalCol + 3; c++) {
            Cell mergedCell = yearMonthRow.getCell(c);
            if (mergedCell == null)
                mergedCell = yearMonthRow.createCell(c);
            mergedCell.setCellStyle(headerStyle);
        }

        // 创建品目中分类名行（第二行）
        Row categoryRow = sheet.createRow(1);
        Cell categoryTitleCell = categoryRow.createCell(0);
        categoryTitleCell.setCellValue("品目中分类名");
        categoryTitleCell.setCellStyle(headerStyle);

        for (int i = 0; i < productCategories.size(); i++) {
            int col = i * 4 + 1;
            Cell cell = categoryRow.createCell(col);
            cell.setCellValue(productCategories.get(i));
            // 合并每个产品分类的4列
            sheet.addMergedRegion(new CellRangeAddress(1, 1, col, col + 3));
            for (int c = col; c <= col + 3; c++) {
                Cell mergedCell = categoryRow.getCell(c);
                if (mergedCell == null)
                    mergedCell = categoryRow.createCell(c);
                mergedCell.setCellStyle(headerStyle);
            }
        }
        Cell totalCategoryCell = categoryRow.createCell(totalCol);
        totalCategoryCell.setCellValue("合计");
        // 合并合计的4列
        sheet.addMergedRegion(new CellRangeAddress(1, 1, totalCol, totalCol + 3));
        for (int c = totalCol; c <= totalCol + 3; c++) {
            Cell mergedCell = categoryRow.getCell(c);
            if (mergedCell == null)
                mergedCell = categoryRow.createCell(c);
            mergedCell.setCellStyle(headerStyle);
        }

        // 创建表头行（第三行）
        Row headerRow = sheet.createRow(2);
        Cell customerLabelCell = headerRow.createCell(0);
        customerLabelCell.setCellValue("需求方略称");
        customerLabelCell.setCellStyle(headerStyle);

        for (int i = 0; i < productCategories.size(); i++) {
            int col = i * 4 + 1;
            Cell quantityCell = headerRow.createCell(col);
            quantityCell.setCellValue("出货数");
            quantityCell.setCellStyle(headerStyle);
            Cell salesQuantityCell = headerRow.createCell(col + 1);
            salesQuantityCell.setCellValue("销售额数");
            salesQuantityCell.setCellStyle(headerStyle);
            Cell amountCell = headerRow.createCell(col + 2);
            amountCell.setCellValue("销售额金额（不含税）");
            amountCell.setCellStyle(headerStyle);
            Cell copperCell = headerRow.createCell(col + 3);
            copperCell.setCellValue("铜金额");
            copperCell.setCellStyle(headerStyle);
        }
        Cell totalQuantityCell = headerRow.createCell(totalCol);
        totalQuantityCell.setCellValue("出货数");
        totalQuantityCell.setCellStyle(headerStyle);
        Cell totalSalesQuantityCell = headerRow.createCell(totalCol + 1);
        totalSalesQuantityCell.setCellValue("销售额数");
        totalSalesQuantityCell.setCellStyle(headerStyle);
        Cell totalAmountCell = headerRow.createCell(totalCol + 2);
        totalAmountCell.setCellValue("销售额金额（不含税）");
        totalAmountCell.setCellStyle(headerStyle);
        Cell totalCopperCell = headerRow.createCell(totalCol + 3);
        totalCopperCell.setCellValue("铜金额");
        totalCopperCell.setCellStyle(headerStyle);

        // 按客户分组数据
        Map<String, List<ShipmentSalesDiffDTO>> groupedData = dataList.stream()
                .collect(Collectors.groupingBy(ShipmentSalesDiffDTO::getCustomerAlias));

        // 填充数据行
        int rowIndex = 3;
        for (Map.Entry<String, List<ShipmentSalesDiffDTO>> entry : groupedData.entrySet()) {
            String customerAlias = entry.getKey();
            List<ShipmentSalesDiffDTO> customerData = entry.getValue();

            Row dataRow = sheet.createRow(rowIndex++);

            // 客户简称
            Cell customerCell = dataRow.createCell(0);
            customerCell.setCellValue(customerAlias);
            customerCell.setCellStyle(textStyle);

            // 初始化合计
            BigDecimal totalQuantity = BigDecimal.ZERO;
            BigDecimal totalSalesQuantity = BigDecimal.ZERO;
            BigDecimal totalAmount = BigDecimal.ZERO;
            BigDecimal totalCopper = BigDecimal.ZERO;

            // 填充产品数据
            for (int i = 0; i < productCategories.size(); i++) {
                String category = productCategories.get(i);

                // 查找该客户该产品的数据
                ShipmentSalesDiffDTO categoryData = customerData.stream()
                        .filter(dto -> category.equals(dto.getProductMiddleCategory()))
                        .findFirst()
                        .orElse(null);

                int col = i * 4 + 1;
                Cell quantityCell = dataRow.createCell(col);
                Cell salesQuantityCell = dataRow.createCell(col + 1);
                Cell amountCell = dataRow.createCell(col + 2);
                Cell copperCell = dataRow.createCell(col + 3);

                if (categoryData != null) {
                    BigDecimal quantity = categoryData.getShipmentQuantity();
                    BigDecimal salesQuantity = categoryData.getSalesQuantity();
                    BigDecimal amount = categoryData.getSalesAmount();
                    BigDecimal copper = categoryData.getCopperAmount();

                    quantityCell.setCellValue(NumberFormatUtil.getQuantityValue(quantity));
                    salesQuantityCell.setCellValue(NumberFormatUtil.getQuantityValue(salesQuantity));
                    amountCell.setCellValue(NumberFormatUtil.getAmountValue(amount));
                    copperCell.setCellValue(NumberFormatUtil.getAmountValue(copper));

                    // 累加合计
                    if (quantity != null)
                        totalQuantity = totalQuantity.add(quantity);
                    if (salesQuantity != null)
                        totalSalesQuantity = totalSalesQuantity.add(salesQuantity);
                    if (amount != null)
                        totalAmount = totalAmount.add(amount);
                    if (copper != null)
                        totalCopper = totalCopper.add(copper);
                } else {
                    quantityCell.setCellValue("");
                    salesQuantityCell.setCellValue("");
                    amountCell.setCellValue("");
                    copperCell.setCellValue("");
                }

                quantityCell.setCellStyle(dataStyle);
                salesQuantityCell.setCellStyle(dataStyle);
                amountCell.setCellStyle(dataStyle);
                copperCell.setCellStyle(dataStyle);
            }

            // 填充合计列
            Cell totalQuantityDataCell = dataRow.createCell(totalCol);
            totalQuantityDataCell.setCellValue(NumberFormatUtil.getQuantityValue(totalQuantity));
            totalQuantityDataCell.setCellStyle(dataStyle);

            Cell totalSalesQuantityDataCell = dataRow.createCell(totalCol + 1);
            totalSalesQuantityDataCell.setCellValue(NumberFormatUtil.getQuantityValue(totalSalesQuantity));
            totalSalesQuantityDataCell.setCellStyle(dataStyle);

            Cell totalAmountDataCell = dataRow.createCell(totalCol + 2);
            totalAmountDataCell.setCellValue(NumberFormatUtil.getAmountValue(totalAmount));
            totalAmountDataCell.setCellStyle(dataStyle);

            Cell totalCopperDataCell = dataRow.createCell(totalCol + 3);
            totalCopperDataCell.setCellValue(NumberFormatUtil.getAmountValue(totalCopper));
            totalCopperDataCell.setCellStyle(dataStyle);
        }

        // 创建合计行专用样式（右对齐+灰色底色）
        CellStyle totalDataStyle = workbook.createCellStyle();
        totalDataStyle.setAlignment(HorizontalAlignment.RIGHT);
        totalDataStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        totalDataStyle.setBorderTop(BorderStyle.THIN);
        totalDataStyle.setBorderBottom(BorderStyle.THIN);
        totalDataStyle.setBorderLeft(BorderStyle.THIN);
        totalDataStyle.setBorderRight(BorderStyle.THIN);
        totalDataStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
        totalDataStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);

        // 创建总计行（最后一行）
        Row totalRow = sheet.createRow(rowIndex);
        Cell totalLabelCell = totalRow.createCell(0);
        totalLabelCell.setCellValue("总计");
        totalLabelCell.setCellStyle(totalDataStyle);

        // 计算每个产品分类的总计
        Map<String, BigDecimal> categoryTotalQuantity = new HashMap<>();
        Map<String, BigDecimal> categoryTotalSalesQuantity = new HashMap<>();
        Map<String, BigDecimal> categoryTotalAmount = new HashMap<>();
        Map<String, BigDecimal> categoryTotalCopper = new HashMap<>();

        for (ShipmentSalesDiffDTO dto : dataList) {
            String category = dto.getProductMiddleCategory();
            BigDecimal quantity = dto.getShipmentQuantity();
            BigDecimal salesQuantity = dto.getSalesQuantity();
            BigDecimal amount = dto.getSalesAmount();
            BigDecimal copper = dto.getCopperAmount();

            if (category != null) {
                // 累加数量
                if (quantity != null) {
                    categoryTotalQuantity.put(category,
                            categoryTotalQuantity.getOrDefault(category, BigDecimal.ZERO).add(quantity));
                }
                if (salesQuantity != null) {
                    categoryTotalSalesQuantity.put(category,
                            categoryTotalSalesQuantity.getOrDefault(category, BigDecimal.ZERO).add(salesQuantity));
                }
                // 累加金额
                if (amount != null) {
                    categoryTotalAmount.put(category,
                            categoryTotalAmount.getOrDefault(category, BigDecimal.ZERO).add(amount));
                }
                if (copper != null) {
                    categoryTotalCopper.put(category,
                            categoryTotalCopper.getOrDefault(category, BigDecimal.ZERO).add(copper));
                }
            }
        }

        // 总计的总计
        BigDecimal grandTotalQuantity = BigDecimal.ZERO;
        BigDecimal grandTotalSalesQuantity = BigDecimal.ZERO;
        BigDecimal grandTotalAmount = BigDecimal.ZERO;
        BigDecimal grandTotalCopper = BigDecimal.ZERO;

        // 填充产品总计
        for (int i = 0; i < productCategories.size(); i++) {
            String category = productCategories.get(i);

            BigDecimal quantity = categoryTotalQuantity.getOrDefault(category, BigDecimal.ZERO);
            BigDecimal salesQuantity = categoryTotalSalesQuantity.getOrDefault(category, BigDecimal.ZERO);
            BigDecimal amount = categoryTotalAmount.getOrDefault(category, BigDecimal.ZERO);
            BigDecimal copper = categoryTotalCopper.getOrDefault(category, BigDecimal.ZERO);

            Cell quantityCell = totalRow.createCell(i * 4 + 1);
            quantityCell.setCellValue(quantity.doubleValue());
            quantityCell.setCellStyle(totalDataStyle);

            Cell salesQuantityCell = totalRow.createCell(i * 4 + 2);
            salesQuantityCell.setCellValue(salesQuantity.doubleValue());
            salesQuantityCell.setCellStyle(totalDataStyle);

            Cell amountCell = totalRow.createCell(i * 4 + 3);
            amountCell.setCellValue(amount.doubleValue());
            amountCell.setCellStyle(totalDataStyle);

            Cell copperCell = totalRow.createCell(i * 4 + 4);
            copperCell.setCellValue(copper.doubleValue());
            copperCell.setCellStyle(totalDataStyle);

            // 累加总计的总计
            grandTotalQuantity = grandTotalQuantity.add(quantity);
            grandTotalSalesQuantity = grandTotalSalesQuantity.add(salesQuantity);
            grandTotalAmount = grandTotalAmount.add(amount);
            grandTotalCopper = grandTotalCopper.add(copper);
        }

        // 填充总计的总计
        Cell grandTotalQuantityCell = totalRow.createCell(totalCol);
        grandTotalQuantityCell.setCellValue(grandTotalQuantity.doubleValue());
        grandTotalQuantityCell.setCellStyle(totalDataStyle);

        Cell grandTotalSalesQuantityCell = totalRow.createCell(totalCol + 1);
        grandTotalSalesQuantityCell.setCellValue(grandTotalSalesQuantity.doubleValue());
        grandTotalSalesQuantityCell.setCellStyle(totalDataStyle);

        Cell grandTotalAmountCell = totalRow.createCell(totalCol + 2);
        grandTotalAmountCell.setCellValue(grandTotalAmount.doubleValue());
        grandTotalAmountCell.setCellStyle(totalDataStyle);

        Cell grandTotalCopperCell = totalRow.createCell(totalCol + 3);
        grandTotalCopperCell.setCellValue(grandTotalCopper.doubleValue());
        grandTotalCopperCell.setCellStyle(totalDataStyle);

        // 设置文件名
        String fileName = "ShipmentSalesDiffReport_"
                + new SimpleDateFormat("yyyyMMddHHmmss").format(new Date())
                + ".xlsx";

        // 设置响应头
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setHeader("Content-Disposition", "attachment;filename=" + fileName);

        // 写入响应
        ServletOutputStream outputStream = response.getOutputStream();
        workbook.write(outputStream);
        outputStream.flush();
        outputStream.close();
        workbook.close();
    }

    @Override
    public void exportShipmentSalesDiffByYearMonth(String yearMonth, HttpServletResponse response) throws Exception {
        // 查询数据
        List<ShipmentSalesDiffDTO> dataList = listShipmentSalesDiffByYearMonth(yearMonth);

        if (dataList == null || dataList.isEmpty()) {
            throw new Exception("没有数据可导出");
        }

        // 创建工作簿
        Workbook workbook = new XSSFWorkbook();
        Sheet sheet = workbook.createSheet("出货和销售额差");

        // 设置默认行高和列宽
        sheet.setDefaultRowHeight((short) 400);
        int[] columnWidths = { 3500, 5000, 3000, 3000, 3000, 3000, 3000 };
        for (int i = 0; i < columnWidths.length; i++) {
            sheet.setColumnWidth(i, columnWidths[i]);
        }

        // 创建样式
        CellStyle headerStyle = workbook.createCellStyle();
        headerStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
        headerStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        headerStyle.setBorderTop(BorderStyle.THIN);
        headerStyle.setBorderBottom(BorderStyle.THIN);
        headerStyle.setBorderLeft(BorderStyle.THIN);
        headerStyle.setBorderRight(BorderStyle.THIN);
        headerStyle.setAlignment(HorizontalAlignment.CENTER);
        headerStyle.setVerticalAlignment(VerticalAlignment.CENTER);

        Font headerFont = workbook.createFont();
        headerFont.setBold(true);
        headerStyle.setFont(headerFont);

        CellStyle dataStyle = workbook.createCellStyle();
        dataStyle.setBorderTop(BorderStyle.THIN);
        dataStyle.setBorderBottom(BorderStyle.THIN);
        dataStyle.setBorderLeft(BorderStyle.THIN);
        dataStyle.setBorderRight(BorderStyle.THIN);
        dataStyle.setAlignment(HorizontalAlignment.CENTER);
        dataStyle.setVerticalAlignment(VerticalAlignment.CENTER);

        // 创建表头
        Row headerRow = sheet.createRow(0);
        String[] headers = { "会计年月", "需求方略称", "产品代码", "产品中分类", "差额数量", "差额金额", "差额铜金额" };
        for (int i = 0; i < headers.length; i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(headers[i]);
            cell.setCellStyle(headerStyle);
        }

        // 填充数据
        int rowIndex = 1;
        for (ShipmentSalesDiffDTO data : dataList) {
            Row row = sheet.createRow(rowIndex++);

            Cell cell0 = row.createCell(0);
            cell0.setCellValue(data.getAccountingYearMonth() != null ? data.getAccountingYearMonth() : "");
            cell0.setCellStyle(dataStyle);

            Cell cell1 = row.createCell(1);
            cell1.setCellValue(data.getCustomerAlias() != null ? data.getCustomerAlias() : "");
            cell1.setCellStyle(dataStyle);

            Cell cell2 = row.createCell(2);
            cell2.setCellValue(data.getProductCode() != null ? data.getProductCode() : "");
            cell2.setCellStyle(dataStyle);

            Cell cell3 = row.createCell(3);
            cell3.setCellValue(data.getProductMiddleCategory() != null ? data.getProductMiddleCategory() : "");
            cell3.setCellStyle(dataStyle);

            Cell cell4 = row.createCell(4);
            cell4.setCellValue(data.getSalesQuantity() != null ? data.getSalesQuantity().doubleValue() : 0.0);
            cell4.setCellStyle(dataStyle);

            Cell cell5 = row.createCell(5);
            cell5.setCellValue(data.getSalesAmount() != null ? data.getSalesAmount().doubleValue() : 0.0);
            cell5.setCellStyle(dataStyle);

            Cell cell6 = row.createCell(6);
            cell6.setCellValue(data.getCopperAmount() != null ? data.getCopperAmount().doubleValue() : 0.0);
            cell6.setCellStyle(dataStyle);
        }

        // 设置文件名
        String fileName = "ShipmentSalesDiff_" + yearMonth + "_"
                + DateUtils.convertIntToDateString(DateUtils.getCurrentTime(), "yyyyMMddHHmmss")
                + ".xlsx";

        // 设置响应头
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setHeader("Content-Disposition", "attachment;filename=" + fileName);

        // 写入响应输出流
        ServletOutputStream outputStream = response.getOutputStream();
        workbook.write(outputStream);
        outputStream.flush();
        outputStream.close();
        workbook.close();
    }

    @Override
    public List<CustomerSalesRevenueDTO> listCustomerSalesRevenue(String startDate, String endDate) {
        return salesAmountMapper.listCustomerSalesRevenue(startDate, endDate);
    }

    @Override
    public void exportCustomerSalesRevenue(String startDate, String endDate, HttpServletResponse response)
            throws Exception {
        // 查询数据
        List<CustomerSalesRevenueDTO> dataList = listCustomerSalesRevenue(startDate, endDate);

        if (dataList == null || dataList.isEmpty()) {
            throw new Exception("没有数据可导出");
        }

        // 创建工作簿
        Workbook workbook = new XSSFWorkbook();
        Sheet sheet = workbook.createSheet("客户销售额");

        // 设置默认行高和列宽
        sheet.setDefaultRowHeight((short) 400);
        int[] columnWidths = { 3500, 5000, 5000, 2500, 3000, 3000, 3000, 3000, 3500, 3000, 4000 };
        for (int i = 0; i < columnWidths.length; i++) {
            sheet.setColumnWidth(i, columnWidths[i]);
        }

        // 创建表头样式
        CellStyle headerStyle = workbook.createCellStyle();
        Font headerFont = workbook.createFont();
        headerFont.setBold(true);
        headerStyle.setFont(headerFont);
        headerStyle.setAlignment(HorizontalAlignment.CENTER);
        headerStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        headerStyle.setBorderTop(BorderStyle.THIN);
        headerStyle.setBorderBottom(BorderStyle.THIN);
        headerStyle.setBorderLeft(BorderStyle.THIN);
        headerStyle.setBorderRight(BorderStyle.THIN);
        headerStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
        headerStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);

        // 创建数据样式
        CellStyle dataStyle = workbook.createCellStyle();
        dataStyle.setAlignment(HorizontalAlignment.RIGHT);
        dataStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        dataStyle.setBorderTop(BorderStyle.THIN);
        dataStyle.setBorderBottom(BorderStyle.THIN);
        dataStyle.setBorderLeft(BorderStyle.THIN);
        dataStyle.setBorderRight(BorderStyle.THIN);

        // 创建文本样式
        CellStyle textStyle = workbook.createCellStyle();
        textStyle.setAlignment(HorizontalAlignment.LEFT);
        textStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        textStyle.setBorderTop(BorderStyle.THIN);
        textStyle.setBorderBottom(BorderStyle.THIN);
        textStyle.setBorderLeft(BorderStyle.THIN);
        textStyle.setBorderRight(BorderStyle.THIN);

        // 创建日期样式
        CellStyle dateStyle = workbook.createCellStyle();
        dateStyle.setAlignment(HorizontalAlignment.CENTER);
        dateStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        dateStyle.setBorderTop(BorderStyle.THIN);
        dateStyle.setBorderBottom(BorderStyle.THIN);
        dateStyle.setBorderLeft(BorderStyle.THIN);
        dateStyle.setBorderRight(BorderStyle.THIN);

        // 创建表头行
        Row headerRow = sheet.createRow(0);
        String[] headers = { "会计年月", "需求方略称", "品名", "尺寸", "出货日", "到达日", "销售额数", "销售额单价", "销售额金额", "铜价", "品目中分类名" };
        for (int i = 0; i < headers.length; i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(headers[i]);
            cell.setCellStyle(headerStyle);
        }

        // 填充数据行
        int rowIndex = 1;

        // 初始化合计数据
        BigDecimal totalSalesQuantity = BigDecimal.ZERO;
        BigDecimal totalSalesAmount = BigDecimal.ZERO;

        for (CustomerSalesRevenueDTO dto : dataList) {
            Row dataRow = sheet.createRow(rowIndex++);

            // 会计年月
            Cell yearMonthCell = dataRow.createCell(0);
            yearMonthCell.setCellValue(dto.getAccountingYearMonth());
            yearMonthCell.setCellStyle(textStyle);

            // 客户简称
            Cell customerCell = dataRow.createCell(1);
            customerCell.setCellValue(dto.getCustomerAlias());
            customerCell.setCellStyle(textStyle);

            // 产品代码
            Cell productCodeCell = dataRow.createCell(2);
            productCodeCell.setCellValue(dto.getProductCode());
            productCodeCell.setCellStyle(textStyle);

            // 尺寸
            Cell sizeCell = dataRow.createCell(3);
            sizeCell.setCellValue(dto.getSize());
            sizeCell.setCellStyle(textStyle);

            // 出库日期
            Cell shipmentDateCell = dataRow.createCell(4);
            if (dto.getShipmentDate() != null) {
                shipmentDateCell.setCellValue(dto.getShipmentDate());
            }
            shipmentDateCell.setCellStyle(dateStyle);

            // 到货日期
            Cell arrivalDateCell = dataRow.createCell(5);
            if (dto.getArrivalDate() != null) {
                arrivalDateCell.setCellValue(dto.getArrivalDate());
            }
            arrivalDateCell.setCellStyle(dateStyle);

            // 销售额数
            Cell quantityCell = dataRow.createCell(6);
            if (dto.getSalesQuantity() != null) {
                quantityCell.setCellValue(NumberFormatUtil.getQuantityValue(dto.getSalesQuantity()));
                // 累加合计
                totalSalesQuantity = totalSalesQuantity.add(dto.getSalesQuantity());
            }
            quantityCell.setCellStyle(dataStyle);

            // 销售单价
            Cell unitPriceCell = dataRow.createCell(7);
            if (dto.getSalesUnitPrice() != null) {
                unitPriceCell.setCellValue(NumberFormatUtil.getUnitPriceValue(dto.getSalesUnitPrice()));
            }
            unitPriceCell.setCellStyle(dataStyle);

            // 销售额金额
            Cell amountCell = dataRow.createCell(8);
            if (dto.getSalesAmount() != null) {
                amountCell.setCellValue(NumberFormatUtil.getAmountValue(dto.getSalesAmount()));
                // 累加合计
                totalSalesAmount = totalSalesAmount.add(dto.getSalesAmount());
            }
            amountCell.setCellStyle(dataStyle);

            // 铜价
            Cell copperPriceCell = dataRow.createCell(9);
            if (dto.getCopperPrice() != null) {
                copperPriceCell.setCellValue(NumberFormatUtil.getCopperPriceValue(dto.getCopperPrice()));
            }
            copperPriceCell.setCellStyle(dataStyle);

            // 产品中分类
            Cell categoryCell = dataRow.createCell(10);
            categoryCell.setCellValue(dto.getProductMiddleCategory());
            categoryCell.setCellStyle(textStyle);
        }

        // 创建合计行专用样式（右对齐+灰色底色）
        CellStyle totalDataStyle = workbook.createCellStyle();
        totalDataStyle.setAlignment(HorizontalAlignment.RIGHT);
        totalDataStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        totalDataStyle.setBorderTop(BorderStyle.THIN);
        totalDataStyle.setBorderBottom(BorderStyle.THIN);
        totalDataStyle.setBorderLeft(BorderStyle.THIN);
        totalDataStyle.setBorderRight(BorderStyle.THIN);
        totalDataStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
        totalDataStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);

        // 创建合计行表头样式（居中+灰色底色）
        CellStyle totalHeaderStyle = workbook.createCellStyle();
        totalHeaderStyle.setAlignment(HorizontalAlignment.CENTER);
        totalHeaderStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        totalHeaderStyle.setBorderTop(BorderStyle.THIN);
        totalHeaderStyle.setBorderBottom(BorderStyle.THIN);
        totalHeaderStyle.setBorderLeft(BorderStyle.THIN);
        totalHeaderStyle.setBorderRight(BorderStyle.THIN);
        totalHeaderStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
        totalHeaderStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        Font totalHeaderFont = workbook.createFont();
        totalHeaderFont.setBold(true);
        totalHeaderStyle.setFont(totalHeaderFont);

        // 创建合计行
        Row totalRow = sheet.createRow(rowIndex);

        // 合计标题
        Cell totalLabelCell = totalRow.createCell(0);
        totalLabelCell.setCellValue("合计");
        totalLabelCell.setCellStyle(totalHeaderStyle);

        // 合并第1-6列为"合计"
        sheet.addMergedRegion(new CellRangeAddress(rowIndex, rowIndex, 0, 5));
        for (int i = 1; i <= 5; i++) {
            Cell cell = totalRow.createCell(i);
            cell.setCellStyle(totalHeaderStyle);
        }

        // 销售额数合计
        Cell totalQuantityCell = totalRow.createCell(6);
        totalQuantityCell.setCellValue(totalSalesQuantity.doubleValue());
        totalQuantityCell.setCellStyle(totalDataStyle);

        // 销售单价（不合计）
        Cell totalUnitPriceCell = totalRow.createCell(7);
        totalUnitPriceCell.setCellStyle(totalDataStyle);

        // 销售额金额合计
        Cell totalAmountCell = totalRow.createCell(8);
        totalAmountCell.setCellValue(totalSalesAmount.doubleValue());
        totalAmountCell.setCellStyle(totalDataStyle);

        // 铜价（不合计）
        Cell totalCopperPriceCell = totalRow.createCell(9);
        totalCopperPriceCell.setCellStyle(totalDataStyle);

        // 产品中分类（不合计）
        Cell totalCategoryCell = totalRow.createCell(10);
        totalCategoryCell.setCellStyle(totalDataStyle);

        // 设置文件名
        String fileName = "CustomerSalesReport_"
                + DateUtils.convertIntToDateString(DateUtils.getCurrentTime(), "yyyyMMddHHmmss")
                + ".xlsx";

        // 设置响应头
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setHeader("Content-Disposition", "attachment;filename=" + fileName);

        // 写入响应输出流
        ServletOutputStream outputStream = response.getOutputStream();
        workbook.write(outputStream);
        outputStream.flush();
        outputStream.close();
        workbook.close();
    }

    @Override
    public List<MonthlyShipmentDetailDTO> listMonthlyShipmentDetail(String startDate, String endDate) {
        return salesAmountMapper.listMonthlyShipmentDetail(startDate, endDate);
    }

    @Override
    public void exportMonthlyShipmentDetail(String startDate, String endDate, HttpServletResponse response)
            throws Exception {
        List<MonthlyShipmentDetailDTO> dataList = salesAmountMapper.listMonthlyShipmentDetail(startDate, endDate);

        // 创建工作簿
        Workbook workbook = new XSSFWorkbook();
        Sheet sheet = workbook.createSheet("月出货明细");

        // 设置列宽
        sheet.setColumnWidth(0, 3000); // 会计年月
        sheet.setColumnWidth(1, 4000); // 需求方略称
        sheet.setColumnWidth(2, 6000); // 品名
        sheet.setColumnWidth(3, 2000); // 尺寸
        sheet.setColumnWidth(4, 4000); // 品目中分类名
        sheet.setColumnWidth(5, 3000); // 出货数
        sheet.setColumnWidth(6, 3000); // 线盘

        // 创建表头样式
        CellStyle headerStyle = workbook.createCellStyle();
        Font headerFont = workbook.createFont();
        headerFont.setBold(true);
        headerStyle.setFont(headerFont);
        headerStyle.setAlignment(HorizontalAlignment.CENTER);
        headerStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        headerStyle.setBorderBottom(BorderStyle.THIN);
        headerStyle.setBorderLeft(BorderStyle.THIN);
        headerStyle.setBorderRight(BorderStyle.THIN);
        headerStyle.setBorderTop(BorderStyle.THIN);
        headerStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
        headerStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);

        // 创建表头
        Row headerRow = sheet.createRow(0);
        String[] headers = { "会计年月", "需求方略称", "品名", "尺寸", "品目中分类名", "出货数", "线盘" };
        for (int i = 0; i < headers.length; i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(headers[i]);
            cell.setCellStyle(headerStyle);
        }

        // 创建数据行样式
        CellStyle dataStyle = workbook.createCellStyle();
        dataStyle.setBorderBottom(BorderStyle.THIN);
        dataStyle.setBorderLeft(BorderStyle.THIN);
        dataStyle.setBorderRight(BorderStyle.THIN);
        dataStyle.setBorderTop(BorderStyle.THIN);

        // 数值型单元格样式
        CellStyle numberStyle = workbook.createCellStyle();
        numberStyle.cloneStyleFrom(dataStyle);
        numberStyle.setAlignment(HorizontalAlignment.RIGHT);

        // 填充数据
        int rowIndex = 1;
        for (MonthlyShipmentDetailDTO data : dataList) {
            Row row = sheet.createRow(rowIndex++);

            // 会计年月
            Cell cell0 = row.createCell(0);
            cell0.setCellValue(data.getAccountingYearMonth());
            cell0.setCellStyle(dataStyle);

            // 需求方略称
            Cell cell1 = row.createCell(1);
            cell1.setCellValue(data.getCustomerAlias());
            cell1.setCellStyle(dataStyle);

            // 品名
            Cell cell2 = row.createCell(2);
            cell2.setCellValue(data.getProductCode());
            cell2.setCellStyle(dataStyle);

            // 尺寸
            Cell cell3 = row.createCell(3);
            cell3.setCellValue(data.getSize());
            cell3.setCellStyle(dataStyle);

            // 品目中分类名
            Cell cell4 = row.createCell(4);
            cell4.setCellValue(data.getProductMiddleCategory());
            cell4.setCellStyle(dataStyle);

            // 出货数
            Cell cell5 = row.createCell(5);
            cell5.setCellValue(
                    data.getShipmentQuantity() != null
                            ? NumberFormatUtil.getQuantityValue(BigDecimal.valueOf(data.getShipmentQuantity()))
                            : 0);
            cell5.setCellStyle(numberStyle);

            // 线盘
            Cell cell6 = row.createCell(6);
            cell6.setCellValue(data.getWireReelName());
            cell6.setCellStyle(dataStyle);
        }

        // 设置文件名
        String fileName = "MonthlyShipmentDetail_"
                + new SimpleDateFormat("yyyyMMddHHmmss").format(new Date())
                + ".xlsx";

        // 设置响应头
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setHeader("Content-Disposition", "attachment;filename=" + fileName);

        // 写入响应
        ServletOutputStream outputStream = response.getOutputStream();
        workbook.write(outputStream);
        outputStream.flush();
        outputStream.close();
        workbook.close();
    }

    @Override
    public List<MonthlyShipmentPerformanceDTO> listMonthlyShipmentPerformance(String startDate, String endDate) {
        return salesAmountMapper.listMonthlyShipmentPerformance(startDate, endDate);
    }

    @Override
    public void exportMonthlyShipmentPerformance(String startDate, String endDate, HttpServletResponse response)
            throws Exception {
        try {
            System.out.println("开始导出月出货实绩数据 - startDate: " + startDate + ", endDate: " + endDate);

            // 查询数据
            List<MonthlyShipmentPerformanceDTO> dataList = listMonthlyShipmentPerformance(startDate, endDate);

            if (dataList == null || dataList.isEmpty()) {
                System.out.println("没有找到符合条件的数据");
                throw new Exception("没有数据可导出");
            }

            System.out.println("查询到 " + dataList.size() + " 条记录");

            // 数据验证，检查是否有空值或格式不一致的数据
            int invalidCount = 0;
            for (int i = 0; i < dataList.size(); i++) {
                MonthlyShipmentPerformanceDTO dto = dataList.get(i);
                boolean isValid = true;

                if (dto.getAccountingYearMonth() == null || dto.getAccountingYearMonth().isEmpty()) {
                    System.out.println("警告：记录[" + i + "]的会计年月为空");
                    isValid = false;
                }

                if (dto.getDomesticOrForeign() == null) {
                    System.out.println("警告：记录[" + i + "]的国内/国外为空");
                    isValid = false;
                }

                if (dto.getCustomerAlias() == null) {
                    System.out.println("警告：记录[" + i + "]的客户简称为空");
                    isValid = false;
                }

                if (dto.getProductMiddleCategory() == null) {
                    System.out.println("警告：记录[" + i + "]的产品中分类为空");
                    isValid = false;
                }

                if (dto.getProductCode() == null) {
                    System.out.println("警告：记录[" + i + "]的产品代码为空");
                    isValid = false;
                }

                if (!isValid) {
                    invalidCount++;
                }
            }

            if (invalidCount > 0) {
                System.out.println("警告：发现 " + invalidCount + " 条数据存在空值或格式不一致问题，但仍将继续处理");
            }

            // 打印所有数据，便于调试
            for (int i = 0; i < dataList.size() && i < 10; i++) { // 限制10条，避免日志过多
                MonthlyShipmentPerformanceDTO dto = dataList.get(i);
                System.out.println("数据[" + i + "]: 会计年月=[" + dto.getAccountingYearMonth() +
                        "], 国内/国外=[" + dto.getDomesticOrForeign() +
                        "], 客户简称=[" + dto.getCustomerAlias() +
                        "], 产品中分类=[" + dto.getProductMiddleCategory() +
                        "], 产品代码=[" + dto.getProductCode() +
                        "], 出货数=[" + dto.getShipmentQuantity() + "]");
            }

            // 创建工作簿
            Workbook workbook = new XSSFWorkbook();
            Sheet sheet = workbook.createSheet("月出货实绩");

            // 收集所有唯一的会计年月并按顺序排列
            Set<String> uniqueMonths = new TreeSet<>();
            for (MonthlyShipmentPerformanceDTO dto : dataList) {
                if (dto.getAccountingYearMonth() != null && !dto.getAccountingYearMonth().isEmpty()) {
                    uniqueMonths.add(dto.getAccountingYearMonth());
                }
            }
            List<String> monthsList = new ArrayList<>(uniqueMonths);
            System.out.println("找到的唯一会计年月: " + monthsList);

            // 设置默认行高和列宽
            sheet.setDefaultRowHeight((short) 400);
            // 固定列 + 每个月一列 + 合计列
            int columnCount = 4 + monthsList.size() + 1;
            for (int i = 0; i < columnCount; i++) {
                sheet.setColumnWidth(i, 4500); // 增加列宽，确保能显示完整的数据
            }

            // 创建标题样式
            CellStyle titleStyle = workbook.createCellStyle();
            Font titleFont = workbook.createFont();
            titleFont.setBold(true);
            titleFont.setFontHeightInPoints((short) 12);
            titleStyle.setFont(titleFont);
            titleStyle.setAlignment(HorizontalAlignment.CENTER);
            titleStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            titleStyle.setBorderTop(BorderStyle.THIN);
            titleStyle.setBorderBottom(BorderStyle.THIN);
            titleStyle.setBorderLeft(BorderStyle.THIN);
            titleStyle.setBorderRight(BorderStyle.THIN);
            titleStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
            titleStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);

            // 创建表头样式
            CellStyle headerStyle = workbook.createCellStyle();
            Font headerFont = workbook.createFont();
            headerFont.setBold(true);
            headerStyle.setFont(headerFont);
            headerStyle.setAlignment(HorizontalAlignment.CENTER);
            headerStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            headerStyle.setBorderTop(BorderStyle.THIN);
            headerStyle.setBorderBottom(BorderStyle.THIN);
            headerStyle.setBorderLeft(BorderStyle.THIN);
            headerStyle.setBorderRight(BorderStyle.THIN);
            headerStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
            headerStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);

            // 创建数据样式
            CellStyle dataStyle = workbook.createCellStyle();
            dataStyle.setAlignment(HorizontalAlignment.RIGHT);
            dataStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            dataStyle.setBorderTop(BorderStyle.THIN);
            dataStyle.setBorderBottom(BorderStyle.THIN);
            dataStyle.setBorderLeft(BorderStyle.THIN);
            dataStyle.setBorderRight(BorderStyle.THIN);

            // 创建文本数据样式
            CellStyle textStyle = workbook.createCellStyle();
            textStyle.setAlignment(HorizontalAlignment.LEFT);
            textStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            textStyle.setBorderTop(BorderStyle.THIN);
            textStyle.setBorderBottom(BorderStyle.THIN);
            textStyle.setBorderLeft(BorderStyle.THIN);
            textStyle.setBorderRight(BorderStyle.THIN);

            // 创建合计样式
            CellStyle totalStyle = workbook.createCellStyle();
            Font totalFont = workbook.createFont();
            totalFont.setBold(true);
            totalStyle.setFont(totalFont);
            totalStyle.setAlignment(HorizontalAlignment.RIGHT);
            totalStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            totalStyle.setBorderTop(BorderStyle.THIN);
            totalStyle.setBorderBottom(BorderStyle.THIN);
            totalStyle.setBorderLeft(BorderStyle.THIN);
            totalStyle.setBorderRight(BorderStyle.THIN);
            totalStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
            totalStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);

            // 创建合计数据样式
            CellStyle totalDataStyle = workbook.createCellStyle();
            totalDataStyle.setFont(totalFont);
            totalDataStyle.setAlignment(HorizontalAlignment.RIGHT);
            totalDataStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            totalDataStyle.setBorderTop(BorderStyle.THIN);
            totalDataStyle.setBorderBottom(BorderStyle.THIN);
            totalDataStyle.setBorderLeft(BorderStyle.THIN);
            totalDataStyle.setBorderRight(BorderStyle.THIN);
            totalDataStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
            totalDataStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);

            // 创建会计年月行（第一行）
            Row yearMonthRow = sheet.createRow(0);
            Cell yearMonthTitleCell = yearMonthRow.createCell(0);
            yearMonthTitleCell.setCellValue("会计年月");
            yearMonthTitleCell.setCellStyle(titleStyle);

            // 合并前四列
            sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, 3));
            for (int i = 1; i <= 3; i++) {
                Cell cell = yearMonthRow.createCell(i);
                cell.setCellStyle(titleStyle);
            }

            // 填充会计年月值
            for (int i = 0; i < monthsList.size(); i++) {
                String yearMonth = monthsList.get(i);
                int startCol = 4 + i;
                Cell yearMonthCell = yearMonthRow.createCell(startCol);
                yearMonthCell.setCellValue(yearMonth.replace("-", ""));
                yearMonthCell.setCellStyle(titleStyle);
            }

            // 创建表头行（第二行）
            Row headerRow = sheet.createRow(1);

            // 固定列表头
            String[] fixedHeaders = { "国内/国外", "需求方略称", "品目中分类名", "品名" };
            for (int i = 0; i < fixedHeaders.length; i++) {
                Cell cell = headerRow.createCell(i);
                cell.setCellValue(fixedHeaders[i]);
                cell.setCellStyle(headerStyle);
            }

            // 填充每个会计年月的子表头
            for (int i = 0; i < monthsList.size(); i++) {
                int col = 4 + i;
                Cell cell = headerRow.createCell(col);
                cell.setCellValue("出货数");
                cell.setCellStyle(headerStyle);
            }

            // 按客户和产品分组数据
            Map<String, List<MonthlyShipmentPerformanceDTO>> groupedData = new HashMap<>();

            // 遍历数据进行分组
            for (MonthlyShipmentPerformanceDTO dto : dataList) {
                // 确保所有字段都不为null，如果为null则使用空字符串替代
                String domesticOrForeign = dto.getDomesticOrForeign() != null ? dto.getDomesticOrForeign() : "";
                String customerAlias = dto.getCustomerAlias() != null ? dto.getCustomerAlias() : "";
                String category = dto.getProductMiddleCategory() != null ? dto.getProductMiddleCategory() : "";
                String productCode = dto.getProductCode() != null ? dto.getProductCode() : "";

                // 处理可能包含下划线的字段，替换为其他字符，避免影响分割
                domesticOrForeign = domesticOrForeign.replace("_", "-");
                customerAlias = customerAlias.replace("_", "-");
                category = category.replace("_", "-");
                productCode = productCode.replace("_", "-");

                // 使用组合键作为主键（国内/国外_客户_分类_产品）
                String key = domesticOrForeign + "_" + customerAlias + "_" + category + "_" + productCode;

                // 初始化分组
                if (!groupedData.containsKey(key)) {
                    groupedData.put(key, new ArrayList<>());
                }

                // 添加数据到分组
                groupedData.get(key).add(dto);
            }

            // 初始化总计数组，每个会计年月一个值（出货数）
            BigDecimal[] totalValues = new BigDecimal[monthsList.size()];
            for (int i = 0; i < monthsList.size(); i++) {
                totalValues[i] = BigDecimal.ZERO; // 确保初始化为0而不是null
            }

            // 填充数据行
            int rowIndex = 2;

            // 遍历分组数据
            for (Map.Entry<String, List<MonthlyShipmentPerformanceDTO>> entry : groupedData.entrySet()) {
                String[] keys = entry.getKey().split("_");
                // 添加安全检查，确保数组索引不会越界
                String domesticOrForeign = keys.length > 0 ? keys[0] : "";
                String customerAlias = keys.length > 1 ? keys[1] : "";
                String category = keys.length > 2 ? keys[2] : "";
                String productCode = keys.length > 3 ? keys[3] : "";

                List<MonthlyShipmentPerformanceDTO> items = entry.getValue();

                // 创建数据行
                Row dataRow = sheet.createRow(rowIndex++);

                // 填充固定列
                Cell domesticCell = dataRow.createCell(0);
                domesticCell.setCellValue(domesticOrForeign);
                domesticCell.setCellStyle(textStyle);

                Cell customerCell = dataRow.createCell(1);
                customerCell.setCellValue(customerAlias);
                customerCell.setCellStyle(textStyle);

                Cell categoryCell = dataRow.createCell(2);
                categoryCell.setCellValue(category);
                categoryCell.setCellStyle(textStyle);

                Cell productCell = dataRow.createCell(3);
                productCell.setCellValue(productCode);
                productCell.setCellStyle(textStyle);

                // 初始化所有月份的单元格
                for (int i = 0; i < monthsList.size(); i++) {
                    Cell cell = dataRow.createCell(4 + i);
                    cell.setCellStyle(dataStyle);
                    cell.setCellValue(0); // 默认为0
                }

                // 填充每个会计年月的数据
                for (MonthlyShipmentPerformanceDTO item : items) {
                    String month = item.getAccountingYearMonth();
                    int monthIndex = monthsList.indexOf(month);

                    if (monthIndex >= 0) {
                        // 出货数
                        Cell quantityCell = dataRow.getCell(4 + monthIndex);
                        if (item.getShipmentQuantity() != null) {
                            quantityCell.setCellValue(NumberFormatUtil.getQuantityValue(item.getShipmentQuantity()));

                            // 累加总计
                            if (totalValues[monthIndex] == null) {
                                totalValues[monthIndex] = item.getShipmentQuantity();
                            } else {
                                totalValues[monthIndex] = totalValues[monthIndex].add(item.getShipmentQuantity());
                            }
                        } else {
                            quantityCell.setCellValue(0);
                        }
                    }
                }
            }

            // 添加总计行
            Row totalRow = sheet.createRow(rowIndex);

            // 总计标题
            Cell totalLabelCell = totalRow.createCell(0);
            totalLabelCell.setCellValue("总计");
            totalLabelCell.setCellStyle(totalStyle);

            // 合并前四列
            sheet.addMergedRegion(new CellRangeAddress(rowIndex, rowIndex, 0, 3));
            for (int i = 1; i <= 3; i++) {
                Cell cell = totalRow.createCell(i);
                cell.setCellStyle(totalStyle);
            }

            // 填充总计数据
            for (int i = 0; i < monthsList.size(); i++) {
                Cell totalQuantityCell = totalRow.createCell(4 + i);
                // 确保数值不为null
                BigDecimal value = totalValues[i];
                totalQuantityCell.setCellValue(value != null ? value.doubleValue() : 0);
                totalQuantityCell.setCellStyle(totalStyle);
            }

            // 设置文件名
            String fileName = "MonthlyShipmentPerformance_" + new SimpleDateFormat("yyyyMMddHHmmss").format(new Date())
                    + ".xlsx";

            // 设置HTTP响应头
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setHeader("Content-Disposition", "attachment; filename=\"" + fileName + "\"");

            // 输出到响应流
            ServletOutputStream outputStream = response.getOutputStream();
            workbook.write(outputStream);
            outputStream.flush();
            workbook.close();

            System.out.println("月出货实绩数据导出完成");
        } catch (Exception e) {
            e.printStackTrace();
            System.err.println("导出月出货实绩数据时发生错误: " + e.getMessage());
            throw e;
        }
    }

    /**
     * 查询Cu Base数据
     * 
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return Cu Base数据列表
     */
    @Override
    public List<CuBaseDTO> listCuBase(String startDate, String endDate) {
        // 移除空检查，允许传入空的日期参数
        return cuBaseMapper.listCuBase(startDate, endDate);
    }

    /**
     * 导出Cu Base数据到Excel
     * 
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @param response  HTTP响应
     * @throws Exception 异常
     */
    @Override
    public void exportCuBase(String startDate, String endDate, HttpServletResponse response) throws Exception {
        try {
            // 查询数据
            List<CuBaseDTO> dataList = listCuBase(startDate, endDate);

            if (dataList == null || dataList.isEmpty()) {
                throw new Exception("没有数据可导出");
            }

            System.out.println("查询到 " + dataList.size() + " 条 Cu Base 记录");

            // 创建工作簿
            Workbook workbook = new XSSFWorkbook();
            Sheet sheet = workbook.createSheet("Cu Base");

            // 收集所有唯一的会计年月
            Set<String> uniqueYearMonths = new TreeSet<>();
            for (CuBaseDTO dto : dataList) {
                if (dto.getAccountingYearMonth() != null && !dto.getAccountingYearMonth().isEmpty()) {
                    uniqueYearMonths.add(dto.getAccountingYearMonth());
                }
            }
            List<String> yearMonthList = new ArrayList<>(uniqueYearMonths);

            // 设置列宽
            sheet.setDefaultRowHeight((short) 400);
            // 基础列宽（前三列：需求方略称、标准货币C、铜单价）
            sheet.setColumnWidth(0, 4000); // 需求方略称
            sheet.setColumnWidth(1, 3000); // 标准货币C
            sheet.setColumnWidth(2, 5000); // 铜单价

            // 为每个会计年月设置3列宽度（销售额数、销售额金额、铜金额）
            for (int i = 0; i < yearMonthList.size(); i++) {
                int baseCol = 3 + i * 3;
                sheet.setColumnWidth(baseCol, 3500); // 销售额数
                sheet.setColumnWidth(baseCol + 1, 4500); // 销售额金额
                sheet.setColumnWidth(baseCol + 2, 4500); // 铜金额
            }

            // 创建表头样式
            CellStyle headerStyle = workbook.createCellStyle();
            Font headerFont = workbook.createFont();
            headerFont.setBold(true);
            headerStyle.setFont(headerFont);
            headerStyle.setAlignment(HorizontalAlignment.CENTER);
            headerStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            headerStyle.setBorderTop(BorderStyle.THIN);
            headerStyle.setBorderBottom(BorderStyle.THIN);
            headerStyle.setBorderLeft(BorderStyle.THIN);
            headerStyle.setBorderRight(BorderStyle.THIN);
            headerStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
            headerStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);

            // 创建数据样式（右对齐）
            CellStyle dataStyle = workbook.createCellStyle();
            dataStyle.setAlignment(HorizontalAlignment.RIGHT);
            dataStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            dataStyle.setBorderTop(BorderStyle.THIN);
            dataStyle.setBorderBottom(BorderStyle.THIN);
            dataStyle.setBorderLeft(BorderStyle.THIN);
            dataStyle.setBorderRight(BorderStyle.THIN);

            // 文本样式（左对齐）
            CellStyle textStyle = workbook.createCellStyle();
            textStyle.setAlignment(HorizontalAlignment.LEFT);
            textStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            textStyle.setBorderTop(BorderStyle.THIN);
            textStyle.setBorderBottom(BorderStyle.THIN);
            textStyle.setBorderLeft(BorderStyle.THIN);
            textStyle.setBorderRight(BorderStyle.THIN);

            // 合计行样式
            CellStyle totalStyle = workbook.createCellStyle();
            Font totalFont = workbook.createFont();
            totalFont.setBold(true);
            totalStyle.setFont(totalFont);
            totalStyle.setAlignment(HorizontalAlignment.RIGHT);
            totalStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            totalStyle.setBorderTop(BorderStyle.THIN);
            totalStyle.setBorderBottom(BorderStyle.THIN);
            totalStyle.setBorderLeft(BorderStyle.THIN);
            totalStyle.setBorderRight(BorderStyle.THIN);
            totalStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
            totalStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);

            // 创建会计年月行（第一行）
            Row yearMonthRow = sheet.createRow(0);

            // 固定列标题
            Cell fixedHeaderCell = yearMonthRow.createCell(0);
            fixedHeaderCell.setCellValue("会计年月");
            fixedHeaderCell.setCellStyle(headerStyle);

            // 合并前三列
            sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, 2));
            for (int i = 1; i <= 2; i++) {
                Cell cell = yearMonthRow.createCell(i);
                cell.setCellStyle(headerStyle);
            }

            // 填充会计年月值
            for (int i = 0; i < yearMonthList.size(); i++) {
                String yearMonth = yearMonthList.get(i);
                int startCol = 3 + i * 3;
                Cell yearMonthCell = yearMonthRow.createCell(startCol);
                yearMonthCell.setCellValue(yearMonth.replace("-", ""));
                yearMonthCell.setCellStyle(headerStyle);

                // 合并每个会计年月的3列
                sheet.addMergedRegion(new CellRangeAddress(0, 0, startCol, startCol + 2));
                for (int j = 1; j <= 2; j++) {
                    Cell cell = yearMonthRow.createCell(startCol + j);
                    cell.setCellStyle(headerStyle);
                }
            }

            // 创建表头行（第二行）
            Row headerRow = sheet.createRow(1);

            // 固定列表头
            String[] fixedHeaders = { "需求方略称", "标准货币C", "铜单价（不含税・功能货币）" };
            for (int i = 0; i < fixedHeaders.length; i++) {
                Cell cell = headerRow.createCell(i);
                cell.setCellValue(fixedHeaders[i]);
                cell.setCellStyle(headerStyle);
            }

            // 填充每个会计年月的子表头
            String[] subHeaders = { "销售额数", "销售额金额（不含税）", "铜金额（不含税・功能货币）" };
            for (int i = 0; i < yearMonthList.size(); i++) {
                int startCol = 3 + i * 3;
                for (int j = 0; j < subHeaders.length; j++) {
                    Cell cell = headerRow.createCell(startCol + j);
                    cell.setCellValue(subHeaders[j]);
                    cell.setCellStyle(headerStyle);
                }
            }

            // 按客户+货币+铜单价分组数据
            Map<String, Map<String, List<CuBaseDTO>>> groupedData = new HashMap<>();

            // 遍历数据进行分组
            for (CuBaseDTO dto : dataList) {
                String customerAlias = dto.getCustomerAlias();
                String currency = dto.getSettlementCurrency();
                BigDecimal copperPrice = dto.getCopperPrice();
                String yearMonth = dto.getAccountingYearMonth();

                // 使用客户+货币作为主键
                String customerKey = customerAlias + "_" + currency;

                // 使用铜单价作为二级键
                String priceKey = copperPrice.toString();

                // 初始化客户分组
                if (!groupedData.containsKey(customerKey)) {
                    groupedData.put(customerKey, new HashMap<>());
                }

                // 初始化铜单价分组
                Map<String, List<CuBaseDTO>> customerGroup = groupedData.get(customerKey);
                if (!customerGroup.containsKey(priceKey)) {
                    customerGroup.put(priceKey, new ArrayList<>());
                }

                // 添加数据到分组
                customerGroup.get(priceKey).add(dto);
            }

            // 初始化总计数组，每个会计年月三个值（销售额数、销售额金额、铜金额）
            BigDecimal[][] totalValues = new BigDecimal[yearMonthList.size()][3];
            for (int i = 0; i < yearMonthList.size(); i++) {
                for (int j = 0; j < 3; j++) {
                    totalValues[i][j] = BigDecimal.ZERO;
                }
            }

            // 填充数据行
            int rowIndex = 2;

            // 外层循环：客户和货币
            for (Map.Entry<String, Map<String, List<CuBaseDTO>>> customerEntry : groupedData.entrySet()) {
                String[] keys = customerEntry.getKey().split("_");
                String customerAlias = keys[0];
                String currency = keys.length > 1 ? keys[1] : "";

                // 内层循环：铜单价
                for (Map.Entry<String, List<CuBaseDTO>> priceEntry : customerEntry.getValue().entrySet()) {
                    BigDecimal copperPrice = new BigDecimal(priceEntry.getKey());
                    List<CuBaseDTO> items = priceEntry.getValue();

                    // 创建数据行
                    Row dataRow = sheet.createRow(rowIndex++);

                    // 填充固定列
                    Cell customerCell = dataRow.createCell(0);
                    customerCell.setCellValue(customerAlias);
                    customerCell.setCellStyle(textStyle);

                    Cell currencyCell = dataRow.createCell(1);
                    currencyCell.setCellValue(currency);
                    currencyCell.setCellStyle(textStyle);

                    Cell priceCell = dataRow.createCell(2);
                    priceCell.setCellValue(copperPrice.doubleValue());
                    priceCell.setCellStyle(dataStyle);

                    // 初始化所有年月的空单元格
                    for (int i = 0; i < yearMonthList.size(); i++) {
                        for (int j = 0; j < 3; j++) {
                            Cell cell = dataRow.createCell(3 + i * 3 + j);
                            cell.setCellStyle(dataStyle);
                            // 用空字符串初始化，避免显示0
                            cell.setCellValue("");
                        }
                    }

                    // 填充每个会计年月的数据
                    for (CuBaseDTO item : items) {
                        String yearMonth = item.getAccountingYearMonth();
                        int monthIndex = yearMonthList.indexOf(yearMonth);

                        if (monthIndex >= 0) {
                            int startCol = 3 + monthIndex * 3;

                            // 销售额数
                            Cell quantityCell = dataRow.getCell(startCol);
                            quantityCell.setCellValue(NumberFormatUtil.getQuantityValue(item.getSalesQuantity()));

                            // 销售额金额
                            Cell amountCell = dataRow.getCell(startCol + 1);
                            amountCell.setCellValue(NumberFormatUtil.getAmountValue(item.getSalesAmount()));

                            // 铜金额
                            Cell copperCell = dataRow.getCell(startCol + 2);
                            copperCell.setCellValue(NumberFormatUtil.getAmountValue(item.getCopperAmount()));

                            // 累加总计
                            totalValues[monthIndex][0] = totalValues[monthIndex][0].add(item.getSalesQuantity());
                            totalValues[monthIndex][1] = totalValues[monthIndex][1].add(item.getSalesAmount());
                            totalValues[monthIndex][2] = totalValues[monthIndex][2].add(item.getCopperAmount());
                        }
                    }
                }
            }

            // 添加总计行
            Row totalRow = sheet.createRow(rowIndex);

            // 总计标题
            Cell totalLabelCell = totalRow.createCell(0);
            totalLabelCell.setCellValue("总计");
            totalLabelCell.setCellStyle(totalStyle);

            // 合并前三列
            sheet.addMergedRegion(new CellRangeAddress(rowIndex, rowIndex, 0, 2));
            for (int i = 1; i <= 2; i++) {
                Cell cell = totalRow.createCell(i);
                cell.setCellStyle(totalStyle);
            }

            // 填充总计数据
            for (int i = 0; i < yearMonthList.size(); i++) {
                int startCol = 3 + i * 3;

                // 销售额数总计
                Cell totalQuantityCell = totalRow.createCell(startCol);
                totalQuantityCell.setCellValue(totalValues[i][0].doubleValue());
                totalQuantityCell.setCellStyle(totalStyle);

                // 销售额金额总计
                Cell totalAmountCell = totalRow.createCell(startCol + 1);
                totalAmountCell.setCellValue(totalValues[i][1].doubleValue());
                totalAmountCell.setCellStyle(totalStyle);

                // 铜金额总计
                Cell totalCopperCell = totalRow.createCell(startCol + 2);
                totalCopperCell.setCellValue(totalValues[i][2].doubleValue());
                totalCopperCell.setCellStyle(totalStyle);
            }

            // 设置文件名
            String fileName = "CuBaseReport_"
                    + DateUtils.convertIntToDateString(DateUtils.getCurrentTime(), "yyyyMMddHHmmss") + ".xlsx";

            // 设置HTTP响应头
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setHeader("Content-Disposition", "attachment; filename=\"" + fileName + "\"");

            // 输出到响应流
            ServletOutputStream outputStream = response.getOutputStream();
            workbook.write(outputStream);
            outputStream.flush();
            workbook.close();

            System.out.println("Cu Base数据导出完成: " + fileName);
        } catch (Exception e) {
            e.printStackTrace();
            System.err.println("Cu Base数据导出异常: " + e.getMessage());
            throw e;
        }
    }

    @Override
    public List<ZeroBaseReportDTO> listZeroBaseReport(String startAccountingYearMonth, String endAccountingYearMonth) {
        return salesAmountMapper.listZeroBaseReport(startAccountingYearMonth, endAccountingYearMonth);
    }

    @Override
    public void exportZeroBaseReport(String startAccountingYearMonth, String endAccountingYearMonth,
            HttpServletResponse response) throws Exception {
        try {
            // 查询数据
            List<ZeroBaseReportDTO> dataList = listZeroBaseReport(startAccountingYearMonth, endAccountingYearMonth);

            if (dataList == null || dataList.isEmpty()) {
                throw new Exception("没有数据可导出");
            }

            // 创建工作簿
            Workbook workbook = new XSSFWorkbook();
            Sheet sheet = workbook.createSheet("0-base报表");

            // 设置默认行高和列宽
            sheet.setDefaultRowHeight((short) 400);

            // 基础列数：会计年、品目中分类名、需求方略称、品名、尺寸、货币、0base直接成本、销售额0base单价
            int baseColumns = 8;

            // 获取所有会计年月并排序（过滤null值）
            Set<String> yearMonthSet = dataList.stream()
                    .map(ZeroBaseReportDTO::getAccountingYearMonth)
                    .filter(yearMonth -> yearMonth != null && !yearMonth.trim().isEmpty())
                    .collect(Collectors.toSet());
            List<String> yearMonths = new ArrayList<>(yearMonthSet);
            Collections.sort(yearMonths);

            // 总列数 = 基础列数 + 月份列数 + 合计列
            int totalColumns = baseColumns + yearMonths.size() + 1;

            // 设置列宽
            for (int i = 0; i < totalColumns; i++) {
                sheet.setColumnWidth(i, 3500);
            }

            // 创建样式
            CellStyle headerStyle = workbook.createCellStyle();
            headerStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
            headerStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
            headerStyle.setBorderTop(BorderStyle.THIN);
            headerStyle.setBorderBottom(BorderStyle.THIN);
            headerStyle.setBorderLeft(BorderStyle.THIN);
            headerStyle.setBorderRight(BorderStyle.THIN);
            headerStyle.setAlignment(HorizontalAlignment.CENTER);
            headerStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            Font headerFont = workbook.createFont();
            headerFont.setBold(true);
            headerStyle.setFont(headerFont);

            CellStyle dataStyle = workbook.createCellStyle();
            dataStyle.setBorderTop(BorderStyle.THIN);
            dataStyle.setBorderBottom(BorderStyle.THIN);
            dataStyle.setBorderLeft(BorderStyle.THIN);
            dataStyle.setBorderRight(BorderStyle.THIN);
            dataStyle.setAlignment(HorizontalAlignment.CENTER);
            dataStyle.setVerticalAlignment(VerticalAlignment.CENTER);

            CellStyle numberStyle = workbook.createCellStyle();
            numberStyle.setBorderTop(BorderStyle.THIN);
            numberStyle.setBorderBottom(BorderStyle.THIN);
            numberStyle.setBorderLeft(BorderStyle.THIN);
            numberStyle.setBorderRight(BorderStyle.THIN);
            numberStyle.setAlignment(HorizontalAlignment.RIGHT);
            numberStyle.setVerticalAlignment(VerticalAlignment.CENTER);

            // 创建合计行样式
            CellStyle totalRowStyle = workbook.createCellStyle();
            totalRowStyle.setBorderTop(BorderStyle.THIN);
            totalRowStyle.setBorderBottom(BorderStyle.THIN);
            totalRowStyle.setBorderLeft(BorderStyle.THIN);
            totalRowStyle.setBorderRight(BorderStyle.THIN);
            totalRowStyle.setAlignment(HorizontalAlignment.CENTER);
            totalRowStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            Font totalFont = workbook.createFont();
            totalFont.setBold(true);
            totalRowStyle.setFont(totalFont);

            // 创建合计行数字样式
            CellStyle totalNumberStyle = workbook.createCellStyle();
            totalNumberStyle.setBorderTop(BorderStyle.THIN);
            totalNumberStyle.setBorderBottom(BorderStyle.THIN);
            totalNumberStyle.setBorderLeft(BorderStyle.THIN);
            totalNumberStyle.setBorderRight(BorderStyle.THIN);
            totalNumberStyle.setAlignment(HorizontalAlignment.RIGHT);
            totalNumberStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            totalNumberStyle.setFont(totalFont);

            // 创建表头
            Row headerRow = sheet.createRow(0);
            int colIndex = 0;

            // 基础列表头
            String[] baseHeaders = { "会计年", "品目中分类名", "需求方略称", "品名", "尺寸", "货币",
                    "0base直接成本（单价・功能货币）", "销售额0base单价（不含税・功能货币）" };
            for (String header : baseHeaders) {
                Cell cell = headerRow.createCell(colIndex++);
                cell.setCellValue(header);
                cell.setCellStyle(headerStyle);
            }

            // 月份列表头
            for (String yearMonth : yearMonths) {
                Cell cell = headerRow.createCell(colIndex++);
                cell.setCellValue(yearMonth + "销售额数");
                cell.setCellStyle(headerStyle);
            }

            // 合计列表头
            Cell totalHeaderCell = headerRow.createCell(colIndex);
            totalHeaderCell.setCellValue("合计");
            totalHeaderCell.setCellStyle(headerStyle);

            // 按产品维度分组数据（处理null值）
            Map<String, List<ZeroBaseReportDTO>> groupedData = dataList.stream()
                    .collect(Collectors.groupingBy(
                            item -> (item.getProductMiddleCategory() != null ? item.getProductMiddleCategory() : "")
                                    + "|" +
                                    (item.getCustomerAlias() != null ? item.getCustomerAlias() : "") + "|" +
                                    (item.getProductCode() != null ? item.getProductCode() : "") + "|" +
                                    (item.getLabelSizeName() != null ? item.getLabelSizeName() : "") + "|" +
                                    (item.getCurrency() != null ? item.getCurrency() : "") + "|" +
                                    (item.getZeroBaseDirectCost() != null ? item.getZeroBaseDirectCost().toString()
                                            : "0")
                                    + "|" +
                                    (item.getZeroBaseSalesUnitPrice() != null
                                            ? item.getZeroBaseSalesUnitPrice().toString()
                                            : "0")));

            // 初始化合计变量
            double totalDirectCost = 0.0;
            double totalUnitPrice = 0.0;
            Map<String, Double> monthlyTotals = new HashMap<>();
            for (String yearMonth : yearMonths) {
                monthlyTotals.put(yearMonth, 0.0);
            }
            double grandTotal = 0.0;

            // 填充数据行
            int rowIndex = 1;
            for (Map.Entry<String, List<ZeroBaseReportDTO>> entry : groupedData.entrySet()) {
                String[] keyParts = entry.getKey().split("\\|");
                List<ZeroBaseReportDTO> items = entry.getValue();

                Row dataRow = sheet.createRow(rowIndex++);
                int colIdx = 0;

                // 基础数据列
                // 会计年（从第一个数据项获取）
                String accountingYear = items.get(0).getAccountingYearMonth();
                if (accountingYear != null && accountingYear.length() >= 4) {
                    accountingYear = accountingYear.substring(0, 4);
                }
                Cell yearCell = dataRow.createCell(colIdx++);
                yearCell.setCellValue(accountingYear != null ? accountingYear : "");
                yearCell.setCellStyle(dataStyle);

                // 品目中分类名
                Cell categoryCell = dataRow.createCell(colIdx++);
                categoryCell.setCellValue(keyParts[0]);
                categoryCell.setCellStyle(dataStyle);

                // 需求方略称
                Cell customerCell = dataRow.createCell(colIdx++);
                customerCell.setCellValue(keyParts[1]);
                customerCell.setCellStyle(dataStyle);

                // 品名
                Cell productCell = dataRow.createCell(colIdx++);
                productCell.setCellValue(keyParts[2]);
                productCell.setCellStyle(dataStyle);

                // 尺寸
                Cell sizeCell = dataRow.createCell(colIdx++);
                sizeCell.setCellValue(keyParts[3]);
                sizeCell.setCellStyle(dataStyle);

                // 货币
                Cell currencyCell = dataRow.createCell(colIdx++);
                currencyCell.setCellValue(keyParts[4]);
                currencyCell.setCellStyle(dataStyle);

                // 0base直接成本
                Cell costCell = dataRow.createCell(colIdx++);
                double costValue = 0.0;
                try {
                    BigDecimal costBigDecimal = new BigDecimal(keyParts[5]);
                    costValue = NumberFormatUtil.getUnitPriceValue(costBigDecimal);
                    costCell.setCellValue(costValue);
                } catch (NumberFormatException e) {
                    costCell.setCellValue(0.0);
                }
                costCell.setCellStyle(numberStyle);
                totalDirectCost += costValue;

                // 销售额0base单价
                Cell unitPriceCell = dataRow.createCell(colIdx++);
                double unitPriceValue = 0.0;
                try {
                    BigDecimal unitPriceBigDecimal = new BigDecimal(keyParts[6]);
                    unitPriceValue = NumberFormatUtil.getUnitPriceValue(unitPriceBigDecimal);
                    unitPriceCell.setCellValue(unitPriceValue);
                } catch (NumberFormatException e) {
                    unitPriceCell.setCellValue(0.0);
                }
                unitPriceCell.setCellStyle(numberStyle);
                totalUnitPrice += unitPriceValue;

                // 按月份填充销售额数（处理null值）
                Map<String, BigDecimal> monthlyData = items.stream()
                        .filter(item -> item.getAccountingYearMonth() != null && item.getSalesQuantity() != null)
                        .collect(Collectors.toMap(
                                ZeroBaseReportDTO::getAccountingYearMonth,
                                ZeroBaseReportDTO::getSalesQuantity,
                                BigDecimal::add));

                BigDecimal totalQuantity = BigDecimal.ZERO;
                for (String yearMonth : yearMonths) {
                    Cell monthCell = dataRow.createCell(colIdx++);
                    BigDecimal quantity = monthlyData.getOrDefault(yearMonth, BigDecimal.ZERO);
                    double quantityValue = NumberFormatUtil.getQuantityValue(quantity);
                    monthCell.setCellValue(quantityValue);
                    monthCell.setCellStyle(numberStyle);
                    totalQuantity = totalQuantity.add(quantity);

                    // 累计月度合计
                    monthlyTotals.put(yearMonth, monthlyTotals.get(yearMonth) + quantityValue);
                }

                // 合计列
                Cell totalCell = dataRow.createCell(colIdx);
                double rowTotal = totalQuantity.doubleValue();
                totalCell.setCellValue(rowTotal);
                totalCell.setCellStyle(numberStyle);
                grandTotal += rowTotal;
            }

            // 添加合计行
            Row totalRow = sheet.createRow(rowIndex);
            int totalColIdx = 0;

            // 第一列显示"合计"
            Cell totalLabelCell = totalRow.createCell(totalColIdx++);
            totalLabelCell.setCellValue("合计");
            totalLabelCell.setCellStyle(totalRowStyle);

            // 空白列（品目中分类名、需求方略称、品名、尺寸、货币）
            for (int i = 0; i < 5; i++) {
                Cell emptyCell = totalRow.createCell(totalColIdx++);
                emptyCell.setCellValue("");
                emptyCell.setCellStyle(totalRowStyle);
            }

            // 0base直接成本合计
            Cell totalCostCell = totalRow.createCell(totalColIdx++);
            totalCostCell.setCellValue(totalDirectCost);
            totalCostCell.setCellStyle(totalNumberStyle);

            // 销售额0base单价合计
            Cell totalUnitPriceCell = totalRow.createCell(totalColIdx++);
            totalUnitPriceCell.setCellValue(totalUnitPrice);
            totalUnitPriceCell.setCellStyle(totalNumberStyle);

            // 各月份销售额数合计
            for (String yearMonth : yearMonths) {
                Cell monthTotalCell = totalRow.createCell(totalColIdx++);
                monthTotalCell.setCellValue(monthlyTotals.get(yearMonth));
                monthTotalCell.setCellStyle(totalNumberStyle);
            }

            // 总合计列
            Cell grandTotalCell = totalRow.createCell(totalColIdx);
            grandTotalCell.setCellValue(grandTotal);
            grandTotalCell.setCellStyle(totalNumberStyle);

            // 设置文件名
            String fileName = "ZeroBaseReport_"
                    + DateUtils.convertIntToDateString(DateUtils.getCurrentTime(), "yyyyMMddHHmmss") + ".xlsx";

            // 设置HTTP响应头
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setHeader("Content-Disposition", "attachment; filename=\"" + fileName + "\"");

            // 输出到响应流
            ServletOutputStream outputStream = response.getOutputStream();
            workbook.write(outputStream);
            outputStream.flush();
            workbook.close();

            System.out.println("0-base报表导出完成: " + fileName);
        } catch (Exception e) {
            e.printStackTrace();
            System.err.println("0-base报表导出异常: " + e.getMessage());
            throw e;
        }
    }

    @Override
    public List<UserMarginDTO> listUserMargin(String accountingYearMonth) {
        return salesAmountMapper.listUserMargin(accountingYearMonth);
    }

    @Override
    public void exportUserMargin(String accountingYearMonth, HttpServletResponse response) throws Exception {
        try {
            // 查询数据
            List<UserMarginDTO> dataList = listUserMargin(accountingYearMonth);

            if (dataList == null || dataList.isEmpty()) {
                throw new Exception("没有数据可导出");
            }

            // 创建工作簿
            Workbook workbook = new XSSFWorkbook();
            Sheet sheet = workbook.createSheet("User-Margin报表");

            // 设置默认行高和列宽
            sheet.setDefaultRowHeight((short) 400);

            // 设置列宽
            for (int i = 0; i < 26; i++) { // 26列
                sheet.setColumnWidth(i, 3500);
            }

            // 创建样式
            CellStyle headerStyle = workbook.createCellStyle();
            headerStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
            headerStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
            headerStyle.setBorderTop(BorderStyle.THIN);
            headerStyle.setBorderBottom(BorderStyle.THIN);
            headerStyle.setBorderLeft(BorderStyle.THIN);
            headerStyle.setBorderRight(BorderStyle.THIN);
            headerStyle.setAlignment(HorizontalAlignment.CENTER);
            headerStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            Font headerFont = workbook.createFont();
            headerFont.setBold(true);
            headerStyle.setFont(headerFont);

            CellStyle dataStyle = workbook.createCellStyle();
            dataStyle.setBorderTop(BorderStyle.THIN);
            dataStyle.setBorderBottom(BorderStyle.THIN);
            dataStyle.setBorderLeft(BorderStyle.THIN);
            dataStyle.setBorderRight(BorderStyle.THIN);
            dataStyle.setAlignment(HorizontalAlignment.CENTER);
            dataStyle.setVerticalAlignment(VerticalAlignment.CENTER);

            CellStyle numberStyle = workbook.createCellStyle();
            numberStyle.setBorderTop(BorderStyle.THIN);
            numberStyle.setBorderBottom(BorderStyle.THIN);
            numberStyle.setBorderLeft(BorderStyle.THIN);
            numberStyle.setBorderRight(BorderStyle.THIN);
            numberStyle.setAlignment(HorizontalAlignment.RIGHT);
            numberStyle.setVerticalAlignment(VerticalAlignment.CENTER);

            // 创建合计行样式
            CellStyle totalRowStyle = workbook.createCellStyle();
            totalRowStyle.setBorderTop(BorderStyle.THIN);
            totalRowStyle.setBorderBottom(BorderStyle.THIN);
            totalRowStyle.setBorderLeft(BorderStyle.THIN);
            totalRowStyle.setBorderRight(BorderStyle.THIN);
            totalRowStyle.setAlignment(HorizontalAlignment.CENTER);
            totalRowStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            Font totalFont = workbook.createFont();
            totalFont.setBold(true);
            totalRowStyle.setFont(totalFont);

            // 创建合计行数字样式
            CellStyle totalNumberStyle = workbook.createCellStyle();
            totalNumberStyle.setBorderTop(BorderStyle.THIN);
            totalNumberStyle.setBorderBottom(BorderStyle.THIN);
            totalNumberStyle.setBorderLeft(BorderStyle.THIN);
            totalNumberStyle.setBorderRight(BorderStyle.THIN);
            totalNumberStyle.setAlignment(HorizontalAlignment.RIGHT);
            totalNumberStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            totalNumberStyle.setFont(totalFont);

            // 创建表头
            Row headerRow = sheet.createRow(0);
            String[] headers = {
                    "会计年月", "据点C", "需求方C", "需求方略称", "品名", "品目组名", "尺寸", "需求方组名",
                    "品目中分类名", "品目C", "运费（单价・功能货币）", "0base直接成本（单价・功能货币）",
                    "销售额0base单价（不含税・功能货币）", "铜价", "销售额铜条件", "接单种类分类名", "货币C",
                    "销售额数", "0base直接成本（金额・功能货币）", "销售额0base金额（不含税・功能货币）",
                    "销售额金额（不含税）", "0B综利益额", "铜金额", "目付量", "目付金额", "销售额升水单价（不含税・功能货币）"
            };

            for (int i = 0; i < headers.length; i++) {
                Cell cell = headerRow.createCell(i);
                cell.setCellValue(headers[i]);
                cell.setCellStyle(headerStyle);
            }

            // 初始化合计变量
            double totalSalesQuantity = 0.0;
            double totalZeroBaseDirectCostAmount = 0.0;
            double totalZeroBaseSalesAmount = 0.0;
            double totalSalesAmount = 0.0;
            double totalZeroBaseProfitAmount = 0.0;
            double totalCopperAmount = 0.0;
            double totalFilmWeight = 0.0;
            double totalFilmWeightAmount = 0.0;
            double totalPremiumUnitPrice = 0.0;

            // 填充数据行
            int rowIndex = 1;
            for (UserMarginDTO item : dataList) {
                Row dataRow = sheet.createRow(rowIndex++);
                int colIndex = 0;

                // 填充数据
                Cell accountingYearMonthCell = dataRow.createCell(colIndex++);
                accountingYearMonthCell
                        .setCellValue(item.getAccountingYearMonth() != null ? item.getAccountingYearMonth() : "");
                accountingYearMonthCell.setCellStyle(dataStyle);

                Cell siteCodeCell = dataRow.createCell(colIndex++);
                siteCodeCell.setCellValue(item.getSiteCode() != null ? item.getSiteCode() : "");
                siteCodeCell.setCellStyle(dataStyle);

                Cell customerCodeCell = dataRow.createCell(colIndex++);
                customerCodeCell.setCellValue(item.getCustomerCode() != null ? item.getCustomerCode() : "");
                customerCodeCell.setCellStyle(dataStyle);

                Cell customerAliasCell = dataRow.createCell(colIndex++);
                customerAliasCell.setCellValue(item.getCustomerAlias() != null ? item.getCustomerAlias() : "");
                customerAliasCell.setCellStyle(dataStyle);

                Cell productCodeCell = dataRow.createCell(colIndex++);
                productCodeCell.setCellValue(item.getProductCode() != null ? item.getProductCode() : "");
                productCodeCell.setCellStyle(dataStyle);

                Cell productGroupNameCell = dataRow.createCell(colIndex++);
                productGroupNameCell.setCellValue(item.getProductGroupName() != null ? item.getProductGroupName() : "");
                productGroupNameCell.setCellStyle(dataStyle);

                Cell labelSizeNameCell = dataRow.createCell(colIndex++);
                labelSizeNameCell.setCellValue(item.getLabelSizeName() != null ? item.getLabelSizeName() : "");
                labelSizeNameCell.setCellStyle(dataStyle);

                Cell customerGroupNameCell = dataRow.createCell(colIndex++);
                customerGroupNameCell
                        .setCellValue(item.getCustomerGroupName() != null ? item.getCustomerGroupName() : "");
                customerGroupNameCell.setCellStyle(dataStyle);

                Cell productMiddleCategoryCell = dataRow.createCell(colIndex++);
                productMiddleCategoryCell
                        .setCellValue(item.getProductMiddleCategory() != null ? item.getProductMiddleCategory() : "");
                productMiddleCategoryCell.setCellStyle(dataStyle);

                Cell productCategoryCodeCell = dataRow.createCell(colIndex++);
                productCategoryCodeCell
                        .setCellValue(item.getProductCategoryCode() != null ? item.getProductCategoryCode() : "");
                productCategoryCodeCell.setCellStyle(dataStyle);

                // 数值字段
                Cell transportCostCell = dataRow.createCell(colIndex++);
                double transportCost = item.getTransportationCost() != null
                        ? NumberFormatUtil.getTransportationCostValue(item.getTransportationCost())
                        : 0.0;
                transportCostCell.setCellValue(transportCost);
                transportCostCell.setCellStyle(numberStyle);

                Cell directCostCell = dataRow.createCell(colIndex++);
                double directCost = item.getZeroBaseDirectCost() != null
                        ? NumberFormatUtil.getUnitPriceValue(item.getZeroBaseDirectCost())
                        : 0.0;
                directCostCell.setCellValue(directCost);
                directCostCell.setCellStyle(numberStyle);

                Cell unitPriceCell = dataRow.createCell(colIndex++);
                double unitPrice = item.getZeroBaseSalesUnitPrice() != null
                        ? NumberFormatUtil.getUnitPriceValue(item.getZeroBaseSalesUnitPrice())
                        : 0.0;
                unitPriceCell.setCellValue(unitPrice);
                unitPriceCell.setCellStyle(numberStyle);

                Cell copperPriceCell = dataRow.createCell(colIndex++);
                double copperPrice = item.getCopperPrice() != null
                        ? NumberFormatUtil.getCopperPriceValue(item.getCopperPrice())
                        : 0.0;
                copperPriceCell.setCellValue(copperPrice);
                copperPriceCell.setCellStyle(numberStyle);

                Cell copperConditionCell = dataRow.createCell(colIndex++);
                copperConditionCell.setCellValue(item.getCopperCondition() != null ? item.getCopperCondition() : "");
                copperConditionCell.setCellStyle(dataStyle);

                Cell orderTypeCategoryCell = dataRow.createCell(colIndex++);
                orderTypeCategoryCell
                        .setCellValue(item.getOrderTypeCategory() != null ? item.getOrderTypeCategory() : "");
                orderTypeCategoryCell.setCellStyle(dataStyle);

                Cell currencyCodeCell = dataRow.createCell(colIndex++);
                currencyCodeCell.setCellValue(item.getCurrencyCode() != null ? item.getCurrencyCode() : "");
                currencyCodeCell.setCellStyle(dataStyle);

                Cell quantityCell = dataRow.createCell(colIndex++);
                double quantity = item.getSalesQuantity() != null
                        ? NumberFormatUtil.getQuantityValue(item.getSalesQuantity())
                        : 0.0;
                quantityCell.setCellValue(quantity);
                quantityCell.setCellStyle(numberStyle);
                totalSalesQuantity += quantity;

                Cell directCostAmountCell = dataRow.createCell(colIndex++);
                double directCostAmount = item.getZeroBaseDirectCostAmount() != null
                        ? NumberFormatUtil.getZeroBaseDirectCostAmountValue(item.getZeroBaseDirectCostAmount())
                        : 0.0;
                directCostAmountCell.setCellValue(directCostAmount);
                directCostAmountCell.setCellStyle(numberStyle);
                totalZeroBaseDirectCostAmount += directCostAmount;

                Cell salesAmountCell = dataRow.createCell(colIndex++);
                double salesAmountValue = item.getZeroBaseSalesAmount() != null
                        ? NumberFormatUtil.getAmountValue(item.getZeroBaseSalesAmount())
                        : 0.0;
                salesAmountCell.setCellValue(salesAmountValue);
                salesAmountCell.setCellStyle(numberStyle);
                totalZeroBaseSalesAmount += salesAmountValue;

                Cell totalSalesAmountCell = dataRow.createCell(colIndex++);
                double totalSalesAmountValue = item.getSalesAmount() != null
                        ? NumberFormatUtil.getAmountValue(item.getSalesAmount())
                        : 0.0;
                totalSalesAmountCell.setCellValue(totalSalesAmountValue);
                totalSalesAmountCell.setCellStyle(numberStyle);
                totalSalesAmount += totalSalesAmountValue;

                Cell profitAmountCell = dataRow.createCell(colIndex++);
                double profitAmount = item.getZeroBaseProfitAmount() != null
                        ? NumberFormatUtil.getAmountValue(item.getZeroBaseProfitAmount())
                        : 0.0;
                profitAmountCell.setCellValue(profitAmount);
                profitAmountCell.setCellStyle(numberStyle);
                totalZeroBaseProfitAmount += profitAmount;

                Cell copperAmountCell = dataRow.createCell(colIndex++);
                double copperAmount = item.getCopperAmount() != null
                        ? NumberFormatUtil.getAmountValue(item.getCopperAmount())
                        : 0.0;
                copperAmountCell.setCellValue(copperAmount);
                copperAmountCell.setCellStyle(numberStyle);
                totalCopperAmount += copperAmount;

                Cell filmWeightCell = dataRow.createCell(colIndex++);
                double filmWeight = item.getFilmWeight() != null
                        ? NumberFormatUtil.getFilmWeightValue(item.getFilmWeight())
                        : 0.0;
                filmWeightCell.setCellValue(filmWeight);
                filmWeightCell.setCellStyle(numberStyle);
                totalFilmWeight += filmWeight;

                Cell filmWeightAmountCell = dataRow.createCell(colIndex++);
                double filmWeightAmount = item.getFilmWeightAmount() != null
                        ? NumberFormatUtil.getAmountValue(item.getFilmWeightAmount())
                        : 0.0;
                filmWeightAmountCell.setCellValue(filmWeightAmount);
                filmWeightAmountCell.setCellStyle(numberStyle);
                totalFilmWeightAmount += filmWeightAmount;

                Cell premiumCell = dataRow.createCell(colIndex++);
                double premium = item.getPremiumUnitPrice() != null
                        ? NumberFormatUtil.getTransportationCostValue(item.getPremiumUnitPrice())
                        : 0.0;
                premiumCell.setCellValue(premium);
                premiumCell.setCellStyle(numberStyle);
                totalPremiumUnitPrice += premium;
            }

            // 添加合计行
            Row totalRow = sheet.createRow(rowIndex);
            int totalColIndex = 0;

            // 第一列显示"合计"
            Cell totalLabelCell = totalRow.createCell(totalColIndex++);
            totalLabelCell.setCellValue("合计");
            totalLabelCell.setCellStyle(totalRowStyle);

            // 空白列（前16列除了第一列）
            for (int i = 1; i < 17; i++) {
                Cell emptyCell = totalRow.createCell(totalColIndex++);
                emptyCell.setCellValue("");
                emptyCell.setCellStyle(totalRowStyle);
            }

            // 汇总数值列
            totalRow.createCell(totalColIndex++).setCellValue(totalSalesQuantity);
            totalRow.createCell(totalColIndex++).setCellValue(totalZeroBaseDirectCostAmount);
            totalRow.createCell(totalColIndex++).setCellValue(totalZeroBaseSalesAmount);
            totalRow.createCell(totalColIndex++).setCellValue(totalSalesAmount);
            totalRow.createCell(totalColIndex++).setCellValue(totalZeroBaseProfitAmount);
            totalRow.createCell(totalColIndex++).setCellValue(totalCopperAmount);
            totalRow.createCell(totalColIndex++).setCellValue(totalFilmWeight);
            totalRow.createCell(totalColIndex++).setCellValue(totalFilmWeightAmount);
            totalRow.createCell(totalColIndex++).setCellValue(totalPremiumUnitPrice);

            // 设置合计行样式
            for (int i = 17; i < 26; i++) {
                totalRow.getCell(i).setCellStyle(totalNumberStyle);
            }

            // 设置文件名
            String fileName = "UserMarginReport_"
                    + DateUtils.convertIntToDateString(DateUtils.getCurrentTime(), "yyyyMMddHHmmss") + ".xlsx";

            // 设置HTTP响应头
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setHeader("Content-Disposition", "attachment; filename=\"" + fileName + "\"");

            // 输出到响应流
            ServletOutputStream outputStream = response.getOutputStream();
            workbook.write(outputStream);
            outputStream.flush();
            workbook.close();

            System.out.println("User-Margin报表导出完成: " + fileName);
        } catch (Exception e) {
            e.printStackTrace();
            System.err.println("User-Margin报表导出异常: " + e.getMessage());
            throw e;
        }
    }
}