<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hongru.mapper.businessOps.OrderMapper">

	<select id="orderEntryDetailsListByPage" resultType="com.hongru.entity.businessOps.OrderEntryDetails">
		SELECT DISTINCT
			t1.[流水号] AS id
			, t1.[状态] AS status
			, t1.[订单NO] AS orderNo
			, t1.[据点] AS stronghold
			, t1.[接单日] AS orderDate
			, t1.[接单种类] AS orderType
			, t1.[签约方] AS contractPartyCustomerCode
			, t3.[合同方简称] AS contractPartyCustomerAlias
			, t1.[需求方] AS demandCustomerCode
			, t4.[客户简称] AS demandCustomerAlias
			, t1.[客户订单号] AS customerOrderNo
			, t1.[创建人姓名] AS creatorName
			, t1.[创建时间] AS createdTime
			, t1.[最后修改人姓名] AS updaterName
			, t1.[最后修改时间] AS updatedTime
			, t1.[附件ID] AS attachmentId
			, (SELECT TOP 1 [出货计划确认] FROM [BusinessOps].[dbo].[订单明细表] WHERE [订单NO] = t1.[订单NO] AND [状态] != 9 AND [出货计划确认] IS NOT NULL) AS shipmentPlanStatus
		FROM [BusinessOps].[dbo].[订单表] t1
		LEFT JOIN [BusinessOps].[dbo].[订单明细表] t2 ON t1.[订单NO] = t2.[订单NO]
		LEFT JOIN [BusinessOps].[dbo].[合同方详情表] t3 ON t1.[签约方] COLLATE DATABASE_DEFAULT = t3.[合同方代码] COLLATE DATABASE_DEFAULT
		LEFT JOIN [sumitomo].[dbo].[客户表] t4 ON t1.[需求方] COLLATE DATABASE_DEFAULT = t4.[客户代码] COLLATE DATABASE_DEFAULT
		<where>
			<if test="status != null and status != ''">
				<if test="status == 0">
					AND t1.[状态] != 9
					AND t2.[状态] != 9
					AND t2.[订单确认] = 0
				</if>
				<if test="status == 1">
					AND t1.[状态] != 9
					AND t2.[状态] != 9
					AND t2.[订单确认] = 1
				</if>
				<if test="status == 9">
					AND t2.[状态] = 9
				</if>
			</if>
			<if test="status == null or status == ''">
				AND t2.[状态] != 9
			</if>
			<if test="orderStartDate != null and orderStartDate != ''">
				AND t1.[接单日] &gt;= #{orderStartDate}
			</if>
			<if test="orderEndDate != null and orderEndDate != ''">
				AND t1.[接单日] &lt;= #{orderEndDate}
			</if>
			<if test="prepareStartDate != null and prepareStartDate != ''">
				AND t2.[准备日期] &gt;= #{prepareStartDate}
			</if>
			<if test="prepareEndDate != null and prepareEndDate != ''">
				AND t2.[准备日期] &lt;= #{prepareEndDate}
			</if>
			<if test="outboundStartDate != null and outboundStartDate != ''">
				AND t2.[出库日期] &gt;= #{outboundStartDate}
			</if>
			<if test="outboundEndDate != null and outboundEndDate != ''">
				AND t2.[出库日期] &lt;= #{outboundEndDate}
			</if>
			<if test="demandCustomerCode != null and demandCustomerCode != ''">
				AND t1.[需求方] = #{demandCustomerCode}
			</if>
			<if test="contractPartyCustomerCode != null and contractPartyCustomerCode != ''">
				AND t1.[签约方] = #{contractPartyCustomerCode}
			</if>
			<if test="orderType != null and orderType != ''">
				AND t1.[接单种类] = #{orderType}
			</if>
			<if test="userName != null and userName != ''">
				AND t1.[创建人姓名] = #{userName}
			</if>
		</where>
		ORDER BY t1.[接单日]
		<if test="pageInfo">
			OFFSET ${pageInfo.offset} ROWS
			FETCH NEXT ${pageInfo.limit} ROWS ONLY
		</if>
	</select>

	<select id="orderEntryDetailsListByPageCount" resultType="integer">
		SELECT COUNT(DISTINCT t1.[流水号]) FROM [BusinessOps].[dbo].[订单表] t1
		LEFT JOIN [BusinessOps].[dbo].[订单明细表] t2 ON t1.[订单NO] = t2.[订单NO] AND t2.[状态] != 9
		<where>
			<if test="status != null and status != ''">
				<if test="status == 0">
					AND t1.[状态] != 9
					AND t2.[状态] != 9
					AND t2.[确认] = 0
				</if>
				<if test="status == 1">
					AND t1.[状态] != 9
					AND t2.[状态] != 9
					AND t2.[确认] = 1
				</if>
				<if test="status == 9">
					AND t2.[状态] = 9
				</if>
			</if>
			<if test="status == null or status == ''">
				AND t2.[状态] != 9
			</if>
			<if test="orderStartDate != null and orderStartDate != ''">
				AND t1.[接单日] &gt;= #{orderStartDate}
			</if>
			<if test="orderEndDate != null and orderEndDate != ''">
				AND t1.[接单日] &lt;= #{orderEndDate}
			</if>
			<if test="prepareStartDate != null and prepareStartDate != ''">
				AND t2.[准备日期] &gt;= #{prepareStartDate}
			</if>
			<if test="prepareEndDate != null and prepareEndDate != ''">
				AND t2.[准备日期] &lt;= #{prepareEndDate}
			</if>
			<if test="outboundStartDate != null and outboundStartDate != ''">
				AND t2.[出库日期] &gt;= #{outboundStartDate}
			</if>
			<if test="outboundEndDate != null and outboundEndDate != ''">
				AND t2.[出库日期] &lt;= #{outboundEndDate}
			</if>
			<if test="demandCustomerCode != null and demandCustomerCode != ''">
				AND t1.[需求方] = #{demandCustomerCode}
			</if>
			<if test="contractPartyCustomerCode != null and contractPartyCustomerCode != ''">
				AND t1.[签约方] = #{contractPartyCustomerCode}
			</if>
			<if test="orderType != null and orderType != ''">
				AND t1.[接单种类] = #{orderType}
			</if>
			<if test="userName != null and userName != ''">
				AND t1.[创建人姓名] = #{userName}
			</if>
		</where>
	</select>

	<select id="orderEntryDetailsListByPageForExport" resultType="com.hongru.entity.businessOps.OrderEntryDetails">
		SELECT DISTINCT
			t1.[流水号] AS id
			, t1.[状态] AS status
			, t1.[订单NO] AS orderNo
			, t1.[据点] AS stronghold
			, t1.[接单日] AS orderDate
			, t1.[接单种类] AS orderType
			, t1.[签约方] AS contractPartyCustomerCode
			, t3.[客户简称] AS contractPartyCustomerAlias
			, t1.[需求方] AS demandCustomerCode
			, t4.[客户简称] AS demandCustomerAlias
			, t1.[客户订单号] AS customerOrderNo
			, t1.[创建人姓名] AS creatorName
			, t1.[创建时间] AS createdTime
			, t1.[最后修改人姓名] AS updaterName
			, t1.[最后修改时间] AS updatedTime
			, t1.[附件ID] AS attachmentId
			, t2.[准备日期] AS prepareDate
			, t2.[到货日期] AS arrivalDate
			, t2.[尺寸] AS size
			, t2.[线盘名称] AS wireSpool
			, t2.[部品编号] AS partNumber
			, t2.[订单数量] AS orderQuantity
			, t2.[已送数量] AS deliveredQuantity
			, t2.[订单序号] AS orderSerialNum
			, t2.[产品代码] AS modelNumber
			, t2.[出库日期] AS outboundDate
			, t2.[准备日期] AS prepareDate
			, t2.[明细备注] AS detailRemark
			, t2.[铜合同类别] AS copperContractType
			, t2.[铜签约No] AS copperContractNo
			, t2.[铜条件] AS copperCondition
			, t2.[铜货币] AS copperCurrency
			, t2.[换算率] AS conversionRate
			, t2.[铜基本] AS copperBase
			, t2.[升水] AS premium
			, t2.[换算后铜单价] AS convertedCopperPrice
			, t2.[零基础] AS zeroBase
			, t2.[接单单价] AS orderUnitPrice
			, t2.[接单金额] AS orderAmount
		FROM [BusinessOps].[dbo].[订单表] t1
		LEFT JOIN [BusinessOps].[dbo].[订单明细表] t2 ON t1.[订单NO] = t2.[订单NO]
		LEFT JOIN [sumitomo].[dbo].[客户表] t3 ON t1.[签约方] COLLATE DATABASE_DEFAULT = t3.[客户代码] COLLATE DATABASE_DEFAULT
		LEFT JOIN [sumitomo].[dbo].[客户表] t4 ON t1.[需求方] COLLATE DATABASE_DEFAULT = t4.[客户代码] COLLATE DATABASE_DEFAULT
		<where>
			<if test="status != null and status != ''">
				<if test="status == 0">
					AND t1.[状态] != 9
					AND t2.[状态] != 9
					AND t2.[订单确认] = 0
				</if>
				<if test="status == 1">
					AND t1.[状态] != 9
					AND t2.[状态] != 9
					AND t2.[订单确认] = 1
				</if>
				<if test="status == 9">
					AND t2.[状态] = 9
				</if>
			</if>
			<if test="status == null or status == ''">
				AND t2.[状态] != 9
			</if>
			<if test="planStatus != null and planStatus != ''">
				AND t2.[出货计划确认] = #{planStatus}
			</if>
			<if test="planStatus == null or planStatus == ''">
				AND t2.[出货计划确认] IS NULL
			</if>
			<if test="orderStartDate != null and orderStartDate != ''">
				AND t1.[接单日] &gt;= #{orderStartDate}
			</if>
			<if test="orderEndDate != null and orderEndDate != ''">
				AND t1.[接单日] &lt;= #{orderEndDate}
			</if>
			<if test="prepareStartDate != null and prepareStartDate != ''">
				AND t2.[准备日期] &gt;= #{prepareStartDate}
			</if>
			<if test="prepareEndDate != null and prepareEndDate != ''">
				AND t2.[准备日期] &lt;= #{prepareEndDate}
			</if>
			<if test="outboundStartDate != null and outboundStartDate != ''">
				AND t2.[出库日期] &gt;= #{outboundStartDate}
			</if>
			<if test="outboundEndDate != null and outboundEndDate != ''">
				AND t2.[出库日期] &lt;= #{outboundEndDate}
			</if>
			<if test="demandCustomerCode != null and demandCustomerCode != ''">
				AND t1.[需求方] = #{demandCustomerCode}
			</if>
			<if test="contractPartyCustomerCode != null and contractPartyCustomerCode != ''">
				AND t1.[签约方] = #{contractPartyCustomerCode}
			</if>
			<if test="orderType != null and orderType != ''">
				AND t1.[接单种类] = #{orderType}
			</if>	
			<if test="userName != null and userName != ''">
				AND t1.[创建人姓名] = #{userName}
			</if>
		</where>
		ORDER BY t1.[需求方], t1.[订单NO], t2.[订单序号]
		<if test="pageInfo">
			OFFSET ${pageInfo.offset} ROWS
			FETCH NEXT ${pageInfo.limit} ROWS ONLY
		</if>
	</select>

	<select id="orderEntryDetailsListByPageCountForExport" resultType="integer">
		SELECT COUNT(DISTINCT t1.[流水号]) FROM [BusinessOps].[dbo].[订单表] t1
		LEFT JOIN [BusinessOps].[dbo].[订单明细表] t2 ON t1.[订单NO] = t2.[订单NO] AND t2.[状态] != 9
		<where>
			<if test="status != null and status != ''">
				<if test="status == 0">
					AND t1.[状态] != 9
					AND t2.[状态] != 9
					AND t2.[订单确认] = 0
				</if>
				<if test="status == 1">
					AND t1.[状态] != 9
					AND t2.[状态] != 9
					AND t2.[订单确认] = 1
				</if>
				<if test="status == 9">
					AND t2.[状态] = 9
				</if>
			</if>
			<if test="status == null or status == ''">
				AND t2.[状态] != 9
			</if>
			<if test="planStatus != null and planStatus != ''">
				AND t2.[出货计划确认] = #{planStatus}
			</if>
			<if test="planStatus == null or planStatus == ''">
				AND t2.[出货计划确认] IS NULL
			</if>
			<if test="orderStartDate != null and orderStartDate != ''">
				AND t1.[接单日] &gt;= #{orderStartDate}
			</if>
			<if test="orderEndDate != null and orderEndDate != ''">
				AND t1.[接单日] &lt;= #{orderEndDate}
			</if>
			<if test="prepareStartDate != null and prepareStartDate != ''">
				AND t2.[准备日期] &gt;= #{prepareStartDate}
			</if>
			<if test="prepareEndDate != null and prepareEndDate != ''">
				AND t2.[准备日期] &lt;= #{prepareEndDate}
			</if>
			<if test="outboundStartDate != null and outboundStartDate != ''">
				AND t2.[出库日期] &gt;= #{outboundStartDate}
			</if>
			<if test="outboundEndDate != null and outboundEndDate != ''">
				AND t2.[出库日期] &lt;= #{outboundEndDate}
			</if>
			<if test="demandCustomerCode != null and demandCustomerCode != ''">
				AND t1.[需求方] = #{demandCustomerCode}
			</if>
			<if test="contractPartyCustomerCode != null and contractPartyCustomerCode != ''">
				AND t1.[签约方] = #{contractPartyCustomerCode}
			</if>
			<if test="orderType != null and orderType != ''">
				AND t1.[接单种类] = #{orderType}
			</if>
			<if test="userName != null and userName != ''">
				AND t1.[创建人姓名] = #{userName}
			</if>
		</where>
	</select>

	<select id="orderEntryDetailsById" resultType="com.hongru.entity.businessOps.OrderEntryDetails">
		SELECT
			t1.[流水号] AS id
			, t1.[接单日] AS orderDate
			, t1.[需求方] AS demandCustomerCode
			, t1.[签约方] AS contractPartyCustomerCode
			, t1.[接单种类] AS orderType
			, t1.[客户订单号] AS customerOrderNo
			, t1.[据点] AS stronghold
			, t1.[订单NO] AS orderNo
			, t1.[附件ID] AS attachmentId
			, t2.[准备日期] AS prepareDate
			, t2.[到货日期] AS arrivalDate
			, t2.[尺寸] AS size
			, t2.[线盘名称] AS wireSpool
			, t2.[部品编号] AS partNumber
			, t2.[订单数量] AS orderQuantity
			, t2.[已送数量] AS deliveredQuantity
			, t2.[订单序号] AS orderSerialNum
			, t2.[条码] AS barcode
			, t2.[产品代码] AS modelNumber
			, t2.[出库日期] AS outboundDate
			, t2.[准备日期] AS prepareDate
			, t2.[明细备注] AS detailRemark
			, t2.[铜合同类别] AS copperContractType
			, t2.[铜签约No] AS copperContractNo
			, t2.[铜条件] AS copperCondition
			, t2.[铜货币] AS copperCurrency
			, t2.[换算率] AS conversionRate
			, t2.[铜基本] AS copperBase
			, t2.[升水] AS premium
			, t2.[换算后铜单价] AS convertedCopperPrice
			, t2.[零基础] AS zeroBase
			, t2.[接单单价] AS orderUnitPrice
			, t2.[接单金额] AS orderAmount
		FROM [BusinessOps].[dbo].[订单表] t1
		LEFT JOIN [BusinessOps].[dbo].[订单明细表] t2 ON t1.[订单NO] = t2.[订单NO]
		WHERE t1.[流水号] = #{id}
		AND (t2.[订单序号] IS NOT NULL AND t2.[订单序号] != '')
		<if test="status != null and status != ''">
			<if test="status == 0">
				AND t1.[状态] != 9
				AND t2.[状态] != 9
				AND t2.[确认] = 0
			</if>
			<if test="status == 1">
				AND t1.[状态] != 9
				AND t2.[状态] != 9
				AND t2.[确认] = 1
			</if>
			<if test="status == 9">
				AND t2.[状态] = 9
			</if>
		</if>
		<if test="status == null or status == ''">
			AND t2.[状态] != 9
		</if>
		ORDER BY t2.[订单序号]
	</select>

	<select id="orderEntryDetailsByOrderNo" resultType="com.hongru.entity.businessOps.OrderEntryDetails">
		SELECT
			t1.[流水号] AS id
			, t1.[接单日] AS orderDate
			, t1.[需求方] AS demandCustomerCode
			, t1.[签约方] AS contractPartyCustomerCode
			, t1.[接单种类] AS orderType
			, t1.[客户订单号] AS customerOrderNo
			, t1.[据点] AS stronghold
			, t1.[订单NO] AS orderNo
			, t1.[附件ID] AS attachmentId
			, t2.[准备日期] AS prepareDate
			, t2.[到货日期] AS arrivalDate
			, t2.[尺寸] AS size
			, t2.[线盘名称] AS wireSpool
			, t2.[部品编号] AS partNumber
			, t2.[订单数量] AS orderQuantity
			, t2.[已送数量] AS deliveredQuantity
			, t2.[订单序号] AS orderSerialNum
			, t2.[条码] AS barcode
			, t2.[产品代码] AS modelNumber
			, t2.[出库日期] AS outboundDate
			, t2.[准备日期] AS prepareDate
			, t2.[明细备注] AS detailRemark
			, t2.[出货计划确认] AS shipmentPlanStatus
			, t2.[铜合同类别] AS copperContractType
			, t2.[铜签约No] AS copperContractNo
			, t2.[铜条件] AS copperCondition
			, t2.[铜货币] AS copperCurrency
			, t2.[换算率] AS conversionRate
			, t2.[铜基本] AS copperBase
			, t2.[升水] AS premium
			, t2.[换算后铜单价] AS convertedCopperPrice
			, t2.[零基础] AS zeroBase
			, t2.[接单单价] AS orderUnitPrice
			, t2.[接单金额] AS orderAmount
		FROM [BusinessOps].[dbo].[订单表] t1
		LEFT JOIN [BusinessOps].[dbo].[订单明细表] t2 ON t1.[订单NO] = t2.[订单NO] AND t2.[状态] != 9
		WHERE t1.[订单NO] = #{orderNo}
		AND t1.[状态] != 9
		ORDER BY t2.[订单序号]
	</select>

	<select id="getMaxOrderNoByOrderNoPrefix" resultType="string">
		SELECT TOP 1 [订单NO]
		FROM [BusinessOps].[dbo].[订单表]
		WHERE [订单NO] LIKE CONCAT(#{orderNoPrefix}, '%')
		ORDER BY [订单NO] DESC
	</select>
	
	<select id="getContractPartyCustomerList" resultType="com.hongru.entity.sumitomo.Customers">
		SELECT 
			[合同方代码] AS customerCode
			,[合同方简称] AS customerAlias
		FROM 
			[BusinessOps].[dbo].[合同方详情表]
		ORDER BY [合同方代码] ASC
	</select>

	<insert id="insertOrderEntryInfo" parameterType="com.hongru.entity.businessOps.OrderEntryDetails">
		INSERT INTO [BusinessOps].[dbo].[订单表]
		(
			[状态],
			[订单NO],
			[据点],
			[接单日],
			[接单种类],
			[签约方],
			[需求方],
			[客户订单号],
			[附件ID],
			[创建人姓名],
			[创建时间]
		)
		VALUES
		(
			#{orderEntryInfo.status},
			#{orderEntryInfo.orderNo},
			#{orderEntryInfo.stronghold},
			#{orderEntryInfo.orderDate},
			#{orderEntryInfo.orderType},
			#{orderEntryInfo.contractPartyCustomerCode},
			#{orderEntryInfo.demandCustomerCode},
			#{orderEntryInfo.customerOrderNo},
			#{orderEntryInfo.attachmentId},
			#{orderEntryInfo.creatorName},
			GETDATE()
		)
	</insert>

	<insert id="batchInsertOrderEntryDetails" parameterType="java.util.List">
		INSERT INTO [BusinessOps].[dbo].[订单明细表]
		(
			[订单NO],
			[订单序号],
			[条码],
			[产品代码],
			[出库日期],
			[准备日期],
			[到货日期],
			[尺寸],
			[线盘名称],
			[部品编号],
			[订单数量],
			[已送数量],
			[状态],
			[明细备注],
			[创建人姓名],
			[创建时间],
			[订单确认],
			[铜合同类别],
			[铜签约No],
			[铜条件],
			[铜货币],
			[换算率],
			[铜基本],
			[升水],
			[换算后铜单价],
			[零基础],
			[接单单价],
			[接单金额]
		)
		VALUES
		<foreach collection="orderDetailsList" item="item" separator=",">
		(
			#{item.orderNo},
			#{item.orderSerialNum},
			#{item.barcode},
			#{item.modelNumber},
			<if test="item.outboundDate != null and item.outboundDate != ''">
				TRY_CONVERT(date, #{item.outboundDate})
			</if>
			<if test="item.outboundDate == null or item.outboundDate == ''">
				NULL
			</if>,
			<if test="item.prepareDate != null and item.prepareDate != ''">
				TRY_CONVERT(date, #{item.prepareDate})
			</if>
			<if test="item.prepareDate == null or item.prepareDate == ''">
				NULL
			</if>,
			<if test="item.arrivalDate != null and item.arrivalDate != ''">
				TRY_CONVERT(date, #{item.arrivalDate})
			</if>
			<if test="item.arrivalDate == null or item.arrivalDate == ''">
				NULL
			</if>,
			#{item.size},
			#{item.wireSpool},
			#{item.partNumber},
			#{item.orderQuantity},
			#{item.deliveredQuantity},
			#{item.status},
			#{item.detailRemark},
			#{item.creatorName},
			GETDATE(),
			0,
			#{item.copperContractType},
			#{item.copperContractNo},
			#{item.copperCondition},
			#{item.copperCurrency},
			#{item.conversionRate},
			#{item.copperBase},
			#{item.premium},
			#{item.convertedCopperPrice},
			#{item.zeroBase},
			#{item.orderUnitPrice},
			#{item.orderAmount}
		)
		</foreach>
	</insert>

	<update id="updateOrderEntryInfo" parameterType="com.hongru.entity.businessOps.OrderEntryDetails">
		UPDATE [BusinessOps].[dbo].[订单表]
		SET
		[需求方] = #{orderEntryInfo.demandCustomerCode},
		[签约方] = #{orderEntryInfo.contractPartyCustomerCode},
		[客户订单号] = #{orderEntryInfo.customerOrderNo},
		[附件ID] = #{orderEntryInfo.attachmentId},
		[最后修改人姓名] = #{orderEntryInfo.updaterName},
		[最后修改时间] = GETDATE()
		WHERE [流水号] = #{orderEntryInfo.id}
	</update>

	<update id="updateOrderEntryDetail" parameterType="com.hongru.entity.businessOps.OrderEntryDetails">
		UPDATE [BusinessOps].[dbo].[订单明细表]
		SET
		[出库日期] = <if test="orderEntryDetails.outboundDate != null and orderEntryDetails.outboundDate != ''">
			TRY_CONVERT(date, #{orderEntryDetails.outboundDate})
		</if>
		<if test="orderEntryDetails.outboundDate == null or orderEntryDetails.outboundDate == ''">
			NULL
		</if>,
		[准备日期] = <if test="orderEntryDetails.prepareDate != null and orderEntryDetails.prepareDate != ''">
			TRY_CONVERT(date, #{orderEntryDetails.prepareDate})
		</if>
		<if test="orderEntryDetails.prepareDate == null or orderEntryDetails.prepareDate == ''">
			NULL
		</if>,
		[到货日期] = <if test="orderEntryDetails.arrivalDate != null and orderEntryDetails.arrivalDate != ''">
			TRY_CONVERT(date, #{orderEntryDetails.arrivalDate})
		</if>
		<if test="orderEntryDetails.arrivalDate == null or orderEntryDetails.arrivalDate == ''">
			NULL
		</if>,
		[产品代码] = #{orderEntryDetails.modelNumber},
		[尺寸] = #{orderEntryDetails.size},
		[线盘名称] = #{orderEntryDetails.wireSpool},
		[部品编号] = #{orderEntryDetails.partNumber},
		[条码] = #{orderEntryDetails.barcode},
		[订单数量] = #{orderEntryDetails.orderQuantity},
		[已送数量] = #{orderEntryDetails.deliveredQuantity},
		[明细备注] = #{orderEntryDetails.detailRemark},
		[铜合同类别] = #{orderEntryDetails.copperContractType},
		[铜签约No] = #{orderEntryDetails.copperContractNo},
		[铜条件] = #{orderEntryDetails.copperCondition},
		[铜货币] = #{orderEntryDetails.copperCurrency},
		[换算率] = #{orderEntryDetails.conversionRate},
		[铜基本] = #{orderEntryDetails.copperBase},
		[升水] = #{orderEntryDetails.premium},
		[换算后铜单价] = #{orderEntryDetails.convertedCopperPrice},
		[零基础] = #{orderEntryDetails.zeroBase},
		[接单单价] = #{orderEntryDetails.orderUnitPrice},
		[接单金额] = #{orderEntryDetails.orderAmount},
		[最后修改人姓名] = #{orderEntryDetails.updaterName},
		[最后修改时间] = GETDATE()
		WHERE [订单NO] = #{orderEntryDetails.orderNo} AND [订单序号] = #{orderEntryDetails.orderSerialNum}
	</update>

	<update id="updateOrderEntryInfoStatus" parameterType="integer">
		UPDATE [BusinessOps].[dbo].[订单表]
		SET [状态] = 9
		WHERE [流水号] = #{id}
	</update>

	<update id="updateOrderEntryDetailStatus" parameterType="integer">
		UPDATE [BusinessOps].[dbo].[订单明细表]
		SET [状态] = 9
		WHERE [订单NO] = #{orderNo}
		<if test="orderSerialNum != null and orderSerialNum != ''">
			AND [订单序号] = #{orderSerialNum}
		</if>
	</update>

	<update id="updateOrderEntryDetailDeliveredQuantity" parameterType="com.hongru.entity.businessOps.OrderEntryDetails">
		UPDATE [BusinessOps].[dbo].[订单明细表]
		SET [已送数量] = #{orderEntryInfo.deliveredQuantity},
		[最后修改人姓名] = #{orderEntryInfo.updaterName},
		[最后修改时间] = GETDATE()
		WHERE [订单NO] = #{orderEntryInfo.orderNo}
		AND [订单序号] = #{orderEntryInfo.orderSerialNum}
	</update>

	<select id="queryOrderConfirmListByPage" resultType="com.hongru.entity.businessOps.OrderEntryDetails">
		SELECT
			t2.[订单NO] AS orderNo
			, t2.[订单序号] AS orderSerialNum
			, t1.[签约方] AS contractPartyCustomerCode
			, t3.[合同方简称] AS contractPartyCustomerAlias
			, t1.[需求方] AS demandCustomerCode
			, t4.[客户简称] AS demandCustomerAlias
			, t4.[客户名称] AS demandCustomerName
			, t2.[条码] AS barcode
			, t2.[产品代码] AS modelNumber
			, t2.[订单数量] AS orderQuantity
		    , t2.[线盘名称] AS wireSpool
			, t1.[客户订单号] AS customerOrderNo
			, t2.[出库日期] AS outboundDate
			, t2.[到货日期] AS arrivalDate
			, t2.[状态] AS status
			, t1.[创建人姓名] AS creatorName
			, t1.[创建时间] AS createdTime
			, t1.[最后修改人姓名] AS updaterName
			, t1.[最后修改时间] AS updatedTime
		FROM [BusinessOps].[dbo].[订单表] t1
		LEFT JOIN [BusinessOps].[dbo].[订单明细表] t2 ON t1.[订单NO] = t2.[订单NO]
		LEFT JOIN [BusinessOps].[dbo].[合同方详情表] t3 ON t1.[签约方] COLLATE DATABASE_DEFAULT = t3.[合同方代码] COLLATE DATABASE_DEFAULT
		LEFT JOIN [sumitomo].[dbo].[客户表] t4 ON t1.[需求方] COLLATE DATABASE_DEFAULT = t4.[客户代码] COLLATE DATABASE_DEFAULT
		<where>
			<if test="userName != null and userName != ''">
				AND t1.[创建人姓名] = #{userName}
			</if>
			<if test="outboundStartDate != null and outboundStartDate != ''">
				AND t2.[出库日期] &gt;= #{outboundStartDate}
			</if>
			<if test="outboundEndDate != null and outboundEndDate != ''">
				AND t2.[出库日期] &lt;= #{outboundEndDate}
			</if>
			<if test="demandCustomerCode != null and demandCustomerCode != ''">
				AND t1.[需求方] = #{demandCustomerCode}
			</if>
			<if test="contractPartyCustomerCode != null and contractPartyCustomerCode != ''">
				AND t1.[签约方] = #{contractPartyCustomerCode}
			</if>
			<if test="orderNoList != null and orderNoList.size() > 0">
				AND t2.[订单NO] IN
				<foreach collection="orderNoList" item="item" separator="," open="(" close=")">
					#{item}
				</foreach>
			</if>
			<if test="orderNoList == null or orderNoList.size() == 0">
				AND t2.[订单确认] = 0
			</if>
		</where>
		ORDER BY t2.[订单NO] DESC, t2.[订单序号] ASC
		<if test="pageInfo">
			OFFSET ${pageInfo.offset} ROWS
			FETCH NEXT ${pageInfo.limit} ROWS ONLY
		</if>
	</select>

	<select id="queryOrderConfirmListByPageCount" resultType="integer">
		SELECT COUNT(1) FROM [BusinessOps].[dbo].[订单表] t1
		LEFT JOIN [BusinessOps].[dbo].[订单明细表] t2 ON t1.[订单NO] = t2.[订单NO]
		LEFT JOIN [BusinessOps].[dbo].[合同方详情表] t3 ON t1.[签约方] COLLATE DATABASE_DEFAULT = t3.[合同方代码] COLLATE DATABASE_DEFAULT
		LEFT JOIN [sumitomo].[dbo].[客户表] t4 ON t1.[需求方] COLLATE DATABASE_DEFAULT = t4.[客户代码] COLLATE DATABASE_DEFAULT
		<where>
			<if test="userName != null and userName != ''">
				AND t1.[创建人姓名] = #{userName}
			</if>
			<if test="outboundStartDate != null and outboundStartDate != ''">
				AND t2.[出库日期] &gt;= #{outboundStartDate}
			</if>
			<if test="outboundEndDate != null and outboundEndDate != ''">
				AND t2.[出库日期] &lt;= #{outboundEndDate}
			</if>
			<if test="demandCustomerCode != null and demandCustomerCode != ''">
				AND t1.[需求方] = #{demandCustomerCode}
			</if>
			<if test="contractPartyCustomerCode != null and contractPartyCustomerCode != ''">
				AND t1.[签约方] = #{contractPartyCustomerCode}
			</if>
			<if test="orderNoList != null and orderNoList.size() > 0">
				AND t2.[订单NO] IN
				<foreach collection="orderNoList" item="item" separator="," open="(" close=")">
					#{item}
				</foreach>
			</if>
			<if test="orderNoList == null or orderNoList.size() == 0">
				AND t2.[订单确认] = 0
			</if>
		</where>
	</select>

	<update id="updateOrderConfirm" parameterType="java.util.List">
		UPDATE [BusinessOps].[dbo].[订单明细表]
		SET [订单确认] = 1,
		    [出货计划确认] = null
		WHERE (CONVERT(VARCHAR, [订单NO]) + '-' + CONVERT(VARCHAR, [订单序号])) IN
		<foreach collection="orderNoAndSerialNumsList" item="item" separator="," open="(" close=")">
			#{item}
		</foreach>
	</update>

	<select id="queryShipmentPlanEntryListByPage" resultType="com.hongru.entity.businessOps.OrderEntryDetails">
		SELECT
			t2.[出库日期] AS outboundDate
			, t1.[签约方] AS contractPartyCustomerCode
			, t3.[合同方简称] AS contractPartyCustomerAlias
			, t1.[需求方] AS demandCustomerCode
			, t4.[客户简称] AS demandCustomerAlias
			, t4.[客户地址] AS customerAddress
			, COUNT(1) AS orderCount
		FROM [BusinessOps].[dbo].[订单表] t1
		LEFT JOIN [BusinessOps].[dbo].[订单明细表] t2 ON t1.[订单NO] = t2.[订单NO] AND t2.[状态] != 9
		LEFT JOIN [BusinessOps].[dbo].[合同方详情表] t3 ON t1.[签约方] COLLATE DATABASE_DEFAULT = t3.[合同方代码] COLLATE DATABASE_DEFAULT
		LEFT JOIN [sumitomo].[dbo].[客户表] t4 ON t1.[需求方] COLLATE DATABASE_DEFAULT = t4.[客户代码] COLLATE DATABASE_DEFAULT
		<where>
			t1.[状态] != 9
			AND t2.[订单确认] = 1
			<if test="planStatus != null and planStatus != ''">
				AND t2.[出货计划确认] = #{planStatus}
			</if>
			<if test="planStatus == null or planStatus == ''">
				AND t2.[出货计划确认] IS NULL
			</if>
			<if test="outboundStartDate != null and outboundStartDate != ''">
				AND t2.[出库日期] &gt;= #{outboundStartDate}
			</if>
			<if test="outboundEndDate != null and outboundEndDate != ''">
				AND t2.[出库日期] &lt;= #{outboundEndDate}
			</if>
			<if test="demandCustomerCode != null and demandCustomerCode != ''">
				AND t1.[需求方] = #{demandCustomerCode}
			</if>
			<if test="contractPartyCustomerCode != null and contractPartyCustomerCode != ''">
				AND t1.[签约方] = #{contractPartyCustomerCode}
			</if>
			<if test="userName != null and userName != ''">
				AND t1.[创建人姓名] = #{userName}
			</if>
		</where>
		GROUP BY t2.[出库日期], t1.[签约方], t3.[合同方简称], t1.[需求方], t4.[客户简称], t4.[客户地址]
		ORDER BY t2.[出库日期] ASC, t1.[签约方] ASC
		<if test="pageInfo">
			OFFSET ${pageInfo.offset} ROWS
			FETCH NEXT ${pageInfo.limit} ROWS ONLY
		</if>
	</select>

	<select id="queryShipmentPlanEntryListByPageCount" resultType="integer">
		SELECT COUNT(1) FROM (
			SELECT DISTINCT t2.[出库日期], t1.[签约方], t3.[合同方简称], t1.[需求方], t4.[客户简称], t4.[客户地址] FROM [BusinessOps].[dbo].[订单表] t1
			LEFT JOIN [BusinessOps].[dbo].[订单明细表] t2 ON t1.[订单NO] = t2.[订单NO] AND t2.[状态] != 9
			LEFT JOIN [BusinessOps].[dbo].[合同方详情表] t3 ON t1.[签约方] COLLATE DATABASE_DEFAULT = t3.[合同方代码] COLLATE DATABASE_DEFAULT
			LEFT JOIN [sumitomo].[dbo].[客户表] t4 ON t1.[需求方] COLLATE DATABASE_DEFAULT = t4.[客户代码] COLLATE DATABASE_DEFAULT
			<where>
				t1.[状态] != 9
				AND t2.[订单确认] = 1
				<if test="planStatus != null and planStatus != ''">
					AND t2.[出货计划确认] = #{planStatus}
				</if>
				<if test="outboundStartDate != null and outboundStartDate != ''">
					AND t2.[出库日期] &gt;= #{outboundStartDate}
				</if>
				<if test="outboundEndDate != null and outboundEndDate != ''">
					AND t2.[出库日期] &lt;= #{outboundEndDate}
				</if>
				<if test="demandCustomerCode != null and demandCustomerCode != ''">
					AND t1.[需求方] = #{demandCustomerCode}
				</if>
				<if test="contractPartyCustomerCode != null and contractPartyCustomerCode != ''">
					AND t1.[签约方] = #{contractPartyCustomerCode}
				</if>
				<if test="userName != null and userName != ''">
					AND t1.[创建人姓名] = #{userName}
				</if>
			</where>
		) AS t
	</select>

	<select id="queryShipmentPlanEntryDetailList" resultType="com.hongru.entity.businessOps.OrderEntryDetails">
		SELECT
			t3.[客户简称] AS demandCustomerAlias
			, t2.[订单NO] AS orderNo
			, t2.[订单序号] AS orderSerialNum
			, CASE t1.[接单种类]
				WHEN 0 THEN '标准'
				ELSE ''
				END AS orderType
			, t2.[条码] AS barcode
			, t2.[产品代码] AS modelNumber
			, t2.[订单数量] AS orderQuantity
			, t2.[出库日期] AS outboundDate
			, t2.[到货日期] AS arrivalDate
			, t1.[签约方] AS contractPartyCustomerCode
			, t1.[需求方] AS demandCustomerCode
		FROM [BusinessOps].[dbo].[订单表] t1
		LEFT JOIN [BusinessOps].[dbo].[订单明细表] t2 ON t1.[订单NO] = t2.[订单NO] AND t2.[状态] != 9
		LEFT JOIN [sumitomo].[dbo].[客户表] t3 ON t1.[需求方] COLLATE DATABASE_DEFAULT = t3.[客户代码] COLLATE DATABASE_DEFAULT
		<where>
			t1.[状态] != 9
			AND t2.[订单确认] = 1
			<if test="planStatus != null and planStatus != ''">
				AND t2.[出货计划确认] = #{planStatus}
			</if>
			<if test="outboundDate != null and outboundDate != ''">
				AND t2.[出库日期] = #{outboundDate}
			</if>
			<if test="demandCustomerCode != null and demandCustomerCode != ''">
				AND t1.[需求方] = #{demandCustomerCode}
			</if>
			<if test="contractPartyCustomerCode != null and contractPartyCustomerCode != ''">
				AND t1.[签约方] = #{contractPartyCustomerCode}
			</if>
			<if test="confirmDataParamList != null and confirmDataParamList.size() > 0">
				AND (CONVERT(VARCHAR, [出库日期]) + '-' + CONVERT(VARCHAR, [需求方]) + '-' + CONVERT(VARCHAR, [签约方])) IN
				<foreach collection="confirmDataParamList" item="item" separator="," open="(" close=")">
					#{item}
				</foreach>
			</if>
		</where>
		ORDER BY t2.[出库日期] ASC, t1.[签约方] ASC
	</select>

	<select id="getMaxPlanNumberByplanNumberPrefix" resultType="string">
		SELECT TOP 1 [出货计划番号]
		FROM [BusinessOps].[dbo].[订单明细表]
		WHERE [出货计划番号] LIKE CONCAT(#{planNumberPrefix}, '%')
		ORDER BY [出货计划番号] DESC
	</select>

	<update id="updateShipmentPlanEntryDetails" parameterType="java.util.List">
		UPDATE [BusinessOps].[dbo].[订单明细表]
		<set>
			<if test="planStatus != null and planStatus != ''">
				[出货计划确认] = #{planStatus},
			</if>
			<if test="planNumber != null and planNumber != ''">
				[出货计划番号] = #{planNumber},
			</if>
		</set>
		WHERE (CONVERT(VARCHAR, [订单NO]	) + '-' + CONVERT(VARCHAR, [订单序号])) IN
		<foreach collection="orderNoAndSerialNumsList" item="item" separator="," open="(" close=")">
			#{item}
		</foreach>
	</update>

	<select id="queryShipmentQuantityEntryListByPage" resultType="com.hongru.entity.businessOps.ShipmentQuantityInfo">
		SELECT
			[流水号] AS id
			, CONVERT(VARCHAR, [到货日期], 23) AS outboundDate
			, [客户代码] AS customerCode
			, [客户简称] AS customerAlias
			, [产品代码] AS productCode
			, [尺寸] AS size
			, [出库数量] AS shipmentQuantity
			, [送货单号] AS shipmentOrderNo
			, [送货单序号] AS shipmentOrderSerial
			, [客户订单号] AS customerOrderNo
			, [产品中分类] AS productCategory
			, [出库计划番号] AS shipmentPlanNo
			, [创建者] AS creator
			, [创建时间] AS createTime
		FROM [BusinessOps].[dbo].[出库数量表]
		<where>
			<if test="outboundStartDate != null and outboundStartDate != ''">
				AND [到货日期] &gt;= #{outboundStartDate}
			</if>
			<if test="outboundEndDate != null and outboundEndDate != ''">
				AND [到货日期] &lt;= #{outboundEndDate}
			</if>
			<if test="customerCode != null and customerCode != ''">
				AND [客户代码] = #{customerCode}
			</if>
			<if test="userName != null and userName != ''">
				AND [创建者] = #{userName}
			</if>
		</where>
		ORDER BY [到货日期] ASC, [客户代码] ASC
		<if test="pageInfo">
			OFFSET ${pageInfo.offset} ROWS
			FETCH NEXT ${pageInfo.limit} ROWS ONLY
		</if>
	</select>

	<select id="queryShipmentQuantityEntryListByPageCount" resultType="integer">
		SELECT COUNT(1)
		FROM [BusinessOps].[dbo].[出库数量表]
		<where>
			<if test="outboundStartDate != null and outboundStartDate != ''">
				AND [到货日期] &gt;= #{outboundStartDate}
			</if>
			<if test="outboundEndDate != null and outboundEndDate != ''">
				AND [到货日期] &lt;= #{outboundEndDate}
			</if>
			<if test="customerCode != null and customerCode != ''">
				AND [客户代码] = #{customerCode}
			</if>
			<if test="userName != null and userName != ''">
				AND [创建者] = #{userName}
			</if>
		</where>
	</select>

	<select id="queryShipmentQuantityEntryDetailList" resultType="com.hongru.entity.businessOps.ShipmentQuantityInfo">
		SELECT
			[到货日期] AS outboundDate
			, [产品代码] AS productCode
			, [客户订单号] AS customerOrderNo
			, [送货单号] AS shipmentOrderNo
			, [送货单序号] AS shipmentOrderSerial
		FROM [BusinessOps].[dbo].[出库数量表]
		<where>
			<if test="queryParamList != null and queryParamList.size() > 0">
				AND (CONVERT(VARCHAR, [到货日期]) + '-' + CONVERT(VARCHAR, [产品代码]) + '-' + CONVERT(VARCHAR, [客户订单号])) IN
				<foreach collection="queryParamList" item="item" separator="," open="(" close=")">
					#{item}
				</foreach>
			</if>
		</where>
	</select>

	<select id="queryShipmentQuantityEntryDetail" resultType="com.hongru.entity.businessOps.ShipmentQuantityInfo">
		SELECT
			[流水号] AS id
			, [到货日期] AS outboundDate
			, [客户代码] AS customerCode
			, [客户简称] AS customerAlias
			, [产品代码] AS productCode
			, [尺寸] AS size
			, [出库数量] AS shipmentQuantity
			, [送货单号] AS shipmentOrderNo
			, [送货单序号] AS shipmentOrderSerial
			, [客户订单号] AS customerOrderNo
			, [产品中分类] AS productCategory
			, [出库计划番号] AS shipmentPlanNo
			, [创建者] AS creator
			, [创建时间] AS createTime
		FROM [BusinessOps].[dbo].[出库数量表]
		WHERE [流水号] = #{id}
	</select>

	<select id="queryShipmentQuantityEntryListByPageForAdd" resultType="com.hongru.entity.businessOps.ShipmentQuantityInfo">
		SELECT * FROM 
		(
			SELECT
				CONVERT(VARCHAR(10), t1.[到货日期], 23) AS outboundDate
				, t2.[客户代码] AS customerCode
				, t2.[客户简称] AS customerAlias
				, t1.[产品代码] AS productCode
				, t1.[尺寸] AS size
				, t1.[送货单号] AS shipmentOrderNo
				, ROW_NUMBER() OVER (PARTITION BY t1.[送货单号] ORDER BY t1.尺寸) AS shipmentOrderSerial
				, SUM(t1.[重量]) AS shipmentQuantity
				, RTRIM(LTRIM(t1.[客户订单号])) AS customerOrderNo
				, CASE WHEN ( LEFT(t1.机器, 1)=4) THEN '丸線MW　EM'
					WHEN ( LEFT(t1.机器, 1)=5) THEN '丸線MW　EF'
					WHEN ( LEFT(t1.机器, 1)=6) THEN '平角MW　EH'
					ELSE '超極細線'
					END AS productCategory
			FROM 
				[sumitomo].[dbo].[出库表] t1
				LEFT JOIN [sumitomo].[dbo].[客户表] t2 ON t1.[客户代码] = t2.[客户代码]
			<where>
				t1.[出库确认] = '1'
				<if test="outboundStartDate != null and outboundStartDate != ''">
					AND t1.[到货日期] &gt;= #{outboundStartDate}
				</if>
				<if test="outboundEndDate != null and outboundEndDate != ''">
					AND t1.[到货日期] &lt;= #{outboundEndDate}
				</if>
				<if test="customerCode != null and customerCode != ''">
					AND t1.[客户代码] = #{customerCode}
				</if>
			</where>
			GROUP BY t1.[到货日期], t2.[客户代码], t2.[客户简称], t1.[产品代码], t1.[尺寸], t1.[送货单号], t1.[客户订单号], LEFT(t1.机器, 1)
		) AS t
		LEFT JOIN [BusinessOps].[dbo].[出库数量表] t3 ON t.[outboundDate] = t3.[到货日期] AND t.[productCode] = t3.[产品代码] AND t.[customerOrderNo] = t3.[客户订单号]
		<where>
			t3.[流水号] IS NULL
			<if test="outboundDateAndCustomerCodeAndProductCodeList != null and outboundDateAndCustomerCodeAndProductCodeList.size() > 0">
				AND (CONVERT(VARCHAR, outboundDate) + '-' + CONVERT(VARCHAR, customerCode) + '-' + CONVERT(VARCHAR, productCode)) IN
				<foreach collection="outboundDateAndCustomerCodeAndProductCodeList" item="item" separator="," open="(" close=")">
					#{item}
				</foreach>
			</if>
		</where>
		ORDER BY customerAlias, productCode
		<if test="pageInfo">
			OFFSET ${pageInfo.offset} ROWS
			FETCH NEXT ${pageInfo.limit} ROWS ONLY
		</if>
	</select>

	<select id="queryShipmentQuantityEntryListByPageForAddCount" resultType="integer">
		SELECT COUNT(1) FROM 
		(
			SELECT
				CONVERT(VARCHAR(10), t1.[到货日期], 23) AS outboundDate
				, t2.[客户代码] AS customerCode
				, t2.[客户简称] AS customerAlias
				, t1.[产品代码] AS productCode
				, t1.[尺寸] AS size
				, t1.[送货单号] AS shipmentOrderNo
				, ROW_NUMBER() OVER (PARTITION BY t1.[送货单号] ORDER BY t1.尺寸) AS shipmentOrderSerial
				, SUM(t1.[重量]) AS shipmentQuantity
				, RTRIM(LTRIM(t1.[客户订单号])) AS customerOrderNo
				, CASE WHEN ( LEFT(t1.机器, 1)=4) THEN '丸線MW　EM'
					WHEN ( LEFT(t1.机器, 1)=5) THEN '丸線MW　EF'
					WHEN ( LEFT(t1.机器, 1)=6) THEN '平角MW　EH'
					ELSE '超極細線'
					END AS productCategory
			FROM 
				[sumitomo].[dbo].[出库表] t1
				LEFT JOIN [sumitomo].[dbo].[客户表] t2 ON t1.[客户代码] = t2.[客户代码]
			<where>
				t1.[出库确认] = '1'
				<if test="outboundStartDate != null and outboundStartDate != ''">
					AND t1.[到货日期] &gt;= #{outboundStartDate}
				</if>
				<if test="outboundEndDate != null and outboundEndDate != ''">
					AND t1.[到货日期] &lt;= #{outboundEndDate}
				</if>
				<if test="customerCode != null and customerCode != ''">
					AND t1.[客户代码] = #{customerCode}
				</if>
			</where>
			GROUP BY t1.[到货日期], t2.[客户代码], t2.[客户简称], t1.[产品代码], t1.[尺寸], t1.[送货单号], t1.[客户订单号], LEFT(t1.机器, 1)
		) AS t
		LEFT JOIN [BusinessOps].[dbo].[出库数量表] t3 ON t.[outboundDate] = t3.[到货日期] AND t.[productCode] = t3.[产品代码] AND t.[customerOrderNo] = t3.[客户订单号]
		<where>
			t3.[流水号] IS NULL
			<if test="outboundDateAndCustomerCodeAndProductCodeList != null and outboundDateAndCustomerCodeAndProductCodeList.size() > 0">
				AND (CONVERT(VARCHAR, outboundDate) + '-' + CONVERT(VARCHAR, customerCode) + '-' + CONVERT(VARCHAR, productCode)) IN
				<foreach collection="outboundDateAndCustomerCodeAndProductCodeList" item="item" separator="," open="(" close=")">
					#{item}
				</foreach>
			</if>
		</where>
	</select>

	<select id="queryShipmentPlanNoList" resultType="com.hongru.entity.businessOps.ShipmentQuantityInfo">
		SELECT
			t2.[到货日期] AS outboundDate
			, t2.[产品代码] AS productCode
			, t1.[客户订单号] AS customerOrderNo
			, t2.[订单序号] AS shipmentOrderSerial
			, t2.[订单NO] AS shipmentOrderNo
			, MAX(t2.[出货计划番号]) AS shipmentPlanNo
		FROM [BusinessOps].[dbo].[订单表] t1
		LEFT JOIN [BusinessOps].[dbo].[订单明细表] t2 ON t1.[订单NO] = t2.[订单NO] AND t2.[状态] != 9
		<where>
			t1.[状态] != 9
			AND t2.[订单确认] = 1
			AND t2.[出货计划确认] = 1
			<if test="queryParamList != null and queryParamList.size() > 0">
				AND (CONVERT(VARCHAR, t2.[到货日期]) + '-' + CONVERT(VARCHAR, t2.[产品代码]) + '-' + CONVERT(VARCHAR, t1.[客户订单号])) IN
				<foreach collection="queryParamList" item="item" separator="," open="(" close=")">
					#{item}
				</foreach>
			</if>
		</where>
		GROUP BY t2.[到货日期], t2.[产品代码], t1.[客户订单号], t2.[订单序号], t2.[订单NO]
	</select>

	<insert id="batchAddShipmentQuantityEntryList" parameterType="java.util.List">
		INSERT INTO [BusinessOps].[dbo].[出库数量表]
		(
			[到货日期],
			[客户代码],
			[客户简称],
			[产品代码],
			[尺寸],
			[出库数量],
			[送货单号],
			[送货单序号],
			[客户订单号],
			[产品中分类],
			[出库计划番号],
			[创建者],
			[创建时间]
		)
		VALUES
		<foreach collection="shipmentQuantityInfoList" item="item" separator=",">
		(
			#{item.outboundDate},
			#{item.customerCode},
			#{item.customerAlias},
			#{item.productCode},
			#{item.size},
			#{item.shipmentQuantity},
			#{item.shipmentOrderNo},
			#{item.shipmentOrderSerial},
			#{item.customerOrderNo},
			#{item.productCategory},
			#{item.shipmentPlanNo},
			#{item.creator},
			GETDATE()
		)
		</foreach>
	</insert>

	<update id="updateShipmentQuantityEntry" parameterType="com.hongru.entity.businessOps.ShipmentQuantityInfo">
		UPDATE [BusinessOps].[dbo].[出库数量表]
		<set>
			<if test="shipmentQuantity != null and shipmentQuantity != ''">
				[出库数量] = #{shipmentQuantity},
			</if>
			<if test="shipmentPlanNo != null and shipmentPlanNo != ''">
				[出库计划番号] = #{shipmentPlanNo},
			</if>
		</set>
		WHERE [流水号] = #{id}
	</update>

	<delete id="deleteShipmentQuantityEntry" parameterType="integer">
		DELETE FROM [BusinessOps].[dbo].[出库数量表]
		WHERE [流水号] = #{id}
	</delete>

	<!-- 根据客户代码查询产品信息 -->
	<select id="getProductListByCustomerCode" resultType="String">
		SELECT DISTINCT [产品代码]
		FROM [sumitomo].[dbo].[产品表]
		WHERE [用户名] = #{customerCode}
		ORDER BY [产品代码]
	</select>

	<!-- 根据客户代码和铜合同类别查询铜合同列表 -->
	<select id="getCopperContractList" resultType="String">
		SELECT [铜签约No]
		FROM [BusinessOps].[dbo].[铜合同表]
		WHERE [客户代码] = #{customerCode}
		AND [铜签约类别] = #{copperContractType}
		AND [状态] = '0'
	</select>

	<!-- 根据铜合同No查询铜合同详情 -->
	<select id="getCopperContractDetail" resultType="com.hongru.entity.businessOps.OrderEntryDetails">
		SELECT
			a.[铜条件] AS copperCondition,
			a.[签约铜价] AS copperBase,
			b.[货币] AS copperCurrency
		FROM [BusinessOps].[dbo].[铜合同表] a
		LEFT JOIN [BusinessOps].[dbo].[铜条件表] b ON a.[铜条件] = b.[铜条件]
		WHERE a.[铜签约No] = #{copperContractNo}
	</select>

	<!-- 查询所有铜条件列表 -->
	<select id="getCopperConditionList" resultType="String">
		SELECT DISTINCT [铜条件]
		FROM [BusinessOps].[dbo].[铜条件表]
	</select>

	<!-- 查询所有铜条件列表（包含货币信息） -->
	<select id="getCopperConditionListWithCurrency" resultType="Map">
		SELECT DISTINCT [铜条件] as condition, [货币] as currency
		FROM [BusinessOps].[dbo].[铜条件表]
		ORDER BY [铜条件]
	</select>

</mapper>