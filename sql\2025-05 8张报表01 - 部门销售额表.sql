-- 创建销售额表
CREATE TABLE 销售额表 (
    销售额NO NVARCHAR(50) PRIMARY KEY,  -- 销售额编号，主键
    客户简称 NVARCHAR(100) NOT NULL,   -- 客户简称
    结算货币 NVARCHAR(10) NOT NULL,     -- 结算货币（RMB/USD等）
    销售额日 DATE NOT NULL,            -- 销售额日期
    创建时间 DATETIME DEFAULT GETDATE(), -- 记录创建时间
    更新时间 DATETIME DEFAULT GETDATE(), -- 记录更新时间
    创建人 NVARCHAR(50),               -- 创建人
    更新人 NVARCHAR(50)                -- 更新人
);

-- 创建销售额明细表
CREATE TABLE 销售额明细表 (
    销售额明细ID INT IDENTITY(1,1) PRIMARY KEY, -- 自增主键
    销售额NO NVARCHAR(50) NOT NULL,              -- 关联销售额表的外键
    产品中分类 NVARCHAR(100) NOT NULL,          -- 产品中分类
    数量 DECIMAL(18,3) NOT NULL,                -- 销售数量
    销售金额 DECIMAL(18,2) NOT NULL,            -- 销售金额（不含税）
    创建时间 DATETIME DEFAULT GETDATE(),        -- 记录创建时间
    更新时间 DATETIME DEFAULT GETDATE(),        -- 记录更新时间
    创建人 NVARCHAR(50),                        -- 创建人
    更新人 NVARCHAR(50),                        -- 更新人
    CONSTRAINT FK_销售额明细_销售额 FOREIGN KEY (销售额NO) REFERENCES 销售额表(销售额NO)
);

-- 创建索引
CREATE INDEX IDX_销售额表_销售额日 ON 销售额表(销售额日);
CREATE INDEX IDX_销售额明细_销售额NO ON 销售额明细表(销售额NO);
CREATE INDEX IDX_销售额明细_产品中分类 ON 销售额明细表(产品中分类);

-- 添加表注释
EXEC sp_addextendedproperty 'MS_Description', '销售额主表', 'SCHEMA', 'dbo', 'TABLE', '销售额表';
EXEC sp_addextendedproperty 'MS_Description', '销售额明细表', 'SCHEMA', 'dbo', 'TABLE', '销售额明细表';

-- 添加字段注释
EXEC sp_addextendedproperty 'MS_Description', '销售额编号', 'SCHEMA', 'dbo', 'TABLE', '销售额表', 'COLUMN', '销售额NO';
EXEC sp_addextendedproperty 'MS_Description', '客户简称', 'SCHEMA', 'dbo', 'TABLE', '销售额表', 'COLUMN', '客户简称';
EXEC sp_addextendedproperty 'MS_Description', '结算货币', 'SCHEMA', 'dbo', 'TABLE', '销售额表', 'COLUMN', '结算货币';
EXEC sp_addextendedproperty 'MS_Description', '销售额日期', 'SCHEMA', 'dbo', 'TABLE', '销售额表', 'COLUMN', '销售额日';

EXEC sp_addextendedproperty 'MS_Description', '销售额明细ID', 'SCHEMA', 'dbo', 'TABLE', '销售额明细表', 'COLUMN', '销售额明细ID';
EXEC sp_addextendedproperty 'MS_Description', '销售额编号', 'SCHEMA', 'dbo', 'TABLE', '销售额明细表', 'COLUMN', '销售额NO';
EXEC sp_addextendedproperty 'MS_Description', '产品中分类', 'SCHEMA', 'dbo', 'TABLE', '销售额明细表', 'COLUMN', '产品中分类';
EXEC sp_addextendedproperty 'MS_Description', '销售数量', 'SCHEMA', 'dbo', 'TABLE', '销售额明细表', 'COLUMN', '数量';
EXEC sp_addextendedproperty 'MS_Description', '销售金额（不含税）', 'SCHEMA', 'dbo', 'TABLE', '销售额明细表', 'COLUMN', '销售金额'; 


-- 根据需求描述，需要在销售额明细表中添加以下字段：
-- 1. 产品代码
-- 2. 销售单价
-- 3. 铜base
-- 4. 产品重分类

-- 更新销售额明细表结构
ALTER TABLE 销售额明细表
ADD 产品代码 NVARCHAR(50) NOT NULL DEFAULT '', -- 产品代码
    销售单价 DECIMAL(18,2) NOT NULL DEFAULT 0, -- 销售单价
    铜base DECIMAL(18,2) NOT NULL DEFAULT 0,   -- 铜基准价
    产品重分类 NVARCHAR(100);                  -- 产品重分类

-- 创建新字段的索引
CREATE INDEX IDX_销售额明细_产品代码 ON 销售额明细表(产品代码);
CREATE INDEX IDX_销售额明细_产品重分类 ON 销售额明细表(产品重分类);

-- 添加新字段的注释
EXEC sp_addextendedproperty 'MS_Description', '产品代码', 'SCHEMA', 'dbo', 'TABLE', '销售额明细表', 'COLUMN', '产品代码';
EXEC sp_addextendedproperty 'MS_Description', '销售单价', 'SCHEMA', 'dbo', 'TABLE', '销售额明细表', 'COLUMN', '销售单价';
EXEC sp_addextendedproperty 'MS_Description', '铜基准价', 'SCHEMA', 'dbo', 'TABLE', '销售额明细表', 'COLUMN', '铜base';
EXEC sp_addextendedproperty 'MS_Description', '产品重分类', 'SCHEMA', 'dbo', 'TABLE', '销售额明细表', 'COLUMN', '产品重分类';




-- 下面根据需求和现有表结构，补充缺失字段（已存在的字段不再重复添加）

-- 1. 出库日期（需求有，表结构中未见，需补充）
IF COL_LENGTH('销售额明细表', '出库日期') IS NULL
    ALTER TABLE 销售额明细表
    ADD 出库日期 DATE NULL;
    -- 添加字段注释
    EXEC sp_addextendedproperty 'MS_Description', '出库日期', 'SCHEMA', 'dbo', 'TABLE', '销售额明细表', 'COLUMN', '出库日期';

-- 2. 到货日期（需求有，表结构中未见，需补充）
-- 先判断字段是否已存在，只有不存在时才添加字段和注释，避免重复添加导致报错
IF COL_LENGTH('销售额明细表', '到货日期') IS NULL
BEGIN
    ALTER TABLE 销售额明细表
    ADD 到货日期 DATE NULL;
    -- 添加字段注释
    EXEC sp_addextendedproperty 'MS_Description', '到货日期', 'SCHEMA', 'dbo', 'TABLE', '销售额明细表', 'COLUMN', '到货日期';
END
ELSE
BEGIN
    -- 字段已存在，仅确保注释存在（如果需要可重复执行，不会报错）
    EXEC sp_addextendedproperty 'MS_Description', '到货日期', 'SCHEMA', 'dbo', 'TABLE', '销售额明细表', 'COLUMN', '到货日期';
END

-- 其余字段（产品代码、销售单价、铜base、产品中分类、数量、销售金额）已在表结构中存在，无需重复添加
