<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hongru.mapper.businessOps.TaxRateCalculationMapper">
    <sql id="taxRateCalculation_sql">
		pc.[流水号] AS id,pc.[税率代码] AS taxCode,pc.[税率计算名] AS taxCalcName,pc.[税率] AS taxRate,
		pc.[创建人姓名] AS creatorName,pc.[创建时间] AS createdTime,
		pc.[更新人姓名] AS updaterName,pc.[更新时间] AS updatedTime
	</sql>

	<insert id="insertTaxRateCalculation" parameterType="com.hongru.entity.businessOps.TaxRateCalculation">
		INSERT INTO [businessOps].[dbo].[税率计算表]
		(
		[税率代码],
		[税率计算名],
		[税率],
		[创建人姓名],
		[创建时间]
		)VALUES(
		#{taxRateCalculation.taxCode},
		#{taxRateCalculation.taxCalcName},
		#{taxRateCalculation.taxRate},
		#{taxRateCalculation.creatorName},
		#{taxRateCalculation.createdTime}
		)
	</insert>

	<select id="selectTaxRateCalculationById" resultType="com.hongru.entity.businessOps.TaxRateCalculation">
		SELECT
		<include refid="taxRateCalculation_sql"/>
		FROM [businessOps].[dbo].[税率计算表] pc
		<where>
			<if test="id != null">
				AND pc.[流水号] = #{id}
			</if>
		</where>
	</select>

	<select id="listTaxRateCalculation" resultType="com.hongru.entity.businessOps.TaxRateCalculation">
		SELECT
		<include refid="taxRateCalculation_sql"/>
		FROM [businessOps].[dbo].[税率计算表] pc
		<where>
			<if test="taxCalcName != null and taxCalcName != ''">
				AND pc.[税率计算名] = #{taxCalcName}
			</if>
			<if test="taxRate != null">
				AND pc.[税率] = #{taxRate}
			</if>
		</where>
		ORDER BY pc.[税率代码]
	</select>

	<update id="updateTaxRateCalculation">
		UPDATE [businessOps].[dbo].[税率计算表]
		<set>
			<if test="taxRateCalculation.taxCalcName != null and taxRateCalculation.taxCalcName != ''">
				[税率计算名] = #{taxRateCalculation.taxCalcName},
			</if>
			<if test="taxRateCalculation.taxRate != null">
				[税率] = #{taxRateCalculation.taxRate},
			</if>
			<if test="taxRateCalculation.updaterName != null and taxRateCalculation.updaterName != ''">
				[更新人姓名] = #{taxRateCalculation.updaterName},
			</if>
			<if test="taxRateCalculation.updatedTime != null">
				[更新时间] = #{taxRateCalculation.updatedTime},
			</if>
		</set>
		WHERE [流水号] = #{taxRateCalculation.id}
	</update>
	
	<delete id="deleteTaxRateCalculation">
		DELETE [businessOps].[dbo].[税率计算表] WHERE [流水号] = #{id}
	</delete>
	
	<select id="selectMaxTaxCode" resultType="integer">
		SELECT TOP(1) [税率代码] AS taxCode FROM [businessOps].[dbo].[税率计算表] pc ORDER BY [税率代码] DESC
	</select>
	
	<select id="findTaxRateByTaxCode" resultType="com.hongru.entity.businessOps.TaxRateCalculation">
		SELECT
		<include refid="taxRateCalculation_sql"/>
		FROM [businessOps].[dbo].[税率计算表] pc
		<where>
			<if test="taxRateCode != null and taxRateCode != ''">
				AND pc.[税率代码] = #{taxRateCode}
			</if>
		</where>
	</select>
</mapper>