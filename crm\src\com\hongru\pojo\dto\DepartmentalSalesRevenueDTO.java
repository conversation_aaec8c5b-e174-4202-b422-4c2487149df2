package com.hongru.pojo.dto;

import java.math.BigDecimal;
import java.util.List;

import com.hongru.support.page.PageInfo;

/**
 * 部门销售额DTO
 */
public class DepartmentalSalesRevenueDTO {
    
    private String customerAlias; // 客户简称
    private String settlementCurrency; // 结算货币
    private String productMiddleCategory; // 产品中分类
    private BigDecimal salesQuantity; // 销售额数
    private BigDecimal salesAmount; // 销售金额
    
    // 分页信息
    private PageInfo pageInfo;
    
    // 查询结果列表
    private List<DepartmentalSalesRevenueDTO> departmentalSalesRevenueList;
    
    public String getCustomerAlias() {
        return customerAlias;
    }
    
    public void setCustomerAlias(String customerAlias) {
        this.customerAlias = customerAlias;
    }
    
    public String getSettlementCurrency() {
        return settlementCurrency;
    }
    
    public void setSettlementCurrency(String settlementCurrency) {
        this.settlementCurrency = settlementCurrency;
    }
    
    public String getProductMiddleCategory() {
        return productMiddleCategory;
    }
    
    public void setProductMiddleCategory(String productMiddleCategory) {
        this.productMiddleCategory = productMiddleCategory;
    }
    
    public BigDecimal getSalesQuantity() {
        return salesQuantity;
    }
    
    public void setSalesQuantity(BigDecimal salesQuantity) {
        this.salesQuantity = salesQuantity;
    }
    
    public BigDecimal getSalesAmount() {
        return salesAmount;
    }
    
    public void setSalesAmount(BigDecimal salesAmount) {
        this.salesAmount = salesAmount;
    }
    
    public PageInfo getPageInfo() {
        return pageInfo;
    }
    
    public void setPageInfo(PageInfo pageInfo) {
        this.pageInfo = pageInfo;
    }
    
    public List<DepartmentalSalesRevenueDTO> getDepartmentalSalesRevenueList() {
        return departmentalSalesRevenueList;
    }
    
    public void setDepartmentalSalesRevenueList(List<DepartmentalSalesRevenueDTO> departmentalSalesRevenueList) {
        this.departmentalSalesRevenueList = departmentalSalesRevenueList;
    }
}
