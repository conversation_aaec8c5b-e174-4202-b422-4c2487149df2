layui.use(['laydate', 'layer', 'table', 'element', 'form'], function(){
    var laydate = layui.laydate //日期
        ,layer = layui.layer //弹层
        ,table = layui.table //表格
        ,element = layui.element //元素操作
    var form = layui.form;
    
    // 日期范围选择器
    laydate.render({
        elem: '#salesDateRange'
        ,type: 'date'
        ,range: true
        ,format: 'yyyy-MM-dd'
    });
    
    // 执行一个 table 实例
    var url = baselocation+'/salesReport/monthlyShipmentDetails/list';
    table.render({
        elem: '#demo'
        ,height: 'full-170'
        ,url: url //数据接口
        ,cellMinWidth: 80 //全局定义常规单元格的最小宽度
        ,parseData:function(res){ //res 即为原始返回的数据
            return {
                "code": 0, //解析接口状态
                "msg": '', //解析提示文本
                "count": res.total, //解析数据长度
                "data": res.rows //解析数据列表
            };
        }
        ,method:'post'
        ,title: '月出货明细'
        ,page: false //关闭分页
        ,where: {}  // 初始加载时不带任何参数
        ,toolbar: '#toolbarDemo' //开启工具栏，此处显示默认图标，可以自定义模板，详见文档
        ,defaultToolbar: ['filter']
        ,totalRow: true //开启合计行
        ,cols: [[ //表头
            {field: 'accountingYearMonth', title: '会计年月', align:'center', fixed: 'left'}
            ,{field: 'customerAlias', title: '需求方略称', align:'center'}
            ,{field: 'productCode', title: '品名', align:'center'}
            ,{field: 'size', title: '尺寸', align:'center', templet: function(d){
                if(d.size != null){
                    return parseFloat(d.size).toFixed(4);
                }else{
                    return "0.0000";
                }
            }}
            ,{field: 'productMiddleCategory', title: '品目中分类名', align:'center'}
            ,{field: 'shipmentQuantity', title: '出货数', align:'right', totalRow: true, templet: function(d){
                if(d.shipmentQuantity != null){
                    return parseFloat(d.shipmentQuantity).toFixed(3);
                }else{
                    return "0.000";
                }
            }}
            ,{field: 'wireReelName', title: '线盘', align:'center'}
        ]]
    });
    
    //监听头工具栏事件
    table.on('toolbar(test)', function(obj){
        if(obj.event === 'refresh'){
            search();
        }
    });
});

/**
 * 进行检索
 */
function search() {
    var dateRange = $("#salesDateRange").val();
    var startDate = "";
    var endDate = "";
    
    if (dateRange) {
        var dates = dateRange.split(" - ");
        if (dates.length == 2) {
            startDate = dates[0];
            endDate = dates[1];
        }
    }
    
    layui.table.reload('demo', {
        where: {
            startDate: startDate,
            endDate: endDate,
            _t: new Date().getTime() // 添加时间戳防止缓存
        }
    });
}

/**
 * 导出数据
 */
function exportData() {
    var dateRange = $("#salesDateRange").val();
    var startDate = "";
    var endDate = "";
    
    if (dateRange) {
        var dates = dateRange.split(" - ");
        if (dates.length == 2) {
            startDate = dates[0];
            endDate = dates[1];
        }
    }
    
    var url = baselocation + '/salesReport/monthlyShipmentDetails/export?startDate=' + startDate + '&endDate=' + endDate + '&_t=' + new Date().getTime();
    window.location.href = url;
}