package com.hongru.service.impl.businessOps;

import com.hongru.entity.businessOps.*;
import com.hongru.entity.sumitomo.Customers;
import com.hongru.mapper.businessOps.*;
import com.hongru.pojo.dto.*;
import com.hongru.service.businessOps.IOrderService;
import com.hongru.support.page.PageInfo;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

@Service
public class OrderServiceImpl implements IOrderService {

	@Autowired
	private OrderMapper orderMapper;

	/*======================订单详情表======================*/

	/**
	 * 订单详情列表
	 * @param pageInfo 分页信息
	 * @param orderStartDate 接单日期开始
	 * @param orderEndDate 接单日期结束
	 * @param prepareStartDate 准备日期开始
	 * @param prepareEndDate 准备日期结束
	 * @param outboundStartDate 出库日期开始
	 * @param outboundEndDate 出库日期结束
	 * @param demandCustomerCode 需求方
	 * @param contractPartyCustomerCode 合同方
	 * @param orderType 订单类型
	 * @param status 订单状态
	 * @param userName 用户名
	 * @throws Exception
	 * @return
	*/
	@Override
	public OrderEntryDetailsPageDTO orderEntryDetailsListByPage(PageInfo pageInfo, String orderStartDate, String orderEndDate, String prepareStartDate, String prepareEndDate, String outboundStartDate, String outboundEndDate, String demandCustomerCode, String contractPartyCustomerCode, String orderType, String status, String userName) throws Exception {
		List<OrderEntryDetails> orderEntryDetailsList = orderMapper.orderEntryDetailsListByPage(pageInfo, orderStartDate, orderEndDate, prepareStartDate, prepareEndDate, outboundStartDate, outboundEndDate, demandCustomerCode, contractPartyCustomerCode, orderType, status, userName);
		Integer total = orderMapper.orderEntryDetailsListByPageCount(orderStartDate, orderEndDate, prepareStartDate, prepareEndDate, outboundStartDate, outboundEndDate, demandCustomerCode, contractPartyCustomerCode, orderType, status, userName);
		pageInfo.setTotal(total);
		return new OrderEntryDetailsPageDTO(pageInfo, orderEntryDetailsList);
	}

	/**
	 * 订单详情列表导出
	 * @param pageInfo 分页信息
	 * @param orderStartDate 接单日期开始
	 * @param orderEndDate 接单日期结束
	 * @param prepareStartDate 准备日期开始
	 * @param prepareEndDate 准备日期结束
	 * @param outboundStartDate 出库日期开始
	 * @param outboundEndDate 出库日期结束
	 * @param demandCustomerCode 需求方
	 * @param contractPartyCustomerCode 合同方
	 * @param orderType 订单类型
	 * @param status 订单状态
	 * @param planStatus 出货计划状态
	 * @param userName 用户名
	 * @throws Exception
	 * @return
	*/
	@Override
	public OrderEntryDetailsPageDTO orderEntryDetailsListByPageForExport(PageInfo pageInfo, String orderStartDate, String orderEndDate, String prepareStartDate, String prepareEndDate, String outboundStartDate, String outboundEndDate, String demandCustomerCode, String contractPartyCustomerCode, String orderType, String status, String planStatus, String userName) throws Exception {
		List<OrderEntryDetails> orderEntryDetailsList = orderMapper.orderEntryDetailsListByPageForExport(pageInfo, orderStartDate, orderEndDate, prepareStartDate, prepareEndDate, outboundStartDate, outboundEndDate, demandCustomerCode, contractPartyCustomerCode, orderType, status, planStatus, userName);
		Integer total = orderMapper.orderEntryDetailsListByPageCountForExport(orderStartDate, orderEndDate, prepareStartDate, prepareEndDate, outboundStartDate, outboundEndDate, demandCustomerCode, contractPartyCustomerCode, orderType, status, planStatus, userName);
		pageInfo.setTotal(total);
		return new OrderEntryDetailsPageDTO(pageInfo, orderEntryDetailsList);
	}

	/**
	 * 订单详情
	 * @param id 订单详情id
	 * @param status 订单状态
	 * @throws Exception
	 * @return
	*/
	@Override
	public List<OrderEntryDetails> getOrderEntryDetailsById(Integer id, String status) throws Exception {
		return orderMapper.orderEntryDetailsById(id, status);
	}

	/**
     * 根据订单号获取订单详情
     * @param orderNo 订单号
     * @return
     */
	@Override
	public List<OrderEntryDetails> orderEntryDetailsByOrderNo(String orderNo) throws Exception {
		return orderMapper.orderEntryDetailsByOrderNo(orderNo);
	}

	/**
	 * 根据订单号前缀获取最大订单号
	 * @param orderNoPrefix 订单号前缀 (如：D250423)
	 * @throws Exception
	 * @return
	*/
	@Override
	public String getMaxOrderNoByOrderNoPrefix(String orderNoPrefix) throws Exception {
		return orderMapper.getMaxOrderNoByOrderNoPrefix(orderNoPrefix);
	}

	/**
	 * 获取合同方信息列表
	 * @throws Exception
	 * @return
	*/
	@Override
	public List<Customers> getContractPartyCustomerList() throws Exception {
		return orderMapper.getContractPartyCustomerList();
	}
	
	/**
	 * 新增订单信息
	 * @param orderEntryDetails 订单信息
	 * @throws Exception
	 * @return
	*/
	@Override
	public int insertOrderEntryInfo(OrderEntryDetails orderEntryDetails) throws Exception {
		return orderMapper.insertOrderEntryInfo(orderEntryDetails);
	}

	/**
	 * 批量新增订单详情
	 * @param orderDetailsList 订单详情列表
	 * @throws Exception
	 * @return
	*/
	@Override
	public int batchInsertOrderEntryDetails(List<OrderEntryDetails> orderDetailsList) throws Exception {
		int result = 0;
		if (orderDetailsList != null && !orderDetailsList.isEmpty()) {
			int batchSize = 100;
			for (int i = 0; i < orderDetailsList.size(); i += batchSize) {
				int endIndex = Math.min(i + batchSize, orderDetailsList.size());
				List<OrderEntryDetails> batch = orderDetailsList.subList(i, endIndex);
				result += orderMapper.batchInsertOrderEntryDetails(batch);
			}
		}
		return result;
	}

	/**
	 * 更新订单信息
	 * @param orderEntryDetails 订单信息
	 * @throws Exception
	 * @return
	*/
	@Override
	public void updateOrderEntryInfo(OrderEntryDetails orderEntryDetails) throws Exception {
		orderMapper.updateOrderEntryInfo(orderEntryDetails);
	}

	/**
	 * 更新订单详情信息
	 * @param orderEntryDetails 订单详情信息
	 * @throws Exception
	 * @return
	*/
	@Override
	public void updateOrderEntryDetail(OrderEntryDetails orderEntryDetails) throws Exception {
		orderMapper.updateOrderEntryDetail(orderEntryDetails);
	}

	/**
	 * 更新订单详情已送数量信息
	 * @param orderEntryDetails 订单详情信息
	 * @throws Exception
	 * @return
	 */
	@Override
	public void updateOrderEntryDetailDeliveredQuantity(OrderEntryDetails orderEntryDetails) throws Exception {
		orderMapper.updateOrderEntryDetailDeliveredQuantity(orderEntryDetails);
	}

	/**
	 * 删除订单信息
	 * @param id 订单信息id
	 * @throws Exception
	 * @return
	*/
	@Override
	public void deleteOrderEntryInfo(Integer id) throws Exception {
		orderMapper.updateOrderEntryInfoStatus(id);
	}

	/**
	 * 删除订单详情信息
	 * @param orderNo 订单号
	 * @param orderSerialNum 订单序号
	 * @throws Exception
	 * @return
	*/
	@Override
	public void deleteOrderEntryDetail(String orderNo, String orderSerialNum) throws Exception {
		orderMapper.updateOrderEntryDetailStatus(orderNo, orderSerialNum);
	}

	/**
	 * 订单确认列表
	 * 
	 * @param pageInfo                  分页信息
	 * @param outboundStartDate         出库日期开始
	 * @param outboundEndDate           出库日期结束
	 * @param demandCustomerCode        需求方
	 * @param contractPartyCustomerCode 合同方
	 * @param orderNoList               订单号列表
	 * @param userName                  用户名
	 * @throws Exception
	 * @return
	 */
	@Override
	public OrderEntryDetailsPageDTO queryOrderConfirmListByPage(PageInfo pageInfo, String outboundStartDate,
			String outboundEndDate, String demandCustomerCode, String contractPartyCustomerCode,
			List<String> orderNoList, String userName) throws Exception {
		List<OrderEntryDetails> orderEntryDetailsList = orderMapper.queryOrderConfirmListByPage(pageInfo,
				outboundStartDate, outboundEndDate, demandCustomerCode, contractPartyCustomerCode, orderNoList,
				userName);
		Integer total = orderMapper.queryOrderConfirmListByPageCount(outboundStartDate, outboundEndDate,
				demandCustomerCode, contractPartyCustomerCode, orderNoList, userName);
		pageInfo.setTotal(total);
		return new OrderEntryDetailsPageDTO(pageInfo, orderEntryDetailsList);
	}
	
	/**
	 * 更新订单确认状态
	 * @param orderNoAndSerialNumsList 订单号及订单序号拼接列表 （如：[D250423-1, D250423-2]）
	 * @throws Exception
	 * @return
	*/
	@Override
	public void updateOrderConfirm(List<String> orderNoAndSerialNumsList) throws Exception {
		orderMapper.updateOrderConfirm(orderNoAndSerialNumsList);
	}

	/**
	 * 出库计划录入列表
	 * @param pageInfo 分页信息
	 * @param outboundStartDate 出库开始日期
	 * @param outboundEndDate 出库结束日期
	 * @param demandCustomerCode 需求方
	 * @param contractPartyCustomerCode 合同方
	 * @param planStatus 出货计划状态(null: 未登录计划, 0: 待确认, 1: 已确认)
	 * @param userName 用户名
	 * @throws Exception
	 * @return
	*/
	@Override
	public OrderEntryDetailsPageDTO queryShipmentPlanEntryListByPage(PageInfo pageInfo, String outboundStartDate, String outboundEndDate, String demandCustomerCode, String contractPartyCustomerCode, String planStatus, String userName) throws Exception {
		List<OrderEntryDetails> orderEntryDetailsList = orderMapper.queryShipmentPlanEntryListByPage(pageInfo, outboundStartDate, outboundEndDate, demandCustomerCode, contractPartyCustomerCode, planStatus, userName);
		Integer total = orderMapper.queryShipmentPlanEntryListByPageCount(outboundStartDate, outboundEndDate, demandCustomerCode, contractPartyCustomerCode, planStatus, userName);
		pageInfo.setTotal(total);
		return new OrderEntryDetailsPageDTO(pageInfo, orderEntryDetailsList);
	}

	/**
	 * 出库计划录入详情列表
	 * @param outboundDate 出库日期
	 * @param demandCustomerCode 需求方
	 * @param contractPartyCustomerCode 合同方
	 * @param confirmDataParamList 出库日期-需求方-合同方拼接列表（例：[2025-05-14-001-002, 2025-05-14-001-003]）
	 * @param planStatus 出货计划状态(null: 未登录计划, 0: 待确认, 1: 已确认)
	 * @return
	 */
	@Override
	public List<OrderEntryDetails> queryShipmentPlanEntryDetailList(String outboundDate, String demandCustomerCode, String contractPartyCustomerCode, List<String> confirmDataParamList, String planStatus) throws Exception {
		return orderMapper.queryShipmentPlanEntryDetailList(outboundDate, demandCustomerCode, contractPartyCustomerCode, confirmDataParamList, planStatus);
	}

	/**
	 * 根据出货计划番号前缀获取最大出货计划番号
	 * @param planNumberPrefix 出货计划番号前缀 (如：250423)
	 * @return
	 */
	@Override
	public String getMaxPlanNumberByplanNumberPrefix(String planNumberPrefix) throws Exception {
		return orderMapper.getMaxPlanNumberByplanNumberPrefix(planNumberPrefix);
	}

	/**
	 * 更新出货计划
	 * @param orderNoAndSerialNumsList 订单号及订单序号拼接列表 （如：[D250423-1, D250423-2]）
	 * @param planStatus 出货计划状态(0: 待确认, 1: 已确认)
	 * @param planNumber 出货计划番号
	 * @throws Exception
	 */
	@Override
	public void updateShipmentPlanEntryDetails(List<String> orderNoAndSerialNumsList, String planStatus, String planNumber) throws Exception {
		orderMapper.updateShipmentPlanEntryDetails(orderNoAndSerialNumsList, planStatus, planNumber);
	}
	
	/**
	 * 出库数量录入列表
	 * @param pageInfo 分页信息
	 * @param outboundStartDate 出库开始日期
	 * @param outboundEndDate 出库结束日期
	 * @param customerCode 客户代码
	 * @param userName 用户名
	 * @throws Exception
	 * @return
	*/
	@Override
	public ShipmentQuantityInfoPageDTO queryShipmentQuantityEntryListByPage(PageInfo pageInfo, String outboundStartDate, String outboundEndDate, String customerCode, String userName) throws Exception {
		List<ShipmentQuantityInfo> shipmentQuantityInfoList = orderMapper.queryShipmentQuantityEntryListByPage(pageInfo, outboundStartDate, outboundEndDate, customerCode, userName);
		Integer total = orderMapper.queryShipmentQuantityEntryListByPageCount(outboundStartDate, outboundEndDate, customerCode, userName);
		pageInfo.setTotal(total);
		return new ShipmentQuantityInfoPageDTO(pageInfo, shipmentQuantityInfoList);
	}

	/**
	 * 出库数量详情列表
	 * @param queryParamList 出库日期-产品代码-客户订单号-集合拼接字符串列表（例：[2025-05-14-001-002, 2025-05-14-001-003]）
	 * @return
	 */
	@Override
	public List<ShipmentQuantityInfo> queryShipmentQuantityEntryDetailList(List<String> queryParamList) throws Exception {
		return orderMapper.queryShipmentQuantityEntryDetailList(queryParamList);
	}

	/**
	 * 出库数量信息详情
	 * @param id 流水号
	 * @return
	 */
	@Override
	public ShipmentQuantityInfo queryShipmentQuantityEntryDetail(String id) throws Exception {
		return orderMapper.queryShipmentQuantityEntryDetail(id);
	}

	/**
	 * 查询出库信息列表（用于新增出库数量）
	 * 
	 * @param pageInfo                                      分页信息
	 * @param outboundStartDate                             出库开始日期
	 * @param outboundEndDate                               出库结束日期
	 * @param customerCode                                  客户代码
	 * @param userName                                      用户名
	 * @param outboundDateAndCustomerCodeAndProductCodeList 到货日期-客户编号-产品编号集合拼接字符串（例：2025-04-23-001-002,2025-04-23-003-006）
	 * @return
	 */
	@Override
	public ShipmentQuantityInfoPageDTO queryShipmentQuantityEntryListForAdd(PageInfo pageInfo, String outboundStartDate,
			String outboundEndDate, String customerCode, String userName,
			List<String> outboundDateAndCustomerCodeAndProductCodeList) throws Exception {
		List<ShipmentQuantityInfo> shipmentQuantityInfoList = orderMapper.queryShipmentQuantityEntryListByPageForAdd(
				pageInfo, outboundStartDate, outboundEndDate, customerCode, userName,
				outboundDateAndCustomerCodeAndProductCodeList);
		Integer total = orderMapper.queryShipmentQuantityEntryListByPageForAddCount(outboundStartDate, outboundEndDate,
				customerCode, userName, outboundDateAndCustomerCodeAndProductCodeList);
		pageInfo.setTotal(total);
		return new ShipmentQuantityInfoPageDTO(pageInfo, shipmentQuantityInfoList);
	}

	/**
	 * 查询出货计划番号列表
	 * @param queryParamList 出库日期-产品代码-客户订单号拼接列表（例：[2025-05-14-001-002, 2025-05-14-001-003]）
	 * @return
	 */
	@Override
	public List<ShipmentQuantityInfo> queryShipmentPlanNoList(List<String> queryParamList) throws Exception {
		return orderMapper.queryShipmentPlanNoList(queryParamList);
	}

	/**
	 * 批量新增出库数量信息
	 * @param shipmentQuantityInfoList 出库数量信息列表
	 * @return
	 */
	@Override
	public void batchAddShipmentQuantityEntryList(List<ShipmentQuantityInfo> shipmentQuantityInfoList) throws Exception {
		// 每50条批量新增一次
		int batchSize = 50;
		for (int i = 0; i < shipmentQuantityInfoList.size(); i += batchSize) {
			int endIndex = Math.min(i + batchSize, shipmentQuantityInfoList.size());
			List<ShipmentQuantityInfo> batch = shipmentQuantityInfoList.subList(i, endIndex);
			orderMapper.batchAddShipmentQuantityEntryList(batch);
		}
	}

	/**
	 * 更新出库数量信息
	 * @param id 流水号
	 * @param shipmentQuantity 出库数量
	 * @param shipmentPlanNo 出库计划番号
	 * @return
	 * @throws Exception
	 */
	@Override
	public void updateShipmentQuantityEntry(Integer id, String shipmentQuantity, String shipmentPlanNo) throws Exception {
		orderMapper.updateShipmentQuantityEntry(id, shipmentQuantity, shipmentPlanNo);
	}

	/**
	 * 删除出库数量信息
	 * @param id 流水号
	 * @throws Exception
	 */
	@Override
	public void deleteShipmentQuantityEntry(Integer id) throws Exception {
		orderMapper.deleteShipmentQuantityEntry(id);
	}

	/**
	 * 根据客户代码查询产品信息
	 * 
	 * @param customerCode 客户代码
	 * @return
	 * @throws Exception
	 */
	@Override
	public List<String> getProductListByCustomerCode(String customerCode) throws Exception {
		return orderMapper.getProductListByCustomerCode(customerCode);
	}

	/**
	 * 根据客户代码和铜合同类别查询铜合同列表
	 * 
	 * @param customerCode       客户代码
	 * @param copperContractType 铜合同类别
	 * @return
	 * @throws Exception
	 */
	@Override
	public List<String> getCopperContractList(String customerCode, String copperContractType) throws Exception {
		return orderMapper.getCopperContractList(customerCode, copperContractType);
	}

	/**
	 * 根据铜合同No查询铜合同详情
	 * 
	 * @param copperContractNo 铜合同No
	 * @return
	 * @throws Exception
	 */
	@Override
	public OrderEntryDetails getCopperContractDetail(String copperContractNo) throws Exception {
		return orderMapper.getCopperContractDetail(copperContractNo);
	}

	/**
	 * 查询所有铜条件列表
	 * 
	 * @return
	 * @throws Exception
	 */
	@Override
	public List<Map<String, Object>> getCopperConditionList() throws Exception {
		return orderMapper.getCopperConditionListWithCurrency();
	}
}