/*
 * @Author: jhalie110 <EMAIL>
 * @Date: 2025-05-15 19:54:33
 * @LastEditors: jhalie110 <EMAIL>
 * @LastEditTime: 2025-05-31 01:27:34
 * @FilePath: \business-system\prjback\WebContent\static\hongru\js\order\shipmentQuantityEntry_add.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
layui.use(['laydate', 'layer', 'table', 'element','form'], function(){
    var laydate = layui.laydate //日期
        ,layer = layui.layer //弹层
        ,table = layui.table //表格
        ,element = layui.element //元素操作
    var form = layui.form;

    var today = new Date();
    var year = today.getFullYear();
    var month = today.getMonth() + 1;
    var day = today.getDate();
    
    // 格式化日期为yyyy-MM-dd
    month = month < 10 ? '0' + month : month;
    day = day < 10 ? '0' + day : day;
    
    var todayStr = year + '-' + month + '-' + day;
    // 出库日期默认今天
    $('#outboundStartDate').val(todayStr);
    $('#outboundEndDate').val(todayStr);

    // 出库开始日期
    var outboundInsStart = layui.laydate.render({
        elem: '#outboundStartDate'
        ,done: function(value, date){
            // if (value) {
            //     //更新结束日期的最小日期
            //     outboundInsEnd.config.min = lay.extend({}, date, {
            //         month: date.month - 1
            //     });

            //     //自动弹出结束日期的选择器
            //     outboundInsEnd.config.elem[0].focus();
            // } 

            if (!value) {
                $('#outboundStartDate').val(todayStr);
            }
        }
    });
    
    // 出库结束日期
    var outboundInsEnd = layui.laydate.render({
        elem: '#outboundEndDate'
        ,done: function(value, date){
            // if (value) {
            //     //更新开始日期的最大日期
            //     outboundInsStart.config.max = lay.extend({}, date, {
            //         month: date.month - 1
            //     });
            // }

            if (!value) {
                $('#outboundEndDate').val(todayStr);
            }
        }
    });
    
    //执行一个 table 实例
    var url = baselocation+'/order/shipmentQuantityEntry/listForAdd';
    table.render({
        elem: '#demo'
        ,height: 'full-70'
        ,url: url //数据接口
        ,cellMinWidth: 80 //全局定义常规单元格的最小宽度
        ,parseData:function(res){ //res 即为原始返回的数据
            return {
                "code": 0, //解析接口状态
                "msg": '', //解析提示文本
                "count": res.total, //解析数据长度
                "data": res.rows //解析数据列表
            };
        }
        ,method:'post'
        ,title: '订单确认列表'
        ,page: true //开启分页
        ,limits: [10,20,50,100]
        ,where:$("#formSearch").serializeJsonObject()
        ,toolbar: '#toolbarDemo' //开启工具栏，此处显示默认图标，可以自定义模板，详见文档
        ,defaultToolbar: ['filter']
        ,totalRow: false //开启合计行
        ,cols: [[ //表头
            {type: 'checkbox', fixed: 'left'}
            ,{field: 'outboundDate',title: '到货日期',align:'center'}
            ,{field: 'customerCode',title: '客户代码',align:'center'}
            ,{field: 'customerAlias',title: '客户简称',align:'center'}
            ,{field: 'productCode',title: '产品代码',align:'center'}
            ,{field: 'shipmentQuantity',title: '出库数量（KG）',align:'center'}
            ,{field: 'customerOrderNo',title: '客户订单号',align:'center'}
            ,{field: 'productCategory',title: '品目分类名',align:'center'}
            // ,{title: '操作',minWidth:70, align:'center',fixed: 'right', toolbar: '#barDemo'}
        ]]
    });

    //监听头工具栏事件
    table.on('toolbar(test)', function(obj){
        var checkStatus = table.checkStatus(obj.config.id)
            ,data = checkStatus.data; //获取选中的数据
        switch(obj.event){
            case 'confirm':
                console.log('确认按钮点击');
                // 获取所有被勾选的数据
                if(data.length === 0){
                    layer.msg('请选择需要确认的出库数量信息', {icon: 0});
                    return;
                }
                
               // 收集所有选中行的到货日期、客户编号、产品编号
               var outboundDateAndCustomerCodeAndProductCodeList = [];
               $.each(data, function(index, item) {
                   outboundDateAndCustomerCodeAndProductCodeList.push((item?.outboundDate?.trim() || '') + '-' + (item?.customerCode?.trim() || '') + '-' + (item?.productCode?.trim() || ''));
               });

               var outboundDateAndCustomerCodeAndProductCodeListStr = outboundDateAndCustomerCodeAndProductCodeList.join(',');
                
                // 确认操作
                layer.confirm('确定要确认选中的' + data.length + '条出库数量信息吗？', {
                    btn: ['确定', '取消']
                }, function() {
                    // 发送确认请求
                    $.ajax({
                        type: 'post',
                        url: baselocation + '/order/shipmentQuantityEntry/add',
                        data: {
                            outboundStartDate: $('#outboundStartDate').val(),
                            outboundEndDate: $('#outboundEndDate').val(),
                            customerCode: $('#customerCode').val(),
                            outboundDateAndCustomerCodeAndProductCodeListStr: outboundDateAndCustomerCodeAndProductCodeListStr
                        },
                        dataType: 'json',
                        success: function(result) {
                            if(result.code == 1) {
                                layer.msg('确认成功!', {
                                    icon: 1,
                                    time: 1500
                                }, function() {
                                    // 刷新表格
                                    search();
                                    parent.$('#outboundStartDate').val($('#outboundStartDate').val());
                                    parent.$('#outboundEndDate').val($('#outboundEndDate').val());
                                    parent.$('#customerCode').val($('#customerCode').val());
                                    parent.search();
                                });
                            } else {
                                layer.alert(result.data || '操作失败，请重试！', {icon: 2});
                            }
                        },
                        error: function() {
                            layer.alert('网络错误，请稍后重试！', {icon: 2});
                        }
                    });
                });
                // layer_show('确认', baselocation+"/order/orderConfirm/confirm/view", document.body.clientWidth-10, document.body.clientHeight-10);
                break;
            case 'refresh':
                var temp = $("#formSearch").serializeJsonObject();
                console.info(temp);
                layui.table.reload('demo', {
                    page: {
                        curr: 1
                    }
                    ,where: temp
                }, 'data');
                break;
        };
    });

    //监听行工具事件
    table.on('tool(test)', function(obj){ //注：tool 是工具条事件名，test 是 table 原始容器的属性 lay-filter="对应的值"
        var data = obj.data //获得当前行数据
            ,layEvent = obj.event; //获得 lay-event 对应的值
        if(layEvent === 'detail'){
            layer_show('详情',baselocation+"/order/shipmentPlanEntry/detail/view?outboundDate="+data.outboundDate+"&demandCustomerCode="+data.demandCustomerCode+"&contractPartyCustomerCode="+data.contractPartyCustomerCode,document.body.clientWidth-10, document.body.clientHeight-10);
        }
    });
});

function search() {
	var temp = $("#formSearch").serializeJsonObject();
	console.info(temp);
    
    if ( !temp?.outboundStartDate && !temp?.outboundEndDate) {
        var today = new Date();
        var year = today.getFullYear();
        var month = today.getMonth() + 1;
        var day = today.getDate();
        
        // 格式化日期为yyyy-MM-dd
        month = month < 10 ? '0' + month : month;
        day = day < 10 ? '0' + day : day;
        
        var todayStr = year + '-' + month + '-' + day;
        // 出库日期默认今天
        $('#outboundStartDate').val(todayStr);
        $('#outboundEndDate').val(todayStr);
        temp.outboundStartDate = todayStr;
        temp.outboundEndDate = todayStr;
    }

	layui.table.reload('demo', {
		page: {
			curr: 1
		}
		,where: temp
	}, 'data');
}

// 订单附件下载功能
function downloadOrderAttachment(attachmentId) {
    if (!attachmentId || attachmentId.trim() === '') {
        layer.msg('未找到附件', {icon: 2});
        return;
    }

    // 直接打开下载链接
    window.open(baselocation + '/common/download?id=' + attachmentId);

    // 或者使用异步方式下载 - 如果需要先验证权限等操作
    /*
    $.ajax({
        url: baselocation + '/common/checkDownloadPermission',
        type: 'post',
        data: {id: attachmentId},
        success: function(res) {
            if (res.code == 1) {
                window.open(baselocation + '/common/download?id=' + attachmentId);
            } else {
                layer.msg(res.message || '下载失败', {icon: 2});
            }
        },
        error: function() {
            layer.msg('下载请求失败', {icon: 2});
        }
    });
    */
}
