package com.hongru.entity.businessOps;

import java.math.BigDecimal;

import com.baomidou.mybatisplus.annotations.TableField;
import com.baomidou.mybatisplus.annotations.TableId;
import com.baomidou.mybatisplus.annotations.TableName;
import com.baomidou.mybatisplus.enums.IdType;


@TableName("铜合同表")//CostPrice
public class CopperContract {
	/* 状态-正常 */
	public static final short STATE_NORMAL = 0;
	/* 状态-删除 */
	public static final short STATE_DELETED = 9;
	
	/* 流水号 */
	@TableId(value="流水号", type= IdType.AUTO)
	protected int id;
	/* 铜签约No */
	protected String copperSignNo;
	/* 铜签约类别 */
	protected String copperSignType;
	/* 铜条件 */
	protected String copperCondition;
	/* 客户代码 */
	protected String customerCode;
	/* 签约铜价格 */
	protected BigDecimal signedCopperPrice;
	/* 税率 */
	protected BigDecimal taxRate;
	/* 免税签约铜价 */
	protected BigDecimal signedCopperPriceExTax;
	/* 签约数量 */
	protected BigDecimal signedQuantity;
	/* 实际数量 */
	protected BigDecimal actualQuantity;
	/* 剩余数量 */
	protected BigDecimal remainingQuantity;
	/* 预约日期 */
	protected String appointmentDate;
	/* 使用月 */
	protected String useMonth;
	/* 状态 */
	protected short status;
	/* 创建人姓名 */
	protected String creatorName;
	/* 创建时间 */
	protected String createdTime;
	/* 更新人姓名 */
	protected String updaterName;
	/* 更新时间 */
	protected String updatedTime;
	
	/* 客户简称 */
	@TableField(exist = false)
	protected String customerAlias;
	
	/* 货币 */
	@TableField(exist = false)
	protected String currency;
	
	public int getId() {
		return id;
	}
	public void setId(int id) {
		this.id = id;
	}
	public String getCopperSignNo() {
		return copperSignNo;
	}
	public void setCopperSignNo(String copperSignNo) {
		this.copperSignNo = copperSignNo;
	}
	public String getCopperSignType() {
		return copperSignType;
	}
	public void setCopperSignType(String copperSignType) {
		this.copperSignType = copperSignType;
	}
	public String getCopperCondition() {
		return copperCondition;
	}
	public void setCopperCondition(String copperCondition) {
		this.copperCondition = copperCondition;
	}
	public String getCustomerCode() {
		return customerCode;
	}
	public void setCustomerCode(String customerCode) {
		this.customerCode = customerCode;
	}
	public BigDecimal getSignedCopperPrice() {
		return signedCopperPrice;
	}
	public void setSignedCopperPrice(BigDecimal signedCopperPrice) {
		this.signedCopperPrice = signedCopperPrice;
	}
	public BigDecimal getTaxRate() {
		return taxRate;
	}
	public void setTaxRate(BigDecimal taxRate) {
		this.taxRate = taxRate;
	}
	public BigDecimal getSignedCopperPriceExTax() {
		return signedCopperPriceExTax;
	}
	public void setSignedCopperPriceExTax(BigDecimal signedCopperPriceExTax) {
		this.signedCopperPriceExTax = signedCopperPriceExTax;
	}
	public BigDecimal getSignedQuantity() {
		return signedQuantity;
	}
	public void setSignedQuantity(BigDecimal signedQuantity) {
		this.signedQuantity = signedQuantity;
	}
	public BigDecimal getActualQuantity() {
		return actualQuantity;
	}
	public void setActualQuantity(BigDecimal actualQuantity) {
		this.actualQuantity = actualQuantity;
	}
	public BigDecimal getRemainingQuantity() {
		return remainingQuantity;
	}
	public void setRemainingQuantity(BigDecimal remainingQuantity) {
		this.remainingQuantity = remainingQuantity;
	}
	public String getAppointmentDate() {
		return appointmentDate;
	}
	public void setAppointmentDate(String appointmentDate) {
		this.appointmentDate = appointmentDate;
	}
	public String getUseMonth() {
		return useMonth;
	}
	public void setUseMonth(String useMonth) {
		this.useMonth = useMonth;
	}
	public short getStatus() {
		return status;
	}
	public void setStatus(short status) {
		this.status = status;
	}
	public String getCreatorName() {
		return creatorName;
	}
	public void setCreatorName(String creatorName) {
		this.creatorName = creatorName;
	}
	public String getCreatedTime() {
		return createdTime;
	}
	public void setCreatedTime(String createdTime) {
		this.createdTime = createdTime;
	}
	public String getUpdaterName() {
		return updaterName;
	}
	public void setUpdaterName(String updaterName) {
		this.updaterName = updaterName;
	}
	public String getUpdatedTime() {
		return updatedTime;
	}
	public void setUpdatedTime(String updatedTime) {
		this.updatedTime = updatedTime;
	}
	public String getCustomerAlias() {
		return customerAlias;
	}
	public void setCustomerAlias(String customerAlias) {
		this.customerAlias = customerAlias;
	}
	public String getCurrency() {
		return currency;
	}
	public void setCurrency(String currency) {
		this.currency = currency;
	}
}