<%@ page language="java" import="java.util.*"
         contentType="text/html; charset=utf-8"%>
<%@ include file="/WEB-INF/layouts/base.jsp"%>
<html>
<head>
    <title>仓库修改</title>
    <style>
        /*设置数据表表头字体*/
        .layui-table th .layui-table-cell{
            text-align: center;
            font-size: 16px;
            font-weight: bold;
        }
    </style>
</head>
<body>
<form class="layui-form hr-form-add" action="${ctx}/salesCommon/wareHouse/edit" method="post" id="submitForm">
    <div class="layui-fluid hr-layui-fluid">
        <div class="layui-card">
            <div class="layui-card-body">
                <div class="layui-form-item layui-row">

                    <div class="layui-inline layui-col-md5">
                        <label class="layui-form-label"><span class="star">*</span>仓库:</label>
                        <div class="layui-input-block">
                            <input type="text" class="layui-input" id="wareHouseCode" name="wareHouseCode" value="${wareHouse.wareHouseCode}" required lay-verify="required" />
                        </div>
                    </div>
                    <div class="layui-inline layui-col-md5">
                        <label class="layui-form-label"><span class="star">*</span>仓库名:</label>
                        <div class="layui-input-block">
                            <input type="text" class="layui-input" id="wareHouseName" name="wareHouseName" value="${wareHouse.wareHouseName}" required lay-verify="required" />
                        </div>
                    </div>
                    <div class="layui-inline layui-col-md5">
                        <label class="layui-form-label">仓库场所区分:</label>
                        <div class="layui-input-block">
                            <select class="layui-select" id="wareHousePlaceDiff" name="wareHousePlaceDiff" required>
                                <option value="">${wareHouse.wareHousePlaceDiff}</option>
                                <option value="社内">社内</option>
                                <option value="签约方(委托)">签约方(委托)</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline layui-col-md5">
                        <label class="layui-form-label">仓库区分:</label>
                        <div class="layui-input-block">
                            <select class="layui-select" id="wareHouseDiff" name="wareHouseDiff" required>
                                <option value="">${wareHouse.wareHouseDiff}</option>
                                <option value="成品">成品</option>
                                <option value="半成品">半成品</option>
                                <option value="原料">原料</option>
                                <option value="共通">共通</option>
                            </select>
                        </div>
                    </div>
                </div>

                </div>
                <div class="layui-form-item layui-row">
                    <div class="layui-inline layui-col-md12">
                        <label class="layui-form-label"></label>
                        <div class="layui-input-block">
                            <input type="hidden" id="id" name="id" value="${wareHouse.id}"/>
                            <button type="button" class="layui-btn layui-bg-blue" lay-submit lay-filter="formDemo">保存</button>
                            <button type="button" class="layui-btn layui-btn-primary" onclick="closeAll();">取消</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</form>
<myfooter>
    <script src="${ctxsta}/hongru/js/salesCommon/wareHouse_modify.js?time=1"></script>
</myfooter>
</body>
</html>
