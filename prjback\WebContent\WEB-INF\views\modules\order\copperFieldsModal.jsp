<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt"%>
<c:set var="ctx" value="${pageContext.request.contextPath}"/>
<c:set var="ctxsta" value="${pageContext.request.contextPath}/static"/>

<!-- 铜字段录入弹窗 -->
<div id="copperFieldsModal" style="display: none; padding: 20px;">
    <form class="layui-form" id="copperFieldsForm">
        <div class="layui-row">
            <div class="layui-col-md6">
                <div class="layui-form-item">
                    <label class="layui-form-label">铜合同类别:</label>
                    <div class="layui-input-block">
                        <select name="copperContractType" id="copperContractType" lay-filter="copperContractType">
                            <option value="">请选择</option>
                            <option value="LME">LME</option>
                            <option value="SHFE">SHFE</option>
                        </select>
                    </div>
                </div>
            </div>
            <div class="layui-col-md6">
                <div class="layui-form-item">
                    <label class="layui-form-label">铜签约No:</label>
                    <div class="layui-input-block">
                        <select name="copperContractNo" id="copperContractNo" lay-filter="copperContractNo">
                            <option value="">请选择</option>
                        </select>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="layui-row">
            <div class="layui-col-md6">
                <div class="layui-form-item">
                    <label class="layui-form-label">铜条件:</label>
                    <div class="layui-input-block">
                        <select name="copperCondition" id="copperCondition" lay-filter="copperCondition">
                            <option value="">请选择</option>
                        </select>
                    </div>
                </div>
            </div>
            <div class="layui-col-md6">
                <div class="layui-form-item">
                    <label class="layui-form-label">铜货币:</label>
                    <div class="layui-input-block">
                        <input type="text" name="copperCurrency" id="copperCurrency" class="layui-input" readonly>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="layui-row">
            <div class="layui-col-md6">
                <div class="layui-form-item">
                    <label class="layui-form-label">换算率:</label>
                    <div class="layui-input-block">
                        <input type="number" name="conversionRate" id="conversionRate" class="layui-input" step="0.0001" oninput="calculateOrderAmount()">
                    </div>
                </div>
            </div>
            <div class="layui-col-md6">
                <div class="layui-form-item">
                    <label class="layui-form-label">铜基本:</label>
                    <div class="layui-input-block">
                        <input type="number" name="copperBase" id="copperBase" class="layui-input" step="0.01" readonly>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="layui-row">
            <div class="layui-col-md6">
                <div class="layui-form-item">
                    <label class="layui-form-label">升水:</label>
                    <div class="layui-input-block">
                        <input type="number" name="premium" id="premium" class="layui-input" step="0.01" oninput="calculateOrderAmount()">
                    </div>
                </div>
            </div>
            <div class="layui-col-md6">
                <div class="layui-form-item">
                    <label class="layui-form-label">换算后铜单价:</label>
                    <div class="layui-input-block">
                        <input type="number" name="convertedCopperPrice" id="convertedCopperPrice" class="layui-input" step="0.01" readonly>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="layui-row">
            <div class="layui-col-md6">
                <div class="layui-form-item">
                    <label class="layui-form-label">0base:</label>
                    <div class="layui-input-block">
                        <input type="number" name="zeroBase" id="zeroBase" class="layui-input" step="0.01" oninput="calculateOrderAmount()">
                    </div>
                </div>
            </div>
            <div class="layui-col-md6">
                <div class="layui-form-item">
                    <label class="layui-form-label">接单单价:</label>
                    <div class="layui-input-block">
                        <input type="number" name="orderUnitPrice" id="orderUnitPrice" class="layui-input" step="0.01" readonly>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="layui-row">
            <div class="layui-col-md6">
                <div class="layui-form-item">
                    <label class="layui-form-label">订单数量:</label>
                    <div class="layui-input-block">
                        <input type="number" name="orderQuantity" id="modalOrderQuantity" class="layui-input" step="0.01" oninput="calculateOrderAmount()" placeholder="请输入订单数量">
                    </div>
                </div>
            </div>
            <div class="layui-col-md6">
                <div class="layui-form-item">
                    <label class="layui-form-label">接单金额:</label>
                    <div class="layui-input-block">
                        <input type="number" name="orderAmount" id="orderAmount" class="layui-input" step="0.01" readonly>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="layui-form-item" style="text-align: center; margin-top: 20px;">
            <button type="button" class="layui-btn" onclick="saveCopperFields()">确定</button>
            <button type="button" class="layui-btn layui-btn-primary" onclick="closeCopperFieldsModal()">取消</button>
        </div>
        
        <!-- 隐藏字段，用于存储当前行的引用 -->
        <input type="hidden" id="currentRowRef" value="">
        <input type="hidden" id="currentCustomerCode" value="">
    </form>
</div>

<script>
// 铜字段相关的JavaScript函数
var copperFieldsModal;
var currentEditingRow = null;

// 打开铜字段弹窗
function openCopperFieldsModal(button) {
    currentEditingRow = button;
    var row = $(button).closest('tr');
    var customerCode = $('#demandCustomerCode').val();
    
    if (!customerCode) {
        layer.msg('请先选择需求方', {icon: 2});
        return;
    }

    // 检查订单数量是否已填写
    var orderQuantity = row.find('input[name="orderQuantity"]').val();
    if (!orderQuantity || orderQuantity.trim() === '' || parseFloat(orderQuantity) <= 0) {
        layer.msg('请先设置订单数量再设置接单金额', {icon: 2, time: 3000});
        // 自动聚焦到订单数量输入框
        var orderQuantityInput = row.find('input[name="orderQuantity"]');
        if (orderQuantityInput.length > 0) {
            orderQuantityInput.focus();
            // 高亮显示输入框
            orderQuantityInput.addClass('layui-form-danger');
            setTimeout(function() {
                orderQuantityInput.removeClass('layui-form-danger');
            }, 3000);
        }
        return;
    }

    $('#modalOrderQuantity').val(orderQuantity);
    $('#currentCustomerCode').val(customerCode);

    console.log('copperFieldsModal同步订单数量到弹窗:', orderQuantity);
    
    // 加载铜条件列表
    loadCopperConditionList();
    
    // 打开弹窗
    copperFieldsModal = layer.open({
        type: 1,
        title: '设置接单金额',
        content: $('#copperFieldsModal'),
        area: ['800px', '600px'],
        btn: false,
        closeBtn: 1,
        shadeClose: false
    });
}

// 关闭铜字段弹窗
function closeCopperFieldsModal() {
    layer.close(copperFieldsModal);
    currentEditingRow = null;
}

// 加载铜条件列表
function loadCopperConditionList() {
    $.post(baselocation + '/order/orderEntry/getCopperConditionList', {}, function(result) {
        if (result.code === 1) {
            var select = $('#copperCondition');
            select.empty().append('<option value="">请选择</option>');
            $.each(result.data, function(index, item) {
                select.append('<option value="' + item + '">' + item + '</option>');
            });
            form.render('select');
        }
    });
}

// 铜合同类别变化时加载铜合同列表
function loadCopperContractList(copperContractType) {
    var customerCode = $('#currentCustomerCode').val();
    if (!customerCode || !copperContractType) {
        return;
    }
    
    $.post(baselocation + '/order/orderEntry/getCopperContractList', {
        customerCode: customerCode,
        copperContractType: copperContractType
    }, function(result) {
        if (result.code === 1) {
            var select = $('#copperContractNo');
            select.empty().append('<option value="">请选择</option>');
            $.each(result.data, function(index, item) {
                select.append('<option value="' + item + '">' + item + '</option>');
            });
            form.render('select');
        }
    });
}

// 铜合同No变化时加载合同详情
function loadCopperContractDetail(copperContractNo) {
    if (!copperContractNo) {
        return;
    }
    
    $.post(baselocation + '/order/orderEntry/getCopperContractDetail', {
        copperContractNo: copperContractNo
    }, function(result) {
        if (result.code === 1 && result.data) {
            $('#copperCondition').val(result.data.copperCondition);
            $('#copperBase').val(result.data.copperBase);
            $('#copperCurrency').val(result.data.copperCurrency);
            form.render('select');
            calculateOrderAmount();
        }
    });
}

// 计算接单金额
function calculateOrderAmount() {
    var conversionRate = parseFloat($('#conversionRate').val()) || 0;
    var copperBase = parseFloat($('#copperBase').val()) || 0;
    var premium = parseFloat($('#premium').val()) || 0;
    var zeroBase = parseFloat($('#zeroBase').val()) || 0;
    var orderQuantity = parseFloat($('#modalOrderQuantity').val()) || 0;
    
    if (conversionRate > 0 && copperBase > 0) {
        // 换算后铜单价 = （铜base + 升水）* 换算率
        var convertedCopperPrice = (copperBase + premium) * conversionRate;
        $('#convertedCopperPrice').val(convertedCopperPrice.toFixed(4));
        
        // 接单单价 = 换算后铜单价 + 0base
        var orderUnitPrice = convertedCopperPrice + zeroBase;
        $('#orderUnitPrice').val(orderUnitPrice.toFixed(4));
        
        // 接单金额 = 接单单价 * 订单数量
        var orderAmount = orderUnitPrice * orderQuantity;
        $('#orderAmount').val(orderAmount.toFixed(2));
    }
}

// 保存铜字段数据
function saveCopperFields() {
    if (!currentEditingRow) {
        layer.msg('无效的操作', {icon: 2});
        return;
    }
    
    var orderAmount = $('#orderAmount').val();
    if (!orderAmount || parseFloat(orderAmount) <= 0) {
        layer.msg('请完善铜字段信息以计算接单金额', {icon: 2});
        return;
    }

    // 获取弹窗中的订单数量
    var modalOrderQuantity = $('#modalOrderQuantity').val();

    // 更新按钮显示
    $(currentEditingRow).find('.order-amount-display').text(orderAmount);

    // 同步订单数量回外面的输入框
    var row = $(currentEditingRow).closest('tr');
    var orderQuantityInput = row.find('input[name="orderQuantity"]');
    if (orderQuantityInput.length > 0) {
        orderQuantityInput.val(modalOrderQuantity);
        console.log('copperFieldsModal同步订单数量回外面:', modalOrderQuantity);
    }
    
    // 存储铜字段数据到行的data属性中
    var row = $(currentEditingRow).closest('tr');
    row.data('copperContractType', $('#copperContractType').val());
    row.data('copperContractNo', $('#copperContractNo').val());
    row.data('copperCondition', $('#copperCondition').val());
    row.data('copperCurrency', $('#copperCurrency').val());
    row.data('conversionRate', $('#conversionRate').val());
    row.data('copperBase', $('#copperBase').val());
    row.data('premium', $('#premium').val());
    row.data('convertedCopperPrice', $('#convertedCopperPrice').val());
    row.data('zeroBase', $('#zeroBase').val());
    row.data('orderUnitPrice', $('#orderUnitPrice').val());
    row.data('orderAmount', orderAmount);
    
    layer.msg('设置成功', {icon: 1});
    closeCopperFieldsModal();
}
</script>
