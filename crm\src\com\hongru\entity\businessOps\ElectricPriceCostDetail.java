package com.hongru.entity.businessOps;

import com.baomidou.mybatisplus.annotations.TableId;
import com.baomidou.mybatisplus.annotations.TableName;
import com.baomidou.mybatisplus.enums.IdType;

import java.math.BigDecimal;

@TableName("电力使用明细表")//CostPrice
public class ElectricPriceCostDetail {
	/* 流水号 */
	@TableId(value="流水号", type= IdType.AUTO)
	protected int costId;
	/* 导入标识 */
	protected int importId;
	/* 年月 */
	protected String yearMonth;
	/* 部门 */
	protected String department;
	/* 用电量 */
	protected BigDecimal eleCost;
	/* 年 */
	protected int year;
	/* 月 */
	protected int month;
	/* 创建人标识 */
	protected int creatorId;
	/* 创建人姓名 */
	protected String creatorName;
	/* 创建时间 */
	protected String createdTime;
	/* 最后修改人标识 */
	protected int lastModifierId;
	/* 最后修改人姓名 */
	protected String lastModifierName;
	/* 最后修改时间 */
	protected String lastModifiedTime;
	public int getCostId() {
		return costId;
	}
	public void setCostId(int costId) {
		this.costId = costId;
	}
	public String getYearMonth() {
		return yearMonth;
	}
	public void setYearMonth(String yearMonth) {
		this.yearMonth = yearMonth;
	}
	public BigDecimal getEleCost() {
		return eleCost;
	}
	public void setEleCost(BigDecimal eleCost) {
		this.eleCost = eleCost;
	}
	public int getYear() {
		return year;
	}
	public void setYear(int year) {
		this.year = year;
	}
	public int getMonth() {
		return month;
	}
	public void setMonth(int month) {
		this.month = month;
	}
	public int getCreatorId() {
		return creatorId;
	}
	public void setCreatorId(int creatorId) {
		this.creatorId = creatorId;
	}
	public String getCreatorName() {
		return creatorName;
	}
	public void setCreatorName(String creatorName) {
		this.creatorName = creatorName;
	}
	public String getCreatedTime() {
		return createdTime;
	}
	public void setCreatedTime(String createdTime) {
		this.createdTime = createdTime;
	}
	public int getLastModifierId() {
		return lastModifierId;
	}
	public void setLastModifierId(int lastModifierId) {
		this.lastModifierId = lastModifierId;
	}
	public String getLastModifierName() {
		return lastModifierName;
	}
	public void setLastModifierName(String lastModifierName) {
		this.lastModifierName = lastModifierName;
	}
	public String getLastModifiedTime() {
		return lastModifiedTime;
	}
	public void setLastModifiedTime(String lastModifiedTime) {
		this.lastModifiedTime = lastModifiedTime;
	}
	public String getDepartment() {
		return department;
	}
	public void setDepartment(String department) {
		this.department = department;
	}
	public int getImportId() {
		return importId;
	}
	public void setImportId(int importId) {
		this.importId = importId;
	}
}