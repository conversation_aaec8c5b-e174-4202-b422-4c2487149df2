<%@ page language="java" import="java.util.*"
         contentType="text/html; charset=utf-8"%>
<%@ include file="/WEB-INF/layouts/base.jsp"%>
<html>
<head>
    <title>订单信息详情</title>
    <style>
        /*设置数据表表头字体*/
        .layui-table th .layui-table-cell{
            text-align: center;
            font-size: 14px;
            font-weight: bold;
            padding: 5px 2px;
        }

        /* 详情页面文本显示样式 */
        .layui-input-block {
            min-height: 32px;
        }

        .detail-text {
            line-height: 32px;
            padding: 0 8px;
            color: #333;
        }

        /* 设置表格单元格更紧凑 */
        .layui-table td, .layui-table th {
            padding: 4px 2px;
            text-align: center;
        }

        /* 设置表格行高更小 */
        .layui-table tr {
            line-height: 28px;
        }

        /* 附件预览区域样式 */
        #attachmentPreview {
            margin-top: 5px;
        }

        #attachmentPreview .layui-card {
            box-shadow: 0 1px 2px 0 rgba(0,0,0,.05);
        }

        #attachmentPreview .layui-card-header {
            font-weight: bold;
            color: #1E9FFF;
            padding: 8px 10px;
            font-size: 14px;
        }

        /* 紧凑的卡片样式 */
        .layui-card-body {
            padding: 8px;
        }

        /* 客户信息区域样式 */
        .customer-info-section {
            border: 1px solid #e6e6e6;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }

        /* 表单标签样式 */
        .layui-form-label {
            padding: 8px 10px;
            width: 80px;
        }
    </style>
</head>
<body>
<form class="layui-form hr-form-add" action="javascript:void(0);" method="post" id="submitForm" onsubmit="return false;">
    <div class="layui-fluid hr-layui-fluid">
        <div class="layui-card">
            <div class="layui-card-body">
                <div class="layui-form-item layui-row">

                    <div class="layui-form-item layui-row customer-info-section" style="padding-top: 10px;">
                        <div class="layui-inline layui-col-md3">
                            <label class="layui-form-label">需求方:</label>
                            <div class="layui-input-block detail-text">
                                <c:forEach items="${customersList}" var="customers">
                                    <c:if test="${orderEntryDetails.demandCustomerCode == customers.customerCode}">
                                        ${customers.customerCode} - ${customers.customerAlias}
                                    </c:if>
                                </c:forEach>
                            </div>
                        </div>
                        <div class="layui-inline layui-col-md3">
                            <label class="layui-form-label">合同方:</label>
                            <div class="layui-input-block detail-text">
                                <c:forEach items="${contractPartyCustomerList}" var="customers">
                                    <c:if test="${orderEntryDetails.contractPartyCustomerCode == customers.customerCode}">
                                        ${customers.customerCode} - ${customers.customerAlias}
                                    </c:if>
                                </c:forEach>
                            </div>
                        </div>
                        <div class="layui-inline layui-col-md3">
                            <label class="layui-form-label">客户订单号:</label>
                            <div class="layui-input-block detail-text">
                                ${orderEntryDetails.customerOrderNo}
                            </div>
                        </div>
                        <div class="layui-inline layui-col-md2">
                            <label class="layui-form-label">订单种类:</label>
                            <div class="layui-input-block detail-text">
                                ${orderEntryDetails.orderType == '0' ? '通用' : '样品'}
                            </div>
                        </div>
                    </div>
                    <div class="layui-card">
                        <div class="layui-card-body">
                            <div class="layui-form-item layui-row">
                                <div class="layui-inline layui-col-md12">
                                    <div>
                                        <table class="layui-table">
                                            <thead>
                                                <tr>
                                                    <th style="width: 58px;">订单序号</th>
                                                    <th>准备日期</th>
                                                    <th>出库日期</th>
                                                    <th>到货日期</th>
                                                    <th>型号</th>
                                                    <th>条码</th>
                                                    <th>尺寸</th>
                                                    <th>线盘</th>
                                                    <th>部品编号</th>
                                                    <th>订单数量(kg)</th>
                                                    <th>已送数量(kg)</th>
                                                    <th>剩余数量(kg)</th>
                                                    <th>接单金额</th>
                                                    <th>详情备注</th>
                                                </tr>
                                            </thead>
                                            <tbody id="subOrderTableBody">
                                                <c:if test="${not empty orderEntryDetails.subOrders}">
                                                    <c:forEach items="${orderEntryDetails.subOrders}" var="subOrder">
                                                        <c:if test="${not empty subOrder.orderSerialNum}">
                                                            <tr>
                                                                <td>${subOrder.orderSerialNum}</td>
                                                                <td>${subOrder.prepareDate}</td>
                                                                <td>${subOrder.outboundDate}</td>
                                                                <td>${subOrder.arrivalDate}</td>
                                                                <td>${subOrder.modelNumber}</td>
                                                                <td>${subOrder.barcode}</td>
                                                                <td>${subOrder.size}</td>
                                                                <td>${subOrder.wireSpool}</td>
                                                                <td>${subOrder.partNumber}</td>
                                                                <td>${subOrder.orderQuantity}</td>
                                                                <td>${subOrder.deliveredQuantity}</td>
                                                                <td>${subOrder.remainingQuantity}</td>
                                                                <td style="color: #1E9FFF; font-weight: bold;">
                                                                    <c:choose>
                                                                        <c:when test="${not empty subOrder.orderAmount}">
                                                                            ${subOrder.orderAmount}
                                                                        </c:when>
                                                                        <c:otherwise>
                                                                            <span style="color: #999;">未设置</span>
                                                                        </c:otherwise>
                                                                    </c:choose>
                                                                </td>
                                                                <td>${subOrder.detailRemark}</td>
                                                            </tr>
                                                        </c:if>
                                                    </c:forEach>
                                                </c:if>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="layui-form-item layui-row">
                    <div class="layui-inline layui-col-md12">
                        <div style="text-align: center;">
                            <input type="hidden" id="id" name="id" value="${customerClassify.id}"/>
                            <input type="hidden" name="attachmentId" id="attachmentId" value="${orderEntryDetails.attachmentId}"/>
                        </div>
                    </div>
                </div>

                <!-- 附件预览区域 - 紧凑样式 -->
                <c:if test="${not empty orderEntryDetails.attachmentId}">
                    <div class="layui-card" style="margin-bottom: 5px;">
                        <div class="layui-card-header" style="display: flex; align-items: center;">
                            <i class="layui-icon layui-icon-file" style="margin-right: 5px;"></i>
                            <span id="attachmentTitle">附件预览</span>
                        </div>
                        <div class="layui-card-body" style="padding: 5px;">
                            <div id="attachmentPreview" class="layui-form-item">
                                <div style="margin-top: 5px; text-align: center;">
                                    <div id="previewContent">
                                        <!-- 预览内容将由JS动态加载 -->
                                        <div style="padding:10px; text-align:center;">
                                            <i class="layui-icon layui-icon-loading layui-anim layui-anim-rotate layui-anim-loop" style="font-size:24px;color:#1E9FFF"></i>
                                            <p style="margin-top:5px; font-size: 13px;">正在加载预览...</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </c:if>
            </div>
        </div>
    </div>
</form>
<myfooter>
    <script src="${ctxsta}/hongru/js/order/orderEntry_detail.js?time=3"></script>
</myfooter>
</body>
</html>
