package com.hongru.controller.common;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;

import com.hongru.base.BaseController;
import com.hongru.common.result.HrResult;
import com.hongru.common.util.ServletUtils;
import com.hongru.constant.CommonReturnCode;

/**
 * 通用文件上传控制器
 *
 * 类名称：CommonUploadController
 * 类描述：处理系统中的文件上传请求
 * 创建人：hongru
 * 创建时间：2023年6月15日
 */
@Controller
@RequestMapping(value = "/common")
public class CommonUploadController extends BaseController {

    private static final Logger logger = LoggerFactory.getLogger(CommonUploadController.class);

    /**
     * 文件上传接口
     *
     * @param file    上传的文件
     * @param request HTTP请求对象
     * @return 上传结果，包含文件信息
     */
    @PostMapping(value = "/upload/file")
    @ResponseBody
    public Object uploadFile(@RequestParam(value = "file", required = false) MultipartFile file,
            HttpServletRequest request) {
        try {
            if (file == null || file.isEmpty()) {
                return new HrResult(CommonReturnCode.FAILED, "请选择文件");
            }

            // 获取原始文件名和文件扩展名
            String originalFilename = file.getOriginalFilename();
            String fileExtension = originalFilename.substring(originalFilename.lastIndexOf("."));

            // 生成唯一ID
            String fileId = UUID.randomUUID().toString().replaceAll("-", "");

            // 生成新的文件名 - 使用文件ID作为前缀
            String newFileName = fileId + "_" + originalFilename;

            // 获取上传路径
            String uploadPath = request.getSession().getServletContext().getRealPath("/") + "uploads/";
            File uploadDir = new File(uploadPath);
            if (!uploadDir.exists()) {
                uploadDir.mkdirs();
            }

            // 记录日志
            logger.info("上传文件: ID=" + fileId + ", 文件名=" + originalFilename + ", 新文件名=" + newFileName);

            // 保存文件
            File destFile = new File(uploadPath + newFileName);
            file.transferTo(destFile);

            // 构建返回数据
            Map<String, Object> fileInfo = new HashMap<>();
            fileInfo.put("id", fileId);
            fileInfo.put("fileName", originalFilename); // 原始文件名
            fileInfo.put("newFileName", newFileName); // 存储的文件名
            fileInfo.put("fileUrl", request.getContextPath() + "/uploads/" + newFileName);
            fileInfo.put("uploadTime", new Date());
            fileInfo.put("size", file.getSize());

            return new HrResult(CommonReturnCode.SUCCESS, fileInfo); // 返回文件信息

        } catch (IOException e) {
            logger.error("文件上传失败", e);
            return new HrResult(CommonReturnCode.FAILED, "文件上传失败: " + e.getMessage());
        }
    }

    /**
     * 文件删除接口
     *
     * @param id 文件ID
     * @return 删除结果
     */
    @PostMapping(value = "/upload/remove")
    @ResponseBody
    public Object removeFile(@RequestParam("id") String id) {
        // 实际项目中应该根据文件ID从数据库查询文件信息，然后删除服务器上的文件
        // 这里简化处理，直接返回成功
        return new HrResult(CommonReturnCode.SUCCESS, "删除成功");
    }

    /**
     * 获取文件信息
     *
     * @param id      文件ID
     * @param request HTTP请求对象
     * @return 文件信息
     */
    @RequestMapping(value = "/file/info")
    @ResponseBody
    public Object getFileInfo(@RequestParam("id") String id, HttpServletRequest request) {
        try {
            // 模拟从数据库获取文件信息
            // 实际项目中应该从数据库或文件系统中获取文件信息
            Map<String, Object> fileInfo = new HashMap<>();
            fileInfo.put("id", id);

            // 尝试从文件名中提取原始文件名
            String uploadPath = request.getSession().getServletContext().getRealPath("/") + "uploads/";
            File uploadDir = new File(uploadPath);

            // 记录日志
            logger.info("查找文件ID: " + id + ", 上传路径: " + uploadPath);

            if (uploadDir.exists() && uploadDir.isDirectory()) {
                File[] files = uploadDir.listFiles();

                if (files != null) {
                    logger.info("目录中文件数量: " + files.length);

                    // 先尝试查找以文件ID开头的文件
                    String filePrefix = id + "_";
                    logger.info("查找文件前缀: " + filePrefix);

                    for (File file : files) {
                        if (file.getName().startsWith(filePrefix)) {
                            String fileName = file.getName();
                            // 提取原始文件名
                            String originalFileName = fileName.substring(fileName.indexOf("_") + 1);
                            fileInfo.put("fileName", originalFileName); // 这里存储原始文件名
                            fileInfo.put("newFileName", file.getName()); // 这里存储完整的文件名（包含ID前缀）
                            fileInfo.put("fileUrl", request.getContextPath() + "/uploads/" + file.getName());
                            fileInfo.put("size", file.length());
                            fileInfo.put("originalFileName", originalFileName); // 额外添加一个字段来存储原始文件名
                            logger.info("找到文件: " + fileName + ", 原始文件名: " + originalFileName);
                            break;
                        }
                    }

                    // 如果没找到，尝试精确匹配
                    if (!fileInfo.containsKey("fileName")) {
                        for (File file : files) {
                            if (file.getName().equals(id)) {
                                String fileName = file.getName();
                                fileInfo.put("fileName", fileName);
                                fileInfo.put("newFileName", file.getName());
                                fileInfo.put("fileUrl", request.getContextPath() + "/uploads/" + file.getName());
                                fileInfo.put("size", file.length());
                                logger.info("精确匹配到文件: " + fileName);
                                break;
                            }
                        }
                    }

                    // 如果还是没找到，尝试模糊匹配
                    if (!fileInfo.containsKey("fileName")) {
                        for (File file : files) {
                            if (file.getName().contains(id)) {
                                String fileName = file.getName();
                                // 如果文件名包含时间戳前缀，尝试提取原始文件名
                                if (fileName.contains("_")) {
                                    fileName = fileName.substring(fileName.indexOf("_") + 1);
                                }
                                fileInfo.put("fileName", fileName);
                                fileInfo.put("newFileName", file.getName());
                                fileInfo.put("fileUrl", request.getContextPath() + "/uploads/" + file.getName());
                                fileInfo.put("size", file.length());
                                logger.info("模糊匹配到文件: " + fileName);
                                break;
                            }
                        }
                    }

                    // 如果还是没找到，列出所有文件名供调试
                    if (!fileInfo.containsKey("fileName")) {
                        StringBuilder sb = new StringBuilder("目录中的文件: ");
                        for (File file : files) {
                            sb.append(file.getName()).append(", ");
                        }
                        logger.info(sb.toString());
                    }
                } else {
                    logger.warn("目录中没有文件");
                }
            } else {
                logger.warn("上传目录不存在或不是目录: " + uploadPath);
            }

            // 如果没有找到文件，返回默认信息
            if (!fileInfo.containsKey("fileName")) {
                fileInfo.put("fileName", "附件_" + id);
                fileInfo.put("newFileName", id);
                fileInfo.put("fileUrl", request.getContextPath() + "/uploads/" + id);
                fileInfo.put("size", 0);
            }

            return new HrResult(CommonReturnCode.SUCCESS, fileInfo);
        } catch (Exception e) {
            logger.error("获取文件信息异常", e);
            return new HrResult(CommonReturnCode.FAILED, "获取文件信息失败");
        }
    }

    /**
     * 文件下载接口
     *
     * @param id       文件ID
     * @param response HTTP响应对象
     * @return 下载结果
     */
    @RequestMapping(value = "/download")
    public void downloadFile(@RequestParam("id") String id, HttpServletRequest request, HttpServletResponse response) {
        // 实际项目中应该根据文件ID从数据库查询文件信息，然后从服务器读取文件提供下载
        try {
            // 检查文件ID是否有效
            logger.info("原始文件ID: " + id);

            if (id == null || id.trim().isEmpty()) {
                response.setStatus(HttpServletResponse.SC_BAD_REQUEST);
                response.getWriter().write("Invalid file ID");
                return;
            }

            // 处理可能的逗号问题
            if (id.contains(",")) {
                logger.info("文件ID包含逗号，原始值: " + id);
                id = id.split(",")[0];
                logger.info("处理后的文件ID: " + id);
            }

            // 先获取文件信息，尝试找到实际的文件名
            String uploadPath = request.getSession().getServletContext().getRealPath("/") + "uploads/";
            File uploadDir = new File(uploadPath);
            File file = null;

            // 记录日志
            logger.info("下载文件ID: " + id + ", 上传路径: " + uploadPath);

            try {
                // 先尝试从数据库或缓存中获取文件信息
                // 这里可以添加从数据库查询文件信息的代码

                // 如果没有数据库信息，尝试从文件系统中查找
                if (uploadDir.exists() && uploadDir.isDirectory()) {
                    File[] files = uploadDir.listFiles();
                    if (files != null) {
                        logger.info("目录中文件数量: " + files.length);

                        // 先尝试查找以文件ID开头的文件
                        String filePrefix = id + "_";
                        logger.info("查找文件前缀: " + filePrefix);

                        for (File f : files) {
                            if (f.getName().startsWith(filePrefix)) {
                                file = f;
                                logger.info("找到文件: " + f.getName());
                                break;
                            }
                        }

                        // 如果没找到，尝试精确匹配
                        if (file == null) {
                            for (File f : files) {
                                if (f.getName().equals(id)) {
                                    file = f;
                                    logger.info("精确匹配到文件: " + f.getName());
                                    break;
                                }
                            }
                        }

                        // 如果还是没找到，尝试模糊匹配（文件名包含 ID）
                        if (file == null) {
                            for (File f : files) {
                                if (f.getName().contains(id)) {
                                    file = f;
                                    logger.info("模糊匹配到文件: " + f.getName());
                                    break;
                                }
                            }
                        }

                        // 如果还是没找到，列出所有文件名供调试
                        if (file == null) {
                            StringBuilder sb = new StringBuilder("目录中的文件: ");
                            for (File f : files) {
                                sb.append(f.getName()).append(", ");
                            }
                            logger.info(sb.toString());
                        }
                    } else {
                        logger.warn("目录中没有文件");
                    }
                } else {
                    logger.warn("上传目录不存在或不是目录: " + uploadPath);
                }

                // 如果还是没找到，尝试其他可能的目录
                if (file == null) {
                    String[] possiblePaths = {
                            request.getSession().getServletContext().getRealPath("/uploads/files/") + id,
                            request.getSession().getServletContext().getRealPath("/uploads/images/") + id,
                            request.getSession().getServletContext().getRealPath("/uploads/excels/") + id
                    };

                    logger.info("尝试其他可能的目录");
                    for (String path : possiblePaths) {
                        logger.info("检查路径: " + path);
                        File tempFile = new File(path);
                        if (tempFile.exists()) {
                            file = tempFile;
                            logger.info("在其他目录中找到文件: " + path);
                            break;
                        }
                    }
                }
            } catch (Exception e) {
                logger.error("查找文件时发生异常", e);
            }

            // 检查文件是否存在
            if (file == null || !file.exists()) {
                // 如果文件不存在，返回404错误
                response.setStatus(HttpServletResponse.SC_NOT_FOUND);
                response.getWriter().write("File not found: " + id);
                return;
            }

            // 尝试提取原始文件名
            String fileName = file.getName();
            logger.info("下载文件名: " + fileName);

            // 如果文件名包含下划线，并且以文件ID开头，则提取原始文件名
            if (fileName.contains("_") && fileName.startsWith(id + "_")) {
                fileName = fileName.substring(fileName.indexOf("_") + 1);
                logger.info("提取后的原始文件名: " + fileName);
            }

            // 处理文件名中的特殊字符
            String encodedFileName = URLEncoder.encode(fileName, "UTF-8").replaceAll("\\+", "%20");

            // 添加：检测文件类型，判断是否为PDF
            boolean isPdf = fileName.toLowerCase().endsWith(".pdf");

            // 根据文件类型设置Content-Type
            if (isPdf) {
                // 对于PDF文件，设置application/pdf并使用inline而不是attachment
                response.setContentType("application/pdf");

                // 检查是否是预览请求（通过参数或请求头）
                String userAgent = request.getHeader("User-Agent");
                boolean isDownload = request.getParameter("download") != null;
                boolean isIframe = request.getParameter("iframe") != null && !isDownload;

                if (isIframe && (userAgent != null && userAgent.contains("Mozilla"))) {
                    // 用于iframe预览时使用inline
                    response.setHeader("Content-Disposition", "inline; filename*=UTF-8''" + encodedFileName);
                    logger.info("PDF预览模式: inline");
                } else {
                    // 用于下载时使用attachment
                    response.setHeader("Content-Disposition", "attachment; filename*=UTF-8''" + encodedFileName);
                    logger.info("PDF下载模式: attachment");
                }
            } else {
                // 其他文件类型保持原有行为
                response.setContentType("application/octet-stream");
                response.setHeader("Content-Disposition", "attachment; filename*=UTF-8''" + encodedFileName);
            }

            response.setContentLength((int) file.length());

            // 将文件写入响应输出流
            FileInputStream fis = new FileInputStream(file);
            OutputStream os = response.getOutputStream();
            byte[] buffer = new byte[4096];
            int bytesRead;
            while ((bytesRead = fis.read(buffer)) != -1) {
                os.write(buffer, 0, bytesRead);
            }
            fis.close();
            os.flush();
            os.close();

        } catch (IOException e) {
            logger.error("文件下载失败", e);
            try {
                response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
                response.getWriter().write("Download failed: " + e.getMessage());
            } catch (IOException ex) {
                logger.error("响应错误信息失败", ex);
            }
        }
    }
}