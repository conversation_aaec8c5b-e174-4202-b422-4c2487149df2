layui.use(['form', 'laydate', 'table', 'layer'], function(){
    var form = layui.form
        ,laydate = layui.laydate
        ,table = layui.table
        ,layer = layui.layer;

    // 页面加载时初始化下拉菜单数据
    loadTradeConditions();
    loadPayConditions();
    loadTransportConditions();

    // 初始化日期选择器
    laydate.render({
        elem: '#paymentRequestDate',
        format: 'yyyy-MM-dd',
        trigger: 'click',
        value: new Date() // 默认当前日期
    });

    // 初始化支付日期选择器
    laydate.render({
        elem: '#paymentDate',
        format: 'yyyy-MM-dd',
        trigger: 'click'
//        value: new Date() // 默认当前日期
    });

    // 初始化两行表头表格
    initializePaymentRequestTwoRowTable();

    // 移除延时方案，税额计算将在税率获取完成后的回调中进行

    // 确保原生下拉框不被Layui渲染
    ensureNativeSelectsNotRendered();

    // 使用事件委托处理产品代码变更事件，自动查询产品单价
    $(document).on('change blur', 'input[name="productCode"]', function() {
        var productCode = $(this).val();
        var $currentRow = $(this).closest('tr');

        if (productCode) {
            // 获取客户代码和到货日期
            var customerCode = $currentRow.find('input[name="customerCode"]').val();
            var arrivalDate = $currentRow.find('input[name="arrivalDate"]').val();

            console.log('支付请求登录产品代码变更，尝试查询产品单价:', {
                productCode: productCode,
                customerCode: customerCode,
                arrivalDate: arrivalDate
            });

            if (customerCode && arrivalDate) {
                queryProductPriceForPayment(customerCode, productCode, arrivalDate, $currentRow);
            } else {
                console.log('客户代码或到货日期为空，无法查询产品单价');
            }
        }
    });

    // 使用事件委托阻止明细表格输入框的回车键触发表单提交
    $(document).on('keydown', '#detailTable input', function(e) {
        if (e.keyCode === 13) { // 回车键
            e.preventDefault(); // 阻止默认行为
            e.stopPropagation(); // 阻止事件冒泡
            console.log('阻止了明细表格输入框的回车键提交');
            return false;
        }
    });

    // 使用事件委托处理到货日期变更事件，自动查询产品单价
    $(document).on('change', 'input[name="arrivalDate"]', function() {
        var arrivalDate = $(this).val();
        var $currentRow = $(this).closest('tr');

        if (arrivalDate) {
            // 获取客户代码和产品代码
            var customerCode = $currentRow.find('input[name="customerCode"]').val();
            var productCode = $currentRow.find('input[name="productCode"]').val();

            console.log('支付请求登录到货日期变更，尝试查询产品单价:', {
                arrivalDate: arrivalDate,
                customerCode: customerCode,
                productCode: productCode
            });

            if (customerCode && productCode) {
                queryProductPriceForPayment(customerCode, productCode, arrivalDate, $currentRow);
            } else {
                console.log('客户代码或产品代码为空，无法查询产品单价');
            }
        }
    });

    // 监听客户选择变化，自动加载销售额数据
    form.on('select(customerCodeFilter)', function(data){
        console.log('客户选择:', data.value);

        // 先清空税率字段
        $('#taxRate').val('');

        // 清空明细数据
        clearDetailTable();

        // 清空默认值设定
        $('#defaultCopperContractType').val('');
        $('#defaultCopperContractNo').empty().append('<option value="">请选择</option>');
        $('#defaultCopperCondition').empty().append('<option value="">请选择</option>');
        form.render('select');

        if (data.value) {
            // 获取客户详情信息（货币和税率）
            getCustomerInfo(data.value);

            // 自动加载该客户的销售额数据
            loadSalesDataForPaymentRequest(data.value);
        }
    });

    // 监听默认铜合同类别选择
    form.on('select(defaultCopperContractTypeFilter)', function(data){
        console.log('默认铜合同类别选择:', data.value);
        var customerCode = $('#customerCode').val();
        var paymentRequestDate = $('#paymentRequestDate').val();
        handlePaymentDefaultCopperContractTypeChange(data.value, customerCode, paymentRequestDate);
    });

    // 监听默认铜合同NO选择
    form.on('select(defaultCopperContractNoFilter)', function(data){
        console.log('默认铜合同NO选择:', data.value);
        handlePaymentDefaultCopperContractNoChange(data.value);
    });

    // 监听默认铜条件选择
    form.on('select(defaultCopperConditionFilter)', function(data){
        console.log('默认铜条件选择:', data.value);
        handlePaymentDefaultCopperConditionChange(data.value);
    });

    // 应用默认值到所有明细
    $('#applyDefaultValues').click(function(){
        applyPaymentDefaultValuesToAllDetails();
    });

    // 加载销售额数据函数
    function loadSalesDataForPaymentRequest(customerCode) {
        if (!customerCode) {
            layer.msg('请先选择需求方', {icon: 2});
            return;
        }

        console.log('加载客户销售额数据，客户代码:', customerCode);

        // 发送AJAX请求获取销售额数据
        $.ajax({
            url: baselocation + '/order/paymentRequestLogin/getSalesDataForPaymentRequest',
            type: 'POST',
            data: {customerCode: customerCode},
            success: function(result) {
                console.log('获取销售额数据结果:', result);
                if (result.code === 1) {
                    var salesData = result.data.salesData;
                    if (salesData && salesData.length > 0) {
                        renderDetailTable(salesData);
                        calculateTotalAmount();
                        layer.msg('已加载 ' + salesData.length + ' 条销售额数据', {icon: 1});
                    } else {
                        layer.msg('该客户暂无可用的销售额数据', {icon: 2});
                        clearDetailTable();
                    }
                } else {
                    layer.msg('获取销售额数据失败: ' + (result.message || '未知错误'), {icon: 2});
                }
            },
            error: function(xhr, status, error) {
                console.error('获取销售额数据失败:', error);
                layer.msg('获取销售额数据失败: ' + error, {icon: 2});
            }
        });
    }

    // 渲染明细表格
    function renderDetailTable(salesData) {
        // 清空现有数据
        clearDetailTable();

        salesData.forEach(function(item, index) {
            // 使用新的添加行函数
            addPaymentRequestDetailRow(item);

            // 存储原始数据
            window.salesDataArray = window.salesDataArray || [];
            window.salesDataArray[index] = item;
        });

        // 初始计算所有行的金额
        salesData.forEach(function(item, index) {
            calculateRowAmount(index);
        });
    }

    // 清空明细表格
    function clearDetailTable() {
        $('#detailTable tbody.record').remove();
        window.salesDataArray = [];
        updateTotalAmount(0);
    }

    // 计算行金额（适配新的两行表格结构）
    window.calculateRowAmount = function(recordElement) {
        var $record = $(recordElement).closest('tbody.record');

        var copperBase = parseFloat($record.find('input[name="copperBase"]').val()) || 0;
        var premium = parseFloat($record.find('input[name="premium"]').val()) || 0;
        var zeroBase = parseFloat($record.find('input[name="zeroBase"]').val()) || 0;
        var quantity = parseFloat($record.find('input[name="quantity"]').val()) || 0;

        // 计算换算后铜单价 = 铜base + 升水
        var convertedCopperPrice = copperBase + premium;
        $record.find('input[name="convertedCopperPrice"]').val(convertedCopperPrice.toFixed(2));

        // 计算支付单价 = 铜base + 升水 + 零基础
        var paymentUnitPrice = copperBase + premium + zeroBase;
        $record.find('input[name="paymentUnitPrice"]').val(paymentUnitPrice.toFixed(2));

        // 计算请求金额 = 支付单价 * 数量
        var requestAmount = paymentUnitPrice * quantity;
        $record.find('input[name="requestAmount"]').val(requestAmount.toFixed(2));

        // 如果是数量字段变更，进行数量验证
        if ($(recordElement).attr('name') === 'quantity') {
            validatePaymentRequestQuantitySplit($record);
        }

        // 重新计算总金额
        calculateTotalAmount();
    };

    // 计算总金额
    function calculateTotalAmount() {
        // 使用新的选中金额更新函数
        updateSelectedAmount();
    }

    // 更新总金额显示
    function updateTotalAmount(total) {
        $('#totalAmount').val('¥' + total.toFixed(2));

        // 计算税额 = 总金额 × 税率
        calculateTaxAmount(total);
    }

    // 计算税额的函数
    function calculateTaxAmount(totalAmount) {
        var taxRate = parseFloat($('#taxRate').val()) || 0;
        var taxAmount = totalAmount * (taxRate / 100); // 税率是百分比，需要除以100
        $('#taxAmount').val(taxAmount.toFixed(2));
        console.log('支付请求新增页面计算税额 - 总金额:', totalAmount, '税率:', taxRate + '%', '税额:', taxAmount.toFixed(2));
        console.log('支付请求新增页面税率字段值:', $('#taxRate').val(), '税率字段是否存在:', $('#taxRate').length);
    }

    // 获取表格数据（只返回选中的行）
    function getTableData() {
        var tableData = [];

        // 遍历所有表格记录，只处理选中的行
        $('#detailTable tbody.record').each(function(index) {
            var $record = $(this);

            // 检查该行是否被选中
            var $checkbox = $record.find('input[name="rowSelect"]');
            if (!$checkbox.is(':checked')) {
                console.log('跳过未选中的记录[' + index + ']');
                return; // 跳过未选中的行
            }

            // 调试：检查线盘字段元素
            var $wireDrumInput = $record.find('input[name="wireDrum"]');
            console.log('记录[' + index + '] 线盘字段元素数量:', $wireDrumInput.length);
            if ($wireDrumInput.length > 0) {
                console.log('记录[' + index + '] 线盘字段值:', $wireDrumInput.val());
            }

            // 从表格行中直接获取数据
            var item = {
                // 基础信息（第一行）
                salesAmountNo: $record.find('input[name="salesAmountNo"]').val() || '',  // 修复：获取销售额NO
                deliveryNoteSeq: $record.find('input[name="deliveryNoteSeq"]').val() || '',
                barcode: $record.find('input[name="barcode"]').val() || '',
                productCode: $record.find('input[name="productCode"]').val() || '',
                wireDrum: $record.find('input[name="wireDrum"]').val() || '',
                customerCode: $record.find('input[name="customerCode"]').val() || '',
                shipmentDate: $record.find('input[name="shipmentDate"]').val() || '',
                arrivalDate: $record.find('input[name="arrivalDate"]').val() || '',
                customerOrderNo: $record.find('input[name="customerOrderNo"]').val() || '',
                detailRemark: $record.find('input[name="detailRemark"]').val() || '',
                copperContractType: $record.find('select[name="copperContractType"]').val() || '',
                copperContractNo: $record.find('input[name="copperContractNo"], select[name="copperContractNo"]').val() || '',
                copperCondition: $record.find('input[name="copperCondition"], select[name="copperCondition"]').val() || '',

                // 计算信息（第二行）
                currency: $record.find('input[name="currency"]').val() || '',
                conversionRate: parseFloat($record.find('input[name="conversionRate"]').val()) || 0,
                quantity: parseFloat($record.find('input[name="quantity"]').val()) || 0,
                copperBase: parseFloat($record.find('input[name="copperBase"]').val()) || 0,
                premium: parseFloat($record.find('input[name="premium"]').val()) || 0,
                convertedCopperPrice: parseFloat($record.find('input[name="convertedCopperPrice"]').val()) || 0,
                zeroBase: parseFloat($record.find('input[name="zeroBase"]').val()) || 0,
                paymentUnitPrice: parseFloat($record.find('input[name="paymentUnitPrice"]').val()) || 0,
                requestAmount: parseFloat($record.find('input[name="requestAmount"]').val()) || 0,
                productMiddleCategory: $record.find('input[name="productMiddleCategory"]').val() || '',
                shipmentPlanNo: $record.find('input[name="shipmentPlanNo"]').val() || '',

                // 从原始数据获取的字段
                salesAmountNo: window.salesDataArray && window.salesDataArray[index] ? window.salesDataArray[index].salesAmountNo : '',
                salesAmountSeq: window.salesDataArray && window.salesDataArray[index] ? window.salesDataArray[index].salesAmountSeq : ''
            };

            tableData.push(item);
        });

        return tableData;
    }

    // 监听表单提交
    form.on('submit(formDemo)', function(data) {
        // 表单数据
        var formData = data.field;

        // 验证需求方
        if (!formData.customerCode) {
            layer.msg('需求方不能为空', {icon: 2});
            return false;
        }

        // 验证支付请求日
        if (!formData.paymentRequestDate) {
            layer.msg('支付请求日不能为空', {icon: 2});
            return false;
        }

        // 验证支付日
        if (!formData.paymentDate) {
            layer.msg('支付日不能为空', {icon: 2});
            return false;
        }

        // 获取表格数据
        var tableData = getTableData();

        // 验证明细
        if (!tableData || tableData.length === 0) {
            layer.msg('请先加载销售额数据', {icon: 2});
            return false;
        }
        
        // 添加客户相关条件字段
        formData.tradeCondition = $('#tradeCondition').val() || '';
        formData.payCondition = $('#payCondition').val() || '';
        formData.transportCondition = $('#transportCondition').val() || '';

        // 使用保存的税代码，而不是税率值
        formData.taxCode = $('#taxCode').val() || formData.taxRate; // 优先使用税代码，如果没有则使用税率值作为兼容

        console.log('客户条件字段 - 交易条件:', formData.tradeCondition, '付款条件:', formData.payCondition, '运输条件:', formData.transportCondition, '税代码:', formData.taxCode);

        // 添加明细数据到表单
        formData.details = JSON.stringify(tableData);

        console.log('提交的表单数据:', formData);
        console.log('明细数据详情:', tableData);

        // 检查线盘字段
        tableData.forEach(function(item, index) {
            console.log('明细[' + index + '] 线盘字段值:', item.wireDrum);
        });
        
        // 发送AJAX请求
        $.ajax({
            url: baselocation + '/order/paymentRequestLogin/save',
            type: 'POST',
            data: formData,
            success: function(result) {
                console.log('保存结果:', result);
                if (result.code === 1) {
                    layer.msg('保存成功', {icon: 1, time: 1000}, function(){
                        closeLayer(); // 关闭窗口
                    });
                } else {
                    layer.msg(result.message || '保存失败', {icon: 2});
                }
            },
            error: function(xhr, status, error) {
                console.error('保存失败:', error);
                layer.msg('保存失败: ' + error, {icon: 2});
            }
        });
        
        return false; // 阻止表单默认提交
    });
});

// 确保原生下拉框不被Layui渲染
function ensureNativeSelectsNotRendered() {
    // 定期检查并移除表格中Layui渲染的下拉框
    setInterval(function() {
        $('.layui-table select.native-select').each(function() {
            var $select = $(this);
            var $layuiSelect = $select.next('.layui-form-select');
            if ($layuiSelect.length > 0) {
                $layuiSelect.remove();
                console.log('移除了表格中Layui渲染的下拉框');
            }
            // 确保原生select可见
            $select.show();
        });
    }, 500);
}

// 禁用铜合同字段
function disableCopperContractField($input) {
    if ($input.length === 0) return;

    // 如果是下拉框，禁用它
    if ($input[0].tagName === 'SELECT') {
        $input.prop('disabled', true).empty().append('<option value="">不适用</option>');
    } else {
        // 如果是输入框，设为禁用状态
        $input.prop('disabled', true).val('不适用');
    }
}

function closeLayer() {
    var index = parent.layer.getFrameIndex(window.name);
    parent.layer.close(index);
    parent.location.reload();
}

// 处理铜合同类别变更逻辑
function handleCopperContractTypeChange(copperContractType, $currentRow, customerCode, arrivalDate) {
    console.log('处理铜合同类别变更:', copperContractType, customerCode, arrivalDate);

    // 获取铜合同NO字段（可能是input或select）
    var $copperContractNoInput = $currentRow.find('input[name="copperContractNo"]');
    var $copperContractNoSelect = $currentRow.find('select[name="copperContractNo"]');
    var $copperConditionInput = $currentRow.find('input[name="copperCondition"]');
    var $copperConditionSelect = $currentRow.find('select[name="copperCondition"]');

    console.log('找到的铜合同NO输入框数量:', $copperContractNoInput.length);
    console.log('找到的铜合同NO下拉框数量:', $copperContractNoSelect.length);
    console.log('找到的铜条件输入框数量:', $copperConditionInput.length);
    console.log('找到的铜条件下拉框数量:', $copperConditionSelect.length);

    // 获取铜货币字段（在第二行）
    var $tbody = $currentRow.closest('tbody.record');
    var $secondRow = $tbody.find('tr:last-child');
    var $currencyInput = $secondRow.find('input[name="currency"]');

    if (copperContractType === '1' || copperContractType === '2') {
        // 预约铜/支给铜：启用铜合同NO选择，铜条件为禁用输入框
        console.log('预约铜/支给铜模式');

        // 清空铜合同和铜条件字段
        if ($copperContractNoInput.length > 0) {
            $copperContractNoInput.val('');
        }
        if ($copperContractNoSelect.length > 0) {
            $copperContractNoSelect.val('');
        }

        // 如果铜条件是下拉框，需要先转换回输入框
        if ($copperConditionSelect.length > 0) {
            var $newConditionInput = $('<input type="text" name="copperCondition" class="layui-input">');
            $copperConditionSelect.replaceWith($newConditionInput);
            $copperConditionInput = $newConditionInput;
        }

        // 铜条件输入框禁用，等待从合同中获取
        $copperConditionInput.prop('disabled', true).val('').attr('placeholder', '选择铜合同后自动填入');

        // 清空货币
        $currencyInput.val('');

        // 如果铜合同NO是输入框，转换为下拉框
        if ($copperContractNoInput.length > 0) {
            createCopperContractSelectInPlace($copperContractNoInput, customerCode, copperContractType, arrivalDate);
        } else if ($copperContractNoSelect.length > 0) {
            // 如果已经是下拉框，重新加载数据
            loadCopperContractListForSelect($copperContractNoSelect, customerCode, copperContractType, arrivalDate);
        }

    } else if (copperContractType === '3' || copperContractType === '4') {
        // 一般铜/无偿：禁用铜合同NO，启用铜条件选择
        console.log('一般铜/无偿模式');

        // 清空铜合同和铜条件字段
        if ($copperContractNoInput.length > 0) {
            $copperContractNoInput.val('');
        }
        if ($copperContractNoSelect.length > 0) {
            $copperContractNoSelect.val('');
        }
        $copperConditionInput.val('');
        $currencyInput.val('');

        // 禁用铜合同NO字段
        disableCopperContractField($copperContractNoSelect.length > 0 ? $copperContractNoSelect : $copperContractNoInput);

        if (copperContractType === '3') {
            // 一般铜：先创建铜条件下拉框，然后查询升水表获取默认值
            console.log('支付请求新增：一般铜模式，查询升水表');

            // 铜条件字段启用，并加载铜条件表数据
            $copperConditionInput.prop('disabled', false).attr('placeholder', '请选择铜条件');
            createCopperConditionSelectInPlace($copperConditionInput);

            // 查询升水表数据设置默认值
            queryAscendingWaterForGeneralCopperPayment($currentRow, customerCode, arrivalDate);
        } else {
            // 无偿：铜条件字段启用，并加载铜条件表数据
            $copperConditionInput.prop('disabled', false).attr('placeholder', '请选择铜条件');
            createCopperConditionSelectInPlace($copperConditionInput);
        }
    } else {
        // 其他情况：禁用所有相关字段
        console.log('其他情况，禁用所有字段');

        // 将所有字段转换为禁用的输入框
        if ($copperContractNoSelect.length > 0) {
            var $newContractInput = $('<input type="text" name="copperContractNo" class="layui-input" readonly>');
            $copperContractNoSelect.remove(); // 先移除旧元素
            $copperContractNoSelect.parent().append($newContractInput); // 添加新元素
        } else if ($copperContractNoInput.length > 0) {
            $copperContractNoInput.prop('readonly', true).val('');
        }

        if ($copperConditionSelect.length > 0) {
            var $newConditionInput = $('<input type="text" name="copperCondition" class="layui-input" readonly>');
            $copperConditionSelect.remove(); // 先移除旧元素
            $copperConditionSelect.parent().append($newConditionInput); // 添加新元素
        } else if ($copperConditionInput.length > 0) {
            $copperConditionInput.prop('readonly', true).val('');
        }

        $currencyInput.val('');
    }
}

// 将铜合同NO输入框替换为下拉框
function replaceCopperContractInputWithSelect($input, customerCode, copperContractType, arrivalDate) {
    var currentValue = $input.val();
    var inputName = $input.attr('name');

    // 创建下拉框（使用原生样式，不被Layui渲染）
    var $select = $('<select name="' + inputName + '" class="native-select"></select>');
    $select.append('<option value="">请选择</option>');

    // 替换输入框
    $input.replaceWith($select);

    // 加载铜合同列表
    loadCopperContractList(customerCode, copperContractType, arrivalDate, $select, currentValue);
}

// 将铜条件输入框替换为下拉框
function replaceCopperConditionInputWithSelect($input) {
    var currentValue = $input.val();
    var inputName = $input.attr('name');

    // 创建下拉框
    var $select = $('<select name="' + inputName + '" class="layui-input"></select>');
    $select.append('<option value="">请选择</option>');

    // 替换输入框
    $input.replaceWith($select);

    // 加载铜条件列表
    loadCopperConditionList($select, currentValue);
}

// 加载铜合同列表
function loadCopperContractList(customerCode, copperContractType, arrivalDate, $select, currentValue) {
    if (!customerCode || !copperContractType || !arrivalDate) {
        $select.empty().append('<option value="">参数不完整</option>');
        return;
    }

    // 从到货日期提取年月
    var useMonth = arrivalDate.substring(0, 7);

    $.ajax({
        url: baselocation + '/order/orderEntry/getCopperContractList',
        type: 'POST',
        data: {
            customerCode: customerCode,
            copperContractType: copperContractType,
            useMonth: useMonth
        },
        success: function(result) {
            console.log('铜合同列表返回:', result);

            if (result.code === 1 && result.data) {
                // 清空并重新填充选项
                $select.empty().append('<option value="">请选择</option>');

                $.each(result.data, function(index, item) {
                    var contractNo = item.copperSignNo || '';
                    var copperCondition = item.copperCondition || '';
                    var currency = item.currency || '';

                    if (contractNo) {
                        var selected = (contractNo === currentValue) ? 'selected' : '';
                        var optionHtml = '<option value="' + contractNo + '" ' + selected +
                                       ' data-copper-condition="' + copperCondition + '"' +
                                       ' data-currency="' + currency + '">' +
                                       contractNo + '</option>';
                        $select.append(optionHtml);
                    }
                });

                // 绑定选择事件
                $select.off('change').on('change', function() {
                    var selectedOption = $(this).find('option:selected');
                    var copperCondition = selectedOption.data('copper-condition');
                    var currency = selectedOption.data('currency');
                    var copperContractNo = selectedOption.val();
                    var $copperConditionInput = $(this).closest('tr').find('input[name="copperCondition"]');

                    // 填入铜条件
                    $copperConditionInput.val(copperCondition || '');

                    // 获取铜货币字段（在第二行）
                    var $currentRow = $(this).closest('tr');
                    var $tbody = $currentRow.closest('tbody.record');
                    var $currencyInput = $tbody.find('input[name="currency"]');
                    $currencyInput.val(currency || '');

                    console.log('选择了铜合同:', copperContractNo, '铜条件:', copperCondition, '货币:', currency);

                    // 查询铜合同详情获取铜base（仅限预约铜/支给铜）
                    if (copperContractNo) {
                        var $copperContractTypeSelect = $currentRow.find('select[name="copperContractType"]');
                        var copperContractType = $copperContractTypeSelect.val();

                        // 只有预约铜(1)或支给铜(2)才查询铜合同表获取铜base
                        if (copperContractType === '1' || copperContractType === '2') {
                            var arrivalDate = $currentRow.find('input[name="arrivalDate"]').val();
                            if (arrivalDate) {
                                var useMonth = arrivalDate.substring(0, 7); // 截取年月部分
                                queryCopperBaseForContractCopperPayment($tbody, copperContractNo, useMonth);
                            }
                        }
                    }
                });

            } else {
                $select.empty().append('<option value="">无可用合同</option>');
            }
        },
        error: function(xhr, status, error) {
            $select.empty().append('<option value="">加载失败</option>');
        }
    });
}

// 加载铜条件列表
function loadCopperConditionList($select, currentValue) {
    $.ajax({
        url: baselocation + '/salesCommon/copperConditions/list',
        type: 'POST',
        success: function(result) {
            console.log('铜条件列表返回:', result);

            if (result.code === 1 && result.data) {
                // 清空并重新填充选项
                $select.empty().append('<option value="">请选择</option>');

                $.each(result.data, function(index, item) {
                    var copperCondition = item.copperCondition || '';
                    var currency = item.currency || '';

                    if (copperCondition) {
                        var selected = (copperCondition === currentValue) ? 'selected' : '';
                        var optionHtml = '<option value="' + copperCondition + '" ' + selected +
                                       ' data-currency="' + currency + '">' +
                                       copperCondition + '</option>';
                        $select.append(optionHtml);
                    }
                });

                // 绑定选择事件
                $select.off('change').on('change', function() {
                    var selectedOption = $(this).find('option:selected');
                    var currency = selectedOption.data('currency');
                    var copperCondition = $(this).val();

                    // 获取铜货币字段（在第二行）
                    var $currentRow = $(this).closest('tr');
                    var $tbody = $currentRow.closest('tbody.record');
                    var $currencyInput = $tbody.find('input[name="currency"]');

                    if (currency) {
                        // 直接设置货币
                        $currencyInput.val(currency);
                        console.log('直接设置铜条件货币:', currency);
                    } else if (copperCondition) {
                        // 如果没有货币信息，通过接口获取
                        getCopperConditionDetail(copperCondition, $(this));
                    } else {
                        // 清空货币
                        $currencyInput.val('');
                    }

                    // 根据铜合同类别决定是否查询铜base
                    var $copperContractTypeSelect = $currentRow.find('select[name="copperContractType"]');
                    var copperContractType = $copperContractTypeSelect.val();

                    if (copperContractType === '3' || copperContractType === '4') {
                        // 一般铜/无偿：查询铜价表获取铜base
                        var arrivalDate = $currentRow.find('input[name="arrivalDate"]').val();
                        if (copperCondition && arrivalDate) {
                            queryCopperBaseForGeneralCopperPayment($tbody, copperCondition, arrivalDate);
                        }
                    }
                });

            } else {
                $select.empty().append('<option value="">无可用铜条件</option>');
            }
        },
        error: function(xhr, status, error) {
            $select.empty().append('<option value="">加载失败</option>');
        }
    });
}

// 获取铜条件详情（包括货币）
function getCopperConditionDetail(copperCondition, $select) {
    $.ajax({
        url: baselocation + '/salesCommon/copperConditions/json',
        type: 'POST',
        data: {
            copperCondition: copperCondition
        },
        success: function(result) {
            console.log('铜条件详情返回:', result);

            if (result.code === 1 && result.data) {
                var currency = result.data.currency || '';

                // 设置货币字段
                var $currentRow = $select.closest('tr');
                var $tbody = $currentRow.closest('tbody.record');
                var $currencyInput = $tbody.find('input[name="currency"]');
                $currencyInput.val(currency);

                console.log('设置铜条件货币:', currency);
            } else {
                console.log('获取铜条件详情失败:', result.message);
            }
        },
        error: function(xhr, status, error) {
            console.log('获取铜条件详情异常:', error);
        }
    });
}

// 初始化支付请求登录的两行表头表格
function initializePaymentRequestTwoRowTable() {
    // 添加样式
    if (!$('#paymentRequestTwoRowStyle').length) {
        var style = $('<style id="paymentRequestTwoRowStyle">');
        style.text(
            '#detailTable {' +
                'width: 100%;' +
                'border-collapse: collapse;' +
                'margin-top: 10px;' +
            '}' +
            '#detailTable th, #detailTable td {' +
                'border: 1px solid #e6e6e6;' +
                'padding: 8px 6px;' +
                'text-align: center;' +
                'word-break: break-all;' +
                'font-size: 12px;' +
                'line-height: 1.3;' +
                'vertical-align: middle;' +
            '}' +
            '#detailTable thead th {' +
                'font-weight: bold;' +
                'white-space: nowrap;' +
            '}' +
            '/* 第一行表头 - 基础信息 */' +
            '#detailTable thead tr:first-child th {' +
                'background-color: #e6f7ff;' +
                'color: #1890ff;' +
                'border-bottom: 1px solid #1890ff;' +
            '}' +
            '/* 第二行表头 - 计算信息 */' +
            '#detailTable thead tr:last-child th {' +
                'background-color: #fff2e8;' +
                'color: #fa8c16;' +
                'border-bottom: 1px solid #fa8c16;' +
            '}' +
            '/* 每条记录用两个 <tr>，不同记录间用背景色区分 */' +
            '#detailTable tbody.record:nth-of-type(odd) td {' +
                'background: #f8f8f8;' +
            '}' +
            '#detailTable tbody.record:nth-of-type(even) td {' +
                'background: #fff;' +
            '}' +
            '/* 让第二行数据和第一行数据视觉上依然"连成一体" */' +
            '#detailTable tbody.record tr:first-child td {' +
                'border-bottom: none;' +
                'border-left: 3px solid #1890ff;' +
            '}' +
            '#detailTable tbody.record tr:last-child td {' +
                'border-top: none;' +
                'border-left: 3px solid #fa8c16;' +
                'border-bottom: 2px solid #d2d2d2;' +
            '}' +
            '.table-container {' +
                'overflow-x: auto;' +
                'max-height: 400px;' +
                'overflow-y: auto;' +
            '}' +
            '/* 输入框和下拉框样式 */' +
            '#detailTable input, #detailTable select {' +
                'width: 100%;' +
                'border: 1px solid #d9d9d9;' +
                'background: #fff;' +
                'padding: 4px;' +
                'font-size: 12px;' +
                'height: 28px;' +
                'box-sizing: border-box;' +
                'display: block;' +
                'position: relative;' +
                'z-index: 1;' +
            '}' +
            '#detailTable input:focus, #detailTable select:focus {' +
                'outline: 1px solid #1890ff;' +
                'background: #fff;' +
                'border-color: #1890ff;' +
            '}' +
            '/* 确保下拉框可见 */' +
            '#detailTable select {' +
                'appearance: auto;' +
                '-webkit-appearance: menulist;' +
                '-moz-appearance: menulist;' +
                'cursor: pointer;' +
            '}' +
            '/* 表格单元格样式调整 */' +
            '#detailTable td {' +
                'position: relative;' +
                'overflow: visible;' +
            '}'
        );
        $('head').append(style);
    }

    // 生成初始的空表格HTML
    var html = '<div class="table-container"><table id="detailTable">';

    // 生成表头 - 两行，每行14列（增加复选框列）
    html += '<thead>';
    // 第一行表头 - 基础信息标题
    html += '<tr>';
    html += '<th><input type="checkbox" id="selectAll" onchange="toggleSelectAll()"></th>';
    html += '<th>销售额NO</th><th>序号</th><th>条码</th><th>产品代码</th>';
    html += '<th>线盘</th><th>客户代码</th><th>出货日</th><th>到货日期</th><th>客户订单No.</th>';
    html += '<th>明细内部备注</th><th>铜合同类别</th><th>铜合同NO</th><th>铜条件</th>';
    html += '</tr>';
    // 第二行表头 - 计算信息标题
    html += '<tr>';
    html += '<th></th>'; // 复选框列对应的空列
    html += '<th>铜货币</th><th>换算率</th><th>数量(KG)</th><th>铜base</th>';
    html += '<th>升水</th><th>换算后铜单价</th><th>0base</th><th>支付单价</th>';
    html += '<th>请求金额</th><th>产品中分类</th><th>出货计划番号</th><th>操作</th><th></th>';
    html += '</tr>';
    html += '</thead>';

    html += '</table></div>';

    // 替换原来的表格
    var $container = $('#detailTable').parent();
    console.log('表格容器:', $container.length, $container.html());

    // 保留新增明细按钮，只替换表格部分
    var $addBtn = $container.find('#addDetailByShipmentBtn').parent();
    $container.html('');
    if ($addBtn.length > 0) {
        $container.append($addBtn);
    } else {
        $container.append('<div style="margin-bottom: 10px;"><button type="button" class="layui-btn layui-btn-normal" id="addDetailByShipmentBtn"><i class="layui-icon layui-icon-add-circle"></i> 新增明细</button></div>');
    }
    $container.append(html);

    console.log('已初始化支付请求登录的两行表格，容器内容:', $container.html());

    // 绑定新增明细按钮事件（根据出货单No.自动填充）
    $('#addDetailByShipmentBtn').off('click').on('click', function(){
        // 弹出输入框让用户输入出货单No.
        layer.prompt({
            title: '请输入出货单No.',
            formType: 0, // 输入框类型
            value: '', // 默认值
            maxlength: 50 // 最大长度
        }, function(shipmentNo, index){
            if (shipmentNo && shipmentNo.trim()) {
                // 根据出货单No.获取明细数据
                getPaymentRequestDetailsByShipmentNo(shipmentNo.trim());
                layer.close(index);
            } else {
                layer.msg('请输入有效的出货单No.', {icon: 2});
            }
        });
    });
}

// 添加支付请求明细行到两行表格
function addPaymentRequestDetailRow(item) {
    var tableBody = $('#detailTable');

    // 处理数据
    var processedItem = {
        salesAmountNo: item.salesAmountNo || '',  // 修复：使用销售额NO而不是送货单号
        deliveryNoteSeq: item.deliveryNoteSeq || item.salesAmountSeq || '',
        barcode: item.barcode || '',
        productCode: item.productCode || '',
        reelName: item.reelName || '',
        customerCode: item.customerCode || '',
        shipmentDate: item.shipmentDate || '',
        arrivalDate: item.arrivalDate || '',
        customerOrderNo: item.customerOrderNo || '',
        detailRemark: item.detailRemark || '',
        copperContractType: item.copperContractType || '',
        copperContractNo: item.copperContractNo || '',
        copperCondition: item.copperCondition || '',
        currency: item.currency || '',
        conversionRate: item.conversionRate || '',
        quantity: item.quantity || '',
        copperBase: item.copperBase || '',
        premium: item.premium || '',
        convertedCopperPrice: item.convertedCopperPrice || '',
        zeroBase: item.zeroBase || '',
        salesUnitPrice: item.paymentUnitPrice || item.paymentPrice || '',
        salesAmount: item.requestAmount || '',
        productMiddleCategory: item.productMiddleCategory || '',
        shipmentPlanNo: item.shipmentPlanNo || '',
        originalQuantity: item.originalQuantity || item.quantity || '' // 原始出库数量
    };

    // 生成唯一ID
    var recordId = 'record_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);

    // 生成铜合同类别下拉框（使用原生样式，不被Layui渲染）
    var copperTypeSelect = '<select name="copperContractType" class="native-select">' +
        '<option value="">请选择</option>' +
        '<option value="1"' + (processedItem.copperContractType === '1' ? ' selected' : '') + '>预约铜</option>' +
        '<option value="2"' + (processedItem.copperContractType === '2' ? ' selected' : '') + '>支给铜</option>' +
        '<option value="3"' + (processedItem.copperContractType === '3' ? ' selected' : '') + '>一般铜</option>' +
        '<option value="4"' + (processedItem.copperContractType === '4' ? ' selected' : '') + '>无偿</option>' +
        '</select>';

    // 第一行：基础信息（14列，增加复选框列）
    var firstRowHtml = '<tbody class="record" data-record-id="' + recordId + '">';
    firstRowHtml += '<tr>';
    firstRowHtml += '<td><input type="checkbox" name="rowSelect" class="row-checkbox" data-sales-no="' + (processedItem.salesAmountNo || '') + '" data-detail-seq="' + (processedItem.salesAmountSeq || '') + '" onchange="handleRowCheckboxChange(this)" checked></td>';
    firstRowHtml += '<td><input type="text" name="salesAmountNo" value="' + processedItem.salesAmountNo + '" readonly></td>';  // 修复：显示销售额NO并设为只读
    firstRowHtml += '<td><input type="text" name="deliveryNoteSeq" value="' + processedItem.deliveryNoteSeq + '"></td>';
    firstRowHtml += '<td><input type="text" name="productCode" value="' + processedItem.productCode + '"></td>';
    firstRowHtml += '<td><input type="text" name="wireDrum" value="' + (processedItem.reelName || '') + '"></td>';
    firstRowHtml += '<td><input type="text" name="customerCode" value="' + (processedItem.customerCode || '') + '"></td>';
    firstRowHtml += '<td><input type="date" name="shipmentDate" value="' + processedItem.shipmentDate + '"></td>';
    firstRowHtml += '<td><input type="date" name="arrivalDate" value="' + processedItem.arrivalDate + '"></td>';
    firstRowHtml += '<td><input type="text" name="customerOrderNo" value="' + processedItem.customerOrderNo + '"></td>';
    firstRowHtml += '<td><input type="text" name="detailRemark" value="' + processedItem.detailRemark + '"></td>';
    firstRowHtml += '<td>' + copperTypeSelect + '</td>';
    firstRowHtml += '<td><input type="text" name="copperContractNo" value="' + processedItem.copperContractNo + '"></td>';
    firstRowHtml += '<td><input type="text" name="copperCondition" value="' + processedItem.copperCondition + '"></td>';
    firstRowHtml += '</tr>';

    // 第二行：计算信息（14列，第一列为空对应复选框列）
    firstRowHtml += '<tr>';
    firstRowHtml += '<td></td>'; // 复选框列对应的空列
    firstRowHtml += '<td><input type="text" name="currency" value="' + (processedItem.currency || '') + '" readonly></td>';
    firstRowHtml += '<td><input type="number" step="0.000001" name="conversionRate" value="' + processedItem.conversionRate + '"></td>';
    firstRowHtml += '<td><input type="number" step="0.001" name="quantity" value="' + processedItem.quantity + '" onchange="calculateRowAmount(this)"></td>';
    firstRowHtml += '<td><input type="number" step="0.01" name="copperBase" value="' + processedItem.copperBase + '" onchange="calculateRowAmount(this)"></td>';
    firstRowHtml += '<td><input type="number" step="0.01" name="premium" value="' + processedItem.premium + '" onchange="calculateRowAmount(this)"></td>';
    firstRowHtml += '<td><input type="text" name="convertedCopperPrice" value="' + processedItem.convertedCopperPrice + '" readonly></td>';
    firstRowHtml += '<td><input type="number" step="0.01" name="zeroBase" value="' + processedItem.zeroBase + '" onchange="calculateRowAmount(this)"></td>';
    firstRowHtml += '<td><input type="text" name="paymentUnitPrice" value="' + processedItem.salesUnitPrice + '" readonly></td>';
    firstRowHtml += '<td><input type="text" name="requestAmount" value="' + processedItem.salesAmount + '" readonly></td>';
    firstRowHtml += '<td><input type="text" name="productMiddleCategory" value="' + processedItem.productMiddleCategory + '"></td>';
    firstRowHtml += '<td><input type="text" name="shipmentPlanNo" value="' + (processedItem.shipmentPlanNo || '') + '"></td>';
    firstRowHtml += '<td><button type="button" class="layui-btn layui-btn-xs layui-btn-danger" onclick="removePaymentRequestDetailRow(\'' + recordId + '\')">删除</button></td>';
    firstRowHtml += '<td></td>'; // 空列，与销售额登录保持一致
    firstRowHtml += '</tr>';
    firstRowHtml += '</tbody>';
    // 添加隐藏字段保存原始出库数量
    firstRowHtml += '<input type="hidden" name="originalQuantity" value="' + processedItem.originalQuantity + '">';

    // 添加到表格
    tableBody.append(firstRowHtml);

    // 如果有铜条件但没有货币，通过接口获取货币信息
    if (processedItem.copperCondition && !processedItem.currency) {
        setTimeout(function() {
            var $newRow = tableBody.find('tbody[data-record-id="' + recordId + '"]');
            var $copperConditionInput = $newRow.find('input[name="copperCondition"]');
            getCopperConditionDetail(processedItem.copperCondition, $copperConditionInput);
        }, 100);
    }

    // 绑定铜合同类别变更事件
    var $newRow = tableBody.find('tbody[data-record-id="' + recordId + '"]');
    var $copperTypeSelect = $newRow.find('select[name="copperContractType"]');
    $copperTypeSelect.on('change', function() {
        var copperContractType = $(this).val();
        var $currentRow = $(this).closest('tr');
        var customerCode = $currentRow.find('input[name="customerCode"]').val(); // 使用正确的客户代码
        var arrivalDate = $currentRow.find('input[name="arrivalDate"]').val();

        handleCopperContractTypeChange(copperContractType, $currentRow, customerCode, arrivalDate);
    });

    console.log('已添加支付请求明细行，记录ID:', recordId);
}

// 删除支付请求明细行
function removePaymentRequestDetailRow(recordId) {
    $('#detailTable tbody[data-record-id="' + recordId + '"]').remove();
    console.log('已删除支付请求明细行，记录ID:', recordId);
}

// 直接在原地创建铜合同下拉框并加载数据
function createCopperContractSelectInPlace($input, customerCode, copperContractType, arrivalDate) {
    console.log('=== 在原地创建铜合同下拉框 ===');

    var currentValue = $input.val();
    var inputName = $input.attr('name');

    // 创建下拉框（使用原生样式，不被Layui渲染）
    var $select = $('<select name="' + inputName + '" class="native-select"></select>');
    $select.append('<option value="">加载中...</option>');

    // 替换输入框
    var $parent = $input.parent();
    $input.remove();
    $parent.append($select);

    console.log('已替换为下拉框，开始加载数据...');

    // 检查必要参数
    var missingParams = [];
    if (!customerCode) missingParams.push('客户代码');
    if (!copperContractType) missingParams.push('铜合同类别');
    if (!arrivalDate) missingParams.push('到货日期');

    if (missingParams.length > 0) {
        $select.empty().append('<option value="">缺少参数: ' + missingParams.join(', ') + '</option>');
        console.log('缺少必要参数:', missingParams);
        return;
    }

    // 从到货日期提取年月
    var useMonth = arrivalDate.substring(0, 7);

    $.ajax({
        url: baselocation + '/order/orderEntry/getCopperContractList',
        type: 'POST',
        data: {
            customerCode: customerCode,
            copperContractType: copperContractType,
            useMonth: useMonth
        },
        success: function(result) {
            console.log('=== 铜合同列表AJAX返回 ===');
            console.log('返回结果:', result);

            if (result.code === 1 && result.data) {
                console.log('开始填充下拉框选项，数据数量:', result.data.length);

                // 清空并重新填充选项
                $select.empty().append('<option value="">请选择</option>');

                $.each(result.data, function(index, item) {
                    // 处理两种数据格式：字符串数组或对象数组
                    var contractNo = '';
                    var copperCondition = '';
                    var currency = '';

                    if (typeof item === 'string') {
                        // 如果是字符串数组
                        contractNo = item;
                    } else if (typeof item === 'object' && item !== null) {
                        // 如果是对象数组
                        contractNo = item.copperSignNo || '';
                        copperCondition = item.copperCondition || '';
                        currency = item.currency || '';
                    }

                    if (contractNo) {
                        var selected = (contractNo === currentValue) ? 'selected' : '';
                        var optionHtml = '<option value="' + contractNo + '" ' + selected +
                                       ' data-copper-condition="' + copperCondition + '"' +
                                       ' data-currency="' + currency + '">' +
                                       contractNo + '</option>';
                        $select.append(optionHtml);
                        console.log('添加选项:', contractNo, '铜条件:', copperCondition, '货币:', currency);
                    }
                });

                console.log('下拉框填充完成，总选项数:', $select.find('option').length);

                // 绑定选择事件
                $select.off('change').on('change', function() {
                    var copperContractNo = $(this).val();

                    if (copperContractNo) {
                        // 通过API获取铜合同详情
                        getCopperContractDetail(copperContractNo, $(this));

                        // 查询铜合同详情获取铜base（仅限预约铜/支给铜）
                        var $currentRow = $(this).closest('tr');
                        var $copperContractTypeSelect = $currentRow.find('select[name="copperContractType"]');
                        var copperContractType = $copperContractTypeSelect.val();

                        // 只有预约铜(1)或支给铜(2)才查询铜合同表获取铜base
                        if (copperContractType === '1' || copperContractType === '2') {
                            var arrivalDate = $currentRow.find('input[name="arrivalDate"]').val();
                            if (arrivalDate) {
                                var useMonth = arrivalDate.substring(0, 7); // 截取年月部分
                                var $tbody = $currentRow.closest('tbody.record');
                                queryCopperBaseForContractCopperPayment($tbody, copperContractNo, useMonth);
                            }
                        }
                    } else {
                        // 清空相关字段
                        var $currentRow = $(this).closest('tr');
                        var $copperConditionInput = $currentRow.find('input[name="copperCondition"]');
                        var $tbody = $currentRow.closest('tbody.record');
                        var $currencyInput = $tbody.find('input[name="currency"]');

                        $copperConditionInput.val('');
                        $currencyInput.val('');
                    }
                });

            } else {
                $select.empty().append('<option value="">无可用合同</option>');
                console.log('获取铜合同列表失败:', result.message);
            }
        },
        error: function(xhr, status, error) {
            console.log('获取铜合同列表异常:', error);
            $select.empty().append('<option value="">加载失败</option>');
        }
    });
}

// 为已存在的下拉框重新加载铜合同列表
function loadCopperContractListForSelect($select, customerCode, copperContractType, arrivalDate) {
    console.log('=== 为已存在的下拉框重新加载铜合同列表 ===');

    // 检查必要参数
    var missingParams = [];
    if (!customerCode) missingParams.push('客户代码');
    if (!copperContractType) missingParams.push('铜合同类别');
    if (!arrivalDate) missingParams.push('到货日期');

    if (missingParams.length > 0) {
        $select.empty().append('<option value="">缺少参数: ' + missingParams.join(', ') + '</option>');
        console.log('缺少必要参数:', missingParams);
        return;
    }

    // 保存当前选中的值
    var currentValue = $select.val();

    // 显示加载状态
    $select.empty().append('<option value="">加载中...</option>');

    // 从到货日期提取年月
    var useMonth = arrivalDate.substring(0, 7);

    $.ajax({
        url: baselocation + '/order/orderEntry/getCopperContractList',
        type: 'POST',
        data: {
            customerCode: customerCode,
            copperContractType: copperContractType,
            useMonth: useMonth
        },
        success: function(result) {
            console.log('=== 重新加载铜合同列表AJAX返回 ===');
            console.log('返回结果:', result);

            if (result.code === 1 && result.data) {
                console.log('开始重新填充下拉框选项，数据数量:', result.data.length);

                // 清空并重新填充选项
                $select.empty().append('<option value="">请选择</option>');

                $.each(result.data, function(index, item) {
                    // 处理两种数据格式：字符串数组或对象数组
                    var contractNo = '';
                    var copperCondition = '';
                    var currency = '';

                    if (typeof item === 'string') {
                        // 如果是字符串数组
                        contractNo = item;
                    } else if (typeof item === 'object' && item !== null) {
                        // 如果是对象数组
                        contractNo = item.copperSignNo || '';
                        copperCondition = item.copperCondition || '';
                        currency = item.currency || '';
                    }

                    if (contractNo) {
                        var selected = (contractNo === currentValue) ? 'selected' : '';
                        var optionHtml = '<option value="' + contractNo + '" ' + selected +
                                       ' data-copper-condition="' + copperCondition + '"' +
                                       ' data-currency="' + currency + '">' +
                                       contractNo + '</option>';
                        $select.append(optionHtml);
                        console.log('重新添加选项:', contractNo, '铜条件:', copperCondition, '货币:', currency);
                    }
                });

                console.log('下拉框重新填充完成，总选项数:', $select.find('option').length);

            } else {
                $select.empty().append('<option value="">无可用合同</option>');
                console.log('重新获取铜合同列表失败:', result.message);
            }
        },
        error: function(xhr, status, error) {
            console.log('重新获取铜合同列表异常:', error);
            $select.empty().append('<option value="">加载失败</option>');
        }
    });
}



// 在原地创建铜条件下拉框并加载数据
function createCopperConditionSelectInPlace($input) {
    console.log('=== 在原地创建铜条件下拉框 ===');

    var currentValue = $input.val();
    var inputName = $input.attr('name');

    // 创建下拉框（使用Layui样式）
    var $select = $('<select name="' + inputName + '" class="layui-input"></select>');
    $select.append('<option value="">加载中...</option>');

    // 替换输入框
    $input.replaceWith($select);

    console.log('已替换为铜条件下拉框，开始加载数据...');

    $.ajax({
        url: baselocation + '/order/orderEntry/getCopperConditionList',
        type: 'POST',
        success: function(result) {
            console.log('=== 铜条件列表AJAX返回 ===');
            console.log('返回结果:', result);

            if (result.code === 1 && result.data) {
                console.log('开始填充铜条件下拉框选项，数据数量:', result.data.length);

                // 清空并重新填充选项
                $select.empty().append('<option value="">请选择</option>');

                $.each(result.data, function(index, item) {
                    // 处理不同的数据格式
                    var copperCondition = '';
                    var currency = '';

                    if (typeof item === 'string') {
                        copperCondition = item;
                    } else if (typeof item === 'object' && item !== null) {
                        // 支持两种字段名：condition 或 copperCondition
                        copperCondition = item.condition || item.copperCondition || '';
                        currency = item.currency || '';
                    }

                    if (copperCondition) {
                        var selected = (copperCondition === currentValue) ? 'selected' : '';
                        var optionHtml = '<option value="' + copperCondition + '" ' + selected +
                                       ' data-currency="' + currency + '">' +
                                       copperCondition + '</option>';
                        $select.append(optionHtml);
                        console.log('添加铜条件选项:', copperCondition, '货币:', currency);
                    }
                });

                console.log('铜条件下拉框填充完成，总选项数:', $select.find('option').length);

                // 绑定选择事件
                $select.off('change').on('change', function() {
                    var selectedOption = $(this).find('option:selected');
                    var currency = selectedOption.data('currency');
                    var copperCondition = selectedOption.val();

                    // 获取铜货币字段（在第二行）
                    var $currentRow = $(this).closest('tr');
                    var $tbody = $currentRow.closest('tbody.record');
                    var $currencyInput = $tbody.find('input[name="currency"]');

                    if (currency) {
                        // 直接设置货币
                        $currencyInput.val(currency);
                        console.log('直接设置铜条件货币:', currency);
                    } else if (copperCondition) {
                        // 如果没有货币信息，通过接口获取
                        getCopperConditionDetail(copperCondition, $(this));
                    } else {
                        // 清空货币
                        $currencyInput.val('');
                    }

                    // 根据铜合同类别决定是否查询铜base
                    var $copperContractTypeSelect = $currentRow.find('select[name="copperContractType"]');
                    var copperContractType = $copperContractTypeSelect.val();

                    if (copperContractType === '3' || copperContractType === '4') {
                        // 一般铜/无偿：查询铜价表获取铜base
                        var arrivalDate = $currentRow.find('input[name="arrivalDate"]').val();
                        if (copperCondition && arrivalDate) {
                            queryCopperBaseForGeneralCopperPayment($tbody, copperCondition, arrivalDate);
                        }
                    }
                });

            } else {
                $select.empty().append('<option value="">无可用铜条件</option>');
                console.log('获取铜条件列表失败:', result.message);
            }
        },
        error: function(xhr, status, error) {
            console.log('获取铜条件列表异常:', error);
            $select.empty().append('<option value="">加载失败</option>');
        }
    });
}

// 获取铜合同详情（包括铜条件和货币）
function getCopperContractDetail(copperContractNo, $select) {
    console.log('=== 获取铜合同详情 ===');
    console.log('铜合同号:', copperContractNo);

    $.ajax({
        url: baselocation + '/order/orderEntry/getCopperContractDetail',
        type: 'POST',
        data: {
            copperContractNo: copperContractNo
        },
        success: function(result) {
            console.log('铜合同详情返回:', result);

            if (result.code === 1 && result.data) {
                var copperCondition = result.data.copperCondition || '';
                var currency = result.data.copperCurrency || '';

                // 获取当前行的铜条件输入框
                var $currentRow = $select.closest('tr');
                var $copperConditionInput = $currentRow.find('input[name="copperCondition"]');

                // 获取铜货币字段（在第二行）
                var $tbody = $currentRow.closest('tbody.record');
                var $currencyInput = $tbody.find('input[name="currency"]');

                // 设置铜条件和货币
                $copperConditionInput.val(copperCondition);
                $currencyInput.val(currency);

                console.log('设置铜合同详情 - 铜条件:', copperCondition, '货币:', currency);
            } else {
                console.log('获取铜合同详情失败:', result.message);
            }
        },
        error: function(xhr, status, error) {
            console.log('获取铜合同详情异常:', error);
        }
    });
}

// 获取客户详情信息（货币和税率）
function getCustomerInfo(customerCode) {
    if (!customerCode) {
        return;
    }

    console.log('获取客户详情信息，客户代码:', customerCode);

    $.ajax({
        url: baselocation + '/order/salesLogin/getCustomerInfo',
        type: 'POST',
        data: {customerCode: customerCode},
        success: function(result) {
            console.log('获取客户详情信息结果:', result);
            if (result.code === 1 && result.data) {
                var customerInfo = result.data;
                console.log('客户详情信息:', customerInfo);

                // 设置货币
                if (customerInfo && customerInfo.结算货币) {
                    // 使用Layui的方式设置下拉框值
                    $('#currency').val(customerInfo.结算货币);

                    // 手动设置Layui下拉框的选中状态
                    var $currencySelect = $('#currency').next('.layui-form-select');
                    if ($currencySelect.length > 0) {
                        // 更新显示文本
                        $currencySelect.find('.layui-select-title input').val(customerInfo.结算货币);

                        // 移除所有选项的选中状态
                        $currencySelect.find('dd').removeClass('layui-this');

                        // 设置对应选项为选中状态
                        $currencySelect.find('dd[lay-value="' + customerInfo.结算货币 + '"]').addClass('layui-this');
                    }

                    console.log('设置货币:', customerInfo.结算货币);
                    console.log('设置后的货币值:', $('#currency').val());
                }

                // 根据税代码获取税率（联动方式）
                if (customerInfo && customerInfo.税代码) {
                    $('#taxCode').val(customerInfo.税代码);
                    getTaxRateByTaxCode(customerInfo.税代码);
                    console.log('根据税代码获取税率:', customerInfo.税代码);
                } else {
                    console.log('客户详情信息中没有税代码');
                    // 清空税率和税代码
                    $('#taxRate').val('');
                    $('#taxCode').val('');
                }

                // 设置交易条件、付款条件、运输条件、税率等字段的默认值
                if (customerInfo) {
                    // 交易条件：设置下拉菜单的默认选中值
                    if (customerInfo.交易条件) {
                        $('#tradeCondition').val(customerInfo.交易条件);
                        console.log('设置交易条件默认值:', customerInfo.交易条件);
                    }

                    // 付款条件：设置下拉菜单的默认选中值
                    if (customerInfo.付款条件) {
                        $('#payCondition').val(customerInfo.付款条件);
                        console.log('设置付款条件默认值:', customerInfo.付款条件);
                    }

                    // 运输条件：设置下拉菜单的默认选中值
                    if (customerInfo.运输条件) {
                        $('#transportCondition').val(customerInfo.运输条件);
                        console.log('设置运输条件默认值:', customerInfo.运输条件);
                    }

                    // 重新渲染表单以更新下拉菜单显示
                    layui.form.render('select');
                }
            } else {
                console.log('获取客户详情信息失败:', result.message);
            }
        },
        error: function(xhr, status, error) {
            console.error('获取客户详情信息异常:', error);
        }
    });
}

// 查询升水表数据（用于一般铜）- 支付请求登录专用
function queryAscendingWaterForGeneralCopperPayment($currentRow, customerCode, arrivalDate) {
    console.log('=== 支付请求登录查询升水表数据 ===');
    console.log('客户代码:', customerCode, '到货日期:', arrivalDate);

    if (!customerCode || !arrivalDate) {
        console.log('客户代码或到货日期为空，无法查询升水表');
        var $copperConditionInput = $currentRow.find('input[name="copperCondition"]');
        $copperConditionInput.prop('readonly', false).attr('placeholder', '请先填写客户代码和到货日期');
        return;
    }

    $.ajax({
        url: baselocation + '/salesCommon/getAscendingWaterByCustomerAndDate',
        type: 'POST',
        data: {
            customerCode: customerCode,
            arrivalDate: arrivalDate
        },
        success: function(result) {
            console.log('支付请求登录升水表查询返回:', result);

            var $tbody = $currentRow.closest('tbody.record');
            var $copperConditionSelect = $currentRow.find('select[name="copperCondition"]');
            var $currencyInput = $tbody.find('input[name="currency"]');
            var $premiumInput = $tbody.find('input[name="premium"]'); // 升水字段

            if (result.code === 1 && result.data && result.data.length > 0) {
                // 获取第一条匹配的升水表数据
                var ascendingWaterData = result.data[0];

                console.log('支付请求登录升水表数据:', ascendingWaterData);

                // 设置铜条件（下拉框选中）
                if ($copperConditionSelect.length > 0) {
                    $copperConditionSelect.val(ascendingWaterData.copperCondition || '');
                }

                // 设置铜货币（从铜条件表关联获取）
                $currencyInput.val(ascendingWaterData.currency || '');

                // 设置升水单价
                if ($premiumInput.length > 0) {
                    $premiumInput.val(ascendingWaterData.ascendingWaterPrice || '');
                }

                console.log('支付请求登录已设置升水表数据 - 铜条件:', ascendingWaterData.copperCondition,
                           '货币:', ascendingWaterData.currency,
                           '升水:', ascendingWaterData.ascendingWaterPrice);

                layer.msg('已自动填入升水表数据', {icon: 1, time: 2000});

            } else {
                console.log('支付请求登录未找到匹配的升水表数据');

                // 未找到数据时，允许手动选择铜条件
                var $copperConditionInput = $currentRow.find('input[name="copperCondition"]');
                if ($copperConditionInput.length > 0) {
                    $copperConditionInput.prop('readonly', false).attr('placeholder', '未找到升水表数据，请手动选择铜条件');
                    createCopperConditionSelectInPlace($copperConditionInput);
                }

                layer.msg('未找到匹配的升水表数据，请手动选择铜条件', {icon: 0, time: 3000});
            }
        },
        error: function(xhr, status, error) {
            console.log('支付请求登录查询升水表数据异常:', error);

            var $copperConditionInput = $currentRow.find('input[name="copperCondition"]');
            $copperConditionInput.prop('readonly', false).attr('placeholder', '查询升水表失败，请手动选择铜条件');
            createCopperConditionSelectInPlace($copperConditionInput);

            layer.msg('查询升水表数据失败，请手动选择铜条件', {icon: 2, time: 3000});
        }
    });
}

// 货币换算函数（支付请求登录专用）
function convertCurrencyToRMBPayment(originalPrice, currency, arrivalDate, callback) {
    console.log('=== 支付请求登录货币换算 ===');
    console.log('原价格:', originalPrice, '货币:', currency, '到货日期:', arrivalDate);

    // 如果是人民币，直接返回原价格
    if (!currency || currency === 'RMB') {
        console.log('货币为RMB，无需换算');
        callback(originalPrice, 1.0);
        return;
    }

    // 查询汇率
    $.ajax({
        url: baselocation + '/salesCommon/getCurrencyExchangeRate',
        type: 'POST',
        data: {
            originalCurrency: currency,
            arrivalDate: arrivalDate
        },
        success: function(result) {
            console.log('支付请求登录汇率查询返回:', result);

            if (result.code === 1 && result.data && result.data.exchangeRate) {
                var exchangeRate = parseFloat(result.data.exchangeRate);
                var convertedPrice = (parseFloat(originalPrice) * exchangeRate).toFixed(4);

                console.log('汇率:', exchangeRate, '换算后价格:', convertedPrice);
                callback(convertedPrice, exchangeRate);
            } else {
                console.log('未找到汇率数据:', result.message || '无数据');
                layer.msg(result.message || '货币换算汇率未登录！', {icon: 0, time: 3000});
                callback(originalPrice, 1.0); // 返回原价格
            }
        },
        error: function(xhr, status, error) {
            console.log('支付请求登录查询汇率异常:', error);
            layer.msg('查询汇率失败，使用原价格', {icon: 2, time: 3000});
            callback(originalPrice, 1.0); // 返回原价格
        }
    });
}

// 查询产品单价（支付请求登录专用）
function queryProductPriceForPayment(customerCode, productCode, arrivalDate, $currentRow) {
    console.log('=== 支付请求登录查询产品单价 ===');
    console.log('客户代码:', customerCode, '产品代码:', productCode, '到货日期:', arrivalDate);

    if (!customerCode || !productCode || !arrivalDate) {
        console.log('参数不完整，无法查询产品单价');
        return;
    }

    $.ajax({
        url: baselocation + '/salesCommon/getProductPrice',
        type: 'POST',
        data: {
            customerCode: customerCode,
            productCode: productCode,
            arrivalDate: arrivalDate
        },
        success: function(result) {
            console.log('支付请求登录产品单价查询返回:', result);

            if (result.code === 1 && result.data) {
                var productPrice = result.data;
                var $tbody = $currentRow.closest('tbody.record');
                var $secondRow = $tbody.find('tr:last-child');
                var $paymentUnitPriceInput = $secondRow.find('input[name="paymentUnitPrice"]');

                if (productPrice.productPrice) {
                    // 获取产品价格的货币信息
                    var productCurrency = productPrice.currency || 'RMB';

                    console.log('支付请求登录产品单价:', productPrice.productPrice, '货币:', productCurrency);

                    // 进行货币换算
                    convertCurrencyToRMBPayment(productPrice.productPrice, productCurrency, arrivalDate, function(convertedPrice, exchangeRate) {
                        // 设置支付单价（换算后的价格）
                        $paymentUnitPriceInput.val(convertedPrice);
                        console.log('支付请求登录自动设置支付单价（换算后）:', convertedPrice, '汇率:', exchangeRate);

                        // 触发支付单价变更事件，重新计算支付金额
                        $paymentUnitPriceInput.trigger('change');

                        var message = '已自动填入产品单价: ' + convertedPrice;
                        if (exchangeRate !== 1.0) {
                            message += ' (原价: ' + productPrice.productPrice + ' ' + productCurrency + ', 汇率: ' + exchangeRate + ')';
                        }
                        layer.msg(message, {icon: 1, time: 3000});
                    });
                } else {
                    console.log('支付请求登录产品价格数据中没有单价信息');
                }
            } else {
                console.log('支付请求登录未找到匹配的产品价格:', result.message || '无数据');
                // 不显示错误提示，因为可能是正常情况（没有配置价格）
            }
        },
        error: function(xhr, status, error) {
            console.log('支付请求登录查询产品单价异常:', error);
            // 不显示错误提示，避免影响用户体验
        }
    });
}

// 查询铜价表获取铜base（用于一般铜/无偿）- 支付请求登录专用
function queryCopperBaseForGeneralCopperPayment($tbody, copperCondition, arrivalDate) {
    console.log('=== 支付请求登录查询铜价表获取铜base ===');
    console.log('铜条件:', copperCondition, '到货日期:', arrivalDate);

    if (!copperCondition || !arrivalDate) {
        console.log('铜条件或到货日期为空，无法查询铜base');
        return;
    }

    $.ajax({
        url: baselocation + '/salesCommon/getCopperBase',
        type: 'POST',
        data: {
            copperCondition: copperCondition,
            arrivalDate: arrivalDate
        },
        success: function(result) {
            console.log('支付请求登录铜base查询返回:', result);

            if (result.code === 1 && result.data) {
                var copperPriceData = result.data;
                var $copperBaseInput = $tbody.find('input[name="copperBase"]');

                if (copperPriceData.copperPrice) {
                    // 设置铜base
                    $copperBaseInput.val(copperPriceData.copperPrice);
                    console.log('支付请求登录自动设置铜base:', copperPriceData.copperPrice);

                    // 触发铜base变更事件，重新计算相关金额
                    $copperBaseInput.trigger('change');

                    layer.msg('已自动填入铜base: ' + copperPriceData.copperPrice, {icon: 1, time: 2000});
                } else {
                    console.log('支付请求登录铜价格数据中没有铜base信息');
                }
            } else {
                console.log('支付请求登录未找到匹配的铜base数据:', result.message || '无数据');
                layer.msg('未找到匹配的铜base数据，请手动输入', {icon: 0, time: 3000});
            }
        },
        error: function(xhr, status, error) {
            console.log('支付请求登录查询铜base异常:', error);
            layer.msg('查询铜base失败，请手动输入', {icon: 2, time: 3000});
        }
    });
}

// 查询铜合同详情获取铜base（用于预约铜/支给铜）- 支付请求登录专用
function queryCopperBaseForContractCopperPayment($tbody, copperSignNo, useMonth) {
    console.log('=== 支付请求登录查询铜合同详情获取铜base ===');
    console.log('铜签约NO:', copperSignNo, '使用月:', useMonth);

    if (!copperSignNo || !useMonth) {
        console.log('铜签约NO或使用月为空，无法查询铜合同详情');
        return;
    }

    $.ajax({
        url: baselocation + '/salesCommon/getCopperContractDetailForBase',
        type: 'POST',
        data: {
            copperSignNo: copperSignNo,
            useMonth: useMonth
        },
        success: function(result) {
            console.log('支付请求登录铜合同详情查询返回:', result);

            if (result.code === 1 && result.data) {
                var contractData = result.data;
                var $copperBaseInput = $tbody.find('input[name="copperBase"]');

                if (contractData.signedCopperPriceExTax) {
                    // 设置铜base（使用免税签约铜价）
                    $copperBaseInput.val(contractData.signedCopperPriceExTax);
                    console.log('支付请求登录自动设置铜base（免税签约铜价）:', contractData.signedCopperPriceExTax);

                    // 触发铜base变更事件，重新计算相关金额
                    $copperBaseInput.trigger('change');

                    layer.msg('已自动填入铜base: ' + contractData.signedCopperPriceExTax, {icon: 1, time: 2000});
                } else {
                    console.log('支付请求登录铜合同数据中没有免税签约铜价信息');
                }
            } else {
                console.log('支付请求登录未找到匹配的铜合同数据:', result.message || '无数据');
                layer.msg('未找到匹配的铜合同数据，请手动输入铜base', {icon: 0, time: 3000});
            }
        },
        error: function(xhr, status, error) {
            console.log('支付请求登录查询铜合同详情异常:', error);
            layer.msg('查询铜合同详情失败，请手动输入铜base', {icon: 2, time: 3000});
        }
    });
}

// 加载交易条件下拉菜单数据
function loadTradeConditions() {
    console.log('开始加载交易条件数据，URL:', baselocation + '/order/salesLogin/getTradeConditions');
    $.ajax({
        url: baselocation + '/order/salesLogin/getTradeConditions',
        type: 'POST',
        success: function(result) {
            console.log('交易条件API返回结果:', result);
            if (result.code === 1 && result.data) {
                console.log('交易条件数据:', result.data);
                var $select = $('#tradeCondition');
                $select.empty().append('<option value="">请选择</option>');

                $.each(result.data, function(index, item) {
                    console.log('处理交易条件项:', item);
                    var optionText = item.交易条件 + ' ' + (item.交易条件名 || '');
                    $select.append('<option value="' + item.交易条件 + '">' + optionText + '</option>');
                });

                layui.form.render('select');
                console.log('交易条件下拉菜单数据加载完成，共', result.data.length, '条数据');
            } else {
                console.log('获取交易条件数据失败:', result);
            }
        },
        error: function(xhr, status, error) {
            console.error('获取交易条件数据异常:', error);
            console.error('响应状态:', status);
            console.error('响应内容:', xhr.responseText);
        }
    });
}

// 加载付款条件下拉菜单数据
function loadPayConditions() {
    console.log('开始加载付款条件数据，URL:', baselocation + '/order/salesLogin/getPayConditions');
    $.ajax({
        url: baselocation + '/order/salesLogin/getPayConditions',
        type: 'POST',
        success: function(result) {
            console.log('付款条件API返回结果:', result);
            if (result.code === 1 && result.data) {
                console.log('付款条件数据:', result.data);
                var $select = $('#payCondition');
                $select.empty().append('<option value="">请选择</option>');

                $.each(result.data, function(index, item) {
                    console.log('处理付款条件项:', item);
                    var optionText = item.付款条件 + ' ' + (item.付款条件名 || '');
                    $select.append('<option value="' + item.付款条件 + '">' + optionText + '</option>');
                });

                layui.form.render('select');
                console.log('付款条件下拉菜单数据加载完成，共', result.data.length, '条数据');
            } else {
                console.log('获取付款条件数据失败:', result);
            }
        },
        error: function(xhr, status, error) {
            console.error('获取付款条件数据异常:', error);
            console.error('响应状态:', status);
            console.error('响应内容:', xhr.responseText);
        }
    });
}

// 加载运输条件下拉菜单数据
function loadTransportConditions() {
    console.log('开始加载运输条件数据，URL:', baselocation + '/order/salesLogin/getTransportConditions');
    $.ajax({
        url: baselocation + '/order/salesLogin/getTransportConditions',
        type: 'POST',
        success: function(result) {
            console.log('运输条件API返回结果:', result);
            if (result.code === 1 && result.data) {
                console.log('运输条件数据:', result.data);
                var $select = $('#transportCondition');
                $select.empty().append('<option value="">请选择</option>');

                $.each(result.data, function(index, item) {
                    console.log('处理运输条件项:', item);
                    var optionText = item.运输条件 + ' ' + (item.运输条件名 || '');
                    $select.append('<option value="' + item.运输条件 + '">' + optionText + '</option>');
                });

                layui.form.render('select');
                console.log('运输条件下拉菜单数据加载完成，共', result.data.length, '条数据');
            } else {
                console.log('获取运输条件数据失败:', result);
            }
        },
        error: function(xhr, status, error) {
            console.error('获取运输条件数据异常:', error);
            console.error('响应状态:', status);
            console.error('响应内容:', xhr.responseText);
        }
    });
}

// 全选/取消全选功能
function toggleSelectAll() {
    var selectAllCheckbox = document.getElementById('selectAll');
    var rowCheckboxes = document.querySelectorAll('.row-checkbox');

    for (var i = 0; i < rowCheckboxes.length; i++) {
        rowCheckboxes[i].checked = selectAllCheckbox.checked;
    }

    // 更新选中金额
    updateSelectedAmount();
}

// 更新选中项目的合计金额
function updateSelectedAmount() {
    var selectedTotal = 0;
    var allCheckboxes = document.querySelectorAll('.row-checkbox');
    var checkedCount = 0;

    // 计算选中项目的总金额
    allCheckboxes.forEach(function(checkbox) {
        if (checkbox.checked) {
            checkedCount++;
            var row = checkbox.closest('tbody.record');
            var requestAmountInput = row.querySelector('input[name="requestAmount"]');
            if (requestAmountInput && requestAmountInput.value) {
                selectedTotal += parseFloat(requestAmountInput.value) || 0;
            }
        }
    });

    // 更新总请求金额显示
    document.getElementById('totalAmount').value = selectedTotal.toFixed(2);

    // 更新全选复选框状态
    var selectAllCheckbox = document.getElementById('selectAll');
    if (selectAllCheckbox) {
        if (checkedCount === 0) {
            selectAllCheckbox.indeterminate = false;
            selectAllCheckbox.checked = false;
        } else if (checkedCount === allCheckboxes.length) {
            selectAllCheckbox.indeterminate = false;
            selectAllCheckbox.checked = true;
        } else {
            selectAllCheckbox.indeterminate = true;
            selectAllCheckbox.checked = false;
        }
    }

    console.log('已选中', checkedCount, '项，合计金额:', selectedTotal.toFixed(2));
}

// 处理行复选框变化
function handleRowCheckboxChange(checkbox) {
    var salesNo = checkbox.getAttribute('data-sales-no');
    var detailSeq = checkbox.getAttribute('data-detail-seq');
    var confirmStatus = checkbox.checked ? '1' : '0';

    // 更新选中金额
    updateSelectedAmount();

    // 如果有销售额NO和明细序号，则更新数据库中的确认状态
    if (salesNo && detailSeq) {
        updateSalesDetailConfirmStatus(salesNo, detailSeq, confirmStatus);
    }
}

// 更新销售额明细的支付请求确认状态
function updateSalesDetailConfirmStatus(salesAmountNo, detailSeq, confirmStatus) {
    $.ajax({
        url: ctx + '/order/paymentRequestLogin/updateSalesDetailPaymentRequestConfirm',
        type: 'POST',
        data: {
            salesAmountNo: salesAmountNo,
            detailSeq: detailSeq,
            confirmStatus: confirmStatus
        },
        success: function(result) {
            if (result.code === 200) {
                console.log('更新支付请求确认状态成功:', salesAmountNo, detailSeq, confirmStatus);
            } else {
                console.error('更新支付请求确认状态失败:', result.message);
                layer.msg('更新确认状态失败: ' + result.message, {icon: 2});
            }
        },
        error: function(xhr, status, error) {
            console.error('更新支付请求确认状态异常:', error);
            layer.msg('更新确认状态异常: ' + error, {icon: 2});
        }
    });
}

// 关闭弹窗函数
function closeLayer() {
    var index = parent.layer.getFrameIndex(window.name);
    parent.layer.close(index);
    parent.location.reload();
}

// 根据税代码获取税率
function getTaxRateByTaxCode(taxCode) {
    if (!taxCode) {
        return;
    }

    $.ajax({
        url: baselocation + '/salesCommon/taxRateCodeFun/json',
        type: 'POST',
        data: {taxRateCode: taxCode},
        success: function(result) {
            console.log('获取税率结果:', result);
            if (result.code === 1 && result.data && result.data.taxRate) {
                $('#taxRate').val(result.data.taxRate);
                console.log('设置税率:', result.data.taxRate);
                // 税率变更后重新计算税额
                var totalAmountStr = $('#totalAmount').val().replace('¥', '');
                var totalAmount = parseFloat(totalAmountStr) || 0;
                calculateTaxAmount(totalAmount);
            } else {
                console.log('未找到对应的税率数据');
                // 如果找不到税率，显示税代码作为兼容
                $('#taxRate').val(taxCode);
                // 清空税额
                $('#taxAmount').val('0.00');
            }
        },
        error: function(xhr, status, error) {
            console.error('获取税率失败:', error);
            // 如果请求失败，显示税代码作为兼容
            $('#taxRate').val(taxCode);
            // 清空税额
            $('#taxAmount').val('0.00');
        }
    });
}

// 根据出货单No.获取支付请求明细数据
function getPaymentRequestDetailsByShipmentNo(shipmentNo) {
    console.log('根据出货单No.获取支付请求明细数据:', shipmentNo);

    if (!shipmentNo) {
        layer.msg('出货单No.不能为空', {icon: 2});
        return;
    }

    // 显示加载提示
    var loadingIndex = layer.load(2, {shade: [0.3, '#000']});

    $.ajax({
        url: baselocation + '/order/paymentRequestLogin/getDetailsByShipmentNo',
        type: 'POST',
        data: {shipmentNo: shipmentNo},
        success: function(result) {
            layer.close(loadingIndex);
            console.log('根据出货单No.获取支付请求明细数据结果:', result);

            if (result.code === 1 && result.data && result.data.length > 0) {
                // 添加明细数据到表格
                var addedCount = 0;
                result.data.forEach(function(detail) {
                    addPaymentRequestDetailRow(detail);
                    addedCount++;
                });

                layer.msg('成功添加 ' + addedCount + ' 条明细数据', {icon: 1});
                console.log('已添加支付请求明细数据:', result.data);

                // 重新计算总金额
                calculatePaymentRequestTotalAmount();
            } else {
                layer.msg('未找到出货单No. [' + shipmentNo + '] 的明细数据', {icon: 0});
            }
        },
        error: function(xhr, status, error) {
            layer.close(loadingIndex);
            console.error('获取出货单明细数据失败:', error);
            layer.msg('获取出货单明细数据失败: ' + error, {icon: 2});
        }
    });
}

// 验证支付请求数量拆分是否正确
function validatePaymentRequestQuantitySplit($currentRecord) {
    var salesAmountNo = $currentRecord.find('input[name="salesAmountNo"]').val();
    var deliveryNoteSeq = $currentRecord.find('input[name="deliveryNoteSeq"]').val();
    var originalQuantity = parseFloat($currentRecord.find('input[name="originalQuantity"]').val()) || 0;

    // 如果没有销售额No.或原始数量，则不进行验证
    if (!salesAmountNo || originalQuantity <= 0) {
        return;
    }

    console.log('开始验证支付请求数量拆分 - 销售额No.:', salesAmountNo, '序号:', deliveryNoteSeq, '原始数量:', originalQuantity);

    // 查找所有相同销售额No.和序号的明细行
    var totalQuantity = 0;
    var relatedRecords = [];

    $('#detailTable tbody.record').each(function() {
        var $record = $(this);
        var recordSalesAmountNo = $record.find('input[name="salesAmountNo"]').val();
        var recordDeliveryNoteSeq = $record.find('input[name="deliveryNoteSeq"]').val();
        var recordOriginalQuantity = parseFloat($record.find('input[name="originalQuantity"]').val()) || 0;

        // 检查是否是相同的销售额明细
        if (recordSalesAmountNo === salesAmountNo &&
            recordDeliveryNoteSeq === deliveryNoteSeq &&
            recordOriginalQuantity === originalQuantity) {

            var quantity = parseFloat($record.find('input[name="quantity"]').val()) || 0;
            totalQuantity += quantity;
            relatedRecords.push($record);

            console.log('找到相关行 - 数量:', quantity, '累计总量:', totalQuantity);
        }
    });

    console.log('验证结果 - 相关行数:', relatedRecords.length, '总数量:', totalQuantity, '原始数量:', originalQuantity);

    // 如果只有一行，不需要验证
    if (relatedRecords.length <= 1) {
        return;
    }

    // 验证数量总和是否与原始数量相等
    var quantityDifference = Math.abs(totalQuantity - originalQuantity);
    var tolerance = 0.001; // 允许的误差范围

    if (quantityDifference > tolerance) {
        // 数量不匹配，显示警告信息
        var message = '销售额No. [' + salesAmountNo + '] 序号 [' + deliveryNoteSeq + '] 的数量拆分不正确！\n' +
                     '原始数量: ' + originalQuantity + ' KG\n' +
                     '拆分后总量: ' + totalQuantity + ' KG\n' +
                     '差额: ' + (totalQuantity - originalQuantity).toFixed(3) + ' KG';

        console.warn('支付请求数量验证失败:', message);

        // 高亮显示相关的数量输入框
        relatedRecords.forEach(function($record) {
            var $quantityInput = $record.find('input[name="quantity"]');
            $quantityInput.addClass('quantity-error');
            setTimeout(function() {
                $quantityInput.removeClass('quantity-error');
            }, 5000);
        });

        // 显示提示信息
        layer.alert(message, {
            icon: 0,
            title: '数量验证提醒',
            btn: ['确定'],
            yes: function(index) {
                layer.close(index);
            }
        });
    } else {
        console.log('支付请求数量验证通过');

        // 移除错误样式
        relatedRecords.forEach(function($record) {
            var $quantityInput = $record.find('input[name="quantity"]');
            $quantityInput.removeClass('quantity-error');
        });
    }
}

// 处理支付请求默认铜合同类别变化
function handlePaymentDefaultCopperContractTypeChange(copperContractType, customerCode, paymentRequestDate) {
    console.log('处理支付请求默认铜合同类别变化:', copperContractType, '客户代码:', customerCode, '支付请求日:', paymentRequestDate);

    var $copperContractNoSelect = $('#defaultCopperContractNo');
    var $copperConditionSelect = $('#defaultCopperCondition');

    // 清空下级字段
    $copperContractNoSelect.empty().append('<option value="">请选择</option>');
    $copperConditionSelect.empty().append('<option value="">请选择</option>');

    if (copperContractType && customerCode) {
        if (copperContractType === '1' || copperContractType === '2') {
            // 预约铜或支给铜：加载铜合同列表
            loadPaymentDefaultCopperContractList(customerCode, copperContractType, paymentRequestDate);
        } else if (copperContractType === '3' || copperContractType === '4') {
            // 一般铜或无偿：加载铜条件列表
            loadPaymentDefaultCopperConditionList();
        }
    }

    layui.form.render('select');
}

// 处理支付请求默认铜合同NO变化
function handlePaymentDefaultCopperContractNoChange(copperContractNo) {
    console.log('处理支付请求默认铜合同NO变化:', copperContractNo);

    if (copperContractNo) {
        // 从选中的铜合同获取铜条件
        var selectedOption = $('#defaultCopperContractNo option:selected');
        var copperCondition = selectedOption.data('copper-condition');

        if (copperCondition) {
            $('#defaultCopperCondition').empty()
                .append('<option value="' + copperCondition + '" selected>' + copperCondition + '</option>');
            layui.form.render('select');
        }
    }
}

// 处理支付请求默认铜条件变化
function handlePaymentDefaultCopperConditionChange(copperCondition) {
    console.log('处理支付请求默认铜条件变化:', copperCondition);
    // 这里可以添加额外的处理逻辑，比如更新相关的货币信息等
}

// 加载支付请求默认铜合同列表
function loadPaymentDefaultCopperContractList(customerCode, copperContractType, useMonth) {
    console.log('加载支付请求默认铜合同列表:', customerCode, copperContractType, useMonth);

    if (!customerCode || !copperContractType || !useMonth) {
        console.log('参数不完整，无法加载铜合同列表');
        return;
    }

    $.ajax({
        url: baselocation + '/order/salesLogin/getCopperContractList',
        type: 'POST',
        data: {
            customerCode: customerCode,
            copperContractType: copperContractType,
            useMonth: useMonth,
            status: '0' // 有效状态
        },
        success: function(result) {
            var $select = $('#defaultCopperContractNo');
            $select.empty().append('<option value="">请选择</option>');

            if (result.code === 1 && result.data && result.data.length > 0) {
                $.each(result.data, function(index, item) {
                    var optionText = item.copperSignNo + ' (' + item.copperCondition + ')';
                    var option = '<option value="' + item.copperSignNo + '" ' +
                        'data-copper-condition="' + item.copperCondition + '">' + optionText + '</option>';
                    $select.append(option);
                });
            }

            layui.form.render('select');
        },
        error: function(xhr, status, error) {
            console.error('加载支付请求默认铜合同列表失败:', error);
        }
    });
}

// 加载支付请求默认铜条件列表
function loadPaymentDefaultCopperConditionList() {
    console.log('加载支付请求默认铜条件列表');

    $.ajax({
        url: baselocation + '/order/salesLogin/getCopperConditionList',
        type: 'POST',
        success: function(result) {
            var $select = $('#defaultCopperCondition');
            $select.empty().append('<option value="">请选择</option>');

            if (result.code === 1 && result.data && result.data.length > 0) {
                $.each(result.data, function(index, item) {
                    var optionText = item.copperCondition + ' (' + item.currency + ')';
                    var option = '<option value="' + item.copperCondition + '" ' +
                        'data-currency="' + item.currency + '">' + optionText + '</option>';
                    $select.append(option);
                });
            }

            layui.form.render('select');
        },
        error: function(xhr, status, error) {
            console.error('加载支付请求默认铜条件列表失败:', error);
        }
    });
}

// 应用支付请求默认值到所有明细
function applyPaymentDefaultValuesToAllDetails() {
    var defaultCopperContractType = $('#defaultCopperContractType').val();
    var defaultCopperContractNo = $('#defaultCopperContractNo').val();
    var defaultCopperCondition = $('#defaultCopperCondition').val();

    if (!defaultCopperContractType) {
        layer.msg('请先选择默认铜合同类别', {icon: 2});
        return;
    }

    console.log('应用支付请求默认值到所有明细:', {
        copperContractType: defaultCopperContractType,
        copperContractNo: defaultCopperContractNo,
        copperCondition: defaultCopperCondition
    });

    // 获取默认值的相关数据
    var defaultCurrency = '';
    if (defaultCopperContractType === '1' || defaultCopperContractType === '2') {
        // 预约铜/支给铜：从铜合同NO获取货币
        var selectedContractOption = $('#defaultCopperContractNo option:selected');
        defaultCurrency = selectedContractOption.data('currency') || '';
    } else if (defaultCopperContractType === '3' || defaultCopperContractType === '4') {
        // 一般铜/无偿：从铜条件获取货币
        var selectedConditionOption = $('#defaultCopperCondition option:selected');
        defaultCurrency = selectedConditionOption.data('currency') || '';
    }

    var appliedCount = 0;

    // 应用到所有明细行
    $('#detailTable tbody.record').each(function() {
        var $record = $(this);
        var $firstRow = $record.find('tr:first-child');
        var $secondRow = $record.find('tr:last-child');

        // 设置铜合同类别
        var $copperContractTypeSelect = $firstRow.find('select[name="copperContractType"]');
        if ($copperContractTypeSelect.length > 0) {
            $copperContractTypeSelect.val(defaultCopperContractType);

            // 获取当前行的客户代码和到货日期
            var customerCode = $firstRow.find('input[name="customerCode"]').val();
            var arrivalDate = $firstRow.find('input[name="arrivalDate"]').val();

            console.log('应用支付请求默认值到明细行:', {
                copperContractType: defaultCopperContractType,
                copperContractNo: defaultCopperContractNo,
                copperCondition: defaultCopperCondition,
                customerCode: customerCode,
                arrivalDate: arrivalDate,
                currency: defaultCurrency
            });

            // 触发铜合同类别变更逻辑，这会正确处理字段的级联关系
            handleCopperContractTypeChange(defaultCopperContractType, $firstRow, customerCode, arrivalDate);

            // 等待级联处理完成后，再设置具体的值
            setTimeout(function() {
                if (defaultCopperContractType === '1' || defaultCopperContractType === '2') {
                    // 预约铜/支给铜：复制铜合同NO选项并设置值
                    if (defaultCopperContractNo) {
                        var $copperContractNoSelect = $firstRow.find('select[name="copperContractNo"]');
                        if ($copperContractNoSelect.length > 0) {
                            // 复制默认值区域的铜合同NO选项到明细
                            var $defaultCopperContractNoSelect = $('#defaultCopperContractNo');
                            var defaultOptions = $defaultCopperContractNoSelect.html();

                            console.log('复制铜合同NO选项到支付请求明细:', defaultOptions);
                            $copperContractNoSelect.html(defaultOptions);

                            // 设置选中值
                            $copperContractNoSelect.val(defaultCopperContractNo);
                            console.log('设置支付请求明细铜合同NO值:', defaultCopperContractNo);

                            // 触发change事件以更新铜条件
                            $copperContractNoSelect.trigger('change');
                        }
                    }
                } else if (defaultCopperContractType === '3' || defaultCopperContractType === '4') {
                    // 一般铜/无偿：复制铜条件选项并设置值
                    if (defaultCopperCondition) {
                        var $copperConditionSelect = $firstRow.find('select[name="copperCondition"]');
                        if ($copperConditionSelect.length > 0) {
                            // 复制默认值区域的铜条件选项到明细
                            var $defaultCopperConditionSelect = $('#defaultCopperCondition');
                            var defaultConditionOptions = $defaultCopperConditionSelect.html();

                            console.log('复制铜条件选项到支付请求明细:', defaultConditionOptions);
                            $copperConditionSelect.html(defaultConditionOptions);

                            // 设置选中值
                            $copperConditionSelect.val(defaultCopperCondition);
                            console.log('设置支付请求明细铜条件值:', defaultCopperCondition);

                            // 触发change事件以更新货币
                            $copperConditionSelect.trigger('change');
                        }
                    }
                }

                // 设置货币
                if (defaultCurrency) {
                    var $currencyInput = $secondRow.find('input[name="currency"]');
                    if ($currencyInput.length > 0) {
                        $currencyInput.val(defaultCurrency);
                        console.log('设置支付请求明细货币值:', defaultCurrency);
                    }
                }
            }, 500); // 给级联处理一些时间

            appliedCount++;
        }
    });

    if (appliedCount > 0) {
        layer.msg('已应用默认值到 ' + appliedCount + ' 条明细', {icon: 1});
    } else {
        layer.msg('没有找到可应用的明细行', {icon: 0});
    }

    console.log('支付请求默认值应用完成，影响行数:', appliedCount);
}
