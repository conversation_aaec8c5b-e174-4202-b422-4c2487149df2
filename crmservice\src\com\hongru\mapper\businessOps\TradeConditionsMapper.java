package com.hongru.mapper.businessOps;

import com.baomidou.mybatisplus.mapper.BaseMapper;
import com.hongru.entity.businessOps.TradeConditions;

import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface TradeConditionsMapper extends BaseMapper<TradeConditions> {
	int insertTradeConditions(@Param("tradeConditions") TradeConditions tradeConditions);
  
	TradeConditions selectTradeConditionsById(@Param("id") int id);

    List<TradeConditions> listTradeConditions(@Param("tradeCondition") String tradeCondition, @Param("conditionName") String conditionName);

    void updateTradeConditions(@Param("tradeConditions") TradeConditions tradeConditions);

    void deleteTradeConditions(@Param("id") Integer id);
}
