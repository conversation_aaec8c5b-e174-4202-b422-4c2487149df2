<%@ page language="java" import="java.util.*"
         contentType="text/html; charset=utf-8"%>
<%@ include file="/WEB-INF/layouts/base.jsp"%>
<html>
<head>
    <title>产品价格详情添加</title>
    <style>
        /*设置数据表表头字体*/
        .layui-table th .layui-table-cell{
            text-align: center;
        }
    </style>
</head>
<body>
<form class="layui-form hr-form-add" action="${ctx}/salesCommon/productsPrice/add" method="post" id="submitForm">
    <div class="layui-fluid hr-layui-fluid">
        <div class="layui-card">
            <div class="layui-card-body">
                <div class="layui-form-item layui-row">
                    <div class="layui-inline layui-col-md5">
		                <label class="layui-form-label"><span class="star">*</span>客户代码:</label>
		                <div class="layui-input-block">
		                    <select class="layui-select" id="customerCode" name="customerCode" lay-filter="customerCodeFun" lay-search="true" lay-verify="required" required>
		                        <option value="">请选择</option>
		                        <c:forEach items="${customersList}" var="customers">
		                             <option value="${customers.customerCode}" >${customers.customerCode} - ${customers.customerAlias}</option>
		                        </c:forEach>
		                    </select>
		                 </div>
                    </div>
                    <div class="layui-inline layui-col-md5">
		           		<label class="layui-form-label"><span class="star">*</span>产品代码:</label>
		         		<div class="layui-input-block">
							<select id="productCode" name="productCode" lay-search="" lay-search="true">
								<option value="">请选择</option>
							</select>
		        		</div>
		        	</div>	
                    <div class="layui-inline layui-col-md5">
                        <label class="layui-form-label"><span class="star">*</span>货币:</label>
		           		<div class="layui-input-block">
		          			<select class="layui-select" id="currency" name="currency" lay-search="true" lay-verify="required" required>
		          				<option value="">请选择</option>
		                     	<c:forEach items="${currencyList}" var="currency">
		                        	<option value="${currency}">${currency}</option>
		                       	</c:forEach>
		                    </select>
		          		</div>
                    </div>
                    <div class="layui-inline layui-col-md5">
						<label class="layui-form-label"><span class="star">*</span>适用日期范围:</label>
						<div class="layui-input-block" style="display: flex;justify-content: space-between;align-items: center;align-content: center">
							<input type="text" name="applyDateStart" autocomplete="off" class="layui-input"
									id="applyDateStart" style="display: block;width: 48%" lay-verify="required" required>
										 ~ 
							<input type="text" name="applyDateEnd" autocomplete="off" class="layui-input"
									id="applyDateEnd" style="display: block;width: 48%" lay-verify="required" required>
						</div>
                    </div>
                    <div class="layui-inline layui-col-md5">
                        <label class="layui-form-label"><span class="star">*</span>销售单价:</label>
                        <div class="layui-input-block">
                             <input type="text" class="layui-input" id="productPrice" name="productPrice" value="0" onKeyUp="amount(this)" onBlur="overFormat(this)"  lay-verify="required" required/>
                        </div>
                    </div>
                </div>
                <div class="layui-form-item layui-row">
                    <div class="layui-inline layui-col-md12">
                        <label class="layui-form-label"></label>
                        <div class="layui-input-block">
                            <button type="button" class="layui-btn layui-bg-blue" lay-submit lay-filter="formDemo">保存</button>
                            <button type="button" class="layui-btn layui-btn-primary" onclick="closeAll();">取消</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</form>
<myfooter>
    <script src="${ctxsta}/hongru/js/salesCommon/productsPrice_add.js?time=1"></script>
</myfooter>
</body>
</html>
