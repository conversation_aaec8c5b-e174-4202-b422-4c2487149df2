// 全局变量
var attachmentUploaded = false;
var currentAttachmentId = '';
var currentOriginalFileName = '';
var currentFileType = '';

var form = null;
var productCodeList = [];
$(document).ready(function() {
    // 预加载SheetJS库，用于Excel文件预览
    preloadSheetJSLibrary();

    // 检查是否已有附件ID
    var existingAttachmentId = $('#attachmentId').val();
    if (existingAttachmentId) {
        attachmentUploaded = true;
        currentAttachmentId = existingAttachmentId;
        // 加载附件信息
        loadAttachmentInfo(existingAttachmentId);
    }

    // 初始化表格控制功能
    initTableControls();

    // 初始化添加行按钮
    initAddSubOrderButton();

    // 初始化日期选择器
    initDatePickers();
});

layui.use(['laydate','upload','element','layer','form'], function() {
    form = layui.form;
    var $ = layui.jquery
        ,upload = layui.upload
        ,element = layui.element
        ,layer = layui.layer;
     var laydate = layui.laydate;

    // 初始化已有行的日期选择器
    initExistingDatePickers();

    // 初始化附件上传功能
    initAttachmentUpload();
    
    subOrderSerialNumList = [];

    if ($('#demandCustomerCode').val()) {
        getProductListByCustomerCode($('#demandCustomerCode').val());
        form.render('select');
    }

    form.on('select(demandCustomerCodeFn)', function(data) {
        // 获取产品尺寸列表
        getProductListByCustomerCode(data.value);
        form.render('select');

        // 客户代码变更时，尝试查询产品价格（编辑页面）
        var productCode = $('#subOrderTableBody tr:first-child [name="modelNumber"]').val();
        var arrivalDate = $('#subOrderTableBody tr:first-child [name="arrivalDate"]').val();

        if (data.value && productCode && arrivalDate) {
            console.log('编辑页面客户代码变更，尝试查询产品价格:', {
                customerCode: data.value,
                productCode: productCode,
                arrivalDate: arrivalDate
            });
            queryProductPriceForEdit(data.value, productCode, arrivalDate);
        }
    });

    // 绑定铜字段相关事件
    form.on('select(copperContractType)', function(data) {
        loadCopperContractList(data.value);
    });

    form.on('select(copperContractNo)', function(data) {
        loadCopperContractDetail(data.value);
    });

    // 绑定计算相关字段的输入事件
    $('#conversionRate, #premium, #zeroBase').on('input', function() {
        calculateOrderAmount();
    });

    form.on('submit(formDemo)', function(data) {
        var index = layer.load(2, {
            shade : [ 0.1, '#fff' ]
        });
        var actionUrl = $("#submitForm").attr('action');

        var formData = $('#submitForm').serialize();

        // 确保需求方字段有值（从隐藏字段获取）
        var demandCustomerCodeHidden = $('input[name="demandCustomerCodeHidden"]').val();
        if (demandCustomerCodeHidden && formData.indexOf('demandCustomerCode=') === -1) {
            formData += '&demandCustomerCode=' + encodeURIComponent(demandCustomerCodeHidden);
        }

        console.log(formData);

        var subOrderList = [];
        var $rows = $("#subOrderTableBody tr");
        if ($rows?.length) {
            $rows.each(function() {
                var $row = $(this);
                    var rowData = {
                        orderSerialNum: $row.find('[name="orderSerialNum"]').val(),
                        outboundDate: $row.find('[name="outboundDate"]').val(),
                        arrivalDate: $row.find('[name="arrivalDate"]').val(),
                        prepareDate: $row.find('[name="prepareDate"]').val(),
                        modelNumber: $row.find('[name="modelNumber"]').val(),
                        size: $row.find('[name="size"]').val(),
                        wireSpool: $row.find('[name="wireSpool"]').val(),
                        partNumber: $row.find('[name="partNumber"]').val(),
                        orderQuantity: $row.find('[name="orderQuantity"]').val(),
                        deliveredQuantity: $row.find('[name="deliveredQuantity"]').val(),
                        remainingQuantity: $row.find('[name="remainingQuantity"]').val(),
                        detailRemark: $row.find('[name="detailRemark"]').val(),
                        // 添加铜字段数据
                        copperContractType: $row.data('copperContractType') || '',
                        copperContractNo: $row.data('copperContractNo') || '',
                        copperCondition: $row.data('copperCondition') || '',
                        copperCurrency: $row.data('copperCurrency') || '',
                        conversionRate: $row.data('conversionRate') || '',
                        copperBase: $row.data('copperBase') || '',
                        premium: $row.data('premium') || '',
                        convertedCopperPrice: $row.data('convertedCopperPrice') || '',
                        zeroBase: $row.data('zeroBase') || '',
                        orderUnitPrice: $row.data('orderUnitPrice') || '',
                        orderAmount: $row.data('orderAmount') || ''
                    };

                    subOrderList.push(rowData);
            });
        }

        console.log('subOrderList110 :>> ', subOrderList);

        if (subOrderList?.length) {
            // 格式化子订单信息，方便批量处理
            var orderSerialNum = '';
            var outboundDate = '';
            var arrivalDate = '';
            var prepareDate = '';
            var modelNumber = '';
            var size = '';
            var wireSpool = '';
            var partNumber = '';
            var orderQuantity = '';
            var deliveredQuantity = '';
            var remainingQuantity = '';
            var detailRemark = '';
            var copperContractType = '';
            var copperContractNo = '';
            var copperCondition = '';
            var copperCurrency = '';
            var conversionRate = '';
            var copperBase = '';
            var premium = '';
            var convertedCopperPrice = '';
            var zeroBase = '';
            var orderUnitPrice = '';
            var orderAmount = '';

            subOrderList.forEach(function(item) {
                orderSerialNum = orderSerialNum ? orderSerialNum + ',' + (item.orderSerialNum || '') : (item.orderSerialNum || '');
                outboundDate = outboundDate ? outboundDate + ',' + (item.outboundDate || '') : (item.outboundDate || '');
                arrivalDate = arrivalDate ? arrivalDate + ',' + (item.arrivalDate || '') : (item.arrivalDate || '');
                prepareDate = prepareDate ? prepareDate + ',' + (item.prepareDate || '') : (item.prepareDate || '');
                modelNumber = modelNumber ? modelNumber + ',' + (item.modelNumber || '') : (item.modelNumber || '');
                size = size ? size + ',' + (item.size || '') : (item.size || '');
                wireSpool = wireSpool ? wireSpool + ',' + (item.wireSpool || '') : (item.wireSpool || '');
                partNumber = partNumber ? partNumber + ',' + (item.partNumber || '') : (item.partNumber || '');
                orderQuantity = orderQuantity ? orderQuantity + ',' + (item.orderQuantity || '') : (item.orderQuantity || '');
                deliveredQuantity = deliveredQuantity ? deliveredQuantity + ',' + (item.deliveredQuantity || '') : (item.deliveredQuantity || '');
                remainingQuantity = remainingQuantity ? remainingQuantity + ',' + (item.remainingQuantity || '') : (item.remainingQuantity || '');
                detailRemark = detailRemark ? detailRemark + ',' + (item.detailRemark || '') : (item.detailRemark || '');
                copperContractType = copperContractType ? copperContractType + ',' + (item.copperContractType || '') : (item.copperContractType || '');
                copperContractNo = copperContractNo ? copperContractNo + ',' + (item.copperContractNo || '') : (item.copperContractNo || '');
                copperCondition = copperCondition ? copperCondition + ',' + (item.copperCondition || '') : (item.copperCondition || '');
                copperCurrency = copperCurrency ? copperCurrency + ',' + (item.copperCurrency || '') : (item.copperCurrency || '');
                conversionRate = conversionRate ? conversionRate + ',' + (item.conversionRate || '') : (item.conversionRate || '');
                copperBase = copperBase ? copperBase + ',' + (item.copperBase || '') : (item.copperBase || '');
                premium = premium ? premium + ',' + (item.premium || '') : (item.premium || '');
                convertedCopperPrice = convertedCopperPrice ? convertedCopperPrice + ',' + (item.convertedCopperPrice || '') : (item.convertedCopperPrice || '');
                zeroBase = zeroBase ? zeroBase + ',' + (item.zeroBase || '') : (item.zeroBase || '');
                orderUnitPrice = orderUnitPrice ? orderUnitPrice + ',' + (item.orderUnitPrice || '') : (item.orderUnitPrice || '');
                orderAmount = orderAmount ? orderAmount + ',' + (item.orderAmount || '') : (item.orderAmount || '');
            });

            // 将formData从字符串转换为数组格式
            var formDataArray = [];
            var formDataParts = formData.split('&');
            for (var i = 0; i < formDataParts.length; i++) {
                var pair = formDataParts[i].split('=');
                formDataArray.push({
                    name: decodeURIComponent(pair[0]),
                    value: decodeURIComponent(pair[1] || '')
                });
            }

            // 添加子订单数据
            formDataArray.push({name: 'orderSerialNum', value: orderSerialNum});
            formDataArray.push({name: 'outboundDate', value: outboundDate});
            formDataArray.push({name: 'arrivalDate', value: arrivalDate});
            formDataArray.push({name: 'prepareDate', value: prepareDate});
            formDataArray.push({name: 'modelNumber', value: modelNumber});
            formDataArray.push({name: 'size', value: size});
            formDataArray.push({name: 'wireSpool', value: wireSpool});
            formDataArray.push({name: 'partNumber', value: partNumber});
            formDataArray.push({name: 'orderQuantity', value: orderQuantity});
            formDataArray.push({name: 'deliveredQuantity', value: deliveredQuantity});
            formDataArray.push({name: 'remainingQuantity', value: remainingQuantity});
            formDataArray.push({name: 'detailRemark', value: detailRemark});
            // 添加铜字段数据
            formDataArray.push({name: 'copperContractType', value: copperContractType});
            formDataArray.push({name: 'copperContractNo', value: copperContractNo});
            formDataArray.push({name: 'copperCondition', value: copperCondition});
            formDataArray.push({name: 'copperCurrency', value: copperCurrency});
            formDataArray.push({name: 'conversionRate', value: conversionRate});
            formDataArray.push({name: 'copperBase', value: copperBase});
            formDataArray.push({name: 'premium', value: premium});
            formDataArray.push({name: 'convertedCopperPrice', value: convertedCopperPrice});
            formDataArray.push({name: 'zeroBase', value: zeroBase});
            formDataArray.push({name: 'orderUnitPrice', value: orderUnitPrice});
            formDataArray.push({name: 'orderAmount', value: orderAmount});

            // 更新formData为处理后的数组
            formData = formDataArray;
        }

        $.ajax({
            url : actionUrl,
            type : 'post',
            data : formData,
            success : function(result) {
                layer.closeAll();
                if (result.code == 1) {
                    parent.layer.msg("操作成功!", {
                        shade : 0.3,
                        time : 1500
                    }, function() {
                        parent.window.location.reload();
                    });
                } else {
                    layer.alert(result.message);
                }
            }
        });
        return false;
    });
});

// 关闭页面
function closeAll() {
    var index = parent.layer.getFrameIndex(window.name);
    parent.layer.close(index);
}

// 设置现有的铜字段数据
function setExistingCopperData(existingData) {
    console.log('设置现有铜字段数据:', existingData);

    $('#copperContractNo').val(existingData.copperContractNo);
    setCopperConditionValue(existingData.copperCondition);
    $('#copperCurrency').val(existingData.copperCurrency);
    $('#conversionRate').val(existingData.conversionRate);
    $('#copperBase').val(existingData.copperBase);
    $('#premium').val(existingData.premium);
    $('#convertedCopperPrice').val(existingData.convertedCopperPrice);
    $('#zeroBase').val(existingData.zeroBase);
    $('#orderUnitPrice').val(existingData.orderUnitPrice);
    $('#orderAmount').val(existingData.orderAmount);

    console.log('设置完成后的字段值:');
    console.log('  铜合同类别:', $('#copperContractType').val());
    console.log('  铜签约No:', $('#copperContractNo').val());
    console.log('  铜条件:', $('#copperCondition').val());
    console.log('  接单金额:', $('#orderAmount').val());

    if (form) {
        form.render('select');
    }
}

// 带回调的加载铜签约No列表
function loadCopperContractListWithCallback(copperContractType, callback) {
    var customerCode = $('#demandCustomerCode').val();
    if (!customerCode || !copperContractType) {
        if (callback) callback();
        return;
    }

    $.post(baselocation + '/order/orderEntry/getCopperContractList', {
        customerCode: customerCode,
        copperContractType: copperContractType
    }, function(result) {
        if (result.code === 1) {
            var select = $('#copperContractNo');
            select.empty().append('<option value="">请选择</option>');
            $.each(result.data, function(index, item) {
                select.append('<option value="' + item + '">' + item + '</option>');
            });

            if (form) {
                form.render('select');
            }

            if (callback) {
                callback();
            }
        } else {
            if (callback) callback();
        }
    }).fail(function() {
        if (callback) callback();
    });
}

// 获取产品尺寸列表
function getProductListByCustomerCode(customerCode) {
    // 获取产品尺寸列表
    $.ajax({
        url: baselocation + '/order/orderEntry/getProductListByCustomerCode',
        type: 'post',
        data: {customerCode: customerCode},
        success: function(result) {
            if (result && result.code == 1) {
                productCodeList = result.data;
                console.log('productCodeList :>> ', productCodeList);
                var modelNumber = $('#modelNumber');
                modelNumber.empty();
                modelNumber.append('<option value="">请选择型号</option>');
                productCodeList.forEach(function(item) {
                    modelNumber.append('<option value="' + item + '">' + item + '</option>');
                });

                form.render('select');
            }
        }
    });
}

// 获取产品信息
function getProductInfo(productCode) {
    var size = $('#size');
    var wireSpool = $('#wireSpool');
    var partNumber = $('#partNumber');
    var barcode = $('#barcode');

    size.val('');
    wireSpool.val('');
    partNumber.val('');
    barcode.val('');

    // 获取产品尺寸列表
    $.ajax({
        url: baselocation + '/order/orderEntry/getProductInfo',
        type: 'post',
        data: {productCode: productCode},
        success: function(result) {
            if (result && result.code == 1) {
                var productInfo = result.data || {};
                console.log('productInfo productInfo', productInfo);

                size.val(productInfo?.labelSizeName || '');
                wireSpool.val(productInfo?.wireReelName || '');
                partNumber.val(productInfo?.partNo || '');
                barcode.val(productInfo?.barCode?.trim() || '');
            }
        }
    });
}

// 获取产品线盘列表
function getProductWireReelNameList(size, wireSpool, isEdit) {
    // 获取产品线盘列表
    $.ajax({
        url: baselocation + '/order/orderEntry/getProductWireReelNameList',
        type: 'post',
        data: {customerCode: $('#demandCustomerCode').val(), productCode: $('#modelNumber').val(), size: size},
        success: function(result) {
            if (result && result.code == 1) {
                var wireSpoolList = result.data;
                console.log('wireSpoolList :>> ', wireSpoolList);
                var $wireSpool = $('#wireSpool');  
                $wireSpool.empty();
                $wireSpool.append('<option value="">请选择线盘</option>');
                wireSpoolList.forEach(function(item) {
                    $wireSpool.append('<option value="' + item + '">' + item + '</option>');
                });

                if (isEdit) {
                    $wireSpool.val(wireSpool);
                }

                form.render('select');
            }
        }
    });
}

// 初始化表格控制功能
function initTableControls() {
    console.log('初始化表格控制功能');

    // 放大按钮点击事件
    $('#fullscreenButton').off('click').on('click', function() {
        console.log('点击了放大按钮');
        enterFullscreenMode();
    });

    // 缩小按钮点击事件
    $('#exitFullscreenButton').off('click').on('click', function() {
        console.log('点击了缩小按钮');
        exitFullscreenMode();
    });

    // ESC键退出全屏
    $(document).off('keydown.tableFullscreen').on('keydown.tableFullscreen', function(e) {
        if (e.key === 'Escape' && $('#subOrderContainer').hasClass('table-fullscreen')) {
            console.log('按下ESC键退出全屏');
            exitFullscreenMode();
        }
    });

    // 确保按钮可见
    $('#fullscreenButton').show();
    $('#exitFullscreenButton').hide();
}

// 进入全屏模式
function enterFullscreenMode() {
    var $container = $('#subOrderContainer');

    // 保存当前滚动位置
    var scrollTop = $('.table-scroll-container').scrollTop();

    // 添加全屏类
    $container.addClass('table-fullscreen');

    // 添加单行模式类
    $('.table-scroll-container').addClass('single-mode');

    // 切换按钮显示
    $('#fullscreenButton').hide();
    $('#exitFullscreenButton').show();

    // 恢复滚动位置
    setTimeout(function() {
        $('.table-scroll-container').scrollTop(scrollTop);
    }, 100);

    // 显示全屏模式提示
    layer.msg('已进入全屏模式，按ESC键或点击右上角缩小按钮退出', {time: 3000});
}

// 退出全屏模式
function exitFullscreenMode() {
    var $container = $('#subOrderContainer');

    // 保存当前滚动位置
    var scrollTop = $('.table-scroll-container').scrollTop();

    // 移除全屏类
    $container.removeClass('table-fullscreen');

    // 移除模式类名
    $('.table-scroll-container').removeClass('single-mode');

    // 切换按钮显示
    $('#exitFullscreenButton').hide();
    $('#fullscreenButton').show();

    // 恢复滚动位置
    setTimeout(function() {
        $('.table-scroll-container').scrollTop(scrollTop);
    }, 100);
}

// 初始化添加行按钮
function initAddSubOrderButton() {
    $('#addSubOrderBtn').off('click').on('click', function() {
        console.log('点击了添加行按钮');
        addNewSubOrderRow();
    });
}

// 添加新的子订单行
var subOrderSerialNumList = [];
function addNewSubOrderRow() {
    var demandCustomerCode = $('#demandCustomerCode').val();
    if (!demandCustomerCode) {
        layer.msg('请先选择需求方', {icon: 2});
        return;
    }

    var contractPartyCustomerCode = $('#contractPartyCustomerCode').val();
    if (!contractPartyCustomerCode) {
        layer.msg('请先选择合同方', {icon: 2});
        return;
    }

    var orderType = $('#orderType').val();
    if (!orderType) {
        layer.msg('请先选择订单种类', {icon: 2});
        return;
    }

    // 检查是否已有未保存的行
    var $lastRow = $('#subOrderTableBody tr:last-child');
    if ($lastRow.hasClass('editing-row') || $lastRow.hasClass('single-edit-row')) {
        layer.msg('请先保存当前行再添加新行', {icon: 2});
        return;
    }

    var subOrderList = $('#subOrderTableBody tr.sub-order-row');
    if (subOrderList?.length) {
        subOrderList.each(function() {
            var serialNum = $(this).attr('data-serial');
            if (serialNum && serialNum.trim() != '' && !subOrderSerialNumList.includes(serialNum)) {
                subOrderSerialNumList.push(serialNum);
            }
        });
    }

    // 创建一个新的只读行
    var newRow = `
        <tr class="sub-order-row" data-id="" data-serial="" data-barcode="" data-outbound="" data-arrival="" data-am="" data-pm="" data-model="" data-size="" data-wire="" data-part="" data-order-qty="" data-delivered-qty="" data-remaining-qty="">
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td class="single-row-operation">
                <div class="layui-btn-group">
                    <button type="button" class="layui-btn layui-btn-primary layui-btn-sm" onclick="editSubOrderRow(this)" title="编辑">
                        <i class="layui-icon layui-icon-form"></i>
                    </button>
                    <button type="button" class="layui-btn layui-btn-primary layui-btn-sm" onclick="removeSubOrder(this)" title="删除">
                        <i class="layui-icon layui-icon-delete"></i>
                    </button>
                </div>
            </td>
        </tr>
    `;

    // 添加行到表格
    $('#subOrderTableBody').append(newRow);

    // 自动滚动到底部
    var $container = $('.table-scroll-container');
    $container.scrollTop($container.prop('scrollHeight'));

    // 显示添加成功提示
    layer.msg('已添加新行，点击编辑按钮开始编辑', {icon: 1, time: 2000});

    // 自动触发编辑模式
    var $newRow = $('#subOrderTableBody tr:last-child');
    editSubOrderRow($newRow.find('button:first')[0]);
}

// 清理客户订单号，处理复制粘贴可能带来的特殊字符
function cleanCustomerOrderNo(input) {
    var value = input.value;

    // 移除不可见字符、换行符、制表符等
    value = value.replace(/[\r\n\t\u00A0\u2000-\u200B\u2028\u2029\uFEFF]/g, '');

    // 移除前后空格
    value = value.trim();

    // 移除多余的空格（将多个连续空格替换为单个空格）
    value = value.replace(/\s+/g, ' ');

    // 更新输入框的值
    if (input.value !== value) {
        input.value = value;
    }
}

function checkCustomerOrderNo() {
    // 先清理客户订单号
    var customerOrderNoInput = document.getElementById('customerOrderNo');
    if (customerOrderNoInput) {
        cleanCustomerOrderNo(customerOrderNoInput);
    }

    // 客户订单号改为非必填，移除验证逻辑
    $('#customerOrderNo').removeClass('layui-form-danger');

    // 从主表单中获取订单主信息
    var customerOrderNo = $('#customerOrderNo').val();
    var rowData = {
        customerOrderNo: customerOrderNo ? customerOrderNo.trim() : '',
        orderNo: $('#orderNo').val(),
        demandCustomerCode: $('#demandCustomerCode').val(),
        contractPartyCustomerCode: $('#contractPartyCustomerCode').val(),
        orderType: $('#orderType').val(),
        attachmentId: currentAttachmentId
    };

    $.ajax({
        url: baselocation + '/order/orderEntry/saveSingleRow',
        type: 'post',
        data: rowData,
        success: function(result) {
            if (!result || result.code != 1) {
                // 使用layer.alert显示错误信息，而不是layer.msg，这样错误信息更加明显
                layer.alert(result ? (result.data || '保存失败') : '保存失败', {icon: 2});
            }
        }
    });
}

// 保存子订单行
function saveSubOrderRow(btn, isEdit) {
    var $row = $(btn).closest('tr');

    // 验证必填字段
    var outboundDate = $row.find('[name="outboundDate"]').val();
    var arrivalDate = $row.find('[name="arrivalDate"]').val();
    var size = $row.find('[name="size"]').val();
    var wireSpool = $row.find('[name="wireSpool"]').val();

    // 清理客户订单号（改为非必填）
    var customerOrderNoInput = document.getElementById('customerOrderNo');
    if (customerOrderNoInput) {
        cleanCustomerOrderNo(customerOrderNoInput);
        $('#customerOrderNo').removeClass('layui-form-danger');
    }

    // if (!outboundDate) {
    //     layer.msg('请选择出库日期', {icon: 2});
    //     return;
    // }

    // if (!arrivalDate) {
    //     layer.msg('请选择到货日期', {icon: 2});
    //     return;
    // }

    if (!size) {
        layer.msg('请输入尺寸', {icon: 2});
        return;
    }

    if (!wireSpool) {
        layer.msg('请输入线盘', {icon: 2});
        return;
    }

    // 获取行数据
    var rowData = {
        orderSerialNum: $row.find('[name="orderSerialNum"]').val(),
        outboundDate: outboundDate,
        arrivalDate: arrivalDate,
        prepareDate: $row.find('[name="prepareDate"]').val(),
        modelNumber: $row.find('[name="modelNumber"]').val(),
        size: size,
        wireSpool: wireSpool,
        partNumber: $row.find('[name="partNumber"]').val(),
        orderQuantity: $row.find('[name="orderQuantity"]').val() || 0,
        deliveredQuantity: $row.find('[name="deliveredQuantity"]').val() || 0,
        remainingQuantity: $row.find('[name="remainingQuantity"]').val() || 0,
        detailRemark: $row.find('[name="detailRemark"]').val(),
        barcode: $row.find('[name="barcode"]').val(),
        // 添加铜字段数据
        copperContractType: $row.data('copperContractType') || '',
        copperContractNo: $row.data('copperContractNo') || '',
        copperCondition: $row.data('copperCondition') || '',
        copperCurrency: $row.data('copperCurrency') || '',
        conversionRate: $row.data('conversionRate') || '',
        copperBase: $row.data('copperBase') || '',
        premium: $row.data('premium') || '',
        convertedCopperPrice: $row.data('convertedCopperPrice') || '',
        zeroBase: $row.data('zeroBase') || '',
        orderUnitPrice: $row.data('orderUnitPrice') || '',
        orderAmount: $row.find('.order-amount-input').val() || ''
    };

    // 显示加载中提示
    var loadingIndex = layer.load(1, {
        shade: [0.3, '#fff']
    });

    // 设置超时处理，防止一直转圈
    var timeoutTimer = setTimeout(function() {
        layer.close(loadingIndex);
        layer.msg('请求超时，请重试', {icon: 2});
    }, 10000); // 10秒超时


     // 从主表单中获取订单主信息，并清理客户订单号
     var customerOrderNoInput = document.getElementById('customerOrderNo');
     if (customerOrderNoInput) {
         cleanCustomerOrderNo(customerOrderNoInput);
     }

     rowData.id = $row.find('[name="id"]').val();
     var customerOrderNo = $('#customerOrderNo').val();
     rowData.customerOrderNo = customerOrderNo ? customerOrderNo.trim() : '';
     rowData.orderNo = $('#orderNo').val();
     rowData.demandCustomerCode = $('#demandCustomerCode').val();
     rowData.contractPartyCustomerCode = $('#contractPartyCustomerCode').val();
     rowData.orderType = $('#orderType').val();
     rowData.attachmentId = currentAttachmentId;
 
     $.ajax({
         url: baselocation + '/order/orderEntry/saveSingleRow',
         type: 'post',
         data: rowData,
         success: function(result) {
             // 清除超时计时器
             clearTimeout(timeoutTimer);
 
             // 关闭加载提示
             layer.close(loadingIndex);
 
             if (result && result.code == 1) {
                subOrderSerialNumList.push(rowData?.orderSerialNum?.toString() || '');
                // 移除编辑状态
                $row.removeClass('editing-row');
                $row.removeClass('single-edit-row');

                // 更新行内容为只读状态
                var readOnlyHtml =
                    '<td name="orderSerialNum">' + (rowData.orderSerialNum || '') + '</td>' +
                    '<td>' + (rowData.prepareDate || '') + '</td>' +
                    '<td>' + (rowData.outboundDate || '') + '</td>' +
                    '<td>' + (rowData.arrivalDate || '') + '</td>' +
                    '<td>' + (rowData.modelNumber || '') + '</td>' +
                    '<td>' + (rowData.barcode || '') + '</td>' +
                    '<td>' + (rowData.size || '') + '</td>' +
                    '<td>' + (rowData.wireSpool || '') + '</td>' +
                    '<td>' + (rowData.partNumber || '') + '</td>' +
                    '<td>' + (rowData.orderQuantity || '') + '</td>' +
                    '<td>' + (rowData.deliveredQuantity || '') + '</td>' +
                    '<td>' + (rowData.remainingQuantity || '') + '</td>' +
                    '<td><input type="text" class="layui-input order-amount-input" readonly onclick="openCopperFieldsModal(this)" value="' + (rowData.orderAmount || '') + '" placeholder="点击设置接单金额" title="点击设置接单金额" style="cursor: pointer; background-color: #f8f8f8;" ' +
                    'data-copper-contract-type="' + (rowData.copperContractType || '') + '" ' +
                    'data-copper-contract-no="' + (rowData.copperContractNo || '') + '" ' +
                    'data-copper-condition="' + (rowData.copperCondition || '') + '" ' +
                    'data-copper-currency="' + (rowData.copperCurrency || '') + '" ' +
                    'data-conversion-rate="' + (rowData.conversionRate || '') + '" ' +
                    'data-copper-base="' + (rowData.copperBase || '') + '" ' +
                    'data-premium="' + (rowData.premium || '') + '" ' +
                    'data-converted-copper-price="' + (rowData.convertedCopperPrice || '') + '" ' +
                    'data-zero-base="' + (rowData.zeroBase || '') + '" ' +
                    'data-order-unit-price="' + (rowData.orderUnitPrice || '') + '" ' +
                    'data-order-amount="' + (rowData.orderAmount || '') + '"></td>' +
                    '<td>' + (rowData.detailRemark || '') + '</td>' +
                    '<td class="single-row-operation">' +
                    '<div class="layui-btn-group">' +
                    '<button type="button" class="layui-btn layui-btn-primary layui-btn-sm" onclick="editSubOrderRow(this, '+ true +')" title="编辑">' +
                    '<i class="layui-icon layui-icon-form"></i>' +
                    '</button>' +
                    '<button type="button" class="layui-btn layui-btn-primary layui-btn-sm" onclick="removeSubOrder(this, ' + rowData.orderSerialNum + ')" title="删除">' +
                    '<i class="layui-icon layui-icon-delete"></i>' +
                    '</button>' +
                    '</div>' +
                    '</td>';

                // 保存数据到data属性
                $row.attr({
                    'data-id': rowData.id || '',
                    'data-serial': rowData.orderSerialNum || '',
                    'data-prepare': rowData.prepareDate || '',
                    'data-outbound': rowData.outboundDate || '',
                    'data-arrival': rowData.arrivalDate || '',
                    'data-model': rowData.modelNumber || '',
                    'data-size': rowData.size || '',
                    'data-wire': rowData.wireSpool || '',
                    'data-part': rowData.partNumber || '',
                    'data-order-qty': rowData.orderQuantity || '',
                    'data-delivered-qty': rowData.deliveredQuantity || '',
                    'data-remaining-qty': rowData.remainingQuantity || '',
                    'data-detail-remark': rowData.detailRemark || '',
                    'data-barcode': rowData.barcode || ''
                });

                // 更新HTML内容
                $row.html(readOnlyHtml);

                // 显示保存成功动画
                $row.addClass('save-success');
                setTimeout(function() {
                    $row.removeClass('save-success');
                }, 1000);

                // 显示保存成功提示
                layer.msg('行数据已保存', {icon: 1, time: 1000});

                // 编辑时，不需要自动添加新行
                if (!isEdit) {
                    // 自动添加新的输入行
                    setTimeout(function() {
                        addNewSubOrderRow();
                    }, 500);
                }
             } else {
                 // 使用layer.alert显示错误信息，而不是layer.msg，这样错误信息更加明显
                 layer.alert(result ? (result.data || '保存失败') : '保存失败', {icon: 2});
             }
         },
         error: function(xhr, _, error) {
             // 清除超时计时器
             clearTimeout(timeoutTimer);
 
             console.error('保存失败:', error);
             layer.close(loadingIndex);
 
             // 尝试解析响应中的错误信息
             var errorMsg = '网络错误，请重试';
             try {
                 if (xhr && xhr.responseText) {
                     var response = JSON.parse(xhr.responseText);
                     if (response && response.data) {
                         errorMsg = response.data;
                     }
                 }
             } catch (e) {
                 console.error('解析错误响应失败:', e);
             }
 
             // 使用alert而不是msg，确保错误信息更明显
             layer.alert(errorMsg, {icon: 2});
         },
         complete: function() {
             // 清除超时计时器（以防其他回调未清除）
             clearTimeout(timeoutTimer);
 
             // 确保加载图标一定会关闭
             layer.close(loadingIndex);
         }
     });
}

// 取消子订单行编辑
function cancelSubOrderRow(btn, isEdit) {
    var $row = $(btn).closest('tr');

    if (isEdit) {
        // 取消编辑，恢复原始数据显示
        var readOnlyHtml =
            '<td name="orderSerialNum">' + (editRow?.orderSerialNum || '') + '</td>' +
            '<td>' + (editRow?.prepareDate || '') + '</td>' +
            '<td>' + (editRow?.outboundDate || '') + '</td>' +
            '<td>' + (editRow?.arrivalDate || '') + '</td>' +
            '<td>' + (editRow?.modelNumber || '') + '</td>' +
            '<td>' + (editRow?.barcode || '') + '</td>' +
            '<td>' + (editRow?.size || '') + '</td>' +
            '<td>' + (editRow?.wireSpool || '') + '</td>' +
            '<td>' + (editRow?.partNumber || '') + '</td>' +
            '<td>' + (editRow?.orderQuantity || '') + '</td>' +
            '<td>' + (editRow?.deliveredQuantity || '') + '</td>' +
            '<td>' + (editRow?.remainingQuantity || '') + '</td>' +
            '<td><input type="text" class="layui-input order-amount-input" readonly onclick="openCopperFieldsModal(this)" value="' + (editRow?.orderAmount || '') + '" placeholder="点击设置接单金额" title="点击设置接单金额" style="cursor: pointer; background-color: #f8f8f8;" ' +
            'data-copper-contract-type="' + (editRow?.copperContractType || '') + '" ' +
            'data-copper-contract-no="' + (editRow?.copperContractNo || '') + '" ' +
            'data-copper-condition="' + (editRow?.copperCondition || '') + '" ' +
            'data-copper-currency="' + (editRow?.copperCurrency || '') + '" ' +
            'data-conversion-rate="' + (editRow?.conversionRate || '') + '" ' +
            'data-copper-base="' + (editRow?.copperBase || '') + '" ' +
            'data-premium="' + (editRow?.premium || '') + '" ' +
            'data-converted-copper-price="' + (editRow?.convertedCopperPrice || '') + '" ' +
            'data-zero-base="' + (editRow?.zeroBase || '') + '" ' +
            'data-order-unit-price="' + (editRow?.orderUnitPrice || '') + '" ' +
            'data-order-amount="' + (editRow?.orderAmount || '') + '"></td>' +
            '<td>' + (editRow?.detailRemark || '') + '</td>' +
            '<td class="single-row-operation">' +
            '<div class="layui-btn-group">' +
            '<button type="button" class="layui-btn layui-btn-primary layui-btn-sm" onclick="editSubOrderRow(this, '+ true +')" title="编辑">' +
            '<i class="layui-icon layui-icon-edit"></i>' +
            '</button>' +
            '<button type="button" class="layui-btn layui-btn-primary layui-btn-sm" onclick="removeSubOrder(this, ' + editRow?.orderSerialNum + ')" title="删除">' +
            '<i class="layui-icon layui-icon-delete"></i>' +
            '</button>' +
            '</div>' +
            '</td>';

        // 保存数据到data属性
        $row.attr({
            'data-id': editRow?.id || '',
            'data-serial': editRow?.orderSerialNum || '',
            'data-prepare': editRow?.prepareDate || '',
            'data-outbound': editRow?.outboundDate || '',
            'data-arrival': editRow?.arrivalDate || '',
            'data-model': editRow?.modelNumber || '',
            'data-size': editRow?.size || '',
            'data-wire': editRow?.wireSpool || '',
            'data-part': editRow?.partNumber || '',
            'data-order-qty': editRow?.orderQuantity || '',
            'data-delivered-qty': editRow?.deliveredQuantity || '',
            'data-remaining-qty': editRow?.remainingQuantity || '',
            'data-detail-remark': editRow?.detailRemark || '',
            'data-barcode': editRow?.barcode || ''
        });

        // 更新HTML内容
        $row.html(readOnlyHtml);

        // $row.remove();

        return;
    }

    // 确认是否取消新增
    layer.confirm('确定要取消吗？未保存的数据将丢失', {icon: 3, title:'提示'}, function(index){
        $row.remove();
        layer.close(index);
    });
}

// 编辑子订单行
var editRow = null;
function editSubOrderRow(btn, isEdit) {
    console.log('isEdit :>> ', isEdit);
    var $row = $(btn).closest('tr');

    // 从data属性中获取数据
    var id = $('#id').val() || '';
    var orderSerialNum = $row.attr('data-serial') || '';
    var outboundDate = $row.attr('data-outbound') || '';
    var arrivalDate = $row.attr('data-arrival') || '';
    var prepareDate = $row.attr('data-prepare') || '';
    var modelNumber = $row.attr('data-model') || '';
    var size = $row.attr('data-size') || '';
    var wireSpool = $row.attr('data-wire') || '';
    var partNumber = $row.attr('data-part') || '';
    var orderQuantity = $row.attr('data-order-qty') || '';
    var deliveredQuantity = $row.attr('data-delivered-qty') || '';
    var remainingQuantity = $row.attr('data-remaining-qty') || '';
    var detailRemark = $row.attr('data-detail-remark') || '';
    var barcode = $row.attr('data-barcode') || '';

    editRow = null;
    if (isEdit) {
        // 获取接单金额数据和铜字段数据
        var orderAmount = '';
        var $existingInput = $row.find('.order-amount-input');
        if ($existingInput.length > 0) {
            orderAmount = $existingInput.val() || '';
        }

        editRow = {
            id: id,
            orderSerialNum: orderSerialNum,
            outboundDate: outboundDate,
            arrivalDate: arrivalDate,
            prepareDate: prepareDate,
            modelNumber: modelNumber,
            size: size,
            wireSpool: wireSpool,
            partNumber: partNumber,
            orderQuantity: orderQuantity,
            deliveredQuantity: deliveredQuantity,
            remainingQuantity: remainingQuantity,
            detailRemark: detailRemark,
            barcode: barcode,
            orderAmount: orderAmount,
            // 添加铜字段数据
            copperContractType: $existingInput.attr('data-copper-contract-type') || '',
            copperContractNo: $existingInput.attr('data-copper-contract-no') || '',
            copperCondition: $existingInput.attr('data-copper-condition') || '',
            copperCurrency: $existingInput.attr('data-copper-currency') || '',
            conversionRate: $existingInput.attr('data-conversion-rate') || '',
            copperBase: $existingInput.attr('data-copper-base') || '',
            premium: $existingInput.attr('data-premium') || '',
            convertedCopperPrice: $existingInput.attr('data-converted-copper-price') || '',
            zeroBase: $existingInput.attr('data-zero-base') || '',
            orderUnitPrice: $existingInput.attr('data-order-unit-price') || ''
        };
    } else {
        // 生成当前订单序号（默认1，如果存在其他子订单，则取最大订单序号+1）
        var currentOrderSerialNum = 1;
        if (subOrderSerialNumList?.length) {
            var maxSerialNum = 1;
            subOrderSerialNumList.forEach(function(serialNum) {
                if (serialNum && serialNum.trim() != '') {
                    var numValue = parseInt(serialNum, 10);
                    if (!isNaN(numValue) && numValue > maxSerialNum) {
                        maxSerialNum = numValue;
                    }
                }
            });
            currentOrderSerialNum = maxSerialNum + 1;
        }
        orderSerialNum = currentOrderSerialNum;
    }

    // 获取接单金额数据和铜字段数据
    var orderAmount = '';
    var copperData = {};
    var $existingInput = $row.find('.order-amount-input');
    if ($existingInput.length > 0) {
        orderAmount = $existingInput.val() || '';
        copperData = {
            copperContractType: $existingInput.attr('data-copper-contract-type') || '',
            copperContractNo: $existingInput.attr('data-copper-contract-no') || '',
            copperCondition: $existingInput.attr('data-copper-condition') || '',
            copperCurrency: $existingInput.attr('data-copper-currency') || '',
            conversionRate: $existingInput.attr('data-conversion-rate') || '',
            copperBase: $existingInput.attr('data-copper-base') || '',
            premium: $existingInput.attr('data-premium') || '',
            convertedCopperPrice: $existingInput.attr('data-converted-copper-price') || '',
            zeroBase: $existingInput.attr('data-zero-base') || '',
            orderUnitPrice: $existingInput.attr('data-order-unit-price') || '',
            orderAmount: $existingInput.attr('data-order-amount') || ''
        };
    }

    // 创建编辑行HTML
    var editRowHtml = `
        <td><input type="text" class="layui-input" id="orderSerialNum" name="orderSerialNum" value="${orderSerialNum}" disabled></td>
        <td><input type="text" class="layui-input" id="prepareDate" name="prepareDate" value="${prepareDate}" placeholder="请输入准备日期"></td>
        <td><input type="text" class="layui-input date-picker outbound-date" id="outboundDate" name="outboundDate" value="${outboundDate}" placeholder="请选择出库日期"></td>
        <td><input type="text" class="layui-input date-picker arrival-date" id="arrivalDate" name="arrivalDate" value="${arrivalDate}" placeholder="请选择到货日期"></td>
        <td><select class="layui-select" id="modelNumber" name="modelNumber" lay-search lay-filter="modelNumberFn" value="${modelNumber}"><option value="">请选择型号</option></select></td>
        <td><input type="text" class="layui-input" id="barcode" name="barcode" value="${barcode}" placeholder="请输入条码"></td>
        <td><input type="text" class="layui-input" id="size" name="size" value="${size}" disabled></td>
        <td><input type="text" class="layui-input" id="wireSpool" name="wireSpool" value="${wireSpool}" disabled></td>
        <td><input type="text" class="layui-input" id="partNumber" name="partNumber" value="${partNumber}" disabled></td>
        <td><input type="number" class="layui-input" id="orderQuantity" name="orderQuantity" onblur="calculateRemainingQuantity(this)" value="${orderQuantity}" placeholder="请输入订单数量"></td>
        <td><input type="number" class="layui-input" id="deliveredQuantity" name="deliveredQuantity" onblur="calculateRemainingQuantity(this)" value="${deliveredQuantity}" placeholder="请输入已送数量"></td>
        <td><input type="number" class="layui-input" id="remainingQuantity" name="remainingQuantity" value="${remainingQuantity}" disabled></td>
        <td><input type="text" class="layui-input order-amount-input" readonly onclick="openCopperFieldsModal(this)" value="${orderAmount}" placeholder="点击设置接单金额" title="点击设置接单金额" style="cursor: pointer; background-color: #f8f8f8;" data-copper-contract-type="${copperData.copperContractType}" data-copper-contract-no="${copperData.copperContractNo}" data-copper-condition="${copperData.copperCondition}" data-copper-currency="${copperData.copperCurrency}" data-conversion-rate="${copperData.conversionRate}" data-copper-base="${copperData.copperBase}" data-premium="${copperData.premium}" data-converted-copper-price="${copperData.convertedCopperPrice}" data-zero-base="${copperData.zeroBase}" data-order-unit-price="${copperData.orderUnitPrice}" data-order-amount="${copperData.orderAmount}"></td>
        <td><input type="text" class="layui-input" id="detailRemark" name="detailRemark" value="${detailRemark}" placeholder="请输入详情备注"></td>
        <td class="single-row-operation">
            <div class="layui-btn-group" style="margin-top:5px;">
                <input type="hidden" name="id" value="${id}">
                <button type="button" class="layui-btn layui-btn-primary layui-btn-sm quick-save-btn" title="点击整行快速保存" onclick="saveSubOrderRow(this, ${isEdit})">
                    <i class="layui-icon layui-icon-ok"></i>
                </button>
                <button type="button" class="layui-btn layui-btn-primary layui-btn-sm cancel-row-btn" title="取消" onclick="cancelSubOrderRow(this, ${isEdit})">
                    <i class="layui-icon layui-icon-close"></i>
                </button>
            </div>
        </td>
    `;

    // 更新行内容
    $row.html(editRowHtml);

    // 添加编辑状态样式
    $row.removeClass('sub-order-row');
    $row.addClass('single-edit-row');

    // 初始化日期选择器
    initRowDatePickers($row);

    console.log('productCodeList :>> ', productCodeList);
     // 初始化型号选择框
     var $modelNumber = $('#modelNumber');
     $modelNumber.empty();
     $modelNumber.append('<option value="">请选择型号</option>');
     productCodeList.forEach(function(item) {
         $modelNumber.append('<option value="' + item + '">' + item + '</option>');
     });

     if (isEdit) {
        $modelNumber.val(modelNumber);
     }
 
     // 重新渲染select
     form.render('select');
 
     form.on('select(modelNumberFn)', function(data) {
        // 获取产品信息
        getProductInfo(data.value);

        // 产品代码变更时，尝试查询产品价格（编辑页面）
        var customerCode = $('#demandCustomerCode').val();
        var arrivalDate = $('#subOrderTableBody tr:first-child [name="arrivalDate"]').val();

        if (customerCode && arrivalDate && data.value) {
            console.log('编辑页面产品代码变更，尝试查询产品价格:', {
                customerCode: customerCode,
                productCode: data.value,
                arrivalDate: arrivalDate
            });
            queryProductPriceForEdit(customerCode, data.value, arrivalDate);
        }
     });
 
    // 显示编辑提示
    layer.msg('正在编辑行数据，完成后请点击保存', {icon: 0, time: 2000});
}

// 计算剩余数量
function calculateRemainingQuantity(input) {
    var $row = $(input).closest('tr');
    var orderQuantity = parseFloat($row.find('[name="orderQuantity"]').val()) || 0;
    var deliveredQuantity = parseFloat($row.find('[name="deliveredQuantity"]').val()) || 0;
    var remainingQuantity = orderQuantity - deliveredQuantity;
    $row.find('[name="remainingQuantity"]').val(remainingQuantity);
}

// 为指定行初始化日期选择器
function initRowDatePickers($row) {
    layui.use('laydate', function() {
        var laydate = layui.laydate;

        // 为出库日期输入框初始化日期选择器
        var $outboundDateInput = $row.find('[name="outboundDate"]');
        if ($outboundDateInput.length > 0) {
            laydate.render({
                elem: $outboundDateInput[0],
                type: 'date',
                format: 'yyyy-MM-dd',
                trigger: 'click'
            });
        }

        // 为到货日期输入框初始化日期选择器
        var $arrivalDateInput = $row.find('[name="arrivalDate"]');
        if ($arrivalDateInput.length > 0) {
            laydate.render({
                elem: $arrivalDateInput[0],
                type: 'date',
                format: 'yyyy-MM-dd',
                trigger: 'click'
            });
        }

        // 为准备日期输入框初始化日期选择器
        var $prepareDateInput = $row.find('[name="prepareDate"]');
        if ($prepareDateInput.length > 0) {
            laydate.render({
                elem: $prepareDateInput[0],
                type: 'date',
                format: 'yyyy-MM-dd',
                trigger: 'click'
            });
        }
    });
}

// 初始化日期选择器
function initDatePickers() {
    layui.use('laydate', function() {
        var laydate = layui.laydate;

        // 初始化已有行的日期选择器
        initExistingDatePickers();
    });
}

// 初始化新行的日期选择器
function initNewRowDatePickers() {
    layui.use('laydate', function() {
        var laydate = layui.laydate;

        // 获取最后一行
        var $lastRow = $('#subOrderTableBody tr:last-child');

        // 为最后一行的出库日期输入框初始化日期选择器
        var $outboundDateInput = $lastRow.find('[name="outboundDate"]');
        if ($outboundDateInput.length > 0) {
            laydate.render({
                elem: $outboundDateInput[0],
                type: 'date',
                format: 'yyyy-MM-dd',
                trigger: 'click'
            });
        }

        // 为最后一行的到货日期输入框初始化日期选择器
        var $arrivalDateInput = $lastRow.find('[name="arrivalDate"]');
        if ($arrivalDateInput.length > 0) {
            laydate.render({
                elem: $arrivalDateInput[0],
                type: 'date',
                format: 'yyyy-MM-dd',
                trigger: 'click'
            });
        }
    });
}

function addSubOrder() {
    // 创建一个新的只读行
    var newRow = `
        <tr class="sub-order-row" data-serial="${currentOrderSerialNum}" data-outbound="" data-arrival="" data-prepare="" data-model="" data-size="" data-wire="" data-part="" data-order-qty="" data-delivered-qty="" data-remaining-qty="" data-detail-remark="">
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td class="single-row-operation">
                <div class="layui-btn-group">
                    <button type="button" class="layui-btn layui-btn-primary layui-btn-sm" onclick="editSubOrderRow(this)" title="编辑">
                        <i class="layui-icon layui-icon-form"></i>
                    </button>
                    <button type="button" class="layui-btn layui-btn-primary layui-btn-sm" onclick="removeSubOrder(this)" title="删除">
                        <i class="layui-icon layui-icon-delete"></i>
                    </button>
                </div>
            </td>
        </tr>
    `;

    // 添加行到表格
    $("#subOrderTableBody").append(newRow);

    // 自动触发编辑模式
    var $newRow = $('#subOrderTableBody tr:last-child');
    editSubOrderRow($newRow.find('button:first')[0]);
}

// 删除子订单
function removeSubOrder(button, orderSerialNum) {
    layer.confirm('确定要删除这一行吗？', {icon: 3, title:'提示'}, function(index){
        $.ajax({
            url: baselocation + '/order/orderEntry/removeSubOrder',
            type: 'post',
            data: {
                orderNo: $('#orderNo').val(),
                orderSerialNum: orderSerialNum
            },
            success: function(result) {
                if (result.code == 1) {
                    layer.msg(result.data || '删除成功', {icon: 1});
                    subOrderSerialNumList = subOrderSerialNumList.filter(serialNum => serialNum != orderSerialNum);
                    $(button).closest('tr').remove();
                    layer.close(index);
                } else {
                    layer.msg(result ? (result.data || '删除失败') : '删除失败', {icon: 2});
                }
            }
        });
    });
}

function closeAll() {
    parent.layer.closeAll();
}

// 初始化已有行的日期选择器
function initExistingDatePickers() {
    layui.use('laydate', function(){
        var laydate = layui.laydate;

        // 初始化所有出库日期选择器 - 修复选择器语法
        $('#subOrderTableBody tr').each(function() {
            // 直接使用当前行中的元素，而不是使用eq选择器
            var $outboundDateInput = $(this).find('[name="outboundDate"]');
            var $arrivalDateInput = $(this).find('[name="arrivalDate"]');

            if ($outboundDateInput.length > 0) {
                laydate.render({
                    elem: $outboundDateInput[0], // 使用DOM元素而不是选择器字符串
                    type: 'date',
                    format: 'yyyy-MM-dd',
                    trigger: 'click' // 确保点击时打开日期选择器
                });
            }

            if ($arrivalDateInput.length > 0) {
                laydate.render({
                    elem: $arrivalDateInput[0], // 使用DOM元素而不是选择器字符串
                    type: 'date',
                    format: 'yyyy-MM-dd',
                    trigger: 'click', // 确保点击时打开日期选择器
                    done: function(value) {
                        // 到货日期变更时，尝试查询产品价格（编辑页面）
                        var customerCode = $('#demandCustomerCode').val();
                        var productCode = $($arrivalDateInput[0]).closest('tr').find('[name="modelNumber"]').val();

                        if (customerCode && productCode && value) {
                            console.log('编辑页面到货日期变更，尝试查询产品价格:', {
                                customerCode: customerCode,
                                productCode: productCode,
                                arrivalDate: value
                            });
                            queryProductPriceForEdit(customerCode, productCode, value);
                        }
                    }
                });
            }
        });
    });
}

// 加载附件信息
function loadAttachmentInfo(attachmentId) {
    if (!attachmentId) return;

    // 处理可能的逗号问题
    if (attachmentId.indexOf(',') !== -1) {
        console.log('附件ID包含逗号，原始值:', attachmentId);
        attachmentId = attachmentId.split(',')[0];
        console.log('处理后的附件ID:', attachmentId);
    }

    // 处理可能的空格问题
    attachmentId = attachmentId.trim();

    // 记录日志，便于调试
    console.log('最终使用的附件ID:', attachmentId);

    // 从服务器获取文件信息
    $.ajax({
        url: baselocation + '/common/file/info',
        type: 'GET',
        data: { id: attachmentId },
        dataType: 'json',
        success: function(res) {
            console.log('获取文件信息成功:', res);
            if (res.code === 1 && res.data) {
                // 更新附件信息显示
                updateAttachmentDisplay(res.data);
            } else {
                console.error('获取文件信息失败:', res.message || '未知错误');
                // 使用默认预览
                updateAttachmentDisplay({
                    id: attachmentId,
                    fileName: '附件文件'
                });
            }
        },
        error: function(xhr, _, error) {
            console.error('获取文件信息请求失败:', error, xhr.responseText);
            // 使用默认预览
            updateAttachmentDisplay({
                id: attachmentId,
                fileName: '附件文件'
            });
        }
    });
}

// 初始化附件上传功能
function initAttachmentUpload() {
    try {
        layui.use('upload', function() {
            var upload = layui.upload;
            var layer = layui.layer;

            // 初始化删除按钮点击事件
            $('#removeAttachment').on('click', function(e) {
                e.stopPropagation(); // 阻止事件冒泡
                if (currentAttachmentId) {
                    removeAttachment(currentAttachmentId);
                }
            });

            // 渲染上传组件
            var uploadInst = upload.render({
                elem: '#attachmentUpload',
                url: baselocation + '/common/upload/file', // 替换为实际的上传接口
                accept: 'file', // 允许上传所有类型文件
                exts: 'jpg|jpeg|png|gif|doc|docx|pdf|xls|xlsx|csv|txt', // 限制文件类型
                size: 10240, // 限制文件大小(KB)
                auto: true, // 自动上传
                multiple: false, // 单个文件上传
                choose: function(obj) {
                    // 选择文件时立即更新文件名和类型
                    try {
                        // 重置变量，确保每次都是最新的
                        currentOriginalFileName = '';
                        currentFileType = '';

                        // 获取选择的文件对象
                        obj.preview(function(index, file, result) {
                            console.log('选择了新文件:', file.name);
                            // 立即更新文件名和类型
                            currentOriginalFileName = file.name || '未知文件';
                            currentFileType = getFileType(currentOriginalFileName);

                            // 关键修复：立即更新当前文件对象引用
                            window.lastUploadedFile = file;
                            // 保存到全局变量中，确保多处可用
                            window.currentFileObject = file;
                        });
                    } catch (e) {
                        console.error('选择文件时更新文件名出错:', e);
                    }
                },
                before: function(obj) {
                    // 显示上传中的loading
                    layer.load();
                },
                done: function(res, index, upload) {
                    layer.closeAll('loading'); // 关闭loading

                    // 检查响应是否有效
                    if (!res) {
                        console.error('上传响应为空');
                        // 使用默认响应
                        var fakeRes = {
                            code: 1,
                            message: "上传成功(模拟)",
                            data: {
                                id: "temp_" + new Date().getTime(),
                                // 修复：确保使用当前选择的文件名
                                fileName: currentOriginalFileName || '未知文件',
                                fileObject: window.currentFileObject || window.lastUploadedFile,
                                originalFileName: currentOriginalFileName,
                                fileType: currentFileType
                            }
                        };

                        // 处理模拟响应
                        attachmentUploaded = true;
                        currentAttachmentId = fakeRes.data.id;
                        updateAttachmentDisplay(fakeRes.data);
                        showAttachmentPreview(fakeRes.data);
                        return;
                    }

                    if (res.code === 1) {
                        // 将当前文件名和类型添加到响应数据中，确保一致性
                        if (!res.data) res.data = {};
                        // 修复：始终使用当前选择的文件名，覆盖服务器返回的文件名
                        res.data.fileName = currentOriginalFileName || res.data.fileName || '未知文件';
                        res.data.originalFileName = currentOriginalFileName;
                        res.data.fileType = currentFileType;

                        // 上传成功
                        attachmentUploaded = true;
                        currentAttachmentId = res.data.id || ("temp_" + new Date().getTime());

                        // 重要：添加文件对象到返回数据中，确保预览时能访问到最新上传的文件
                        res.data.fileObject = window.currentFileObject || window.lastUploadedFile;

                        // 更新附件信息显示
                        updateAttachmentDisplay(res.data);

                        // 如果使用的是本地预览，立即显示预览
                        if (window.currentFileObject || window.lastUploadedFile) {
                            showAttachmentPreview(res.data);
                        }
                    } else {
                        // 上传失败
                        layer.msg('附件上传失败：' + (res.message || '未知错误'), {icon: 2});
                        console.error('上传失败:', res);
                    }
                },
                error: function() {
                    layer.closeAll('loading'); // 关闭loading
                    console.log('上传接口错误，使用模拟数据');

                    // 模拟上传成功的响应
                    var fakeRes = {
                        code: 1,
                        message: "上传成功(模拟)",
                        data: {
                            id: "temp_" + new Date().getTime(),
                            // 修复：确保使用当前选择的文件名
                            fileName: currentOriginalFileName || '未知文件',
                            size: window.currentFileObject ? window.currentFileObject.size : (window.lastUploadedFile ? window.lastUploadedFile.size : (1024 * 100)),
                            fileUrl: '',
                            fileObject: window.currentFileObject || window.lastUploadedFile,  // 重要：使用最新的文件对象
                            originalFileName: currentOriginalFileName,
                            fileType: currentFileType
                        }
                    };

                    // 上传成功
                    attachmentUploaded = true;
                    currentAttachmentId = fakeRes.data.id;

                    // 更新附件信息显示
                    updateAttachmentDisplay(fakeRes.data);

                    // 如果使用的是本地预览，立即显示预览
                    if (window.currentFileObject || window.lastUploadedFile) {
                        showAttachmentPreview(fakeRes.data);
                    }
                }
            });
        });
    } catch (error) {
        console.error('初始化附件上传功能出错:', error);
        layer.msg('初始化上传功能失败，请刷新页面重试', {icon: 2});
    }
}

function selectAttachment() {
    // 打开附件选择弹框
    layer.open({
        type: 1,
        title: '',
        area: ['500px', '350px'],
        content: '<div class="layui-form" style="padding: 0; height: 100%; width: 100%;">' +
                 '<div class="layui-form-item" style="margin: 0; height: 100%;">' +
                 '<div class="layui-input-block" style="margin: 0; height: 100%; width: 100%;">' +
                 '<div id="attachmentList" style="height: 100%; width: 100%;">' +
                 '<table class="layui-table" style="margin: 0; width: 100%;">' +
                 '<thead><tr><th width="15%">选择</th><th width="85%">附件名称</th></tr></thead>' +
                 '<tbody id="attachmentListBody">' +
                 '</tbody>' +
                 '</table>' +
                 '</div>' +
                 '</div>' +
                 '</div>' +
                 '</div>',
        success: function(layero, index) {
            // 加载示例数据
            loadAttachmentList();
            
            // 渲染表单元素
            layui.form.render();
        },
        btn: ['确定', '取消'],
        yes: function(index, layero) {
            // 获取选中的附件
            var selectedAttachmentId = $('input[name="attachment"]:checked').val();
            if (selectedAttachmentId) {
                // 获取选中附件的信息
                var selectedFileName = $('input[name="attachment"]:checked').data('filename');
                
                // 构造文件数据
                var fileData = {
                    id: selectedAttachmentId,
                    fileName: selectedFileName,
                    originalFileName: selectedFileName,
                    fileType: getFileType(selectedFileName),
                    size: 1024 * 100,
                    fileUrl: ''
                };
                
                // 更新当前附件ID
                currentAttachmentId = selectedAttachmentId;
                // 标记已上传附件
                attachmentUploaded = true;
                // 更新附件显示
                updateAttachmentDisplay(fileData);
                
                layer.close(index);
            } else {
                layer.msg('请选择一个附件', {icon: 2});
            }
        }
    });
}

// 加载附件列表
function loadAttachmentList() {
    // 示例数据
    var exampleAttachments = [
        {id: 'attach_001', fileName: '订单导入模板.xlsx'},
        {id: 'attach_002', fileName: '客户订单20231001.xlsx'},
        {id: 'attach_003', fileName: '产品清单.pdf'},
        {id: 'attach_004', fileName: '客户需求说明.docx'},
        {id: 'attach_005', fileName: '订单变更申请.pdf'},
        {id: 'attach_006', fileName: '产品规格书.pdf'},
        {id: 'attach_007', fileName: '价格表.xlsx'},
        {id: 'attach_008', fileName: '客户反馈.docx'},
        {id: 'attach_009', fileName: '生产计划.xlsx'},
        {id: 'attach_010', fileName: '质检报告.pdf'}
    ];
    
    var html = '';
    for (var i = 0; i < exampleAttachments.length; i++) {
        var attachment = exampleAttachments[i];
        html += '<tr>' +
                '<td><input type="radio" name="attachment" value="' + attachment.id + '" data-filename="' + attachment.fileName + '" title="" lay-skin="primary"></td>' +
                '<td>' + attachment.fileName + '</td>' +
                '</tr>';
    }
    
    $('#attachmentListBody').html(html);
    // 重新渲染表单
    layui.form.render('radio');
}

// 更新附件信息显示
function updateAttachmentDisplay(fileData) {
    if (!fileData) {
        console.error('更新附件显示失败：fileData为空');
        return;
    }

    // 显示文件信息 - 修复：使用与预览区域相同的文件名获取逻辑，确保一致性
    var fileName = fileData.fileName || fileData.originalFileName || currentOriginalFileName || '未知文件';
    $('#attachmentFileName').text(fileName);
    $('#attachmentId').val(fileData.id || '');
    $('#attachmentInfo').show();

    // 显示预览
    showAttachmentPreview(fileData);
}

// 显示附件预览 - 更紧凑的样式
function showAttachmentPreview(fileData) {
    var $preview = $('#attachmentPreview').find('div');
    if (!$preview.length) {
        console.error('找不到预览容器元素');
        return;
    }

    $preview.empty();

    if (!fileData) {
        console.error('文件数据为空，无法预览');
        $preview.html('<div class="error-message">文件数据错误，无法预览</div>');
        $('#attachmentPreview').show();
        return;
    }

    // 优先使用fileData中的文件名，其次使用全局变量
    var fileName = fileData.fileName || fileData.originalFileName || currentOriginalFileName || '未知文件';
    console.log('预览文件名:', fileName);

    // 更新附件标题
    $('#attachmentTitle').html('附件预览: ' + fileName);

    try {
        // 直接添加预览内容容器，不再单独显示文件名
        $preview.append('<div id="previewContent" style="padding: 5px;"></div>');

        var $previewContent = $('#previewContent');
        if (!$previewContent.length) {
            throw new Error('预览内容容器创建失败');
        }

        // 获取文件类型
        var fileType = fileData.fileType || currentFileType || getFileType(fileName);

        // 优先使用传入数据中的文件对象，其次使用全局专门存放的当前文件对象，最后使用lastUploadedFile
        var fileToPreview = fileData.fileObject || window.currentFileObject || window.lastUploadedFile;

        if (fileToPreview) {
            // 根据文件类型显示不同的预览
            if (fileType === 'image' || /\.(jpg|jpeg|png|gif)$/i.test(fileName)) {
                // 图片预览 - 更紧凑的样式
                var reader = new FileReader();
                reader.onload = function(e) {
                    $previewContent.html(
                        '<div style="display:flex; align-items:center; margin-bottom:5px; font-size:13px;"><i class="layui-icon layui-icon-picture" style="margin-right:5px;"></i>图片预览：<span style="margin-left:5px; font-weight:bold;">' + fileName + '</span></div>' +
                        '<img src="' + e.target.result + '" style="max-width: 100%; max-height: 200px;" />'
                    );
                };
                reader.onerror = function(e) {
                    console.error('读取图片出错:', e);
                    $previewContent.html('<div style="display:flex; align-items:center; font-size:13px;"><i class="layui-icon layui-icon-close" style="margin-right:5px; color:#FF5722;"></i>图片加载失败</div>');
                };
                reader.readAsDataURL(fileToPreview);
            } else if (fileType === 'pdf' || /\.pdf$/i.test(fileName)) {
                // PDF预览 - 更紧凑的样式
                var reader = new FileReader();
                reader.onload = function(e) {
                    $previewContent.html(
                        '<div style="display:flex; align-items:center; margin-bottom:5px; font-size:13px;"><i class="layui-icon layui-icon-read" style="margin-right:5px;"></i>PDF文件预览：<span style="margin-left:5px; font-weight:bold;">' + fileName + '</span></div>' +
                        '<div class="pdf-container" style="width:100%; height:600px; background-color:#f8f8f8;">' +
                        '  <iframe src="' + e.target.result + '" type="application/pdf" width="100%" height="100%" style="border:none;"></iframe>' +
                        '</div>'
                    );
                };
                reader.onerror = function(e) {
                    console.error('读取PDF出错:', e);
                    $previewContent.html(
                        '<div style="display:flex; align-items:center; margin-bottom:5px; font-size:13px;"><i class="layui-icon layui-icon-close" style="margin-right:5px; color:#FF5722;"></i>PDF文件加载失败 <button class="layui-btn layui-btn-xs layui-btn-danger" onclick="downloadAttachment(\'' + fileData.id + '\')" style="margin-left:10px;"><i class="layui-icon layui-icon-download-circle"></i> 下载</button></div>'
                    );
                };
                reader.readAsDataURL(fileToPreview);
            } else if (fileType === 'excel' || /\.(xls|xlsx|csv)$/i.test(fileName)) {
                // Excel文件预览 - 使用SheetJS库 - 更紧凑的布局
                $previewContent.html(
                    '<div style="display:flex; align-items:center; margin-bottom:5px; font-size:13px;"><i class="layui-icon layui-icon-form" style="margin-right:5px;"></i>Excel文件预览：<span style="margin-left:5px; font-weight:bold;">' + fileName + '</span><span id="excel-loading" style="margin-left:10px;"><i class="layui-icon layui-icon-loading layui-anim layui-anim-rotate layui-anim-loop" style="font-size:14px;"></i> 加载中...</span></div>' +
                    '<div id="excel-preview-container" style="width:100%; overflow-x:auto; display:none;"></div>' +
                    '<div class="layui-row" style="margin-top:5px;">'
                );

                // 检查是否已加载SheetJS库
                if (typeof XLSX === 'undefined') {
                    // 动态加载SheetJS库
                    var script = document.createElement('script');
                    script.src = 'https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js';
                    script.onload = function() {
                        parseExcelFile(fileToPreview);
                    };
                    script.onerror = function() {
                        $('#excel-loading').html(
                            '<i class="layui-icon layui-icon-close-fill" style="font-size:30px;color:#FF5722"></i>' +
                            '<p style="margin-top:10px">无法加载Excel预览组件，请直接下载文件查看</p>'
                        );
                    };
                    document.head.appendChild(script);
                } else {
                    parseExcelFile(fileToPreview);
                }
            } else {
                // 其他文件类型 - 更紧凑的布局
                $previewContent.html(
                    '<div style="display:flex; align-items:center; margin-bottom:5px; font-size:13px;"><i class="layui-icon layui-icon-file" style="margin-right:5px;"></i>文件预览：<span style="margin-left:5px; font-weight:bold;">' + fileName + '</span><button class="layui-btn layui-btn-xs" onclick="downloadAttachment(\'' + fileData.id + '\')" style="margin-left:10px;"><i class="layui-icon layui-icon-download-circle"></i> 下载</button></div>'
                );
            }
        } else if (fileData.id) {
            // 如果有文件ID但没有本地文件对象，尝试从服务器获取预览
            var fileType = getFileType(fileName);

            if (fileType === 'image' || /\.(jpg|jpeg|png|gif)$/i.test(fileName)) {
                // 图片文件预览 - 更紧凑的样式
                var imageUrl = fileData.fileUrl || (baselocation + '/common/download?id=' + fileData.id);
                $previewContent.html(
                    '<div style="font-size:14px; font-weight:bold; margin-bottom:5px;"><i class="layui-icon layui-icon-picture" style="margin-right:5px;"></i>图片预览</div>' +
                    '<div style="width:100%; text-align:center; padding:5px;">' +
                    '  <img src="' + imageUrl + '" style="max-width:100%; max-height:350px; border:1px solid #e6e6e6;" />' +
                    '</div>'
                );
            } else if (fileType === 'pdf' || /\.pdf$/i.test(fileName)) {
                // 直接在线预览PDF文件 - 更紧凑的样式
                var fileUrl = baselocation + '/common/download?id=' + fileData.id + '&iframe=true';
                $previewContent.html(
                    '<div style="width:100%; height:450px; border:1px solid #e6e6e6;">' +
                    '  <iframe src="' + fileUrl + '" width="100%" height="100%" style="border:none;" allowfullscreen></iframe>' +
                    '</div>'
                );
            } else if (fileType === 'excel' || /\.(xls|xlsx|csv)$/i.test(fileName)) {
                // 使用与单行录入页面相同的Excel预览方式 - 更紧凑的样式
                $previewContent.html(
                    '<div style="padding:10px; text-align:center;" id="excel-loading">' +
                    '  <i class="layui-icon layui-icon-loading layui-anim layui-anim-rotate layui-anim-loop" style="font-size:24px;"></i>' +
                    '  <p style="margin-top:5px; font-size:13px;">正在加载Excel内容...</p>' +
                    '</div>' +
                    '<div id="excel-preview-container" style="width:100%; overflow-x:auto; display:none;"></div>'
                );

                // 获取文件并预览
                var downloadUrl = baselocation + '/common/download?id=' + fileData.id;
                previewExcelFile(downloadUrl, fileName);
            } else {
                // 显示图标和下载按钮 - 更紧凑的样式
                var iconClass = getFileIconClass(fileType);
                $previewContent.html(
                    '<div style="padding:10px; text-align:center; display:flex; align-items:center; justify-content:center;">' +
                    '  <i class="layui-icon ' + iconClass + '" style="font-size:24px; color:#1E9FFF; margin-right:10px;"></i>' +
                    '  <span style="font-size:14px;">' + fileName + '</span>' +
                    '  <button class="layui-btn layui-btn-sm" onclick="downloadAttachment(\'' + fileData.id + '\')" style="margin-left:15px;">' +
                    '    <i class="layui-icon layui-icon-download-circle"></i> 下载' +
                    '  </button>' +
                    '</div>'
                );
            }
        } else {
            // 如果没有本地文件对象引用，显示紧凑的提示信息
            $previewContent.html(
                '<div style="padding:10px; text-align:center; display:flex; align-items:center; justify-content:center;">' +
                '  <i class="layui-icon layui-icon-file" style="font-size:24px; color:#1E9FFF; margin-right:10px;"></i>' +
                '  <span style="font-size:14px;">' + fileName + '</span>' +
                '  <button class="layui-btn layui-btn-sm" onclick="downloadAttachment(\'' + fileData.id + '\')" style="margin-left:15px;">' +
                '    <i class="layui-icon layui-icon-download-circle"></i> 下载' +
                '  </button>' +
                '</div>'
            );
        }
    } catch (error) {
        console.error('预览生成过程出错:', error);
        $preview.html(
            '<div style="padding:10px; text-align:center;">' +
            '  <i class="layui-icon layui-icon-close-fill" style="font-size:24px;color:#FF5722"></i>' +
            '  <p style="margin-top:5px; font-size: 13px;">预览失败: ' + error.message + '</p>' +
            '</div>'
        );
    }

    // 显示预览区域
    $('#attachmentPreview').show();
}

// 删除附件
function removeAttachment(fileId) {
    if (!fileId) return;

    layer.confirm('确定要删除该附件吗？', {
        btn: ['确定', '取消']
    }, function() {
        // 在实际项目中，这里应该调用接口删除附件
        // 这里简化处理，直接清空附件信息
        $('#attachmentFileName').text('未选择文件');
        $('#attachmentId').val('');
        $('#attachmentInfo').hide();
        $('#attachmentPreview').hide();

        attachmentUploaded = false;
        currentAttachmentId = '';

        layer.msg('附件已删除', {icon: 1});
    });
}

// 下载附件
function downloadAttachment(fileId) {
    // 打印调试信息
    console.log('下载文件ID:', fileId);
    console.log('文件ID类型:', typeof fileId);

    // 去除可能的空格和特殊字符
    if (typeof fileId === 'string') {
        fileId = fileId.trim();
        // 检查是否包含逗号，如果有则只取第一部分
        if (fileId.indexOf(',') !== -1) {
            console.log('文件ID包含逗号，原始值:', fileId);
            fileId = fileId.split(',')[0];
            console.log('处理后的文件ID:', fileId);
        }
        // 再次去除可能的空格
        fileId = fileId.trim();
        console.log('最终使用的文件ID:', fileId);
    }

    if (!fileId || fileId === '') {
        layer.msg('未找到附件', {icon: 2});
        return;
    }

    // 显示加载中提示
    var loadingIndex = layer.load(1, {
        shade: [0.1, '#fff']
    });

    // 先获取文件信息
    $.ajax({
        url: baselocation + '/common/file/info',
        type: 'GET',
        data: { id: fileId },
        dataType: 'json',
        success: function(res) {
            layer.close(loadingIndex);
            console.log('获取文件信息成功:', res);
            if (res.code === 1 && res.data) {
                // 打开下载链接（显式不包含iframe参数以确保是下载而非预览）
                var downloadUrl = baselocation + '/common/download?id=' + fileId + '&download=true';
                console.log('下载链接:', downloadUrl);
                window.open(downloadUrl);
            } else {
                layer.msg('获取文件信息失败: ' + (res.message || '未知错误'), {icon: 2});
            }
        },
        error: function(xhr, _, error) {
            layer.close(loadingIndex);
            console.error('获取文件信息请求失败:', error, xhr.responseText);
            layer.msg('获取文件信息失败: ' + error, {icon: 2});
        }
    });
}

// 获取文件类型
function getFileType(fileName) {
    if (!fileName) return 'unknown';

    var ext = fileName.split('.').pop().toLowerCase();

    if (/^(jpg|jpeg|png|gif|bmp)$/.test(ext)) {
        return 'image';
    } else if (/^(doc|docx|rtf)$/.test(ext)) {
        return 'word';
    } else if (/^(xls|xlsx|csv)$/.test(ext)) {
        return 'excel';
    } else if (ext === 'pdf') {
        return 'pdf';
    } else if (/^(txt|json|xml|html|css|js)$/.test(ext)) {
        return 'text';
    } else {
        return 'unknown';
    }
}

/**
 * 获取文件图标样式
 * @param {string} fileType 文件类型
 * @returns {string} 图标样式类
 */
function getFileIconClass(fileType) {
    switch (fileType) {
        case 'word':
            return 'layui-icon-file-b';
        case 'excel':
            return 'layui-icon-table';
        case 'pdf':
            return 'layui-icon-read';
        case 'text':
            return 'layui-icon-file-b';
        case 'image':
            return 'layui-icon-picture';
        default:
            return 'layui-icon-file';
    }
}

/**
 * 预加载SheetJS库
 */
function preloadSheetJSLibrary() {
    if (typeof XLSX === 'undefined') {
        console.log('预加载SheetJS库');
        var script = document.createElement('script');
        script.src = 'https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js';
        script.async = true;
        script.onload = function() {
            console.log('SheetJS库加载完成');
        };
        script.onerror = function() {
            console.error('SheetJS库加载失败');
        };
        document.head.appendChild(script);
    }
}

/**
 * 预览Excel文件
 * @param {string} fileUrl 文件URL
 * @param {string} fileName 文件名
 */
function previewExcelFile(fileUrl, fileName) {
    // 检查是否已加载SheetJS库
    if (typeof XLSX === 'undefined') {
        // 动态加载SheetJS库
        var script = document.createElement('script');
        script.src = 'https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js';
        script.onload = function() {
            fetchAndParseExcel(fileUrl, fileName);
        };
        script.onerror = function() {
            $('#excel-loading').html(
                '<i class="layui-icon layui-icon-close-fill" style="font-size:30px;color:#FF5722"></i>' +
                '<p style="margin-top:10px">无法加载Excel预览组件，请直接下载文件查看</p>'
            );
        };
        document.head.appendChild(script);
    } else {
        fetchAndParseExcel(fileUrl, fileName);
    }
}

/**
 * 获取并解析Excel文件
 * @param {string} fileUrl 文件URL
 * @param {string} fileName 文件名
 */
function fetchAndParseExcel(fileUrl, fileName) {
    // 记录文件名信息
    console.log('开始解析Excel文件:', fileName);
    // 使用XMLHttpRequest获取文件
    var xhr = new XMLHttpRequest();
    xhr.open('GET', fileUrl, true);
    xhr.responseType = 'arraybuffer';

    xhr.onload = function() {
        if (xhr.status === 200) {
            try {
                var arrayBuffer = xhr.response;
                var data = new Uint8Array(arrayBuffer);
                var workbook = XLSX.read(data, {type: 'array'});

                // 表格样式 - 更紧凑的样式
                var tableStyles = '<style>' +
                    '.excel-table { width: 100%; border-collapse: collapse; border: 1px solid #ddd; font-size: 13px; }' +
                    '.excel-table th, .excel-table td { border: 1px solid #ddd; padding: 4px; text-align: left; }' +
                    '.excel-table tr:nth-child(even) { background-color: #f2f2f2; }' +
                    '.excel-table th { padding: 6px 4px; background-color: #4CAF50; color: white; font-weight: bold; }' +
                    '.excel-table td, .excel-table th { min-width: 70px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap; }' +
                    // 直接应用到原生表格元素，确保样式覆盖
                    'table { border-collapse: collapse !important; }' +
                    'th, td { border: 1px solid #ddd !important; padding: 4px !important; font-size: 13px !important; }' +
                    'th { background-color: #f2f2f2 !important; font-weight: bold !important; }' +
                    '</style>';

                // 如果工作簿有多个工作表，添加选项卡
                if (workbook.SheetNames.length > 1) {
                    var sheetTabs = '<div class="layui-tab layui-tab-brief"><ul class="layui-tab-title">';

                    for (var i = 0; i < workbook.SheetNames.length; i++) {
                        var sheetName = workbook.SheetNames[i];
                        var activeClass = (i === 0) ? 'layui-this' : '';
                        sheetTabs += '<li class="' + activeClass + '" data-sheet="' + sheetName + '">' + sheetName + '</li>';
                    }

                    sheetTabs += '</ul></div>';

                    // 添加工作表选项卡
                    $('#excel-preview-container').before(sheetTabs);

                    // 绑定选项卡切换事件
                    $('.layui-tab-title li').click(function() {
                        var sheetName = $(this).data('sheet');
                        var worksheet = workbook.Sheets[sheetName];
                        var htmlTable = XLSX.utils.sheet_to_html(worksheet, {
                            id: 'excel-table',
                            className: 'excel-table',
                            editable: false,
                            header: tableStyles
                        });

                        $('#excel-preview-container').html(htmlTable);
                        setTimeout(function() {
                            applyTableEnhancements();
                        }, 100);
                        $('.layui-tab-title li').removeClass('layui-this');
                        $(this).addClass('layui-this');
                    });
                }

                // 默认显示第一个工作表
                var firstSheet = workbook.SheetNames[0];
                var worksheet = workbook.Sheets[firstSheet];

                // 将工作表转换为HTML表格
                var htmlTable = XLSX.utils.sheet_to_html(worksheet, {
                    id: 'excel-table',
                    className: 'excel-table',
                    editable: false,
                    header: tableStyles
                });

                // 显示HTML表格
                $('#excel-preview-container').html(htmlTable).show();
                $('#excel-loading').hide();

                // 应用额外的样式强化
                setTimeout(function() {
                    applyTableEnhancements();
                }, 100);

            } catch (e) {
                console.error('解析Excel文件失败:', e);
                $('#excel-loading').html(
                    '<i class="layui-icon layui-icon-close-fill" style="font-size:30px;color:#FF5722"></i>' +
                    '<p style="margin-top:10px">解析Excel文件失败: ' + e.message + '</p>'
                );
            }
        } else {
            console.error('获取Excel文件失败:', xhr.statusText);
            $('#excel-loading').html(
                '<i class="layui-icon layui-icon-close-fill" style="font-size:30px;color:#FF5722"></i>' +
                '<p style="margin-top:10px">获取Excel文件失败: ' + xhr.statusText + '</p>'
            );
        }
    };

    xhr.onerror = function() {
        console.error('网络错误，无法获取Excel文件');
        $('#excel-loading').html(
            '<i class="layui-icon layui-icon-close-fill" style="font-size:30px;color:#FF5722"></i>' +
            '<p style="margin-top:10px">网络错误，无法获取Excel文件</p>'
        );
    };

    xhr.send();
}

/**
 * 应用表格增强样式 - 更紧凑的样式
 */
function applyTableEnhancements() {
    var $table = $('#excel-table');
    if (!$table.length) return;

    // 确保表格有边框
    $table.css({
        'border': '1px solid #ddd',
        'font-size': '13px'
    });

    // 确保表格单元格有边框和填充 - 更紧凑的样式
    $table.find('th, td').css({
        'border': '1px solid #ddd',
        'padding': '4px',
        'font-size': '13px'
    });

    // 为表头添加样式
    $table.find('th').css({
        'background-color': '#f2f2f2',
        'font-weight': 'bold',
        'text-align': 'center',
        'padding': '6px 4px'
    });

    // 为偶数行添加背景色
    $table.find('tr:even').css('background-color', '#f9f9f9');

    // 设置表格行高更小
    $table.find('tr').css('line-height', '24px');

    // 如果表格过宽，添加水平滚动
    var tableWidth = $table.width();
    var containerWidth = $('#excel-preview-container').width();
    if (tableWidth > containerWidth) {
        $('#excel-preview-container').css('overflow-x', 'auto');
    }

    // 添加表头固定功能（如果表格很长）
    if ($table.find('tr').length > 10) {
        $table.find('tr:first-child th').css({
            'position': 'sticky',
            'top': '0',
            'z-index': '10',
            'background-color': '#e6e6e6'
        });
    }

    // 输出确认信息
    console.log('表格样式增强已应用');
}

/**
 * 解析Excel文件并显示
 * @param {File} file Excel文件对象
 */
function parseExcelFile(file) {
    var reader = new FileReader();

    reader.onload = function(e) {
        try {
            // 读取Excel内容
            var data = new Uint8Array(e.target.result);
            var workbook = XLSX.read(data, {type: 'array'});

            // 获取第一个工作表
            var firstSheet = workbook.SheetNames[0];
            var worksheet = workbook.Sheets[firstSheet];

            // 修改：增强表格网格线和边框样式 - 使用更深的颜色和更明显的边框
            var tableStyles = '<style>' +
                // 添加范围限制，防止样式影响其他页面部分
                '#attachmentPreview .excel-table { border-collapse: collapse !important; width: 100%; margin: 10px 0; }' +
                // 强化所有边框 - 使用黑色边框
                '#attachmentPreview .excel-table, #attachmentPreview .excel-table th, #attachmentPreview .excel-table td { border: 1px solid black !important; }' +
                // 表格外边框加粗
                '#attachmentPreview .excel-table { border: 2px solid black !important; }' +
                // 单元格内边距和对齐
                '#attachmentPreview .excel-table th, #attachmentPreview .excel-table td { padding: 8px; text-align: left; }' +
                // 表头样式
                '#attachmentPreview .excel-table th { background-color: #e6e6e6; font-weight: bold; color: black; }' +
                // 行交替颜色
                '#attachmentPreview .excel-table tr:nth-child(even) { background-color: #f9f9f9; }' +
                '#attachmentPreview .excel-table tr:hover { background-color: #f0f0f0; }' +
                // 数字单元格右对齐
                '#attachmentPreview .excel-table td.number-cell { text-align: right; }' +
                // 单元格内容处理
                '#attachmentPreview .excel-table td, #attachmentPreview .excel-table th { min-width: 80px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap; }' +
                // 直接应用到原生表格元素，确保样式覆盖，但限制在附件预览区域内
                '#attachmentPreview table { border-collapse: collapse !important; }' +
                '#attachmentPreview th, #attachmentPreview td { border: 1px solid black !important; padding: 8px !important; }' +
                '</style>';

            // 将工作表转换为HTML表格
            var htmlTable = XLSX.utils.sheet_to_html(worksheet, {
                id: 'excel-table',
                className: 'excel-table',
                editable: false,
                header: tableStyles
            });

            // 显示HTML表格
            $('#excel-preview-container').html(htmlTable).show();
            $('#excel-loading').hide();

            // 应用额外的样式强化
            setTimeout(function() {
                applyTableEnhancements();
            }, 100);

            // 显示所有工作表的选项卡（如果有多个工作表）
            if (workbook.SheetNames.length > 1) {
                var sheetTabs = '<div class="layui-tab layui-tab-brief" style="margin:5px 0;">' +
                    '<ul class="layui-tab-title" style="height:30px;">';

                for (var i = 0; i < workbook.SheetNames.length; i++) {
                    var sheetName = workbook.SheetNames[i];
                    var activeClass = (i === 0) ? 'layui-this' : '';
                    sheetTabs += '<li class="' + activeClass + '" data-sheet="' + sheetName + '" style="padding:0 10px; line-height:30px; font-size:12px;">' + sheetName + '</li>';
                }

                sheetTabs += '</ul></div>';

                // 添加工作表选项卡
                $('#excel-preview-container').before(sheetTabs);

                // 绑定选项卡切换事件
                $('.layui-tab-title li').click(function() {
                    var sheetName = $(this).data('sheet');
                    var worksheet = workbook.Sheets[sheetName];
                    var htmlTable = XLSX.utils.sheet_to_html(worksheet, {
                        id: 'excel-table',
                        className: 'excel-table',
                        editable: false,
                        header: tableStyles
                    });

                    $('#excel-preview-container').html(htmlTable);
                    setTimeout(function() {
                        applyTableEnhancements();
                    }, 100);
                    $('.layui-tab-title li').removeClass('layui-this');
                    $(this).addClass('layui-this');
                });
            }
        } catch (e) {
            console.error('Excel解析错误:', e);
            $('#excel-loading').html(
                '<div style="padding:10px; text-align:center;">' +
                '  <i class="layui-icon layui-icon-close-fill" style="font-size:24px;color:#FF5722"></i>' +
                '  <p style="margin-top:5px; font-size: 13px;">Excel解析失败: ' + e.message + '</p>' +
                '</div>'
            );
        }
    };

    reader.onerror = function(e) {
        console.error('文件读取错误:', e);
        $('#excel-loading').html(
            '<div style="padding:10px; text-align:center;">' +
            '  <i class="layui-icon layui-icon-close-fill" style="font-size:24px;color:#FF5722"></i>' +
            '  <p style="margin-top:5px; font-size: 13px;">文件读取失败</p>' +
            '</div>'
        );
    };

    reader.readAsArrayBuffer(file);
}

// ============================= 铜字段相关功能 =============================

// 铜字段相关的JavaScript函数
var copperFieldsModal;
var currentEditingRow = null;

// 打开铜字段弹窗
function openCopperFieldsModal(inputElement) {
    var row = $(inputElement).closest('tr');

    // 检查是否在编辑状态
    if (!row.hasClass('single-edit-row')) {
        layer.msg('请先点击编辑按钮进入编辑状态，才能设置接单金额', {icon: 0, time: 2000});
        return;
    }

    currentEditingRow = inputElement;
    var row = $(inputElement).closest('tr');
    var customerCode = $('#demandCustomerCode').val();

    if (!customerCode) {
        layer.msg('请先选择需求方', {icon: 2});
        return;
    }

    // 检查订单数量是否已填写
    var orderQuantity = row.find('[name="orderQuantity"]').val();
    if (!orderQuantity || orderQuantity.trim() === '' || parseFloat(orderQuantity) <= 0) {
        layer.msg('请先设置订单数量再设置接单金额', {icon: 2, time: 3000});
        // 自动聚焦到订单数量输入框
        var orderQuantityInput = row.find('[name="orderQuantity"]');
        if (orderQuantityInput.length > 0) {
            orderQuantityInput.focus();
            // 高亮显示输入框
            orderQuantityInput.addClass('layui-form-danger');
            setTimeout(function() {
                orderQuantityInput.removeClass('layui-form-danger');
            }, 3000);
        }
        return;
    }

    // 设置订单数量和客户代码
    $('#modalOrderQuantity').val(orderQuantity);
    $('#currentCustomerCode').val(customerCode);

    // 清空表单
    $('#copperFieldsForm')[0].reset();
    $('#modalOrderQuantity').val(orderQuantity);
    $('#currentCustomerCode').val(customerCode);

    // 从输入框的data属性中加载现有的铜字段数据
    var $input = $(inputElement);
    var existingData = {
        copperContractType: $input.attr('data-copper-contract-type') || '',
        copperContractNo: $input.attr('data-copper-contract-no') || '',
        copperCondition: $input.attr('data-copper-condition') || '',
        copperCurrency: $input.attr('data-copper-currency') || '',
        conversionRate: $input.attr('data-conversion-rate') || '',
        copperBase: $input.attr('data-copper-base') || '',
        premium: $input.attr('data-premium') || '',
        convertedCopperPrice: $input.attr('data-converted-copper-price') || '',
        zeroBase: $input.attr('data-zero-base') || '',
        orderUnitPrice: $input.attr('data-order-unit-price') || '',
        orderAmount: $input.attr('data-order-amount') || ''
    };

    // 加载铜条件列表
    loadCopperConditionList(function() {

        // 调试信息
        console.log('=== 编辑页面铜字段数据加载 ===');
        console.log('输入框当前值:', $input.val());
        console.log('从data属性读取的数据:', existingData);
        console.log('输入框所有data属性:', {
            'data-copper-contract-type': $input.attr('data-copper-contract-type'),
            'data-copper-contract-no': $input.attr('data-copper-contract-no'),
            'data-copper-condition': $input.attr('data-copper-condition'),
            'data-copper-currency': $input.attr('data-copper-currency'),
            'data-conversion-rate': $input.attr('data-conversion-rate'),
            'data-copper-base': $input.attr('data-copper-base'),
            'data-premium': $input.attr('data-premium'),
            'data-converted-copper-price': $input.attr('data-converted-copper-price'),
            'data-zero-base': $input.attr('data-zero-base'),
            'data-order-unit-price': $input.attr('data-order-unit-price'),
            'data-order-amount': $input.attr('data-order-amount')
        });
        console.log('是否有铜合同类别数据:', !!existingData.copperContractType);

        // 如果没有数据，检查是否是数据库加载的问题
        if (!existingData.copperContractType && !existingData.copperContractNo && !existingData.orderAmount) {
            console.warn('警告：没有检测到铜字段数据，可能是以下原因：');
            console.warn('1. 数据库中没有保存铜字段数据');
            console.warn('2. JSP页面没有正确设置data属性');
            console.warn('3. 编辑行生成时没有正确传递data属性');
        }
        console.log('================================');

        // 设置订单数量 - 从外面的输入框同步到弹窗
        var orderQuantity = row.find('[name="orderQuantity"]').val() || '0';
        $('#modalOrderQuantity').val(orderQuantity);

        console.log('同步订单数量到弹窗:', orderQuantity);

        if (existingData.copperContractType) {
            $('#copperContractType').val(existingData.copperContractType);

            // 根据铜合同类别设置模式
            var contractType = existingData.copperContractType;
            if (contractType === '1' || contractType === '2') {
                switchCopperBaseMode('auto');
                $('#copperContractNo').prop('disabled', false);

                // 加载铜签约No列表，然后设置现有值
                var customerCode = $('#demandCustomerCode').val();
                if (customerCode) {
                    loadCopperContractListWithCallback(contractType, function() {
                        // 加载完成后设置现有值
                        $('#copperContractNo').val(existingData.copperContractNo);
                        setCopperConditionValue(existingData.copperCondition);
                        $('#copperCurrency').val(existingData.copperCurrency);
                        $('#conversionRate').val(existingData.conversionRate);
                        $('#copperBase').val(existingData.copperBase);
                        $('#premium').val(existingData.premium);
                        $('#convertedCopperPrice').val(existingData.convertedCopperPrice);
                        $('#zeroBase').val(existingData.zeroBase);
                        $('#orderUnitPrice').val(existingData.orderUnitPrice);
                        $('#orderAmount').val(existingData.orderAmount);

                        if (form) {
                            form.render('select');
                        }
                    });
                } else {
                    // 没有客户代码时，直接设置值
                    setExistingCopperData(existingData);
                }
            } else if (contractType === '3' || contractType === '4') {
                switchCopperBaseMode('manual');
                $('#copperContractNo').prop('disabled', true);

                // 直接设置现有值
                setExistingCopperData(existingData);
            }
        } else {
            console.log('编辑页面没有现有铜字段数据，清空表单并设置默认状态');
            // 清空表单
            $('#copperFieldsForm')[0].reset();
            // 重新设置订单数量（清空表单后需要重新设置）
            $('#modalOrderQuantity').val(orderQuantity);

            // 检查是否有保存的0base值（编辑页面专用）
            var savedZeroBase = row.data('zeroBase');
            if (savedZeroBase) {
                console.log('编辑页面恢复保存的0base值:', savedZeroBase);
                $('#zeroBase').val(savedZeroBase);
            }

            // 默认显示自动填充模式
            switchCopperBaseMode('auto');
            $('#copperContractNo').prop('disabled', false);
        }

        if (form) {
            form.render('select');
        }
    });

    // 打开弹窗
    copperFieldsModal = layer.open({
        type: 1,
        title: '设置接单金额',
        content: $('#copperFieldsModal'),
        area: ['800px', '600px'],
        btn: ['确定', '取消'],
        yes: function(index, layero) {
            saveCopperFields();
        },
        btn2: function(index, layero) {
            closeCopperFieldsModal();
        },
        success: function(layero, index) {
            // 重新渲染表单
            if (form) {
                form.render();

                // 绑定事件
                form.on('select(copperContractType)', function(data) {
                    loadCopperContractList(data.value);
                });

                form.on('select(copperContractNo)', function(data) {
                    loadCopperContractDetail(data.value);
                });
            }

            // 弹窗打开后，延迟一点时间再设置数据，确保表单已完全渲染
            setTimeout(function() {
                console.log('弹窗已打开，确保订单数量正确设置...');
                // 确保订单数量正确设置（防止表单渲染时被清空）
                var currentOrderQuantity = row.find('[name="orderQuantity"]').val();
                if (currentOrderQuantity && $('#modalOrderQuantity').val() !== currentOrderQuantity) {
                    $('#modalOrderQuantity').val(currentOrderQuantity);
                    console.log('重新设置订单数量:', currentOrderQuantity);
                    // 触发重新计算
                    calculateOrderAmount();
                }
            }, 200);
        }
    });
}

// 关闭铜字段弹窗
function closeCopperFieldsModal() {
    layer.close(copperFieldsModal);
    currentEditingRow = null;
}

// 保存铜字段数据
function saveCopperFields() {
    if (!currentEditingRow) {
        layer.msg('无效的操作', {icon: 2});
        return;
    }

    var orderAmount = $('#orderAmount').val();
    if (!orderAmount || parseFloat(orderAmount) <= 0) {
        layer.msg('请完善铜字段信息以计算接单金额', {icon: 2});
        return;
    }

    // 获取弹窗中的订单数量
    var modalOrderQuantity = $('#modalOrderQuantity').val();

    // 更新接单金额输入框显示
    $(currentEditingRow).val(orderAmount);

    // 同步订单数量回外面的输入框
    var row = $(currentEditingRow).closest('tr');
    var orderQuantityInput = row.find('[name="orderQuantity"]');
    if (orderQuantityInput.length > 0) {
        orderQuantityInput.val(modalOrderQuantity);
        console.log('同步订单数量回外面:', modalOrderQuantity);
    }

    // 存储铜字段数据到行的data属性中
    var row = $(currentEditingRow).closest('tr');
    row.data('copperContractType', $('#copperContractType').val());
    row.data('copperContractNo', $('#copperContractNo').val());
    row.data('copperCondition', getCopperConditionValue());
    row.data('copperCurrency', $('#copperCurrency').val());
    row.data('conversionRate', $('#conversionRate').val());
    row.data('copperBase', $('#copperBase').val());
    row.data('premium', $('#premium').val());
    row.data('convertedCopperPrice', $('#convertedCopperPrice').val());
    row.data('zeroBase', $('#zeroBase').val());
    row.data('orderUnitPrice', $('#orderUnitPrice').val());
    row.data('orderAmount', orderAmount);

    // 同时更新输入框的data属性，以便下次打开时能正确加载
    var $input = $(currentEditingRow);
    $input.attr('data-copper-contract-type', $('#copperContractType').val());
    $input.attr('data-copper-contract-no', $('#copperContractNo').val());
    $input.attr('data-copper-condition', getCopperConditionValue());
    $input.attr('data-copper-currency', $('#copperCurrency').val());
    $input.attr('data-conversion-rate', $('#conversionRate').val());
    $input.attr('data-copper-base', $('#copperBase').val());
    $input.attr('data-premium', $('#premium').val());
    $input.attr('data-converted-copper-price', $('#convertedCopperPrice').val());
    $input.attr('data-zero-base', $('#zeroBase').val());
    $input.attr('data-order-unit-price', $('#orderUnitPrice').val());
    $input.attr('data-order-amount', orderAmount);

    layer.msg('设置成功', {icon: 1});
    closeCopperFieldsModal();
}

// 加载铜条件列表
function loadCopperConditionList(callback) {
    $.post(baselocation + '/order/orderEntry/getCopperConditionList', {}, function(result) {
        if (result.code === 1) {
            var select = $('#copperCondition');
            select.empty().append('<option value="">请选择</option>');
            $.each(result.data, function(index, item) {
                // 假设返回的数据格式是 {condition: "条件名", currency: "货币"}
                if (typeof item === 'object') {
                    select.append('<option value="' + item.condition + '" data-currency="' + item.currency + '">' + item.condition + '</option>');
                } else {
                    // 兼容原来的字符串格式
                    select.append('<option value="' + item + '">' + item + '</option>');
                }
            });

            if (form) {
                form.render('select');

                // 绑定铜条件选择事件
                form.on('select(copperCondition)', function(data) {
                    var selectedOption = $(data.elem).find('option:selected');
                    var currency = selectedOption.data('currency');
                    if (currency && data.value) {
                        // 只有选择了有效值才设置货币
                        $('#copperCurrency').val(currency);
                    } else if (!data.value) {
                        // 如果清空选择，也清空货币
                        clearCopperCurrency();
                    }
                    // 触发重新计算
                    calculateOrderAmount();
                });

                // 执行回调
                if (callback && typeof callback === 'function') {
                    callback();
                }
            }
        }
    });
}

// 铜合同类别变化时加载铜合同列表
function loadCopperContractList(copperContractType) {
    // 优先使用demandCustomerCode，因为这是页面主要的客户选择字段
    var customerCode = $('#demandCustomerCode').val() || $('#currentCustomerCode').val();
    console.log('修改页面loadCopperContractList调用:');
    console.log('  铜合同类别:', copperContractType);
    console.log('  demandCustomerCode元素存在:', $('#demandCustomerCode').length > 0);
    console.log('  demandCustomerCode值:', $('#demandCustomerCode').val());
    console.log('  currentCustomerCode元素存在:', $('#currentCustomerCode').length > 0);
    console.log('  currentCustomerCode值:', $('#currentCustomerCode').val());
    console.log('  最终使用的customerCode:', customerCode);

    if (!customerCode || !copperContractType) {
        console.log('修改页面缺少必要参数，退出加载');
        console.log('  customerCode为空:', !customerCode);
        console.log('  copperContractType为空:', !copperContractType);
        return;
    }

    // 清空相关联的字段
    clearCopperRelatedFields();

    // 先加载铜条件列表
    loadCopperConditionList(function() {
        // 在回调函数中重新获取customerCode，确保作用域正确
        var callbackCustomerCode = $('#demandCustomerCode').val() || $('#currentCustomerCode').val();
        console.log('修改页面回调函数中重新获取customerCode:', callbackCustomerCode);

        // 根据铜合同类别设置不同的逻辑
        if (copperContractType === '1' || copperContractType === '2') {
            // 预约铜/支给铜：启用铜签约No，铜base为自动填充，等待选择合同后填充铜条件
            switchCopperBaseMode('auto');
            $('#copperContractNo').prop('disabled', false);

            // 加载铜签约No列表
            console.log('修改页面发送铜签约No请求:', {
                customerCode: callbackCustomerCode,
                copperContractType: copperContractType
            });
            $.post(baselocation + '/order/orderEntry/getCopperContractList', {
                customerCode: callbackCustomerCode,
                copperContractType: copperContractType
            }, function(result) {
                console.log('修改页面铜签约No请求返回:', result);
                if (result.code === 1) {
                    var select = $('#copperContractNo');
                    select.empty().append('<option value="">请选择</option>');
                    console.log('修改页面铜签约No数据:', result.data);
                    $.each(result.data, function(index, item) {
                        select.append('<option value="' + item + '">' + item + '</option>');
                    });
                    if (form) {
                        form.render('select');
                    }
                } else {
                    console.log('修改页面铜签约No请求失败:', result.message);
                }
            }).fail(function(xhr, status, error) {
                console.log('修改页面铜签约No请求异常:', error);
            });
        } else if (copperContractType === '3' || copperContractType === '4') {
            // 一般铜/无偿：禁用铜签约No，铜base为手动输入，铜条件可自由选择
            switchCopperBaseMode('manual');
            $('#copperContractNo').prop('disabled', true).empty().append('<option value="">不适用</option>');

            // 查询升水表获取升水数据
            var arrivalDate = getArrivalDateFromCurrentRowForEdit();
            if (callbackCustomerCode && arrivalDate) {
                queryAscendingWaterForOrderEntryEdit(callbackCustomerCode, arrivalDate);
            }

            if (form) {
                form.render('select');
            }
        }
    });
}

// 铜合同No变化时加载合同详情
function loadCopperContractDetail(copperContractNo) {
    if (!copperContractNo) {
        // 如果没有选择合同，清空自动填充的字段
        setCopperConditionValue('');
        $('#copperBase').val('');
        $('#copperCurrency').val('');
        clearPremiumValueForEdit(); // 清空升水值
        clearCalculatedFields();
        if (form) {
            form.render('select');
        }
        return;
    }

    $.post(baselocation + '/order/orderEntry/getCopperContractDetail', {
        copperContractNo: copperContractNo
    }, function(result) {
        if (result.code === 1 && result.data) {
            setCopperConditionValue(result.data.copperCondition);
            $('#copperBase').val(result.data.copperBase);
            $('#copperCurrency').val(result.data.copperCurrency);

            // 根据铜合同类别决定是否查询升水
            var copperContractType = $('#copperContractType').val();
            if (copperContractType === '3' || copperContractType === '4') {
                // 一般铜/无偿：查询升水表获取升水数据
                var customerCode = $('#demandCustomerCode').val() || $('#currentCustomerCode').val();
                var arrivalDate = getArrivalDateFromCurrentRowForEdit();
                if (customerCode && arrivalDate) {
                    queryAscendingWaterForOrderEntryEdit(customerCode, arrivalDate);
                }
            } else {
                clearPremiumValueForEdit(); // 其他类型清空升水值
            }

            if (form) {
                form.render('select');
            }
            calculateOrderAmount();
        } else {
            // 如果获取失败，清空相关字段
            setCopperConditionValue('');
            $('#copperBase').val('');
            $('#copperCurrency').val('');
            clearPremiumValueForEdit();
            clearCalculatedFields();
        }
    });
}

// 货币换算函数（编辑页面专用）
function convertCurrencyToRMBForEdit(originalPrice, currency, arrivalDate, callback) {
    console.log('=== 订单录入编辑页面货币换算 ===');
    console.log('原价格:', originalPrice, '货币:', currency, '到货日期:', arrivalDate);

    // 如果是人民币，直接返回原价格
    if (!currency || currency === 'RMB') {
        console.log('货币为RMB，无需换算');
        callback(originalPrice, 1.0);
        return;
    }

    // 查询汇率
    $.ajax({
        url: baselocation + '/salesCommon/getCurrencyExchangeRate',
        type: 'POST',
        data: {
            originalCurrency: currency,
            arrivalDate: arrivalDate
        },
        success: function(result) {
            console.log('订单录入编辑页面汇率查询返回:', result);

            if (result.code === 1 && result.data && result.data.exchangeRate) {
                var exchangeRate = parseFloat(result.data.exchangeRate);
                var convertedPrice = (parseFloat(originalPrice) * exchangeRate).toFixed(4);

                console.log('汇率:', exchangeRate, '换算后价格:', convertedPrice);
                callback(convertedPrice, exchangeRate);
            } else {
                console.log('未找到汇率数据:', result.message || '无数据');
                layer.msg(result.message || '货币换算汇率未登录！', {icon: 0, time: 3000});
                callback(originalPrice, 1.0); // 返回原价格
            }
        },
        error: function(xhr, status, error) {
            console.log('订单录入编辑页面查询汇率异常:', error);
            layer.msg('查询汇率失败，使用原价格', {icon: 2, time: 3000});
            callback(originalPrice, 1.0); // 返回原价格
        }
    });
}

// 查询产品单价作为0base（编辑页面专用）
function queryProductPriceForEdit(customerCode, productCode, arrivalDate) {
    console.log('=== 订单录入编辑页面查询产品单价作为0base ===');
    console.log('客户代码:', customerCode, '产品代码:', productCode, '到货日期:', arrivalDate);

    if (!customerCode || !productCode || !arrivalDate) {
        console.log('参数不完整，无法查询产品单价');
        return;
    }

    $.ajax({
        url: baselocation + '/salesCommon/getProductPrice',
        type: 'POST',
        data: {
            customerCode: customerCode,
            productCode: productCode,
            arrivalDate: arrivalDate
        },
        success: function(result) {
            console.log('订单录入编辑页面产品单价查询返回:', result);

            if (result.code === 1 && result.data) {
                var productPrice = result.data;
                var $zeroBaseInput = $('#zeroBase');

                if (productPrice.productPrice) {
                    // 获取产品价格的货币信息
                    var productCurrency = productPrice.currency || 'RMB';

                    console.log('产品单价:', productPrice.productPrice, '货币:', productCurrency);

                    // 进行货币换算
                    convertCurrencyToRMBForEdit(productPrice.productPrice, productCurrency, arrivalDate, function(convertedPrice, exchangeRate) {
                        // 设置0base（换算后的价格）
                        $zeroBaseInput.val(convertedPrice);
                        console.log('自动设置0base（换算后）:', convertedPrice, '汇率:', exchangeRate);

                        // 将0base值保存到所有相关行的数据中（编辑页面专用）
                        saveZeroBaseToAllRowsForEdit(convertedPrice, customerCode, productCode);

                        // 触发0base变更事件，重新计算接单金额
                        calculateOrderAmount();

                        var message = '已自动填入0base: ' + convertedPrice;
                        if (exchangeRate !== 1.0) {
                            message += ' (原价: ' + productPrice.productPrice + ' ' + productCurrency + ', 汇率: ' + exchangeRate + ')';
                        }
                        layer.msg(message, {icon: 1, time: 3000});
                    });
                } else {
                    console.log('产品价格数据中没有单价信息');
                }
            } else {
                console.log('未找到匹配的产品价格:', result.message || '无数据');
                // 不显示错误提示，因为可能是正常情况（没有配置价格）
            }
        },
        error: function(xhr, status, error) {
            console.log('订单录入编辑页面查询产品单价异常:', error);
            // 不显示错误提示，避免影响用户体验
        }
    });
}

// 将0base值保存到所有相关行的数据中（编辑页面专用）
function saveZeroBaseToAllRowsForEdit(zeroBaseValue, customerCode, productCode) {
    console.log('编辑页面保存0base值到相关行:', {
        zeroBaseValue: zeroBaseValue,
        customerCode: customerCode,
        productCode: productCode
    });

    // 遍历所有表格行，找到匹配的行并保存0base值
    $('#subOrderTableBody tr').each(function() {
        var $row = $(this);
        var rowCustomerCode = $('#demandCustomerCode').val(); // 客户代码来自表单顶部
        var rowProductCode = $row.find('input[name="modelNumber"], select[name="modelNumber"]').val();

        console.log('编辑页面检查行数据:', {
            rowCustomerCode: rowCustomerCode,
            rowProductCode: rowProductCode,
            targetCustomerCode: customerCode,
            targetProductCode: productCode
        });

        // 如果客户代码和产品代码匹配，则保存0base值
        if (rowCustomerCode === customerCode && rowProductCode === productCode) {
            $row.data('zeroBase', zeroBaseValue);
            console.log('编辑页面已保存0base值到行数据:', zeroBaseValue);
        }
    });
}

// 计算接单金额
function calculateOrderAmount() {
    var conversionRate = parseFloat($('#conversionRate').val()) || 0;
    var copperBase = parseFloat($('#copperBase').val()) || 0;
    var premium = parseFloat($('#premium').val()) || 0;
    var zeroBase = parseFloat($('#zeroBase').val()) || 0;
    var orderQuantity = parseFloat($('#modalOrderQuantity').val()) || 0;

    if (conversionRate > 0 && copperBase > 0) {
        // 换算后铜单价 = （铜base + 升水）* 换算率
        var convertedCopperPrice = (copperBase + premium) * conversionRate;
        $('#convertedCopperPrice').val(convertedCopperPrice.toFixed(4));

        // 接单单价 = 换算后铜单价 + 0base
        var orderUnitPrice = convertedCopperPrice + zeroBase;
        $('#orderUnitPrice').val(orderUnitPrice.toFixed(4));

        // 接单金额 = 接单单价 * 订单数量
        var orderAmount = orderUnitPrice * orderQuantity;
        $('#orderAmount').val(orderAmount.toFixed(2));
    }
}



// 清空铜相关字段（当合同类别改变时）
function clearCopperRelatedFields() {
    // 清空铜签约No
    $('#copperContractNo').empty().append('<option value="">请选择</option>');

    // 清空铜条件和铜货币
    setCopperConditionValue('');
    clearCopperCurrency();

    // 清空其他字段
    $('#copperBase').val('');

    // 清空升水值
    clearPremiumValueForEdit();

    // 清空计算字段
    clearCalculatedFields();

    if (form) {
        form.render('select');
    }
}

// 清空计算字段
function clearCalculatedFields() {
    $('#convertedCopperPrice').val('');
    $('#orderUnitPrice').val('');
    $('#orderAmount').val('');
}

// 清空铜货币
function clearCopperCurrency() {
    $('#copperCurrency').val('');
}

// 切换铜base和铜条件模式（根据铜合同类别）
function switchCopperBaseMode(mode) {
    if (mode === 'auto') {
        // 自动填充模式（预约铜/支给铜）
        $('#copperBase').removeClass('layui-input').addClass('layui-input auto-filled-input').prop('readonly', true).attr('placeholder', '从合同自动填充').removeAttr('oninput');
        // 铜条件也设为禁用状态
        $('#copperCondition').prop('disabled', true);
    } else if (mode === 'manual') {
        // 手动输入模式（一般铜/无偿）
        $('#copperBase').removeClass('auto-filled-input').addClass('layui-input').prop('readonly', false).attr('placeholder', '请输入铜base').attr('oninput', 'calculateOrderAmount()');
        // 铜条件设为可编辑状态
        $('#copperCondition').prop('disabled', false);
    }

    if (form) {
        form.render('select');
    }
}



// 获取当前铜条件的值
function getCopperConditionValue() {
    return $('#copperCondition').val();
}

// 设置铜条件的值
function setCopperConditionValue(value) {
    $('#copperCondition').val(value);

    // 如果清空铜条件，也清空铜货币
    if (!value) {
        clearCopperCurrency();
    }

    if (form) {
        form.render('select');
    }
}

// 测试函数：检查页面加载时的铜字段数据
function testCopperDataLoading() {
    console.log('=== 测试页面铜字段数据加载 ===');
    $('.order-amount-input').each(function(index, element) {
        var $input = $(element);
        var rowIndex = index + 1;
        console.log('第' + rowIndex + '行铜字段数据:');
        console.log('  接单金额值:', $input.val());
        console.log('  铜合同类别:', $input.attr('data-copper-contract-type'));
        console.log('  铜签约No:', $input.attr('data-copper-contract-no'));
        console.log('  铜条件:', $input.attr('data-copper-condition'));
        console.log('  铜货币:', $input.attr('data-copper-currency'));
        console.log('  铜base:', $input.attr('data-copper-base'));
        console.log('  接单金额data:', $input.attr('data-order-amount'));
        console.log('  ---');
    });
    console.log('=== 测试结束 ===');
}

// 页面加载完成后自动执行测试
$(document).ready(function() {
    setTimeout(function() {
        testCopperDataLoading();
    }, 1000);
});

// 清空升水值（编辑页面专用）
function clearPremiumValueForEdit() {
    $('#premium').val('');
}

// 获取当前行的到货日期（编辑页面专用）
function getArrivalDateFromCurrentRowForEdit() {
    if (!currentEditingRow) {
        return '';
    }
    var row = $(currentEditingRow).closest('tr');
    return row.find('input[name="arrivalDate"], td').filter(function() {
        return $(this).text().match(/\d{4}-\d{2}-\d{2}/);
    }).first().text() || row.find('input[name="arrivalDate"]').val() || '';
}

// 查询升水表数据（用于订单录入编辑页面）
function queryAscendingWaterForOrderEntryEdit(customerCode, arrivalDate) {
    console.log('=== 订单录入编辑页面查询升水表数据 ===');
    console.log('客户代码:', customerCode, '到货日期:', arrivalDate);

    if (!customerCode || !arrivalDate) {
        console.log('客户代码或到货日期为空，无法查询升水表');
        clearPremiumValueForEdit();
        return;
    }

    $.ajax({
        url: baselocation + '/salesCommon/getAscendingWaterByCustomerAndDate',
        type: 'POST',
        data: {
            customerCode: customerCode,
            arrivalDate: arrivalDate
        },
        success: function(result) {
            console.log('订单录入编辑页面升水表查询返回:', result);

            if (result.code === 1 && result.data && result.data.length > 0) {
                // 取第一条数据的升水值直接设置
                var firstItem = result.data[0];
                var ascendingWaterPrice = firstItem.ascendingWaterPrice || 0;

                console.log('编辑页面取第一条升水数据:', {
                    copperCondition: firstItem.copperCondition || '',
                    ascendingWaterPrice: ascendingWaterPrice,
                    copperConditionName: firstItem.copperConditionName || ''
                });

                $('#premium').val(ascendingWaterPrice);
                console.log('编辑页面升水值已设置为:', ascendingWaterPrice);

                // 重新计算接单金额
                calculateOrderAmount();
            } else {
                console.log('订单录入编辑页面未找到匹配的升水数据:', result.message || '无数据');
                clearPremiumValueForEdit();
            }
        },
        error: function(xhr, status, error) {
            console.log('订单录入编辑页面查询升水表异常:', error);
            clearPremiumValueForEdit();
        }
    });
}

