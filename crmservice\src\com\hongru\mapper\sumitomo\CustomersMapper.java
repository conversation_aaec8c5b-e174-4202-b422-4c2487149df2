package com.hongru.mapper.sumitomo;

import com.baomidou.mybatisplus.mapper.BaseMapper;
import com.hongru.entity.sumitomo.Customers;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface CustomersMapper extends BaseMapper<Customers> {

    /**
     * 获取客户表详情
     * 
     * @throws
     * <AUTHOR>
     * @create 2024/2/20 11:08
     * @return
     */
    List<Customers> selectCustomersList();

    /**
     * 根据客户代码获取客户信息
     * 
     * @param customerCode 客户代码
     * @return 客户信息
     */
    Customers selectByCustomerCode(@Param("customerCode") String customerCode);

}
