package com.hongru.mapper.businessOps;

import com.baomidou.mybatisplus.mapper.BaseMapper;
import com.hongru.entity.businessOps.AscendingWater;
import com.hongru.entity.businessOps.CurrencyExchangeRate;
import com.hongru.support.page.PageInfo;

import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface CurrencyExchangeRateMapper extends BaseMapper<CurrencyExchangeRate> {
	int insertCurrencyExchangeRate(@Param("currencyExchangeRate") CurrencyExchangeRate currencyExchangeRate);
  
	CurrencyExchangeRate selectCurrencyExchangeRateById(@Param("id") int id);

    List<CurrencyExchangeRate> currencyExchangeRateListByPage(@Param("pageInfo") PageInfo pageInfo, @Param("originalCurrency") String originalCurrency, @Param("targetCurrency") String targetCurrency, @Param("applyDateStart") String applyDateStart, @Param("applyDateEnd") String applyDateEnd);
    Integer currencyExchangeRateListByPageCount( @Param("originalCurrency") String originalCurrency, @Param("targetCurrency") String targetCurrency, @Param("applyDateStart") String applyDateStart, @Param("applyDateEnd") String applyDateEnd);

    void updateCurrencyExchangeRate(@Param("currencyExchangeRate") CurrencyExchangeRate currencyExchangeRate);

    void deleteCurrencyExchangeRate(@Param("id") Integer id);

    /**
     * 根据换算原货币和适用日期查询汇率
     * 
     * @param originalCurrency 换算原货币
     * @param arrivalDate      到货日期
     * @return 货币换算汇率
     */
    CurrencyExchangeRate getCurrencyExchangeRateByOriginalCurrencyAndDate(
            @Param("originalCurrency") String originalCurrency, @Param("arrivalDate") String arrivalDate);
}
