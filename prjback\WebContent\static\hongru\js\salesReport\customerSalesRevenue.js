layui.use(['laydate', 'layer', 'table', 'element', 'form'], function(){
    var laydate = layui.laydate //日期
        ,layer = layui.layer //弹层
        ,table = layui.table //表格
        ,element = layui.element //元素操作
    var form = layui.form;
    
    // 日期范围选择器
    laydate.render({
        elem: '#salesDateRange'
        ,type: 'date'
        ,range: true
        ,format: 'yyyy-MM-dd'
    });
    
    // 执行一个 table 实例
    var url = baselocation+'/salesReport/customerSalesRevenue/list';
    table.render({
        elem: '#demo'
        ,height: 'full-170'
        ,url: url //数据接口
        ,cellMinWidth: 80 //全局定义常规单元格的最小宽度
        ,parseData:function(res){ //res 即为原始返回的数据
            return {
                "code": 0, //解析接口状态
                "msg": '', //解析提示文本
                "count": res.total, //解析数据长度
                "data": res.rows //解析数据列表
            };
        }
        ,method:'post'
        ,title: '客户销售额'
        ,page: false //关闭分页
        ,where:$("#formSearch").serializeJsonObject()
        ,toolbar: '#toolbarDemo' //开启工具栏，此处显示默认图标，可以自定义模板，详见文档
        ,defaultToolbar: ['filter']
        ,totalRow: true //开启合计行
        ,cols: [[ //表头
            {field: 'accountingYearMonth', title: '会计年月', align:'center', fixed: 'left'}
            ,{field: 'customerAlias', title: '需求方略称', align:'center'}
            ,{field: 'productCode', title: '品名', align:'center'}
            ,{field: 'size', title: '尺寸', align:'center', templet: function(d){
                if(d.size != null){
                    return parseFloat(d.size).toFixed(4);
                }else{
                    return "0.0000";
                }
            }}
            ,{field: 'shipmentDate', title: '出货日', align:'center'}
            ,{field: 'arrivalDate', title: '到达日', align:'center'}
            ,{field: 'salesQuantity', title: '销售额数', align:'right', totalRow: true, templet: function(d){
                if(d.salesQuantity != null){
                    return parseFloat(d.salesQuantity).toFixed(3);
                }else{
                    return "0.000";
                }
            }}
            ,{field: 'salesUnitPrice', title: '销售额单价', align:'right', templet: function(d){
                if(d.salesUnitPrice != null){
                    return parseFloat(d.salesUnitPrice).toFixed(4);
                }else{
                    return "0.0000";
                }
            }}
            ,{field: 'salesAmount', title: '销售额金额', align:'right', totalRow: true, templet: function(d){
                if(d.salesAmount != null){
                    return parseFloat(d.salesAmount).toFixed(2);
                }else{
                    return "0.00";
                }
            }}
            ,{field: 'copperPrice', title: '铜价', align:'right', templet: function(d){
                if(d.copperPrice != null){
                    return parseFloat(d.copperPrice).toFixed(4);
                }else{
                    return "0.0000";
                }
            }}
            ,{field: 'productMiddleCategory', title: '品目中分类名', align:'center'}
        ]]
    });
    
    //监听头工具栏事件
    table.on('toolbar(test)', function(obj){
        var checkStatus = table.checkStatus(obj.config.id);
        switch(obj.event){
            case 'refresh':
                search();
                break;
        };
    });
});

// 检索
function search(){
    var dateRange = $("#salesDateRange").val();
    var startDate = "";
    var endDate = "";
    
    if (dateRange) {
        var dates = dateRange.split(" - ");
        if (dates.length == 2) {
            startDate = dates[0];
            endDate = dates[1];
        }
    }
    
    layui.table.reload('demo', {
        where: {
            startDate: startDate,
            endDate: endDate
        }
    });
}

// 导出
function exportData(){
    var salesDateRange = $("#salesDateRange").val();
    var startDate = "";
    var endDate = "";
    
    if (salesDateRange) {
        var dates = salesDateRange.split(' - ');
        if (dates.length === 2) {
            startDate = dates[0];
            endDate = dates[1];
        }
    }
    
    // 创建表单
    var form = $("<form>");
    form.attr('style', 'display:none');
    form.attr('target', '');
    form.attr('method', 'post');
    form.attr('action', baselocation + '/salesReport/customerSalesRevenue/export');
    
    // 添加参数
    var startDateInput = $('<input>');
    startDateInput.attr('type', 'hidden');
    startDateInput.attr('name', 'startDate');
    startDateInput.attr('value', startDate);
    form.append(startDateInput);
    
    var endDateInput = $('<input>');
    endDateInput.attr('type', 'hidden');
    endDateInput.attr('name', 'endDate');
    endDateInput.attr('value', endDate);
    form.append(endDateInput);
    
    // 提交表单
    $('body').append(form);
    form.submit();
    form.remove();
} 