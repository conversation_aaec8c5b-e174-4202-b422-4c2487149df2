package com.hongru.pojo.dto;

import com.hongru.entity.businessOps.CuPriceCost;
import com.hongru.support.page.PageInfo;

import java.util.List;

public class CuPriceCostDTO {

    private PageInfo pageInfo;

    private List<CuPriceCost> cuPriceCostList;

    public CuPriceCostDTO(PageInfo pageInfo, List<CuPriceCost> cuPriceCostList) {
        super();
        this.pageInfo = pageInfo;
        this.cuPriceCostList = cuPriceCostList;
    }

    public PageInfo getPageInfo() {
        return pageInfo;
    }

    public void setPageInfo(PageInfo pageInfo) {
        this.pageInfo = pageInfo;
    }

    public List<CuPriceCost> getCuPriceCostList() {
        return cuPriceCostList;
    }

    public void setCuPriceCostList(List<CuPriceCost> cuPriceCostList) {
        this.cuPriceCostList = cuPriceCostList;
    }
}
