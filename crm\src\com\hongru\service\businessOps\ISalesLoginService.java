package com.hongru.service.businessOps;

import com.hongru.entity.businessOps.SalesAmount;
import com.hongru.entity.businessOps.SalesAmountDetail;
import com.hongru.pojo.dto.SalesLoginListDTO;
import com.hongru.pojo.dto.SalesAmountExportDTO;
import com.hongru.support.page.PageInfo;

import java.util.List;
import java.util.Map;

/**
 * 销售额登录服务接口
 */
public interface ISalesLoginService {

        /**
         * 分页查询销售额登录列表
         * 
         * @param pageInfo        分页信息
         * @param customerCode    客户代码
         * @param salesAmountDate 销售额日
         * @return 销售额登录列表
         * @throws Exception
         */
        List<SalesLoginListDTO> listSalesLoginByPage(PageInfo pageInfo, String customerCode, String salesAmountDate)
                        throws Exception;

        /**
         * 查询销售额登录列表总数
         *
         * @param customerCode    客户代码
         * @param salesAmountDate 销售额日
         * @return 总数
         * @throws Exception
         */
        Integer listSalesLoginByPageCount(String customerCode, String salesAmountDate) throws Exception;

        /**
         * 分页查询销售额登录列表（按用户过滤）
         *
         * @param pageInfo     分页信息
         * @param customerCode 客户代码
         * @param startDate    开始日期
         * @param endDate      结束日期
         * @param creatorName  创建者名称
         * @return 销售额登录列表
         * @throws Exception
         */
        List<SalesLoginListDTO> listSalesLoginByPageWithUser(PageInfo pageInfo, String customerCode, String startDate,
                        String endDate, String creatorName) throws Exception;

        /**
         * 查询销售额登录列表总数（按用户过滤）
         *
         * @param customerCode 客户代码
         * @param startDate    开始日期
         * @param endDate      结束日期
         * @param creatorName  创建者名称
         * @return 总数
         * @throws Exception
         */
        Integer listSalesLoginByPageCountWithUser(String customerCode, String startDate, String endDate,
                        String creatorName)
                        throws Exception;

        /**
         * 根据ID获取销售额登录详情
         *
         * @param id 销售额ID
         * @return 销售额详情
         * @throws Exception
         */
        SalesAmount getSalesAmountById(Integer id) throws Exception;

        /**
         * 根据销售额NO获取销售额登录详情
         *
         * @param salesAmountNo 销售额NO
         * @return 销售额详情
         * @throws Exception
         */
        SalesAmount getSalesAmountByNo(String salesAmountNo) throws Exception;

        /**
         * 根据销售额NO获取销售额明细列表
         * 
         * @param salesAmountNo 销售额NO
         * @return 销售额明细列表
         * @throws Exception
         */
        List<SalesAmountDetail> getSalesAmountDetailsByNo(String salesAmountNo) throws Exception;

        /**
         * 根据客户代码获取客户详情信息（货币、税率等）
         * 
         * @param customerCode 客户代码
         * @return 客户详情信息
         * @throws Exception
         */
        Map<String, Object> getCustomerDetailsInfo(String customerCode) throws Exception;

        /**
         * 根据客户代码获取出库数据用于生成销售额
         *
         * @param customerCode 客户代码
         * @param arrivalDate  到货日期
         * @return 出库数据列表
         * @throws Exception
         */
        List<Map<String, Object>> getOutboundDataForSalesAmount(String customerCode, String arrivalDate)
                        throws Exception;

        /**
         * 根据客户代码和日期范围获取出库数据用于生成销售额
         *
         * @param customerCode 客户代码
         * @param startDate    开始日期
         * @param endDate      结束日期
         * @return 出库数据列表
         * @throws Exception
         */
        List<Map<String, Object>> getOutboundDataForSalesAmountWithDateRange(String customerCode, String startDate,
                        String endDate)
                        throws Exception;

        /**
         * 根据出货单No.获取明细数据
         *
         * @param shipmentNo 出货单No.
         * @return 明细数据列表
         * @throws Exception
         */
        List<Map<String, Object>> getDetailsByShipmentNo(String shipmentNo) throws Exception;

        /**
         * 生成销售额NO
         * 
         * @return 销售额NO
         * @throws Exception
         */
        String generateSalesAmountNo() throws Exception;

        /**
         * 保存销售额登录数据
         * 
         * @param salesAmount        销售额主表数据
         * @param salesAmountDetails 销售额明细数据
         * @param userName           用户名
         * @return 保存结果
         * @throws Exception
         */
        int saveSalesLoginData(SalesAmount salesAmount, List<SalesAmountDetail> salesAmountDetails, String userName)
                        throws Exception;

        /**
         * 更新销售额登录数据
         * 
         * @param salesAmount        销售额主表数据
         * @param salesAmountDetails 销售额明细数据
         * @param userName           用户名
         * @return 更新结果
         * @throws Exception
         */
        int updateSalesLoginData(SalesAmount salesAmount, List<SalesAmountDetail> salesAmountDetails, String userName)
                        throws Exception;

        /**
         * 根据销售额NO删除销售额登录数据
         *
         * @param salesAmountNo 销售额NO
         * @return 删除结果
         * @throws Exception
         */
        int deleteSalesLoginDataByNo(String salesAmountNo) throws Exception;

        /**
         * 获取交易条件列表
         * 
         * @return 交易条件列表
         * @throws Exception
         */
        List<Map<String, Object>> getTradeConditions() throws Exception;

        /**
         * 获取付款条件列表
         * 
         * @return 付款条件列表
         * @throws Exception
         */
        List<Map<String, Object>> getPayConditions() throws Exception;

        /**
         * 获取运输条件列表
         *
         * @return 运输条件列表
         * @throws Exception
         */
        List<Map<String, Object>> getTransportConditions() throws Exception;

        /**
         * 检查销售额是否已被支付请求登录使用
         *
         * @param salesAmountNo 销售额NO
         * @return true-已被使用，false-未被使用
         * @throws Exception
         */
        boolean isUsedByPaymentRequest(String salesAmountNo) throws Exception;

        /**
         * 获取销售额Excel导出数据
         *
         * @param customerCode         客户代码
         * @param salesAmountDateRange 销售额日期范围
         * @return Excel导出数据列表
         * @throws Exception
         */
        List<SalesAmountExportDTO> getSalesAmountExportData(String customerCode, String salesAmountDateRange)
                        throws Exception;

        /**
         * 根据销售额NO获取Excel导出数据
         *
         * @param salesAmountNo 销售额NO
         * @return Excel导出数据列表
         * @throws Exception
         */
        List<SalesAmountExportDTO> getSalesAmountExportDataByNo(String salesAmountNo) throws Exception;

}
