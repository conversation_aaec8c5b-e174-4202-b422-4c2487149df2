<%@ page language="java" import="java.util.*"
         contentType="text/html; charset=utf-8"%>
<%@ include file="/WEB-INF/layouts/base.jsp"%>
<html>
<head>
    <title>销售额确认</title>
    <style>
        /*设置数据表表头字体*/
        .layui-table th .layui-table-cell{
            text-align: center;
        }
    </style>
</head>
<body class="gray-bg">
<div class="ibox float-e-margins">
    <div class="ibox-content">
        <div class="layui-collapse">
            <div class="layui-colla-item">
                <div class="layui-colla-content layui-show">
                    <form id="searchForm" class="layui-form hr-form-search toolbar table-tool-mini" method="post" action="">
                        <div class="layui-form-item">
                            <div class="layui-inline layui-col-md3">
                                <label class="layui-form-label">需求方:</label>
                                <div class="layui-input-block">
                                    <select class="layui-select" id="customerCode" name="customerCode" lay-search="true">
                                        <option value="">请选择</option>
                                        <c:forEach items="${customersList}" var="customer">
                                            <option value="${customer.customerCode}">${customer.customerCode} - ${customer.customerAlias}</option>
                                        </c:forEach>
                                    </select>
                                </div>
                            </div>
                            <div class="layui-inline layui-col-md3">
                                <label class="layui-form-label">支付请求日:</label>
                                <div class="layui-input-block">
                                    <input type="text" name="paymentRequestDate" autocomplete="off" class="layui-input"
                                           id="paymentRequestDate">
                                </div>
                            </div>
                            <div class="layui-inline layui-col-md1 hr-div-btn">
                                <button type="button" id="searchBtn" class="layui-btn layui-bg-blue"><i class="layui-icon layui-icon-search"></i>检索</button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        <%--初始化table--%>
        <table class="layui-hide" id="salesConfirmTable" lay-filter="salesConfirmTable"></table>
    </div>
</div>

<!-- 自定义头部工具栏 -->
<script type="text/html" id="toolbarDemo">
    <div class="layui-btn-container">
        <button class="layui-btn layui-btn-primary layui-btn-sm" lay-event="refresh"><i class="layui-icon layui-icon-refresh"></i>刷新</button>
    </div>
</script>

<!-- 操作列 -->
<script type="text/html" id="operationBar">
    <button class="layui-btn layui-btn-xs" lay-event="detail">详情</button>
    {{# if(d.paymentRequestConfirm == '0') { }}
    <button class="layui-btn layui-btn-xs layui-btn-normal" lay-event="confirm">确认</button>
    {{# } else { }}
    <button class="layui-btn layui-btn-xs layui-btn-warm" lay-event="cancelConfirm">取消确认</button>
    {{# } }}
</script>

<!-- 确认状态 -->
<script type="text/html" id="confirmStatusTpl">
    {{# if(d.paymentRequestConfirm == '1') { }}
    <span class="layui-badge layui-bg-green">已确认</span>
    {{# } else { }}
    <span class="layui-badge layui-bg-orange">未确认</span>
    {{# } }}
</script>


<myfooter>
    <script src="${basePath}/static/hongru/js/order/salesConfirm_list.js?v=1.1.2"></script>
</myfooter>
</body>
</html>
