layui.use(['form', 'laydate', 'table', 'layer'], function(){
    var form = layui.form
        ,laydate = layui.laydate
        ,table = layui.table
        ,layer = layui.layer;

    // 页面加载时初始化下拉菜单数据
    loadTradeConditions();
    loadPayConditions();
    loadTransportConditions();

    // 初始化日期选择器
    laydate.render({
        elem: '#salesDate',
        format: 'yyyy-MM-dd',
        trigger: 'click'
    });

    // 获取销售额NO（优先使用销售额NO，不再使用ID）
    var salesAmountNo = $('#salesAmountNo').val() || getUrlParam('salesAmountNo');

    // 获取URL参数的函数
    function getUrlParam(name) {
        var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
        var r = window.location.search.substr(1).match(reg);
        if (r != null) return unescape(r[2]);
        return null;
    }

    // 初始化表格 - 使用原生HTML表格（参考新增页面）
    initializeTwoRowEditableTable();

    // 加载数据 - 只使用销售额NO
    if (salesAmountNo && salesAmountNo.trim() !== '') {
        console.log('使用销售额NO获取数据:', salesAmountNo);
        loadDataByNo(salesAmountNo);
    } else {
        console.log('没有有效的销售额NO，无法加载数据');
        layer.msg('缺少销售额NO参数，无法加载数据', {icon: 2});
    }
    
    // 监听客户选择
    form.on('select(customerCodeFilter)', function(data){
        console.log('客户选择:', data);
        console.log('客户代码:', data.value);

        // 先清空货币和税率字段
        $('#currency').val('');
        $('#taxRate').val('');
        form.render('select');

        // 只有在用户明确选择了客户时才更新客户代码字段
        if (data.value && data.value.trim() !== '') {
            // 更新表格中的客户代码字段
            $('#detailTable tbody.record').each(function() {
                var record = $(this);
                var firstRow = record.find('tr:first-child');
                var customerProductCodeInput = firstRow.find('input[name="customerProductCode"]');

                // 只有当客户代码字段为空时才更新
                if (!customerProductCodeInput.val() || customerProductCodeInput.val().trim() === '') {
                    customerProductCodeInput.val(data.value);
                }
            });
        }
        
        if (data.value) {
            // 获取客户详情信息
            $.ajax({
                url: baselocation + '/order/salesLogin/getCustomerInfo',
                type: 'POST',
                data: {customerCode: data.value},
                success: function(result) {
                    console.log('获取客户详情信息结果:', result); // 添加日志，便于调试
                    if (result.code === 1 && result.data) {
                        var customerInfo = result.data;
                        console.log('客户详情信息:', customerInfo); // 添加日志，便于调试
                        
                        // 设置货币
                        if (customerInfo && customerInfo.结算货币) {
                            $('#currency').val(customerInfo.结算货币 || 'RMB');
                            console.log('设置货币:', customerInfo.结算货币);
                        }

                        // 根据税代码获取税率（联动方式）
                        if (customerInfo && customerInfo.税代码) {
                            $('#taxCode').val(customerInfo.税代码);
                            getTaxRateByTaxCode(customerInfo.税代码);
                            console.log('根据税代码获取税率:', customerInfo.税代码);
                        } else {
                            console.log('客户详情信息中没有税代码');
                            // 清空税率和税代码
                            $('#taxRate').val('');
                            $('#taxCode').val('');
                        }

                        // 设置交易条件、付款条件、运输条件等字段的默认值
                        if (customerInfo) {
                            // 交易条件：设置下拉菜单的默认选中值
                            if (customerInfo.交易条件) {
                                $('#tradeCondition').val(customerInfo.交易条件);
                                console.log('设置交易条件默认值:', customerInfo.交易条件);
                            }

                            // 付款条件：设置下拉菜单的默认选中值
                            if (customerInfo.付款条件) {
                                $('#payCondition').val(customerInfo.付款条件);
                                console.log('设置付款条件默认值:', customerInfo.付款条件);
                            }

                            // 运输条件：设置下拉菜单的默认选中值
                            if (customerInfo.运输条件) {
                                $('#transportCondition').val(customerInfo.运输条件);
                                console.log('设置运输条件默认值:', customerInfo.运输条件);
                            }

                            // 重新渲染表单以更新下拉菜单显示
                            form.render('select');
                        }

                        form.render('select'); // 重新渲染表单
                    } else {
                        console.log('获取客户详情信息失败:', result.message); // 添加日志，便于调试
                        layer.msg('获取客户信息失败: ' + (result.message || 'failed'), {icon: 2});
                    }
                },
                error: function(xhr, status, error) {
                    console.log('获取客户详情信息异常:', status, error); // 添加日志，便于调试
                    layer.msg('获取客户信息失败: ' + error, {icon: 2});
                }
            });
        }
    });

    // 监听默认铜合同类别选择
    form.on('select(defaultCopperContractTypeFilter)', function(data){
        console.log('编辑页面默认铜合同类别选择:', data.value);
        var customerCode = $('#customerCode').val();
        var salesDate = $('#salesDate').val();
        handleEditDefaultCopperContractTypeChange(data.value, customerCode, salesDate);
    });

    // 监听默认铜合同NO选择
    form.on('select(defaultCopperContractNoFilter)', function(data){
        console.log('编辑页面默认铜合同NO选择:', data.value);
        handleEditDefaultCopperContractNoChange(data.value);
    });

    // 监听默认铜条件选择
    form.on('select(defaultCopperConditionFilter)', function(data){
        console.log('编辑页面默认铜条件选择:', data.value);
        handleEditDefaultCopperConditionChange(data.value);
    });

    // 应用默认值到所有明细
    $('#applyDefaultValues').click(function(){
        applyEditDefaultValuesToAllDetails();
    });

    // 根据销售额NO加载数据
    function loadDataByNo(salesAmountNo) {
        // 发送AJAX请求获取销售额数据
        $.ajax({
            url: baselocation + '/order/salesLogin/detailByNo',
            type: 'POST',
            data: {salesAmountNo: salesAmountNo},
            success: function(result) {
                console.log('获取编辑数据结果:', result);
                if (result.code === 1) {
                    var salesAmount = result.data.salesAmount;
                    var salesAmountDetails = result.data.salesAmountDetails;

                    console.log('销售额数据:', salesAmount);
                    console.log('销售额明细数据:', salesAmountDetails);

                    // 填充表单数据
                    $('#salesNo').val(salesAmount.salesAmountNo);
                    $('#salesDate').val(salesAmount.salesAmountDate);
                    $('#currency').val(salesAmount.settlementCurrency);
                    // 保存税代码到隐藏字段
                    $('#taxCode').val(salesAmount.taxCode);
                    // 需要通过税代码获取实际税率来显示
                    if (salesAmount.taxCode) {
                        getTaxRateByTaxCode(salesAmount.taxCode);
                    }

                    console.log('编辑页面加载条件字段 - 交易条件:', salesAmount.tradeCondition, '付款条件:', salesAmount.payCondition, '运输条件:', salesAmount.transportCondition);
                    console.log('编辑页面加载客户代码:', salesAmount.customerCode);

                    // 先设置客户代码值
                    $('#customerCode').val(salesAmount.customerCode);

                    // 刷新表单，使下拉框选中值生效
                    layui.form.render('select');

                    // 延迟处理，确保Layui下拉菜单正确设置选中状态
                    setTimeout(function() {
                        console.log('=== 延迟设置客户代码 ===');
                        console.log('要设置的客户代码:', salesAmount.customerCode, '(类型:', typeof salesAmount.customerCode, ')');
                        console.log('当前客户代码值:', $('#customerCode').val());

                        // 检查下拉框选项是否存在（参考支付请求登录编辑页面的成功实现）
                        console.log('=== 检查所有客户选项 ===');
                        var customerOptions = $('#customerCode option');
                        var foundCustomer = false;
                        var matchedOption = null;
                        console.log('下拉框总选项数:', customerOptions.length);

                        // 第一步：精确匹配客户代码
                        customerOptions.each(function(index) {
                            var optionValue = $(this).val();
                            var optionText = $(this).text();
                            console.log('选项' + index + ':', 'value="' + optionValue + '"', 'text="' + optionText + '"');

                            // 精确匹配客户代码
                            if (optionValue === salesAmount.customerCode) {
                                foundCustomer = true;
                                matchedOption = $(this);
                                console.log('✅ 找到匹配的客户选项:', optionValue, optionText);
                            }
                        });

                        // 第二步：如果精确匹配失败，尝试模糊匹配
                        if (!foundCustomer) {
                            console.warn('❌ 未找到匹配的客户选项，客户代码:', salesAmount.customerCode);
                            // 如果没有找到精确匹配，尝试模糊匹配
                            customerOptions.each(function() {
                                var optionText = $(this).text();
                                if (optionText.indexOf(salesAmount.customerCode) !== -1 ||
                                    (salesAmount.customerAlias && optionText.indexOf(salesAmount.customerAlias) !== -1)) {
                                    foundCustomer = true;
                                    matchedOption = $(this);
                                    console.log('✅ 通过模糊匹配找到客户选项:', $(this).val(), optionText);
                                    return false; // 跳出循环
                                }
                            });
                        }

                        // 设置需求方下拉框（参考支付请求登录编辑页面的成功实现）
                        console.log('=== 设置客户代码 ===');
                        if (foundCustomer && matchedOption) {
                            $('#customerCode').val(matchedOption.val());
                            console.log('设置客户选择为:', matchedOption.val(), matchedOption.text());
                        } else {
                            // 如果没有找到匹配项，直接设置值
                            $('#customerCode').val(salesAmount.customerCode);
                            console.log('直接设置客户代码:', salesAmount.customerCode);
                        }

                        // 设置条件字段（在客户代码设置后进行）
                        console.log('=== 设置条件字段 ===');
                        $('#tradeCondition').val(salesAmount.tradeCondition || '');
                        $('#payCondition').val(salesAmount.payCondition || '');
                        $('#transportCondition').val(salesAmount.transportCondition || '');

                        console.log('设置交易条件:', salesAmount.tradeCondition);
                        console.log('设置付款条件:', salesAmount.payCondition);
                        console.log('设置运输条件:', salesAmount.transportCondition);

                        // 重新渲染表单，确保下拉框显示正确的选中状态
                        layui.form.render('select');

                        // 验证条件字段是否设置成功
                        setTimeout(function() {
                            console.log('=== 验证条件字段设置结果 ===');
                            console.log('交易条件最终值:', $('#tradeCondition').val());
                            console.log('付款条件最终值:', $('#payCondition').val());
                            console.log('运输条件最终值:', $('#transportCondition').val());

                            // 检查是否有条件字段设置失败
                            var failedConditions = [];
                            if (salesAmount.tradeCondition && $('#tradeCondition').val() !== salesAmount.tradeCondition) {
                                failedConditions.push('交易条件');
                            }
                            if (salesAmount.payCondition && $('#payCondition').val() !== salesAmount.payCondition) {
                                failedConditions.push('付款条件');
                            }
                            if (salesAmount.transportCondition && $('#transportCondition').val() !== salesAmount.transportCondition) {
                                failedConditions.push('运输条件');
                            }

                            if (failedConditions.length > 0) {
                                console.warn('⚠️ 以下条件字段设置失败:', failedConditions.join(', '));
                                layer.msg('注意：' + failedConditions.join('、') + ' 未能自动选择，请手动选择', {icon: 0, time: 3000});
                            } else {
                                console.log('✅ 所有条件字段设置成功！');
                            }
                        }, 100);

                        // 最终检查客户代码（参考支付请求登录编辑页面的成功实现）
                        console.log('最终的需求方值:', $('#customerCode').val());

                        // 如果仍然没有正确设置，显示警告
                        if ($('#customerCode').val() !== salesAmount.customerCode && !foundCustomer) {
                            console.warn('⚠️ 客户选择设置失败，可能需要手动选择客户');
                            layer.msg('注意：未能自动选择客户，请手动选择正确的需求方', {icon: 0, time: 3000});
                        } else {
                            console.log('✅ 客户代码设置成功！');
                        }
                    }, 200);

                    // 渲染明细表格
                    renderDetailTable(salesAmountDetails, salesAmount);
                } else {
                    layer.msg(result.message || '获取数据失败', {icon: 2});
                }
            },
            error: function() {
                layer.msg('获取数据失败', {icon: 2});
            }
        });
    }
    
    // 渲染明细表格
    function renderDetailTable(detailData, salesAmount) {
        // 从销售额数据中获取客户代码
        var customerCode = salesAmount ? salesAmount.customerCode : $('#customerCode').val();
        console.log('销售额主表客户代码:', customerCode);

        // 处理明细数据
        var tableData = detailData.map(function(item) {
            console.log('处理明细数据项:', item);
            console.log('铜合同类别字段检查:', {
                copperContractType: item.copperContractType,
                铜合同类别: item.铜合同类别,
                最终值: item.copperContractType || item.铜合同类别 || "3"
            });

            // 处理到货日期
            var arrivalDate = item.arrivalDate || "";
            if (arrivalDate) {
                try {
                    var dateObj = new Date(arrivalDate);
                    if (!isNaN(dateObj.getTime())) {
                        var year = dateObj.getFullYear();
                        var month = ('0' + (dateObj.getMonth() + 1)).slice(-2);
                        var day = ('0' + dateObj.getDate()).slice(-2);
                        arrivalDate = year + '-' + month + '-' + day;
                    }
                } catch (e) {
                    console.log('日期格式化出错:', e);
                }
            }

            return {
                deliveryNoteNo: item.deliveryNoteNo || item.deliveryOrderNo || item.出货单No || "",
                deliveryNoteSeq: item.deliveryNoteSeq || item.deliveryOrderSeq || item.送货单序号 || "",
                productCategoryCode: item.productCategoryCode || item.品目C || "", // 条码字段
                productName: item.productCode || item.品名 || "", // 产品代码字段
                reelName: item.reelName || item.线盘 || "",
                customerProductCode: item.customerProductCode || customerCode || item.客户品目C || "", // 客户代码字段
                outboundDate: item.outboundDate || item.shipmentDate || item.出货日 || "",
                arrivalDate: arrivalDate,
                customerOrderNo: item.customerOrderNo || item.客户订单No || "",
                remark: item.detailRemark || item.remark || item.明细备注 || item.明细内部备注 || "",
                copperContractType: item.copperContractType || item.铜合同类别 || "3",
                copperContractNo: item.copperContractNo || item.铜合同No || item.铜签约No || "",
                copperCondition: item.copperCondition || item.铜条件 || "",
                currency: item.currency || item.铜货币 || "",
                conversionRate: item.conversionRate || item.换算率 || "1.0",
                quantity: item.quantity || item.数量 || "0",
                copperBase: item.copperBase || item.铜base || "0",
                premium: item.premium || item.升水 || "0",
                convertedCopperPrice: item.convertedCopperPrice || "0",
                zeroBase: item.zeroBase || item.零基数 || item.零基础 || "0",
                salesUnitPrice: item.salesUnitPrice || "0",
                salesAmount: item.salesAmount || "0",
                productMiddleCategory: item.productMiddleCategory || item.productCategory || item.产品中分类 || "",
                shipmentPlanNo: item.shipmentPlanNo || item.出货计划番号 || item.出库计划番号 || ""
            };
        });

        // 清空表格数据并重新加载数据
        $('#detailTable tbody.record').remove();
        tableData.forEach(function(item) {
            addDetailRowToTwoRowTable(item);
        });

        // 计算总金额
        calculateTotalAmount();

        // 延迟计算税额，确保税率字段已经设置完成
        setTimeout(function() {
            var totalAmount = parseFloat($('#totalAmount').val()) || 0;
            console.log('编辑页面延迟计算税额 - 总金额:', totalAmount, '税率字段值:', $('#taxRate').val());
            if (totalAmount > 0) {
                calculateTaxAmount(totalAmount);
            } else {
                // 即使总金额为0，也尝试计算一次税额以显示0.00
                calculateTaxAmount(0);
            }
        }, 500);

        // 初始化铜合同类别字段状态
        setTimeout(function() {
            initializeCopperContractTypeFields();
        }, 200); // 延迟确保DOM完全渲染
    }

    // 已移除"添加明细"按钮，编辑页面不再支持手动添加明细

    // 添加明细行到两行表格
    function addDetailRowToTwoRowTable(item) {
        var tableBody = $('#detailTable');

        // 处理数据
        var processedItem = {
            deliveryNoteNo: item.deliveryNoteNo || '',
            deliveryNoteSeq: item.deliveryNoteSeq || '',
            productCategoryCode: item.productCategoryCode || '',
            productName: item.productName || '',
            reelName: item.reelName || '',
            customerProductCode: item.customerProductCode || '',
            outboundDate: item.outboundDate || '',
            arrivalDate: item.arrivalDate || '',
            customerOrderNo: item.customerOrderNo || '',
            remark: item.remark || '',
            copperContractType: item.copperContractType || '3',
            copperContractNo: item.copperContractNo || '',
            copperCondition: item.copperCondition || '',
            currency: item.currency || '',
            conversionRate: item.conversionRate || '1.0',
            quantity: item.quantity || '',
            copperBase: item.copperBase || '',
            premium: item.premium || '',
            convertedCopperPrice: item.convertedCopperPrice || '',
            zeroBase: item.zeroBase || '',
            salesUnitPrice: item.salesUnitPrice || '',
            salesAmount: item.salesAmount || '',
            productMiddleCategory: item.productMiddleCategory || '',
            shipmentPlanNo: item.shipmentPlanNo || item.出货计划番号 || item.出库计划番号 || ''
        };

        // 生成铜合同类别下拉框
        console.log('生成下拉框，铜合同类别值:', processedItem.copperContractType);
        var copperTypeOptions = [
            {value: '1', text: '预约铜'},
            {value: '2', text: '支给铜'},
            {value: '3', text: '一般铜'},
            {value: '4', text: '无偿'}
        ];
        var copperTypeSelect = '<select name="copperContractType" onchange="updateCopperContractTypeInTable(this)" lay-ignore>';
        copperTypeOptions.forEach(function(option) {
            var selected = (processedItem.copperContractType == option.value) ? 'selected' : '';
            console.log('选项:', option.value, '当前值:', processedItem.copperContractType, '是否选中:', selected);
            copperTypeSelect += '<option value="' + option.value + '" ' + selected + '>' + option.text + '</option>';
        });
        copperTypeSelect += '</select>';
        console.log('生成的下拉框HTML:', copperTypeSelect);

        // 生成每条记录的两行
        var html = '<tbody class="record">';
        // 第一行：基础信息数据（13列）
        html += '<tr>';
        html += '<td><input type="text" name="deliveryNoteNo" value="' + processedItem.deliveryNoteNo + '"></td>';
        html += '<td><input type="text" name="deliveryNoteSeq" value="' + processedItem.deliveryNoteSeq + '"></td>';
        html += '<td><input type="text" name="productCategoryCode" value="' + processedItem.productCategoryCode + '"></td>';
        html += '<td><input type="text" name="productName" value="' + processedItem.productName + '"></td>';
        html += '<td><input type="text" name="reelName" value="' + processedItem.reelName + '"></td>';
        html += '<td><input type="text" name="customerProductCode" value="' + processedItem.customerProductCode + '"></td>';
        html += '<td><input type="text" name="outboundDate" value="' + processedItem.outboundDate + '"></td>';
        html += '<td><input type="date" name="arrivalDate" value="' + processedItem.arrivalDate + '"></td>';
        html += '<td><input type="text" name="customerOrderNo" value="' + processedItem.customerOrderNo + '"></td>';
        html += '<td><input type="text" name="remark" value="' + processedItem.remark + '"></td>';
        html += '<td>' + copperTypeSelect + '</td>';
        html += '<td><input type="text" name="copperContractNo" value="' + processedItem.copperContractNo + '"></td>';
        html += '<td><input type="text" name="copperCondition" value="' + processedItem.copperCondition + '"></td>';
        html += '</tr>';
        // 第二行：计算信息数据（13列）
        html += '<tr>';
        html += '<td><input type="text" name="currency" value="' + processedItem.currency + '"></td>';
        html += '<td><input type="text" name="conversionRate" value="' + processedItem.conversionRate + '"></td>';
        html += '<td><input type="text" name="quantity" value="' + processedItem.quantity + '"></td>';
        html += '<td><input type="text" name="copperBase" value="' + processedItem.copperBase + '"></td>';
        html += '<td><input type="text" name="premium" value="' + processedItem.premium + '"></td>';
        html += '<td><input type="text" name="convertedCopperPrice" value="' + processedItem.convertedCopperPrice + '" readonly></td>';
        html += '<td><input type="text" name="zeroBase" value="' + processedItem.zeroBase + '"></td>';
        html += '<td><input type="text" name="salesUnitPrice" value="' + processedItem.salesUnitPrice + '" readonly></td>';
        html += '<td><input type="text" name="salesAmount" value="' + processedItem.salesAmount + '" readonly></td>';
        html += '<td><input type="text" name="productMiddleCategory" value="' + processedItem.productMiddleCategory + '"></td>';
        html += '<td><input type="text" name="shipmentPlanNo" value="' + processedItem.shipmentPlanNo + '"></td>';
        html += '<td><button type="button" class="delete-btn" onclick="deleteRecordFromTable(this)">删除</button></td>';
        html += '<td></td>'; // 空单元格，对应第一行的铜条件位置
        html += '</tr>';
        html += '</tbody>';

        tableBody.append(html);
    }

    // 从表格中删除记录
    function deleteRecordFromTable(btn) {
        var record = $(btn).closest('tbody.record');
        record.remove();
        // 重新计算总金额
        calculateTotalAmount();
    }

    // 更新表格中的铜合同类别
    function updateCopperContractTypeInTable(select) {
        var copperContractType = $(select).val();
        var $currentRow = $(select).closest('tr');
        var $tbody = $currentRow.closest('tbody.record');
        var $secondRow = $tbody.find('tr:last-child');

        console.log('铜合同类别已更新为:', copperContractType);

        // 获取相关字段
        var $copperContractNoSelect = $currentRow.find('select[name="copperContractNo"]');
        var $copperConditionInput = $secondRow.find('input[name="copperCondition"]');
        var $currencyInput = $secondRow.find('input[name="currency"]');

        if (copperContractType === '1' || copperContractType === '2') {
            // 预约铜/支给铜：启用铜合同NO下拉框，清空铜条件和铜货币
            $copperContractNoSelect.prop('disabled', false);
            $copperConditionInput.val('').prop('readonly', true);
            $currencyInput.val('').prop('readonly', true);

            // 加载铜合同列表
            var customerCode = $currentRow.find('input[name="customerProductCode"]').val();
            var arrivalDate = $currentRow.find('input[name="arrivalDate"]').val();
            if (customerCode && arrivalDate) {
                loadCopperContractListForRowEdit($copperContractNoSelect, customerCode, copperContractType, arrivalDate);
            }

        } else if (copperContractType === '3' || copperContractType === '4') {
            // 一般铜/无偿：禁用铜合同NO，清空相关字段，启用铜条件选择
            $copperContractNoSelect.prop('disabled', true).empty().append('<option value="">不适用</option>');
            $copperConditionInput.val('').prop('readonly', false);
            $currencyInput.val('').prop('readonly', true);

            if (copperContractType === '3') {
                // 一般铜：先创建铜条件下拉框，然后查询升水表获取默认值
                console.log('编辑页面：一般铜模式，查询升水表');

                // 将铜条件输入框替换为下拉框
                replaceCopperConditionInputWithSelectEdit($copperConditionInput);

                // 获取客户代码和到货日期
                var customerCode = $copperContractNoSelect.closest('tr').find('input[name="customerProductCode"]').val();
                var arrivalDate = $copperContractNoSelect.closest('tr').find('input[name="arrivalDate"]').val();

                // 查询升水表数据设置默认值
                queryAscendingWaterForGeneralCopperEdit($copperContractNoSelect.closest('tr'), customerCode, arrivalDate);
            } else {
                // 无偿：将铜条件输入框替换为下拉框
                replaceCopperConditionInputWithSelectEdit($copperConditionInput);
            }
        }
    }

    // 为单行加载铜合同列表（编辑页面专用）
    function loadCopperContractListForRowEdit($select, customerCode, copperContractType, arrivalDate) {
        if (!customerCode || !copperContractType) {
            return;
        }

        // 计算使用月（从到货日期提取年月）
        var useMonth = '';
        if (arrivalDate) {
            var dateParts = arrivalDate.split('-');
            if (dateParts.length >= 2) {
                useMonth = dateParts[0] + '-' + dateParts[1];
            }
        }

        console.log('编辑页面为单行加载铜合同列表:', customerCode, copperContractType, useMonth);

        $.ajax({
            url: baselocation + '/order/salesLogin/getCopperContractList',
            type: 'POST',
            data: {
                customerCode: customerCode,
                copperContractType: copperContractType,
                useMonth: useMonth,
                status: '0'
            },
            success: function(result) {
                console.log('编辑页面铜合同列表返回:', result);

                if (result.code === 1 && result.data) {
                    $select.empty().append('<option value="">请选择</option>');

                    $.each(result.data, function(index, item) {
                        var contractNo = item.copperSignNo || '';
                        var copperCondition = item.copperCondition || '';
                        var currency = item.currency || '';

                        if (contractNo) {
                            var optionHtml = '<option value="' + contractNo + '"' +
                                           ' data-copper-condition="' + copperCondition + '"' +
                                           ' data-currency="' + currency + '">' +
                                           contractNo + '</option>';
                            $select.append(optionHtml);
                        }
                    });

                    // 绑定选择事件
                    $select.off('change').on('change', function() {
                        var selectedOption = $(this).find('option:selected');
                        var copperCondition = selectedOption.data('copper-condition');
                        var currency = selectedOption.data('currency');
                        var copperContractNo = selectedOption.val();
                        var $currentRow = $(this).closest('tr');
                        var $tbody = $currentRow.closest('tbody.record');
                        var $secondRow = $tbody.find('tr:last-child');

                        // 填入铜条件和铜货币
                        $secondRow.find('input[name="copperCondition"]').val(copperCondition || '');
                        $secondRow.find('input[name="currency"]').val(currency || '');

                        console.log('编辑页面选择了铜合同:', copperContractNo, '铜条件:', copperCondition, '货币:', currency);

                        // 查询铜合同详情获取铜base（预约铜/支给铜）
                        if (copperContractNo) {
                            var arrivalDate = $currentRow.find('input[name="arrivalDate"]').val();
                            if (arrivalDate) {
                                var useMonth = arrivalDate.substring(0, 7); // 截取年月部分
                                queryCopperBaseForContractCopperEdit($tbody, copperContractNo, useMonth);
                            }
                        }
                    });

                } else {
                    $select.empty().append('<option value="">无可用合同</option>');
                    console.log('编辑页面获取铜合同列表失败:', result.message);
                }
            },
            error: function(xhr, status, error) {
                console.log('编辑页面加载铜合同列表异常:', error);
                $select.empty().append('<option value="">加载失败</option>');
            }
        });
    }



    // 获取铜条件详情（包括货币）（编辑页面专用）
    function getCopperConditionDetailEdit(copperCondition, $select) {
        $.ajax({
            url: baselocation + '/salesCommon/copperConditions/json',
            type: 'POST',
            data: {
                copperCondition: copperCondition
            },
            success: function(result) {
                console.log('编辑页面铜条件详情返回:', result);

                if (result.code === 1 && result.data) {
                    var currency = result.data.currency || '';

                    // 设置货币字段
                    var $currentRow = $select.closest('tr');
                    var $tbody = $currentRow.closest('tbody.record');
                    var $secondRow = $tbody.find('tr:last-child');
                    var $currencyInput = $secondRow.find('input[name="currency"]');
                    $currencyInput.val(currency);

                    console.log('编辑页面设置铜条件货币:', currency);
                } else {
                    console.log('编辑页面获取铜条件详情失败:', result.message);
                }
            },
            error: function(xhr, status, error) {
                console.log('编辑页面获取铜条件详情异常:', error);
            }
        });
    }

    // 获取表格中的所有数据
    function getTableData() {
        var data = [];
        $('#detailTable tbody.record').each(function() {
            var record = $(this);
            var firstRow = record.find('tr:first-child');
            var secondRow = record.find('tr:last-child');

            var item = {
                deliveryNoteNo: firstRow.find('input[name="deliveryNoteNo"]').val(),
                deliveryNoteSeq: firstRow.find('input[name="deliveryNoteSeq"]').val(),
                productCategoryCode: firstRow.find('input[name="productCategoryCode"]').val(),
                productName: firstRow.find('input[name="productName"]').val(),
                reelName: firstRow.find('input[name="reelName"]').val(),
                customerProductCode: firstRow.find('input[name="customerProductCode"]').val(),
                outboundDate: firstRow.find('input[name="outboundDate"]').val(),
                arrivalDate: firstRow.find('input[name="arrivalDate"]').val(),
                customerOrderNo: firstRow.find('input[name="customerOrderNo"]').val(),
                remark: firstRow.find('input[name="remark"]').val(),
                copperContractType: firstRow.find('select[name="copperContractType"]').val(),
                copperContractNo: firstRow.find('input[name="copperContractNo"]').val() || firstRow.find('select[name="copperContractNo"]').val(),
                copperCondition: firstRow.find('input[name="copperCondition"]').val() || firstRow.find('select[name="copperCondition"]').val(),
                currency: secondRow.find('input[name="currency"]').val(),
                conversionRate: secondRow.find('input[name="conversionRate"]').val(),
                quantity: secondRow.find('input[name="quantity"]').val(),
                copperBase: secondRow.find('input[name="copperBase"]').val(),
                premium: secondRow.find('input[name="premium"]').val(),
                convertedCopperPrice: secondRow.find('input[name="convertedCopperPrice"]').val(),
                zeroBase: secondRow.find('input[name="zeroBase"]').val(),
                salesUnitPrice: secondRow.find('input[name="salesUnitPrice"]').val(),
                salesAmount: secondRow.find('input[name="salesAmount"]').val(),
                productMiddleCategory: secondRow.find('input[name="productMiddleCategory"]').val(),
                shipmentPlanNo: secondRow.find('input[name="shipmentPlanNo"]').val()
            };

            data.push(item);
        });

        return data;
    }

    // 计算总金额的函数
    function calculateTotalAmount() {
        var totalAmount = 0;
        var tableData = getTableData();

        if (tableData && tableData.length > 0) {
            tableData.forEach(function(item) {
                if (item && item.salesAmount) {
                    totalAmount += parseFloat(item.salesAmount) || 0;
                }
            });
        }

        $('#totalAmount').val(totalAmount.toFixed(2));

        // 计算税额 = 总金额 × 税率
        calculateTaxAmount(totalAmount);
    }

    // 计算税额的函数
    function calculateTaxAmount(totalAmount) {
        var taxRate = parseFloat($('#taxRate').val()) || 0;
        var taxAmount = totalAmount * (taxRate / 100); // 税率是百分比，需要除以100
        $('#taxAmount').val(taxAmount.toFixed(2));
        console.log('编辑页面计算税额 - 总金额:', totalAmount, '税率:', taxRate + '%', '税额:', taxAmount.toFixed(2));
        console.log('编辑页面税率字段值:', $('#taxRate').val(), '税率字段是否存在:', $('#taxRate').length);
    }

    // 使用事件委托处理产品代码变更事件，自动查询产品单价
    $(document).on('change blur', 'input[name="productName"]', function() {
        var productCode = $(this).val();
        var $currentRow = $(this).closest('tr');

        if (productCode) {
            // 获取客户代码和到货日期
            var customerCode = $currentRow.find('input[name="customerProductCode"]').val();
            var arrivalDate = $currentRow.find('input[name="arrivalDate"]').val();

            console.log('产品代码变更，尝试查询产品单价:', {
                productCode: productCode,
                customerCode: customerCode,
                arrivalDate: arrivalDate
            });

            if (customerCode && arrivalDate) {
                queryProductPrice(customerCode, productCode, arrivalDate, $currentRow);
            } else {
                console.log('客户代码或到货日期为空，无法查询产品单价');
            }
        }
    });

    // 使用事件委托处理到货日期变更事件，自动查询产品单价
    $(document).on('change', 'input[name="arrivalDate"]', function() {
        var arrivalDate = $(this).val();
        var $currentRow = $(this).closest('tr');

        if (arrivalDate) {
            // 获取客户代码和产品代码
            var customerCode = $currentRow.find('input[name="customerProductCode"]').val();
            var productCode = $currentRow.find('input[name="productName"]').val();

            console.log('到货日期变更，尝试查询产品单价:', {
                arrivalDate: arrivalDate,
                customerCode: customerCode,
                productCode: productCode
            });

            if (customerCode && productCode) {
                queryProductPrice(customerCode, productCode, arrivalDate, $currentRow);
            } else {
                console.log('客户代码或产品代码为空，无法查询产品单价');
            }
        }
    });

    // 使用事件委托处理0base、铜base、升水、换算率、数量字段变更事件，重新计算销售单价和销售金额
    $(document).on('change blur', 'input[name="zeroBase"], input[name="copperBase"], input[name="premium"], input[name="conversionRate"], input[name="quantity"]', function() {
        var $currentRow = $(this).closest('tr');
        var $tbody = $currentRow.closest('tbody.record');
        var $secondRow = $tbody.find('tr:last-child');

        // 获取所有相关字段的值
        var zeroBase = parseFloat($secondRow.find('input[name="zeroBase"]').val()) || 0;
        var copperBase = parseFloat($secondRow.find('input[name="copperBase"]').val()) || 0;
        var premium = parseFloat($secondRow.find('input[name="premium"]').val()) || 0;
        var conversionRate = parseFloat($secondRow.find('input[name="conversionRate"]').val()) || 1;
        var quantity = parseFloat($secondRow.find('input[name="quantity"]').val()) || 0;

        // 计算换算后铜单价 = (铜base + 升水) * 换算率
        var convertedCopperPrice = (copperBase + premium) * conversionRate;
        $secondRow.find('input[name="convertedCopperPrice"]').val(convertedCopperPrice.toFixed(4));

        // 计算销售单价 = 换算后铜单价 + 0base
        var salesUnitPrice = convertedCopperPrice + zeroBase;
        $secondRow.find('input[name="salesUnitPrice"]').val(salesUnitPrice.toFixed(4));

        // 计算销售金额 = 销售单价 * 数量
        var salesAmount = salesUnitPrice * quantity;
        $secondRow.find('input[name="salesAmount"]').val(salesAmount.toFixed(2));

        // 重新计算总金额
        calculateTotalAmount();

        console.log('编辑页面字段变更重新计算:', {
            zeroBase: zeroBase,
            copperBase: copperBase,
            premium: premium,
            conversionRate: conversionRate,
            quantity: quantity,
            convertedCopperPrice: convertedCopperPrice,
            salesUnitPrice: salesUnitPrice,
            salesAmount: salesAmount
        });
    });

    // 旧的函数已移除，使用新的两行表格实现

    // 监听表单提交
    form.on('submit(formDemo)', function(data) {
        // 表单数据
        var formData = data.field;
        
        // 获取表格数据
        var tableData = getTableData();
        
        // 验证明细
        if (!tableData || tableData.length === 0) {
            layer.msg('请添加明细数据', {icon: 2});
            return false;
        }
        
        // 确保销售额NO正确设置
        formData.salesAmountNo = $('#salesNo').val() || salesAmountNo;
        formData.salesAmountDate = $('#salesDate').val(); // 确保销售额日正确设置
        // 使用保存的税代码，而不是税率值
        formData.taxCode = $('#taxCode').val() || formData.taxRate; // 优先使用税代码，如果没有则使用税率值作为兼容
        
        console.log('提交表单时的销售额NO:', formData.salesAmountNo); // 添加日志，便于调试
        console.log('提交表单时的销售额日:', formData.salesAmountDate); // 添加日志，便于调试
        console.log('提交表单时的税代码:', formData.taxCode); // 添加日志，便于调试
        
        // 处理表格数据，确保所有字段都正确设置
        for (var i = 0; i < tableData.length; i++) {
            // 确保产品中分类不为空
            if (!tableData[i].productMiddleCategory) {
                tableData[i].productMiddleCategory = tableData[i].productMiddleCategory || '未分类';
            }
            
            // 确保明细备注字段存在
            tableData[i].remark = tableData[i].remark || '';
            
            // 添加日志输出，确保明细备注字段的值被正确传递
            console.log('第' + (i + 1) + '行明细备注:', tableData[i].remark);
            console.log('第' + (i + 1) + '行产品中分类:', tableData[i].productMiddleCategory);
            // 添加铜合同类别的调试信息
            console.log('第' + (i + 1) + '行铜合同类别:', tableData[i].copperContractType);
            
            // 确保到货日期格式正确
            if (tableData[i].arrivalDate) {
                try {
                    var dateObj = new Date(tableData[i].arrivalDate);
                    if (!isNaN(dateObj.getTime())) {
                        var year = dateObj.getFullYear();
                        var month = ('0' + (dateObj.getMonth() + 1)).slice(-2);
                        var day = ('0' + dateObj.getDate()).slice(-2);
                        tableData[i].arrivalDate = year + '-' + month + '-' + day;
                    }
                } catch (e) {
                    console.log('日期格式化出错:', e);
                }
            }
            
            // 确保数值字段是数值类型
            tableData[i].conversionRate = parseFloat(tableData[i].conversionRate) || 0;
            tableData[i].quantity = parseFloat(tableData[i].quantity) || 0;
            tableData[i].copperBase = parseFloat(tableData[i].copperBase) || 0;
            tableData[i].premium = parseFloat(tableData[i].premium) || 0;
            tableData[i].zeroBase = parseFloat(tableData[i].zeroBase) || 0;
            
            // 计算其他字段 - 使用正确的公式
            // 换算后铜单价 = (铜base + 升水) * 换算率
            tableData[i].convertedCopperPrice = ((tableData[i].copperBase + tableData[i].premium) * tableData[i].conversionRate).toFixed(4);
            // 销售单价 = 换算后铜单价 + 0base
            tableData[i].salesUnitPrice = (parseFloat(tableData[i].convertedCopperPrice) + tableData[i].zeroBase).toFixed(4);
            // 销售金额 = 销售单价 * 数量
            tableData[i].salesAmount = (parseFloat(tableData[i].salesUnitPrice) * tableData[i].quantity).toFixed(2);
        }

        // 添加客户相关条件字段
        formData.tradeCondition = $('#tradeCondition').val() || '';
        formData.payCondition = $('#payCondition').val() || '';
        formData.transportCondition = $('#transportCondition').val() || '';

        console.log('客户条件字段 - 交易条件:', formData.tradeCondition, '付款条件:', formData.payCondition, '运输条件:', formData.transportCondition);

        formData.salesDetails = JSON.stringify(tableData);

        // 打印提交的数据，便于调试
        console.log('提交的表单数据:', formData);
        console.log('提交的明细数据:', tableData);
        
        // 发送AJAX请求更新数据
        $.ajax({
            url: baselocation + '/order/salesLogin/updateByNo',
            type: 'POST',
            contentType: 'application/json',
            data: JSON.stringify(formData),
            success: function(result) {
                console.log('更新结果:', result); // 添加日志，便于调试
                if (result.code === 1) {
                    layer.msg('更新成功', {icon: 1, time: 1000}, function(){
                        closeLayer();
                    });
                } else {
                    layer.msg(result.message || '更新失败', {icon: 2});
                }
            },
            error: function(xhr, status, error) {
                console.log('更新失败:', xhr.responseText); // 添加错误日志
                layer.msg('更新失败，请检查网络', {icon: 2});
            }
        });
        
        return false; // 阻止表单默认提交
    });

    // 监听铜合同类别下拉框变更事件（使用事件委托）
    $(document).on('change', 'select[name="copperContractType"]', function() {
        var copperContractType = $(this).val();
        var $currentRow = $(this).closest('tr');
        var $tbody = $currentRow.closest('tbody.record');

        // 从当前行获取客户代码（客户品目C字段）和到货日期
        var customerCode = $currentRow.find('input[name="customerProductCode"]').val();
        var arrivalDate = $currentRow.find('input[name="arrivalDate"]').val();

        // 调试信息
        console.log('=== 铜合同类别变更调试信息 ===');
        console.log('铜合同类别:', copperContractType);
        console.log('客户代码(从当前行获取):', customerCode);
        console.log('到货日期:', arrivalDate);
        console.log('客户代码输入框数量:', $currentRow.find('input[name="customerProductCode"]').length);
        console.log('当前行所有input字段:', $currentRow.find('input').map(function() { return $(this).attr('name') + '=' + $(this).val(); }).get());
        console.log('================================');

        // 检查必要参数
        if (!customerCode) {
            layer.msg('请先填写客户品目C（客户代码）', {icon: 0, time: 2000});
            return;
        }

        if ((copperContractType === '1' || copperContractType === '2') && !arrivalDate) {
            layer.msg('预约铜/支给铜需要先填写到货日期', {icon: 0, time: 2000});
            return;
        }

        handleCopperContractTypeChange(copperContractType, $currentRow, customerCode, arrivalDate);
    });

    // 使用事件委托处理动态生成的铜条件下拉框change事件
    $(document).on('change', 'select[name="copperCondition"]', function() {
        var selectedOption = $(this).find('option:selected');
        var currency = selectedOption.data('currency');
        var copperCondition = selectedOption.val();

        console.log('编辑页面铜条件选择事件触发:', copperCondition, '货币:', currency);

        // 获取货币字段（在第二行）
        var $currentRow = $(this).closest('tr');
        var $tbody = $currentRow.closest('tbody.record');
        var $secondRow = $tbody.find('tr:last-child');
        var $currencyInput = $secondRow.find('input[name="currency"]');

        console.log('编辑页面找到的货币字段数量:', $currencyInput.length);
        console.log('编辑页面货币字段元素:', $currencyInput);

        if (currency) {
            // 直接设置货币
            $currencyInput.val(currency);
            console.log('编辑页面直接设置铜条件货币:', currency, '设置后的值:', $currencyInput.val());
        } else if (copperCondition) {
            // 如果没有货币信息，通过接口获取
            console.log('编辑页面通过接口获取铜条件货币:', copperCondition);
            getCopperConditionDetailEdit(copperCondition, $(this));
        } else {
            // 清空货币
            $currencyInput.val('');
            console.log('编辑页面清空铜条件货币');
        }

        // 根据铜合同类别决定是否查询铜base
        var $copperContractTypeSelect = $currentRow.find('select[name="copperContractType"]');
        var copperContractType = $copperContractTypeSelect.val();

        if (copperContractType === '3' || copperContractType === '4') {
            // 一般铜/无偿：查询铜价表获取铜base
            var arrivalDate = $currentRow.find('input[name="arrivalDate"]').val();
            if (copperCondition && arrivalDate) {
                queryCopperBaseForGeneralCopperEdit($tbody, copperCondition, arrivalDate);
            }
        }
    });

    // 页面加载完成后初始化表格中的日期选择器
    // 旧的日期选择器代码已移除，使用新的两行表格实现

    // 页面加载完成后初始化铜合同类别字段状态
    setTimeout(function() {
        initializeCopperContractTypeFields();
    }, 500); // 延迟500ms确保DOM完全渲染


});

// 初始化可编辑的两行表格（参考新增页面）
function initializeTwoRowEditableTable() {
    // 添加样式
    if (!$('#twoRowEditableStyle').length) {
        var style = $('<style id="twoRowEditableStyle">');
        style.text(
            '#detailTable {' +
                'width: 100%;' +
                'border-collapse: collapse;' +
                'margin-top: 10px;' +
            '}' +
            '#detailTable th, #detailTable td {' +
                'border: 1px solid #e6e6e6;' +
                'padding: 8px 6px;' +
                'text-align: center;' +
                'word-break: break-all;' +
                'font-size: 12px;' +
                'line-height: 1.3;' +
                'vertical-align: middle;' +
            '}' +
            '/* 产品代码列宽度调整 - 第一行第4列 */' +
            '#detailTable thead tr:first-child th:nth-child(5), ' +
            '#detailTable tbody.record tr:first-child td:nth-child(5) {' +
                'min-width: 120px;' +
                'width: 120px;' +
            '}' +
            '/* 注意：删除了单位列，所以不再需要单位列的宽度调整 */' +
            '#detailTable thead th {' +
                'font-weight: bold;' +
                'white-space: nowrap;' +
            '}' +
            '/* 第一行表头 - 基础信息 */' +
            '#detailTable thead tr:first-child th {' +
                'background-color: #e6f7ff;' +
                'color: #1890ff;' +
                'border-bottom: 1px solid #1890ff;' +
            '}' +
            '/* 第二行表头 - 计算信息 */' +
            '#detailTable thead tr:last-child th {' +
                'background-color: #fff2e8;' +
                'color: #fa8c16;' +
                'border-bottom: 1px solid #fa8c16;' +
            '}' +
            '/* 每条记录用两个 <tr>，不同记录间用背景色区分 */' +
            '#detailTable tbody.record:nth-of-type(odd) td {' +
                'background: #f8f8f8;' +
            '}' +
            '#detailTable tbody.record:nth-of-type(even) td {' +
                'background: #fff;' +
            '}' +
            '/* 让第二行数据和第一行数据视觉上依然"连成一体" */' +
            '#detailTable tbody.record tr:first-child td {' +
                'border-bottom: none;' +
                'border-left: 3px solid #1890ff;' +
            '}' +
            '#detailTable tbody.record tr:last-child td {' +
                'border-top: none;' +
                'border-left: 3px solid #fa8c16;' +
                'border-bottom: 2px solid #d2d2d2;' +
            '}' +
            '.table-container {' +
                'overflow-x: auto;' +
                'max-height: 400px;' +
                'overflow-y: auto;' +
            '}' +
            '/* 输入框和下拉框样式 */' +
            '#detailTable input, #detailTable select {' +
                'width: 100%;' +
                'border: 1px solid #d9d9d9;' +
                'background: #fff;' +
                'padding: 4px;' +
                'font-size: 12px;' +
                'height: 28px;' +
                'box-sizing: border-box;' +
                'display: block;' +
                'position: relative;' +
                'z-index: 1;' +
            '}' +
            '#detailTable input:focus, #detailTable select:focus {' +
                'outline: 1px solid #1890ff;' +
                'background: #fff;' +
                'border-color: #1890ff;' +
            '}' +
            '/* 确保下拉框可见 */' +
            '#detailTable select {' +
                'appearance: auto;' +
                '-webkit-appearance: menulist;' +
                '-moz-appearance: menulist;' +
                'cursor: pointer;' +
            '}' +
            '/* 表格单元格样式调整 */' +
            '#detailTable td {' +
                'position: relative;' +
                'overflow: visible;' +
            '}' +
            '/* 删除按钮样式 */' +
            '.delete-btn {' +
                'background: #ff4d4f;' +
                'color: white;' +
                'border: none;' +
                'padding: 2px 8px;' +
                'border-radius: 2px;' +
                'cursor: pointer;' +
                'font-size: 12px;' +
            '}' +
            '.delete-btn:hover {' +
                'background: #ff7875;' +
            '}' +
            '/* 强制显示下拉框 */' +
            '#detailTable select[name="copperContractType"] {' +
                'display: block !important;' +
                'visibility: visible !important;' +
                'opacity: 1 !important;' +
                'width: 100% !important;' +
                'height: 28px !important;' +
                'border: 1px solid #d9d9d9 !important;' +
                'background: #fff !important;' +
                'font-size: 12px !important;' +
                'z-index: 999 !important;' +
                'position: relative !important;' +
            '}' +
            '/* 调试用：给铜合同类别单元格添加背景色 */' +
            '#detailTable tbody.record tr:first-child td:nth-child(12) {' +
                'background-color: #e6f7ff !important;' +
                'border: 2px solid #1890ff !important;' +
            '}' +
            '/* 隐藏Layui自动生成的下拉框 */' +
            '#detailTable .layui-form-select {' +
                'display: none !important;' +
            '}' +
            '/* 确保原生下拉框显示 */' +
            '#detailTable select[lay-ignore] {' +
                'display: block !important;' +
                'visibility: visible !important;' +
            '}'
        );
        $('head').append(style);
    }

    // 生成初始的空表格HTML
    var html = '<div class="table-container"><table id="detailTable">';

    // 生成表头 - 两行，每行13列
    html += '<thead>';
    // 第一行表头 - 基础信息标题
    html += '<tr>';
    html += '<th>出货单No.</th><th>送货单序号</th><th>条码</th><th>产品代码</th>';
    html += '<th>线盘</th><th>客户代码</th><th>出货日</th><th>到货日期</th><th>客户订单No.</th>';
    html += '<th>明细内部备注</th><th>铜合同类别</th><th>铜合同NO</th><th>铜条件</th>';
    html += '</tr>';
    // 第二行表头 - 计算信息标题
    html += '<tr>';
    html += '<th>铜货币</th><th>换算率</th><th>数量(KG)</th><th>铜base</th>';
    html += '<th>升水</th><th>换算后铜单价</th><th>0base</th><th>销售单价</th>';
    html += '<th>销售金额</th><th>产品中分类</th><th>出货计划番号</th><th>操作</th><th></th>';
    html += '</tr>';
    html += '</thead>';

    html += '</table></div>';

    // 替换原来的表格
    var $container = $('#detailTable').parent();
    console.log('表格容器:', $container.length, $container.html());

    // 保留新增明细按钮，只替换表格部分
    var $addByShipmentBtn = $container.find('#addDetailByShipmentBtn').parent();
    $container.html('');
    if ($addByShipmentBtn.length > 0) {
        $container.append($addByShipmentBtn);
    } else {
        $container.append('<div style="margin-bottom: 10px;"><button type="button" class="layui-btn layui-btn-normal" id="addDetailByShipmentBtn"><i class="layui-icon layui-icon-add-circle"></i> 新增明细</button></div>');
    }
    $container.append(html);

    console.log('已初始化可编辑的两行表格，容器内容:', $container.html());

    // 新增明细按钮点击事件（根据出货单No.自动填充）
    $('#addDetailByShipmentBtn').off('click').on('click', function(){
        // 弹出输入框让用户输入出货单No.
        layer.prompt({
            title: '请输入出货单No.',
            formType: 0, // 输入框类型
            value: '', // 默认值
            maxlength: 50 // 最大长度
        }, function(shipmentNo, index){
            if (shipmentNo && shipmentNo.trim()) {
                // 根据出货单No.获取明细数据
                getDetailsByShipmentNoForEdit(shipmentNo.trim());
                layer.close(index);
            } else {
                layer.msg('请输入有效的出货单No.', {icon: 2});
            }
        });
    });
}

// 关闭弹窗函数
function closeLayer() {
    var index = parent.layer.getFrameIndex(window.name);
    parent.layer.close(index);
    parent.location.reload();
}

// 根据税代码获取税率
function getTaxRateByTaxCode(taxCode) {
    if (!taxCode) {
        return;
    }

    $.ajax({
        url: baselocation + '/salesCommon/taxRateCodeFun/json',
        type: 'POST',
        data: {taxRateCode: taxCode},
        success: function(result) {
            console.log('获取税率结果:', result);
            if (result.code === 1 && result.data && result.data.taxRate) {
                $('#taxRate').val(result.data.taxRate);
                console.log('设置税率:', result.data.taxRate);
                // 税率变更后重新计算税额
                var totalAmount = parseFloat($('#totalAmount').val()) || 0;
                calculateTaxAmount(totalAmount);
            } else {
                console.log('未找到对应的税率数据');
                // 如果找不到税率，显示税代码作为兼容
                $('#taxRate').val(taxCode);
                // 清空税额
                $('#taxAmount').val('0.00');
            }
        },
        error: function(xhr, status, error) {
            console.error('获取税率失败:', error);
            // 如果请求失败，显示税代码作为兼容
            $('#taxRate').val(taxCode);
            // 清空税额
            $('#taxAmount').val('0.00');
        }
    });
}

// 重复的函数已移除

// 添加明细行到两行表格（参考新增页面）
function addDetailRowToTwoRowTable(item) {
    var tableBody = $('#detailTable');

    // 处理数据
    var processedItem = {
        deliveryNoteNo: item.deliveryNoteNo || '',
        deliveryNoteSeq: item.deliveryNoteSeq || '',
        productCategoryCode: item.productCategoryCode || '',
        productName: item.productName || '',
        reelName: item.reelName || '',
        customerProductCode: item.customerProductCode || '',
        outboundDate: item.outboundDate || '',
        arrivalDate: item.arrivalDate || '',
        customerOrderNo: item.customerOrderNo || '',
        remark: item.remark || '',
        copperContractType: item.copperContractType || '3',
        copperContractNo: item.copperContractNo || '',
        copperCondition: item.copperCondition || '',
        currency: item.currency || '',
        conversionRate: item.conversionRate || '1.0',
        quantity: item.quantity || '',
        copperBase: item.copperBase || '',
        premium: item.premium || '',
        convertedCopperPrice: item.convertedCopperPrice || '',
        zeroBase: item.zeroBase || '',
        salesUnitPrice: item.salesUnitPrice || '',
        salesAmount: item.salesAmount || '',
        productMiddleCategory: item.productMiddleCategory || '',
        shipmentPlanNo: item.shipmentPlanNo || item.出货计划番号 || item.出库计划番号 || ''
    };

    // 生成铜合同类别下拉框
    var copperTypeOptions = [
        {value: '1', text: '预约铜'},
        {value: '2', text: '支给铜'},
        {value: '3', text: '一般铜'},
        {value: '4', text: '无偿'}
    ];
    var copperTypeSelect = '<select name="copperContractType" onchange="updateCopperContractTypeInTable(this)" lay-ignore>';
    copperTypeOptions.forEach(function(option) {
        var selected = (processedItem.copperContractType == option.value) ? 'selected' : '';
        copperTypeSelect += '<option value="' + option.value + '" ' + selected + '>' + option.text + '</option>';
    });
    copperTypeSelect += '</select>';

    // 生成每条记录的两行
    var html = '<tbody class="record">';
    // 第一行：基础信息数据（12列）
    html += '<tr>';
    html += '<td><input type="text" name="deliveryNoteNo" value="' + processedItem.deliveryNoteNo + '"></td>';
    html += '<td><input type="text" name="deliveryNoteSeq" value="' + processedItem.deliveryNoteSeq + '"></td>';
    html += '<td><input type="text" name="productCategoryCode" value="' + processedItem.productCategoryCode + '"></td>';
    html += '<td><input type="text" name="productName" value="' + processedItem.productName + '"></td>';
    html += '<td><input type="text" name="reelName" value="' + processedItem.reelName + '"></td>';
    html += '<td><input type="text" name="customerProductCode" value="' + processedItem.customerProductCode + '"></td>';
    html += '<td><input type="text" name="outboundDate" value="' + processedItem.outboundDate + '"></td>';
    html += '<td><input type="date" name="arrivalDate" value="' + processedItem.arrivalDate + '"></td>';
    html += '<td><input type="text" name="customerOrderNo" value="' + processedItem.customerOrderNo + '"></td>';
    html += '<td><input type="text" name="remark" value="' + processedItem.remark + '"></td>';
    html += '<td>' + copperTypeSelect + '</td>';
    html += '<td><input type="text" name="copperContractNo" value="' + processedItem.copperContractNo + '"></td>';
    html += '<td><input type="text" name="copperCondition" value="' + processedItem.copperCondition + '"></td>';
    html += '</tr>';
    // 第二行：计算信息数据（13列）
    html += '<tr>';
    html += '<td><input type="text" name="currency" value="' + processedItem.currency + '"></td>';
    html += '<td><input type="text" name="conversionRate" value="' + processedItem.conversionRate + '"></td>';
    html += '<td><input type="text" name="quantity" value="' + processedItem.quantity + '"></td>';
    html += '<td><input type="text" name="copperBase" value="' + processedItem.copperBase + '"></td>';
    html += '<td><input type="text" name="premium" value="' + processedItem.premium + '"></td>';
    html += '<td><input type="text" name="convertedCopperPrice" value="' + processedItem.convertedCopperPrice + '" readonly></td>';
    html += '<td><input type="text" name="zeroBase" value="' + processedItem.zeroBase + '"></td>';
    html += '<td><input type="text" name="salesUnitPrice" value="' + processedItem.salesUnitPrice + '" readonly></td>';
    html += '<td><input type="text" name="salesAmount" value="' + processedItem.salesAmount + '" readonly></td>';
    html += '<td><input type="text" name="productMiddleCategory" value="' + processedItem.productMiddleCategory + '"></td>';
    html += '<td><input type="text" name="shipmentPlanNo" value="' + processedItem.shipmentPlanNo + '"></td>';
    html += '<td><button type="button" class="delete-btn" onclick="deleteRecordFromTable(this)">删除</button></td>';
    html += '<td></td>'; // 空单元格，对应第一行的铜条件位置
    html += '</tr>';
    html += '</tbody>';

    tableBody.append(html);

    // 调试：检查下拉框是否正确插入
    setTimeout(function() {
        var selectCount = $('#detailTable select[name="copperContractType"]').length;
        console.log('下拉框数量:', selectCount);

        $('#detailTable select[name="copperContractType"]').each(function(index, select) {
            console.log('下拉框', index, ':', {
                element: select,
                visible: $(select).is(':visible'),
                display: $(select).css('display'),
                visibility: $(select).css('visibility'),
                opacity: $(select).css('opacity'),
                width: $(select).width(),
                height: $(select).height(),
                value: $(select).val(),
                html: $(select).html()
            });
        });

        // 强制显示下拉框
        $('#detailTable select[name="copperContractType"]').css({
            'display': 'block',
            'visibility': 'visible',
            'opacity': '1',
            'width': '100%',
            'height': '28px',
            'border': '1px solid #d9d9d9',
            'background': '#fff',
            'z-index': '999'
        });

        console.log('已强制设置下拉框样式');
    }, 100);
}

// 从表格中删除记录
function deleteRecordFromTable(btn) {
    var record = $(btn).closest('tbody.record');
    record.remove();
    // 重新计算总金额
    calculateTotalAmount();
}

// 更新表格中的铜合同类别
function updateCopperContractTypeInTable(select) {
    var copperContractType = $(select).val();
    var $currentRow = $(select).closest('tr');
    var $tbody = $currentRow.closest('tbody.record');
    var $secondRow = $tbody.find('tr:last-child');

    console.log('铜合同类别已更新为:', copperContractType);

    // 获取相关字段
    var $copperContractNoSelect = $currentRow.find('select[name="copperContractNo"]');
    var $copperConditionInput = $secondRow.find('input[name="copperCondition"]');
    var $currencyInput = $secondRow.find('input[name="currency"]');

    if (copperContractType === '1' || copperContractType === '2') {
        // 预约铜/支给铜：启用铜合同NO下拉框，清空铜条件和铜货币
        $copperContractNoSelect.prop('disabled', false);
        $copperConditionInput.val('').prop('readonly', true);
        $currencyInput.val('').prop('readonly', true);

        // 加载铜合同列表
        var customerCode = $currentRow.find('input[name="customerProductCode"]').val();
        var arrivalDate = $currentRow.find('input[name="arrivalDate"]').val();
        if (customerCode && arrivalDate) {
            loadCopperContractListForRowEdit($copperContractNoSelect, customerCode, copperContractType, arrivalDate);
        }

    } else if (copperContractType === '3' || copperContractType === '4') {
        // 一般铜/无偿：禁用铜合同NO，清空相关字段，启用铜条件选择
        $copperContractNoSelect.prop('disabled', true).empty().append('<option value="">不适用</option>');
        $copperConditionInput.val('').prop('readonly', false);
        $currencyInput.val('').prop('readonly', true);

        if (copperContractType === '3') {
            // 一般铜：先创建铜条件下拉框，然后查询升水表获取默认值
            console.log('编辑页面表格：一般铜模式，查询升水表');

            // 将铜条件输入框替换为下拉框
            replaceCopperConditionInputWithSelectEdit($copperConditionInput);

            // 获取客户代码和到货日期
            var customerCode = $currentRow.find('input[name="customerProductCode"]').val();
            var arrivalDate = $currentRow.find('input[name="arrivalDate"]').val();

            // 查询升水表数据设置默认值
            queryAscendingWaterForGeneralCopperEdit($currentRow, customerCode, arrivalDate);
        } else {
            // 无偿：将铜条件输入框替换为下拉框
            replaceCopperConditionInputWithSelectEdit($copperConditionInput);
        }
    }
}

// 获取表格中的所有数据
function getTableData() {
    var data = [];
    $('#detailTable tbody.record').each(function() {
        var record = $(this);
        var firstRow = record.find('tr:first-child');
        var secondRow = record.find('tr:last-child');

        var item = {
            deliveryNoteNo: firstRow.find('input[name="deliveryNoteNo"]').val(),
            deliveryNoteSeq: firstRow.find('input[name="deliveryNoteSeq"]').val(),
            productCategoryCode: firstRow.find('input[name="productCategoryCode"]').val(),
            productName: firstRow.find('input[name="productName"]').val(),
            reelName: firstRow.find('input[name="reelName"]').val(),
            customerProductCode: firstRow.find('input[name="customerProductCode"]').val(),
            outboundDate: firstRow.find('input[name="outboundDate"]').val(),
            arrivalDate: firstRow.find('input[name="arrivalDate"]').val(),
            customerOrderNo: firstRow.find('input[name="customerOrderNo"]').val(),
            remark: firstRow.find('input[name="remark"]').val(),
            copperContractType: firstRow.find('select[name="copperContractType"]').val(),
            copperContractNo: firstRow.find('input[name="copperContractNo"]').val() || firstRow.find('select[name="copperContractNo"]').val(),
            copperCondition: firstRow.find('input[name="copperCondition"]').val() || firstRow.find('select[name="copperCondition"]').val(),
            currency: secondRow.find('input[name="currency"]').val(),
            conversionRate: secondRow.find('input[name="conversionRate"]').val(),
            quantity: secondRow.find('input[name="quantity"]').val(),
            copperBase: secondRow.find('input[name="copperBase"]').val(),
            premium: secondRow.find('input[name="premium"]').val(),
            convertedCopperPrice: secondRow.find('input[name="convertedCopperPrice"]').val(),
            zeroBase: secondRow.find('input[name="zeroBase"]').val(),
            salesUnitPrice: secondRow.find('input[name="salesUnitPrice"]').val(),
            salesAmount: secondRow.find('input[name="salesAmount"]').val(),
            productMiddleCategory: secondRow.find('input[name="productMiddleCategory"]').val(),
            shipmentPlanNo: secondRow.find('input[name="shipmentPlanNo"]').val()
        };

        data.push(item);
    });

    return data;
}

// 计算总金额的函数
function calculateTotalAmount() {
    var totalAmount = 0;
    var tableData = getTableData();

    if (tableData && tableData.length > 0) {
        tableData.forEach(function(item) {
            if (item && item.salesAmount) {
                totalAmount += parseFloat(item.salesAmount) || 0;
            }
        });
    }

    $('#totalAmount').val(totalAmount.toFixed(2));

    // 计算税额 = 总金额 × 税率
    calculateTaxAmount(totalAmount);
}

// 处理铜合同类别变更逻辑
function handleCopperContractTypeChange(copperContractType, $currentRow, customerCode, arrivalDate) {
    console.log('处理铜合同类别变更:', copperContractType, customerCode, arrivalDate);
    console.log('当前行HTML:', $currentRow.html().substring(0, 200) + '...');

    // 获取铜合同NO和铜条件的输入框（都在同一行）
    var $copperContractNoInput = $currentRow.find('input[name="copperContractNo"]');
    var $copperConditionInput = $currentRow.find('input[name="copperCondition"]');

    console.log('找到的铜合同NO输入框数量:', $copperContractNoInput.length);
    console.log('找到的铜条件输入框数量:', $copperConditionInput.length);

    // 如果在当前行没找到，尝试查找所有相关输入框
    if ($copperContractNoInput.length === 0) {
        console.log('当前行未找到铜合同NO输入框，查找当前行所有input:');
        $currentRow.find('input').each(function(index) {
            console.log('  input[' + index + ']:', $(this).attr('name'), '=', $(this).val());
        });

        // 尝试在整个tbody中查找
        var $tbody = $currentRow.closest('tbody.record');
        $copperContractNoInput = $tbody.find('select[name="copperContractNo"]');
        $copperConditionInput = $tbody.find('input[name="copperCondition"]');
        console.log('在整个tbody中找到的铜合同NO输入框数量:', $copperContractNoInput.length);
        console.log('在整个tbody中找到的铜条件输入框数量:', $copperConditionInput.length);
    }

    if (copperContractType === '1' || copperContractType === '2') {
        // 预约铜或支给铜：铜合同下拉框可选
        console.log('预约铜/支给铜模式');

        if ($copperContractNoInput.length > 0) {
            // 清空铜合同和铜条件字段
            $copperContractNoInput.val('');
            $copperConditionInput.val('');

            // 直接在当前位置创建下拉框并加载数据
            createCopperContractSelectInPlace($copperContractNoInput, customerCode, copperContractType, arrivalDate);
        } else {
            console.log('错误：未找到铜合同NO输入框');
        }

        // 铜条件输入框禁用，等待从合同中获取
        $copperConditionInput.prop('disabled', true).val('').attr('placeholder', '选择铜合同后自动填入');

    } else if (copperContractType === '3' || copperContractType === '4') {
        // 一般铜或无偿：铜合同下拉框disabled
        console.log('一般铜/无偿模式');

        // 清空铜合同和铜条件字段
        if ($copperContractNoInput.length > 0) {
            $copperContractNoInput.val('');
        }
        $copperConditionInput.val('');

        // 将铜合同NO字段设为禁用状态
        disableCopperContractField($copperContractNoInput);

        // 铜条件字段启用，并加载铜条件表数据
        $copperConditionInput.prop('disabled', false).attr('placeholder', '请选择铜条件');
        createCopperConditionSelectInPlace($copperConditionInput);

    } else {
        // 未选择或选择了其他值：重置所有字段
        console.log('重置模式');

        // 清空所有相关字段
        if ($copperContractNoInput.length > 0) {
            $copperContractNoInput.val('');
        }
        $copperConditionInput.val('');

        // 恢复字段为可编辑的输入框状态
        restoreCopperContractField($copperContractNoInput);
        $copperConditionInput.prop('disabled', false).attr('placeholder', '');
    }
}

// 将铜合同NO输入框替换为下拉框
function replaceCopperContractInputWithSelect($input, customerCode, copperContractType, arrivalDate) {
    var currentValue = $input.val();
    var inputName = $input.attr('name');

    // 创建下拉框
    var $select = $('<select name="' + inputName + '" class="layui-input"></select>');
    $select.append('<option value="">请选择</option>');

    // 替换输入框
    $input.replaceWith($select);

    // 加载铜合同列表
    loadCopperContractList(customerCode, copperContractType, arrivalDate, $select, currentValue);
}

// 将铜合同NO下拉框替换为禁用的输入框
function replaceCopperContractSelectWithInput($element) {
    var inputName = $element.attr('name');
    var $input = $('<input type="text" name="' + inputName + '" class="layui-input" disabled value="不适用">');
    $element.replaceWith($input);
}

// 加载铜合同列表
function loadCopperContractList(customerCode, copperContractType, arrivalDate, $select, selectedValue) {
    // 详细检查每个必要参数
    var missingParams = [];
    if (!customerCode) missingParams.push('客户品目C（客户代码）');
    if (!copperContractType) missingParams.push('铜合同类别');
    if (!arrivalDate) missingParams.push('到货日期');

    if (missingParams.length > 0) {
        var message = '无法加载铜合同列表，请先填写：' + missingParams.join('、');
        console.log(message);
        layer.msg(message, {icon: 0, time: 3000});
        $select.empty().append('<option value="">请先填写' + missingParams.join('、') + '</option>');
        return;
    }

    // 从到货日期提取年月
    var useMonth = arrivalDate.substring(0, 7); // YYYY-MM格式

    console.log('加载铜合同列表参数:', {
        customerCode: customerCode,
        copperContractType: copperContractType,
        useMonth: useMonth
    });

    $.ajax({
        url: baselocation + '/order/salesLogin/getCopperContractList',
        type: 'POST',
        data: {
            customerCode: customerCode,
            copperContractType: copperContractType,
            useMonth: useMonth,
            status: '0'
        },
        success: function(result) {
            console.log('=== 铜合同列表AJAX返回 ===');
            console.log('完整返回结果:', result);
            console.log('返回码:', result.code);
            console.log('返回数据:', result.data);
            console.log('数据类型:', typeof result.data);
            console.log('数据长度:', result.data ? result.data.length : 'null');

            if (result.code === 1 && result.data) {
                console.log('开始填充下拉框选项...');
                $select.empty().append('<option value="">请选择</option>');

                $.each(result.data, function(index, item) {
                    console.log('处理第' + index + '项数据:', item);
                    console.log('铜合同NO字段:', item.copperSignNo);
                    console.log('铜条件字段:', item.copperCondition);

                    // 使用正确的字段名
                    var contractNo = item.copperSignNo || '';
                    var copperCondition = item.copperCondition || '';
                    var currency = item.currency || '';

                    if (contractNo) {
                        var selected = (contractNo === selectedValue) ? 'selected' : '';
                        $select.append('<option value="' + contractNo + '" ' + selected +
                                     ' data-copper-condition="' + copperCondition + '"' +
                                     ' data-currency="' + currency + '">' +
                                     contractNo + '</option>');
                        console.log('已添加选项:', contractNo, '铜条件:', copperCondition, '货币:', currency);
                    } else {
                        console.log('警告：第' + index + '项数据没有有效的合同号');
                    }
                });

                console.log('下拉框最终选项数量:', $select.find('option').length);
                console.log('下拉框HTML:', $select.html());

                // 绑定选择事件
                $select.off('change').on('change', function() {
                    var selectedOption = $(this).find('option:selected');
                    var copperCondition = selectedOption.data('copper-condition');
                    var currency = selectedOption.data('currency');
                    var copperContractNo = selectedOption.val();
                    var $copperConditionInput = $(this).closest('tr').find('input[name="copperCondition"]');

                    // 填入铜条件
                    $copperConditionInput.val(copperCondition || '');

                    // 获取铜货币字段（在第二行）
                    var $currentRow = $(this).closest('tr');
                    var $tbody = $currentRow.closest('tbody.record');
                    var $currencyInput = $tbody.find('input[name="currency"]');

                    // 直接设置货币信息（从选项的data属性中获取）
                    $currencyInput.val(currency || '');

                    console.log('选择了铜合同:', copperContractNo, '铜条件:', copperCondition, '货币:', currency);
                });

            } else {
                console.log('获取铜合同列表失败或无数据');
                $select.empty().append('<option value="">无可用合同</option>');
                layer.msg(result.message || '获取铜合同列表失败', {icon: 2});
            }
            console.log('=========================');
        },
        error: function(xhr, status, error) {
            console.log('加载铜合同列表失败:', error);
            $select.empty().append('<option value="">加载失败</option>');
            layer.msg('加载铜合同列表失败', {icon: 2});
        }
    });
}

// 加载铜条件选项
function loadCopperConditionOptions($input) {
    $.ajax({
        url: baselocation + '/order/salesLogin/getCopperConditionList',
        type: 'POST',
        success: function(result) {
            console.log('铜条件列表返回:', result);
            if (result.code === 1 && result.data) {
                // 将输入框替换为下拉框
                var inputName = $input.attr('name');
                var currentValue = $input.val();
                var $select = $('<select name="' + inputName + '" class="layui-input"></select>');
                $select.append('<option value="">请选择</option>');

                $.each(result.data, function(index, item) {
                    var value = typeof item === 'object' ? item.copperCondition : item;
                    var selected = (value === currentValue) ? 'selected' : '';
                    $select.append('<option value="' + value + '" ' + selected + '>' + value + '</option>');
                });

                $input.replaceWith($select);
            } else {
                layer.msg(result.message || '获取铜条件列表失败', {icon: 2});
            }
        },
        error: function(xhr, status, error) {
            console.log('加载铜条件列表失败:', error);
            layer.msg('加载铜条件列表失败', {icon: 2});
        }
    });
}

// 直接在原地创建铜合同下拉框并加载数据
function createCopperContractSelectInPlace($input, customerCode, copperContractType, arrivalDate) {
    console.log('=== 在原地创建铜合同下拉框 ===');

    var currentValue = $input.val();
    var inputName = $input.attr('name');

    // 创建下拉框
    var $select = $('<select name="' + inputName + '" class="layui-input"></select>');
    $select.append('<option value="">加载中...</option>');

    // 替换输入框
    $input.replaceWith($select);

    console.log('已替换为下拉框，开始加载数据...');

    // 检查必要参数
    var missingParams = [];
    if (!customerCode) missingParams.push('客户品目C（客户代码）');
    if (!copperContractType) missingParams.push('铜合同类别');
    if (!arrivalDate) missingParams.push('到货日期');

    if (missingParams.length > 0) {
        var message = '无法加载铜合同列表，请先填写：' + missingParams.join('、');
        console.log(message);
        layer.msg(message, {icon: 0, time: 3000});
        $select.empty().append('<option value="">请先填写' + missingParams.join('、') + '</option>');
        return;
    }

    // 从到货日期提取年月
    var useMonth = arrivalDate.substring(0, 7); // YYYY-MM格式

    console.log('加载铜合同列表参数:', {
        customerCode: customerCode,
        copperContractType: copperContractType,
        useMonth: useMonth
    });

    $.ajax({
        url: baselocation + '/order/salesLogin/getCopperContractList',
        type: 'POST',
        data: {
            customerCode: customerCode,
            copperContractType: copperContractType,
            useMonth: useMonth,
            status: '0'
        },
        success: function(result) {
            console.log('=== 铜合同列表AJAX返回 ===');
            console.log('返回结果:', result);

            if (result.code === 1 && result.data) {
                console.log('开始填充下拉框选项，数据数量:', result.data.length);

                // 清空并重新填充选项
                $select.empty().append('<option value="">请选择</option>');

                $.each(result.data, function(index, item) {
                    var contractNo = item.copperSignNo || '';
                    var copperCondition = item.copperCondition || '';
                    var currency = item.currency || '';

                    if (contractNo) {
                        var selected = (contractNo === currentValue) ? 'selected' : '';
                        var optionHtml = '<option value="' + contractNo + '" ' + selected +
                                       ' data-copper-condition="' + copperCondition + '"' +
                                       ' data-currency="' + currency + '">' +
                                       contractNo + '</option>';
                        $select.append(optionHtml);
                        console.log('添加选项:', contractNo, '铜条件:', copperCondition, '货币:', currency);
                    }
                });

                console.log('最终选项数量:', $select.find('option').length);
                console.log('下拉框HTML:', $select.html());

                // 绑定选择事件
                $select.off('change').on('change', function() {
                    var selectedOption = $(this).find('option:selected');
                    var copperCondition = selectedOption.data('copper-condition');
                    var currency = selectedOption.data('currency');
                    var copperContractNo = selectedOption.val();
                    var $copperConditionInput = $(this).closest('tr').find('input[name="copperCondition"]');

                    // 填入铜条件
                    $copperConditionInput.val(copperCondition || '');

                    // 获取铜货币字段（在第二行）
                    var $currentRow = $(this).closest('tr');
                    var $tbody = $currentRow.closest('tbody.record');
                    var $currencyInput = $tbody.find('input[name="currency"]');

                    // 直接设置货币信息（从选项的data属性中获取）
                    $currencyInput.val(currency || '');

                    console.log('选择了铜合同:', copperContractNo, '铜条件:', copperCondition, '货币:', currency);
                });

            } else {
                console.log('获取铜合同列表失败或无数据');
                $select.empty().append('<option value="">无可用合同</option>');
                layer.msg(result.message || '获取铜合同列表失败', {icon: 2});
            }
            console.log('=========================');
        },
        error: function(xhr, status, error) {
            console.log('加载铜合同列表失败:', error);
            $select.empty().append('<option value="">加载失败</option>');
            layer.msg('加载铜合同列表失败', {icon: 2});
        }
    });

    console.log('=== 创建完成 ===');
}

// 禁用铜合同字段
function disableCopperContractField($input) {
    if ($input.length === 0) return;

    // 如果是下拉框，禁用它
    if ($input[0].tagName === 'SELECT') {
        $input.prop('disabled', true).empty().append('<option value="">不适用</option>');
    } else {
        // 如果是输入框，设为禁用状态
        $input.prop('disabled', true).val('不适用');
    }
}

// 恢复铜合同字段为可编辑输入框
function restoreCopperContractField($input) {
    if ($input.length === 0) return;

    var inputName = $input.attr('name');

    // 如果当前是下拉框，替换为输入框
    if ($input[0].tagName === 'SELECT') {
        var $newInput = $('<input type="text" name="' + inputName + '" class="layui-input" value="">');
        $input.replaceWith($newInput);
    } else {
        // 如果是输入框，启用并清空
        $input.prop('disabled', false).val('');
    }
}

// 在原地创建铜条件下拉框并加载数据
function createCopperConditionSelectInPlace($input) {
    console.log('=== 在原地创建铜条件下拉框 ===');

    var currentValue = $input.val();
    var inputName = $input.attr('name');

    // 创建下拉框
    var $select = $('<select name="' + inputName + '" class="layui-input"></select>');
    $select.append('<option value="">加载中...</option>');

    // 替换输入框
    $input.replaceWith($select);

    console.log('已替换为铜条件下拉框，开始加载数据...');

    $.ajax({
        url: baselocation + '/order/salesLogin/getCopperConditionList',
        type: 'POST',
        success: function(result) {
            console.log('=== 铜条件列表AJAX返回 ===');
            console.log('返回结果:', result);

            if (result.code === 1 && result.data) {
                console.log('开始填充铜条件选项，数据数量:', result.data.length);

                // 清空并重新填充选项
                $select.empty().append('<option value="">请选择</option>');

                $.each(result.data, function(index, item) {
                    var value = typeof item === 'object' ? item.copperCondition : item;
                    var selected = (value === currentValue) ? 'selected' : '';
                    $select.append('<option value="' + value + '" ' + selected + '>' + value + '</option>');
                    console.log('添加铜条件选项:', value);
                });

                console.log('铜条件最终选项数量:', $select.find('option').length);

            } else {
                console.log('获取铜条件列表失败或无数据');
                $select.empty().append('<option value="">无可用铜条件</option>');
                layer.msg(result.message || '获取铜条件列表失败', {icon: 2});
            }
            console.log('=========================');
        },
        error: function(xhr, status, error) {
            console.log('加载铜条件列表失败:', error);
            $select.empty().append('<option value="">加载失败</option>');
            layer.msg('加载铜条件列表失败', {icon: 2});
        }
    });

    console.log('=== 铜条件创建完成 ===');
}

// 初始化所有铜合同类别字段的状态
function initializeCopperContractTypeFields() {
    console.log('=== 初始化铜合同类别字段状态 ===');

    // 查找所有铜合同类别下拉框
    $('select[name="copperContractType"]').each(function() {
        var $select = $(this);
        var copperContractType = $select.val();
        var $currentRow = $select.closest('tr');

        console.log('初始化铜合同类别:', copperContractType);

        if (copperContractType && copperContractType !== '') {
            // 获取必要的参数
            var customerCode = $currentRow.find('input[name="customerProductCode"]').val();
            var arrivalDate = $currentRow.find('input[name="arrivalDate"]').val();

            console.log('初始化参数:', {
                copperContractType: copperContractType,
                customerCode: customerCode,
                arrivalDate: arrivalDate
            });

            // 触发铜合同类别变更逻辑，但不清空现有值
            handleCopperContractTypeChangeForInit(copperContractType, $currentRow, customerCode, arrivalDate);
        }
    });

    console.log('=== 初始化完成 ===');
}

// 初始化时的铜合同类别变更处理（不清空现有值）
function handleCopperContractTypeChangeForInit(copperContractType, $currentRow, customerCode, arrivalDate) {
    console.log('初始化处理铜合同类别变更:', copperContractType);

    // 获取铜合同NO和铜条件的输入框
    var $copperContractNoInput = $currentRow.find('input[name="copperContractNo"]');
    var $copperConditionInput = $currentRow.find('input[name="copperCondition"]');

    // 如果在当前行没找到，尝试在整个tbody中查找
    if ($copperContractNoInput.length === 0) {
        var $tbody = $currentRow.closest('tbody.record');
        $copperContractNoInput = $tbody.find('input[name="copperContractNo"]');
        $copperConditionInput = $tbody.find('input[name="copperCondition"]');
    }

    console.log('找到的字段数量 - 铜合同NO:', $copperContractNoInput.length, '铜条件:', $copperConditionInput.length);

    if (copperContractType === '1' || copperContractType === '2') {
        // 预约铜或支给铜：铜合同下拉框可选
        console.log('初始化：预约铜/支给铜模式');

        if ($copperContractNoInput.length > 0) {
            var currentCopperContractValue = $copperContractNoInput.val();

            // 如果当前是输入框且有值，需要创建下拉框并保持选中状态
            if ($copperContractNoInput[0].tagName === 'INPUT') {
                createCopperContractSelectInPlaceForInit($copperContractNoInput, customerCode, copperContractType, arrivalDate, currentCopperContractValue);
            }
        }

        // 铜条件输入框禁用
        $copperConditionInput.prop('disabled', true).attr('placeholder', '选择铜合同后自动填入');

    } else if (copperContractType === '3' || copperContractType === '4') {
        // 一般铜或无偿：铜合同下拉框disabled
        console.log('初始化：一般铜/无偿模式');

        // 将铜合同NO字段设为禁用状态
        disableCopperContractField($copperContractNoInput);

        // 铜条件字段启用，如果是输入框且有值，需要创建下拉框并保持选中状态
        if ($copperConditionInput.length > 0) {
            var currentCopperConditionValue = $copperConditionInput.val();

            if ($copperConditionInput[0].tagName === 'INPUT') {
                createCopperConditionSelectInPlaceForInit($copperConditionInput, currentCopperConditionValue);
            }
        }
    }
}

// 初始化时创建铜合同下拉框（保持现有值）
function createCopperContractSelectInPlaceForInit($input, customerCode, copperContractType, arrivalDate, currentValue) {
    console.log('=== 初始化创建铜合同下拉框 ===');
    console.log('保持现有值:', currentValue);

    var inputName = $input.attr('name');

    // 创建下拉框
    var $select = $('<select name="' + inputName + '" class="layui-input"></select>');
    $select.append('<option value="">加载中...</option>');

    // 替换输入框
    $input.replaceWith($select);

    // 检查必要参数
    if (!customerCode || !copperContractType || !arrivalDate) {
        $select.empty().append('<option value="">参数不完整</option>');
        return;
    }

    // 从到货日期提取年月
    var useMonth = arrivalDate.substring(0, 7);

    $.ajax({
        url: baselocation + '/order/salesLogin/getCopperContractList',
        type: 'POST',
        data: {
            customerCode: customerCode,
            copperContractType: copperContractType,
            useMonth: useMonth,
            status: '0'
        },
        success: function(result) {
            if (result.code === 1 && result.data) {
                $select.empty().append('<option value="">请选择</option>');

                var hasCurrentValue = false;
                $.each(result.data, function(index, item) {
                    var contractNo = item.copperSignNo || '';
                    var copperCondition = item.copperCondition || '';
                    var currency = item.currency || '';

                    if (contractNo) {
                        var selected = (contractNo === currentValue) ? 'selected' : '';
                        if (contractNo === currentValue) {
                            hasCurrentValue = true;
                        }

                        $select.append('<option value="' + contractNo + '" ' + selected +
                                     ' data-copper-condition="' + copperCondition + '"' +
                                     ' data-currency="' + currency + '">' +
                                     contractNo + '</option>');
                    }
                });

                // 如果当前值不在列表中，添加为选项
                if (currentValue && !hasCurrentValue) {
                    $select.append('<option value="' + currentValue + '" selected>' + currentValue + '</option>');
                }

                // 绑定选择事件
                $select.off('change').on('change', function() {
                    var selectedOption = $(this).find('option:selected');
                    var copperCondition = selectedOption.data('copper-condition');
                    var currency = selectedOption.data('currency');
                    var copperContractNo = selectedOption.val();
                    var $copperConditionInput = $(this).closest('tr').find('input[name="copperCondition"]');

                    // 填入铜条件
                    $copperConditionInput.val(copperCondition || '');

                    // 获取铜货币字段（在第二行）
                    var $currentRow = $(this).closest('tr');
                    var $tbody = $currentRow.closest('tbody.record');
                    var $currencyInput = $tbody.find('input[name="currency"]');

                    // 直接设置货币信息（从选项的data属性中获取）
                    $currencyInput.val(currency || '');

                    console.log('选择了铜合同:', copperContractNo, '铜条件:', copperCondition, '货币:', currency);
                });

                console.log('初始化铜合同下拉框完成，当前值:', $select.val());

            } else {
                $select.empty().append('<option value="">无可用合同</option>');
            }
        },
        error: function(xhr, status, error) {
            $select.empty().append('<option value="">加载失败</option>');
        }
    });
}

// 初始化时创建铜条件下拉框（保持现有值）
function createCopperConditionSelectInPlaceForInit($input, currentValue) {
    console.log('=== 初始化创建铜条件下拉框 ===');
    console.log('保持现有值:', currentValue);

    var inputName = $input.attr('name');

    // 创建下拉框
    var $select = $('<select name="' + inputName + '" class="layui-input"></select>');
    $select.append('<option value="">加载中...</option>');

    // 替换输入框
    $input.replaceWith($select);

    $.ajax({
        url: baselocation + '/order/orderEntry/getCopperConditionList',
        type: 'POST',
        success: function(result) {
            if (result.code === 1 && result.data) {
                $select.empty().append('<option value="">请选择</option>');

                var hasCurrentValue = false;
                $.each(result.data, function(index, item) {
                    var condition = '';
                    var currency = '';

                    if (typeof item === 'object') {
                        condition = item.condition || item.copperCondition || '';
                        currency = item.currency || '';
                    } else {
                        condition = item;
                    }

                    if (condition) {
                        var selected = (condition === currentValue) ? 'selected' : '';
                        if (condition === currentValue) {
                            hasCurrentValue = true;
                        }
                        var optionHtml = '<option value="' + condition + '" ' + selected +
                                       ' data-currency="' + currency + '">' +
                                       condition + '</option>';
                        $select.append(optionHtml);
                    }
                });

                // 如果当前值不在列表中，添加为选项
                if (currentValue && !hasCurrentValue) {
                    $select.append('<option value="' + currentValue + '" selected>' + currentValue + '</option>');
                }

                console.log('初始化铜条件下拉框完成，当前值:', $select.val());

                // 如果有选中的值，自动设置对应的货币
                if ($select.val()) {
                    var selectedOption = $select.find('option:selected');
                    var currency = selectedOption.data('currency');

                    console.log('初始化时检查铜条件货币:', $select.val(), '对应货币:', currency);

                    // 获取货币字段（在第二行）
                    var $currentRow = $select.closest('tr');
                    var $tbody = $currentRow.closest('tbody.record');
                    var $secondRow = $tbody.find('tr:last-child');
                    var $currencyInput = $secondRow.find('input[name="currency"]');

                    console.log('当前货币字段值:', $currencyInput.val());

                    if (currency) {
                        // 设置货币值（如果从下拉框能获取到）
                        $currencyInput.val(currency);
                        console.log('初始化时从下拉框设置铜货币:', currency);
                    } else if ($select.val() && !$currencyInput.val()) {
                        // 如果下拉框没有货币信息，但有铜条件值，且货币字段为空，通过接口获取
                        console.log('初始化时通过接口获取铜条件货币:', $select.val());
                        getCopperConditionDetailEdit($select.val(), $select);
                    }
                }

            } else {
                $select.empty().append('<option value="">无可用铜条件</option>');
            }
        },
        error: function(xhr, status, error) {
            $select.empty().append('<option value="">加载失败</option>');
        }
    });
}

// 查询升水表数据（用于一般铜）- 编辑页面专用
function queryAscendingWaterForGeneralCopperEdit($currentRow, customerCode, arrivalDate) {
    console.log('=== 编辑页面查询升水表数据 ===');
    console.log('客户代码:', customerCode, '到货日期:', arrivalDate);

    if (!customerCode || !arrivalDate) {
        console.log('客户代码或到货日期为空，无法查询升水表');
        var $copperConditionInput = $currentRow.find('input[name="copperCondition"]');
        $copperConditionInput.prop('readonly', false).attr('placeholder', '请先填写客户代码和到货日期');
        return;
    }

    $.ajax({
        url: baselocation + '/salesCommon/getAscendingWaterByCustomerAndDate',
        type: 'POST',
        data: {
            customerCode: customerCode,
            arrivalDate: arrivalDate
        },
        success: function(result) {
            console.log('编辑页面升水表查询返回:', result);

            var $tbody = $currentRow.closest('tbody.record');
            var $copperConditionSelect = $currentRow.find('select[name="copperCondition"]');
            var $currencyInput = $tbody.find('input[name="currency"]');
            var $premiumInput = $tbody.find('input[name="premium"]'); // 升水字段

            if (result.code === 1 && result.data && result.data.length > 0) {
                // 获取第一条匹配的升水表数据
                var ascendingWaterData = result.data[0];

                console.log('编辑页面升水表数据:', ascendingWaterData);

                // 设置铜条件（下拉框选中）
                if ($copperConditionSelect.length > 0) {
                    $copperConditionSelect.val(ascendingWaterData.copperCondition || '');
                }

                // 设置铜货币（从铜条件表关联获取）
                $currencyInput.val(ascendingWaterData.currency || '');

                // 设置升水单价
                if ($premiumInput.length > 0) {
                    $premiumInput.val(ascendingWaterData.ascendingWaterPrice || '');
                }

                console.log('编辑页面已设置升水表数据 - 铜条件:', ascendingWaterData.copperCondition,
                           '货币:', ascendingWaterData.currency,
                           '升水:', ascendingWaterData.ascendingWaterPrice);

                layer.msg('已自动填入升水表数据', {icon: 1, time: 2000});

            } else {
                console.log('编辑页面未找到匹配的升水表数据');

                // 未找到数据时，允许手动选择铜条件
                var $copperConditionInput = $currentRow.find('input[name="copperCondition"]');
                if ($copperConditionInput.length > 0) {
                    $copperConditionInput.prop('readonly', false).attr('placeholder', '未找到升水表数据，请手动选择铜条件');
                    replaceCopperConditionInputWithSelectEdit($copperConditionInput);
                }

                layer.msg('未找到匹配的升水表数据，请手动选择铜条件', {icon: 0, time: 3000});
            }
        },
        error: function(xhr, status, error) {
            console.log('编辑页面查询升水表数据异常:', error);

            var $copperConditionInput = $currentRow.find('input[name="copperCondition"]');
            $copperConditionInput.prop('readonly', false).attr('placeholder', '查询升水表失败，请手动选择铜条件');
            replaceCopperConditionInputWithSelectEdit($copperConditionInput);

            layer.msg('查询升水表数据失败，请手动选择铜条件', {icon: 2, time: 3000});
        }
    });
}

// 将铜条件输入框替换为下拉框（编辑页面专用）- 全局函数
function replaceCopperConditionInputWithSelectEdit($input) {
    var currentValue = $input.val();
    var inputName = $input.attr('name');

    // 创建下拉框
    var $select = $('<select name="' + inputName + '"></select>');
    $select.append('<option value="">请选择</option>');

    // 替换输入框
    $input.replaceWith($select);

    // 加载铜条件列表
    loadCopperConditionListForSelectEdit($select, currentValue);
}

// 为下拉框加载铜条件列表（编辑页面专用）- 全局函数
function loadCopperConditionListForSelectEdit($select, selectedValue) {
    $.ajax({
        url: baselocation + '/order/orderEntry/getCopperConditionList',
        type: 'POST',
        success: function(result) {
            console.log('编辑页面铜条件列表返回:', result);

            if (result.code === 1 && result.data) {
                $select.empty().append('<option value="">请选择</option>');

                $.each(result.data, function(index, item) {
                    var condition = '';
                    var currency = '';

                    if (typeof item === 'object') {
                        condition = item.condition || item.copperCondition || '';
                        currency = item.currency || '';
                    } else {
                        condition = item;
                    }

                    if (condition) {
                        var selected = (condition === selectedValue) ? 'selected' : '';
                        var optionHtml = '<option value="' + condition + '" ' + selected +
                                       ' data-currency="' + currency + '">' +
                                       condition + '</option>';
                        $select.append(optionHtml);
                        console.log('编辑页面添加铜条件选项:', condition, '货币:', currency);
                    }
                });

                // 注意：事件绑定已通过全局事件委托处理，这里不需要重复绑定

            } else {
                console.log('编辑页面获取铜条件列表失败:', result.message);
            }
        },
        error: function(xhr, status, error) {
            console.log('编辑页面加载铜条件列表异常:', error);
        }
    });
}

// 货币换算函数（编辑页面专用）
function convertCurrencyToRMBEdit(originalPrice, currency, arrivalDate, callback) {
    console.log('=== 编辑页面货币换算 ===');
    console.log('原价格:', originalPrice, '货币:', currency, '到货日期:', arrivalDate);

    // 如果是人民币，直接返回原价格
    if (!currency || currency === 'RMB') {
        console.log('货币为RMB，无需换算');
        callback(originalPrice, 1.0);
        return;
    }

    // 查询汇率
    $.ajax({
        url: baselocation + '/salesCommon/getCurrencyExchangeRate',
        type: 'POST',
        data: {
            originalCurrency: currency,
            arrivalDate: arrivalDate
        },
        success: function(result) {
            console.log('编辑页面汇率查询返回:', result);

            if (result.code === 1 && result.data && result.data.exchangeRate) {
                var exchangeRate = parseFloat(result.data.exchangeRate);
                var convertedPrice = (parseFloat(originalPrice) * exchangeRate).toFixed(4);

                console.log('汇率:', exchangeRate, '换算后价格:', convertedPrice);
                callback(convertedPrice, exchangeRate);
            } else {
                console.log('未找到汇率数据:', result.message || '无数据');
                layer.msg(result.message || '货币换算汇率未登录！', {icon: 0, time: 3000});
                callback(originalPrice, 1.0); // 返回原价格
            }
        },
        error: function(xhr, status, error) {
            console.log('编辑页面查询汇率异常:', error);
            layer.msg('查询汇率失败，使用原价格', {icon: 2, time: 3000});
            callback(originalPrice, 1.0); // 返回原价格
        }
    });
}

// 查询产品单价作为0base（编辑页面专用）
function queryProductPrice(customerCode, productCode, arrivalDate, $currentRow) {
    console.log('=== 编辑页面查询产品单价作为0base ===');
    console.log('客户代码:', customerCode, '产品代码:', productCode, '到货日期:', arrivalDate);

    if (!customerCode || !productCode || !arrivalDate) {
        console.log('参数不完整，无法查询产品单价');
        return;
    }

    $.ajax({
        url: baselocation + '/salesCommon/getProductPrice',
        type: 'POST',
        data: {
            customerCode: customerCode,
            productCode: productCode,
            arrivalDate: arrivalDate
        },
        success: function(result) {
            console.log('编辑页面产品单价查询返回:', result);

            if (result.code === 1 && result.data) {
                var productPrice = result.data;
                var $tbody = $currentRow.closest('tbody.record');
                var $secondRow = $tbody.find('tr:last-child');
                var $zeroBaseInput = $secondRow.find('input[name="zeroBase"]');

                if (productPrice.productPrice) {
                    // 获取产品价格的货币信息
                    var productCurrency = productPrice.currency || 'RMB';

                    console.log('编辑页面产品单价:', productPrice.productPrice, '货币:', productCurrency);

                    // 进行货币换算
                    convertCurrencyToRMBEdit(productPrice.productPrice, productCurrency, arrivalDate, function(convertedPrice, exchangeRate) {
                        // 设置0base（换算后的价格）
                        $zeroBaseInput.val(convertedPrice);
                        console.log('编辑页面自动设置0base（换算后）:', convertedPrice, '汇率:', exchangeRate);

                        // 触发0base变更事件，重新计算销售单价和销售金额
                        $zeroBaseInput.trigger('change');

                        var message = '已自动填入0base: ' + convertedPrice;
                        if (exchangeRate !== 1.0) {
                            message += ' (原价: ' + productPrice.productPrice + ' ' + productCurrency + ', 汇率: ' + exchangeRate + ')';
                        }
                        layer.msg(message, {icon: 1, time: 3000});
                    });
                } else {
                    console.log('编辑页面产品价格数据中没有单价信息');
                }
            } else {
                console.log('编辑页面未找到匹配的产品价格:', result.message || '无数据');
                // 不显示错误提示，因为可能是正常情况（没有配置价格）
            }
        },
        error: function(xhr, status, error) {
            console.log('编辑页面查询产品单价异常:', error);
            // 不显示错误提示，避免影响用户体验
        }
    });
}

// 查询铜价表获取铜base（用于一般铜/无偿）- 编辑页面专用
function queryCopperBaseForGeneralCopperEdit($tbody, copperCondition, arrivalDate) {
    console.log('=== 编辑页面查询铜价表获取铜base ===');
    console.log('铜条件:', copperCondition, '到货日期:', arrivalDate);

    if (!copperCondition || !arrivalDate) {
        console.log('铜条件或到货日期为空，无法查询铜base');
        return;
    }

    $.ajax({
        url: baselocation + '/salesCommon/getCopperBase',
        type: 'POST',
        data: {
            copperCondition: copperCondition,
            arrivalDate: arrivalDate
        },
        success: function(result) {
            console.log('编辑页面铜base查询返回:', result);

            if (result.code === 1 && result.data) {
                var copperPriceData = result.data;
                var $copperBaseInput = $tbody.find('input[name="copperBase"]');

                if (copperPriceData.copperPrice) {
                    // 设置铜base
                    $copperBaseInput.val(copperPriceData.copperPrice);
                    console.log('编辑页面自动设置铜base:', copperPriceData.copperPrice);

                    // 触发铜base变更事件，重新计算相关金额
                    $copperBaseInput.trigger('change');

                    layer.msg('已自动填入铜base: ' + copperPriceData.copperPrice, {icon: 1, time: 2000});
                } else {
                    console.log('编辑页面铜价格数据中没有铜base信息');
                }
            } else {
                console.log('编辑页面未找到匹配的铜base数据:', result.message || '无数据');
                layer.msg('未找到匹配的铜base数据，请手动输入', {icon: 0, time: 3000});
            }
        },
        error: function(xhr, status, error) {
            console.log('编辑页面查询铜base异常:', error);
            layer.msg('查询铜base失败，请手动输入', {icon: 2, time: 3000});
        }
    });
}

// 查询铜合同详情获取铜base（用于预约铜/支给铜）- 编辑页面专用
function queryCopperBaseForContractCopperEdit($tbody, copperSignNo, useMonth) {
    console.log('=== 编辑页面查询铜合同详情获取铜base ===');
    console.log('铜签约NO:', copperSignNo, '使用月:', useMonth);

    if (!copperSignNo || !useMonth) {
        console.log('铜签约NO或使用月为空，无法查询铜合同详情');
        return;
    }

    $.ajax({
        url: baselocation + '/salesCommon/getCopperContractDetailForBase',
        type: 'POST',
        data: {
            copperSignNo: copperSignNo,
            useMonth: useMonth
        },
        success: function(result) {
            console.log('编辑页面铜合同详情查询返回:', result);

            if (result.code === 1 && result.data) {
                var contractData = result.data;
                var $copperBaseInput = $tbody.find('input[name="copperBase"]');

                if (contractData.signedCopperPriceExTax) {
                    // 设置铜base（使用免税签约铜价）
                    $copperBaseInput.val(contractData.signedCopperPriceExTax);
                    console.log('编辑页面自动设置铜base（免税签约铜价）:', contractData.signedCopperPriceExTax);

                    // 触发铜base变更事件，重新计算相关金额
                    $copperBaseInput.trigger('change');

                    layer.msg('已自动填入铜base: ' + contractData.signedCopperPriceExTax, {icon: 1, time: 2000});
                } else {
                    console.log('编辑页面铜合同数据中没有免税签约铜价信息');
                }
            } else {
                console.log('编辑页面未找到匹配的铜合同数据:', result.message || '无数据');
                layer.msg('未找到匹配的铜合同数据，请手动输入铜base', {icon: 0, time: 3000});
            }
        },
        error: function(xhr, status, error) {
            console.log('编辑页面查询铜合同详情异常:', error);
            layer.msg('查询铜合同详情失败，请手动输入铜base', {icon: 2, time: 3000});
        }
    });
}

// 加载交易条件下拉菜单数据
function loadTradeConditions() {
    $.ajax({
        url: baselocation + '/order/salesLogin/getTradeConditions',
        type: 'POST',
        success: function(result) {
            if (result.code === 1 && result.data) {
                var $select = $('#tradeCondition');
                $select.empty().append('<option value="">请选择</option>');

                $.each(result.data, function(index, item) {
                    var optionText = item.交易条件 + ' ' + (item.交易条件名 || '');
                    $select.append('<option value="' + item.交易条件 + '">' + optionText + '</option>');
                });

                layui.form.render('select');
                console.log('交易条件下拉菜单数据加载完成');
            } else {
                console.log('获取交易条件数据失败:', result.message);
            }
        },
        error: function(xhr, status, error) {
            console.error('获取交易条件数据异常:', error);
        }
    });
}

// 加载付款条件下拉菜单数据
function loadPayConditions() {
    $.ajax({
        url: baselocation + '/order/salesLogin/getPayConditions',
        type: 'POST',
        success: function(result) {
            if (result.code === 1 && result.data) {
                var $select = $('#payCondition');
                $select.empty().append('<option value="">请选择</option>');

                $.each(result.data, function(index, item) {
                    var optionText = item.付款条件 + ' ' + (item.付款条件名 || '');
                    $select.append('<option value="' + item.付款条件 + '">' + optionText + '</option>');
                });

                layui.form.render('select');
                console.log('付款条件下拉菜单数据加载完成');
            } else {
                console.log('获取付款条件数据失败:', result.message);
            }
        },
        error: function(xhr, status, error) {
            console.error('获取付款条件数据异常:', error);
        }
    });
}

// 加载运输条件下拉菜单数据
function loadTransportConditions() {
    $.ajax({
        url: baselocation + '/order/salesLogin/getTransportConditions',
        type: 'POST',
        success: function(result) {
            if (result.code === 1 && result.data) {
                var $select = $('#transportCondition');
                $select.empty().append('<option value="">请选择</option>');

                $.each(result.data, function(index, item) {
                    var optionText = item.运输条件 + ' ' + (item.运输条件名 || '');
                    $select.append('<option value="' + item.运输条件 + '">' + optionText + '</option>');
                });

                layui.form.render('select');
                console.log('运输条件下拉菜单数据加载完成');
            } else {
                console.log('获取运输条件数据失败:', result.message);
            }
        },
        error: function(xhr, status, error) {
            console.error('获取运输条件数据异常:', error);
        }
    });
}

// 处理编辑页面默认铜合同类别变化
function handleEditDefaultCopperContractTypeChange(copperContractType, customerCode, salesDate) {
    console.log('处理编辑页面默认铜合同类别变化:', copperContractType, '客户代码:', customerCode, '销售日:', salesDate);

    var $copperContractNoSelect = $('#defaultCopperContractNo');
    var $copperConditionSelect = $('#defaultCopperCondition');

    // 清空下级字段
    $copperContractNoSelect.empty().append('<option value="">请选择</option>');
    $copperConditionSelect.empty().append('<option value="">请选择</option>');

    if (copperContractType && customerCode) {
        if (copperContractType === '1' || copperContractType === '2') {
            // 预约铜或支给铜：加载铜合同列表
            loadEditDefaultCopperContractList(customerCode, copperContractType, salesDate);
        } else if (copperContractType === '3' || copperContractType === '4') {
            // 一般铜或无偿：加载铜条件列表
            loadEditDefaultCopperConditionList();
        }
    }

    layui.form.render('select');
}

// 处理编辑页面默认铜合同NO变化
function handleEditDefaultCopperContractNoChange(copperContractNo) {
    console.log('处理编辑页面默认铜合同NO变化:', copperContractNo);

    if (copperContractNo) {
        // 从选中的铜合同获取铜条件
        var selectedOption = $('#defaultCopperContractNo option:selected');
        var copperCondition = selectedOption.data('copper-condition');

        if (copperCondition) {
            $('#defaultCopperCondition').empty()
                .append('<option value="' + copperCondition + '" selected>' + copperCondition + '</option>');
            layui.form.render('select');
        }
    }
}

// 处理编辑页面默认铜条件变化
function handleEditDefaultCopperConditionChange(copperCondition) {
    console.log('处理编辑页面默认铜条件变化:', copperCondition);
    // 这里可以添加额外的处理逻辑，比如更新相关的货币信息等
}

// 加载编辑页面默认铜合同列表
function loadEditDefaultCopperContractList(customerCode, copperContractType, useMonth) {
    console.log('加载编辑页面默认铜合同列表:', customerCode, copperContractType, useMonth);

    if (!customerCode || !copperContractType || !useMonth) {
        console.log('参数不完整，无法加载铜合同列表');
        return;
    }

    $.ajax({
        url: baselocation + '/order/salesLogin/getCopperContractList',
        type: 'POST',
        data: {
            customerCode: customerCode,
            copperContractType: copperContractType,
            useMonth: useMonth,
            status: '0' // 有效状态
        },
        success: function(result) {
            var $select = $('#defaultCopperContractNo');
            $select.empty().append('<option value="">请选择</option>');

            if (result.code === 1 && result.data && result.data.length > 0) {
                $.each(result.data, function(index, item) {
                    var optionText = item.copperSignNo + ' (' + item.copperCondition + ')';
                    var option = '<option value="' + item.copperSignNo + '" ' +
                        'data-copper-condition="' + item.copperCondition + '">' + optionText + '</option>';
                    $select.append(option);
                });
            }

            layui.form.render('select');
        },
        error: function(xhr, status, error) {
            console.error('加载编辑页面默认铜合同列表失败:', error);
        }
    });
}

// 加载编辑页面默认铜条件列表
function loadEditDefaultCopperConditionList() {
    console.log('加载编辑页面默认铜条件列表');

    $.ajax({
        url: baselocation + '/order/salesLogin/getCopperConditionList',
        type: 'POST',
        success: function(result) {
            var $select = $('#defaultCopperCondition');
            $select.empty().append('<option value="">请选择</option>');

            if (result.code === 1 && result.data && result.data.length > 0) {
                $.each(result.data, function(index, item) {
                    var optionText = item.copperCondition + ' (' + item.currency + ')';
                    var option = '<option value="' + item.copperCondition + '" ' +
                        'data-currency="' + item.currency + '">' + optionText + '</option>';
                    $select.append(option);
                });
            }

            layui.form.render('select');
        },
        error: function(xhr, status, error) {
            console.error('加载编辑页面默认铜条件列表失败:', error);
        }
    });
}

// 应用编辑页面默认值到所有明细
function applyEditDefaultValuesToAllDetails() {
    var defaultCopperContractType = $('#defaultCopperContractType').val();
    var defaultCopperContractNo = $('#defaultCopperContractNo').val();
    var defaultCopperCondition = $('#defaultCopperCondition').val();

    if (!defaultCopperContractType) {
        layer.msg('请先选择默认铜合同类别', {icon: 2});
        return;
    }

    console.log('应用编辑页面默认值到所有明细:', {
        copperContractType: defaultCopperContractType,
        copperContractNo: defaultCopperContractNo,
        copperCondition: defaultCopperCondition
    });

    var appliedCount = 0;

    // 应用到所有明细行
    $('#detailTable tbody.record').each(function() {
        var $record = $(this);
        var $firstRow = $record.find('tr:first-child');

        // 设置铜合同类别
        var $copperContractTypeSelect = $firstRow.find('select[name="copperContractType"]');
        if ($copperContractTypeSelect.length > 0) {
            $copperContractTypeSelect.val(defaultCopperContractType);

            // 获取当前行的客户代码和到货日期
            var customerCode = $firstRow.find('input[name="customerProductCode"]').val();
            var arrivalDate = $firstRow.find('input[name="arrivalDate"]').val();

            console.log('应用编辑页面默认值到明细行:', {
                copperContractType: defaultCopperContractType,
                copperContractNo: defaultCopperContractNo,
                copperCondition: defaultCopperCondition,
                customerCode: customerCode,
                arrivalDate: arrivalDate
            });

            // 设置铜合同NO（如果有）
            if (defaultCopperContractNo) {
                var $copperContractNoInput = $firstRow.find('input[name="copperContractNo"]');
                if ($copperContractNoInput.length > 0) {
                    $copperContractNoInput.val(defaultCopperContractNo);
                }
            }

            // 设置铜条件（如果有）
            if (defaultCopperCondition) {
                var $copperConditionInput = $firstRow.find('input[name="copperCondition"]');
                if ($copperConditionInput.length > 0) {
                    $copperConditionInput.val(defaultCopperCondition);
                }
            }

            // 触发铜合同类别变更事件，自动处理相关逻辑
            $copperContractTypeSelect.trigger('change');

            appliedCount++;
        }
    });

    if (appliedCount > 0) {
        layer.msg('已应用默认值到 ' + appliedCount + ' 条明细', {icon: 1});
    } else {
        layer.msg('没有找到可应用的明细行', {icon: 0});
    }

    console.log('编辑页面默认值应用完成，影响行数:', appliedCount);
}

// 根据出货单No.获取明细数据（编辑页面专用）
function getDetailsByShipmentNoForEdit(shipmentNo) {
    console.log('编辑页面根据出货单No.获取明细数据:', shipmentNo);

    if (!shipmentNo) {
        layer.msg('出货单No.不能为空', {icon: 2});
        return;
    }

    // 显示加载提示
    var loadingIndex = layer.load(2, {shade: [0.3, '#000']});

    $.ajax({
        url: baselocation + '/order/salesLogin/getDetailsByShipmentNo',
        type: 'POST',
        data: {shipmentNo: shipmentNo},
        success: function(result) {
            layer.close(loadingIndex);
            console.log('编辑页面根据出货单No.获取明细数据结果:', result);

            if (result.code === 1 && result.data && result.data.length > 0) {
                // 添加明细数据到表格
                var addedCount = 0;
                result.data.forEach(function(detail) {
                    addDetailRowToTwoRowTable(detail);
                    addedCount++;
                });

                layer.msg('成功添加 ' + addedCount + ' 条明细数据', {icon: 1});
                console.log('编辑页面已添加明细数据:', result.data);

                // 重新计算总金额
                calculateTotalAmount();
            } else {
                layer.msg('未找到出货单No. [' + shipmentNo + '] 的明细数据', {icon: 0});
            }
        },
        error: function(xhr, status, error) {
            layer.close(loadingIndex);
            console.error('编辑页面获取出货单明细数据失败:', error);
            layer.msg('获取出货单明细数据失败: ' + error, {icon: 2});
        }
    });
}
