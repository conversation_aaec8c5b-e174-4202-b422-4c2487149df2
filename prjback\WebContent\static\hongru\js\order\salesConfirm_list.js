layui.config({
    base: baselocationsta + '/common/layui/'
}).use(['table', 'form', 'laydate'], function () {
    var table = layui.table;
    var form = layui.form;
    var laydate = layui.laydate;
    var $ = layui.jquery;

    // 初始化日期选择器
    laydate.render({
        elem: '#paymentRequestDate',
        type: 'date',
        format: 'yyyy-MM-dd'
    });

    // 初始化表格
    var tableIns = table.render({
        elem: '#salesConfirmTable',
        url: baselocation + '/order/salesConfirm/list',
        height: 'full-70',
        cellMinWidth: 80,
        parseData: function (res) {
            return {
                "code": 0,
                "msg": '',
                "count": res.total,
                "data": res.rows
            };
        },
        method: 'post',
        title: '销售额确认列表',
        page: true,
        limits: [10, 20, 50, 100],
        where: {},
        toolbar: '#toolbarDemo',
        defaultToolbar: ['filter'],
        totalRow: false,
        cols: [[
            {field: 'zizeng', title: 'NO.', width: 50, type: "numbers", align: 'center'},
            {field: 'paymentRequestNo', title: '支付请求NO', width: 180, align: 'center'},
            {field: 'paymentRequestDate', title: '支付请求日', width: 140, align: 'center'},
            {field: 'contractorCode', title: '签约方代码', align: 'center'},
            {field: 'contractorAlias', title: '签约方简称', align: 'center'},
            {field: 'customerCode', title: '客户代码', align: 'center'},
            {field: 'customerAlias', title: '客户简称', align: 'center'},
            {field: 'settlementCurrency', title: '结算货币', align: 'center'},
            {field: 'salesAmount', title: '销售金额', width: 140, align: 'center', templet: function(d) {
                return d.salesAmount ? parseFloat(d.salesAmount).toFixed(2) : '0.00';
            }},
            {field: 'paymentRequestConfirm', title: '确认状态', align: 'center', templet: '#confirmStatusTpl'},
            {field: 'confirmer', title: '确认者', align: 'center'},
            {field: 'confirmTime', title: '确认时间', align: 'center', templet: function(d) {
                if (d.confirmTime) {
                    var date = new Date(d.confirmTime);
                    return date.getFullYear() + '-' +
                           String(date.getMonth() + 1).padStart(2, '0') + '-' +
                           String(date.getDate()).padStart(2, '0');
                }
                return '';
            }},
            {title: '操作', minWidth: 180, align: 'center', toolbar: '#operationBar'}
        ]]
    });

    // 检索函数
    function search() {
        var customerCode = $('#customerCode').val();
        var paymentRequestDate = $('#paymentRequestDate').val();

        tableIns.reload({
            where: {
                customerCode: customerCode,
                paymentRequestDate: paymentRequestDate
            },
            page: {
                curr: 1
            }
        });
    }

    // 检索按钮事件
    $('#searchBtn').on('click', function () {
        search();
    });

    // 工具栏事件
    table.on('toolbar(salesConfirmTable)', function (obj) {
        switch (obj.event) {
            case 'refresh':
                tableIns.reload();
                break;
        }
    });

    // 行工具事件
    table.on('tool(salesConfirmTable)', function (obj) {
        var data = obj.data;
        switch (obj.event) {
            case 'detail':
                // 跳转到详情页面
                window.open(baselocation + '/order/salesConfirm/detail/view?paymentRequestNo=' + data.paymentRequestNo, '_blank');
                break;
            case 'confirm':
                // 确认销售额
                layer.confirm('确认要确认该销售额吗？', {
                    btn: ['确定', '取消']
                }, function (index) {
                    $.ajax({
                        url: baselocation + '/order/salesConfirm/confirm',
                        type: 'POST',
                        data: {
                            paymentRequestNo: data.paymentRequestNo
                        },
                        dataType: 'json',
                        success: function (result) {
                            if (result.code === 1) {
                                layer.msg('确认成功!', {
                                    icon: 6,
                                    time: 1000
                                }, function () {
                                    tableIns.reload();
                                });
                            } else {
                                layer.alert(result.message, {
                                    icon: 2
                                });
                            }
                        },
                        error: function () {
                            layer.alert('网络异常，请稍后重试', {
                                icon: 2
                            });
                        }
                    });
                    layer.close(index);
                });
                break;
            case 'cancelConfirm':
                // 取消确认销售额
                layer.confirm('确认要取消确认该销售额吗？', {
                    btn: ['确定', '取消']
                }, function (index) {
                    $.ajax({
                        url: baselocation + '/order/salesConfirm/cancelConfirm',
                        type: 'POST',
                        data: {
                            paymentRequestNo: data.paymentRequestNo
                        },
                        dataType: 'json',
                        success: function (result) {
                            if (result.code === 1) {
                                layer.msg('取消确认成功!', {
                                    icon: 6,
                                    time: 1000
                                }, function () {
                                    tableIns.reload();
                                });
                            } else {
                                layer.alert(result.message, {
                                    icon: 2
                                });
                            }
                        },
                        error: function () {
                            layer.alert('网络异常，请稍后重试', {
                                icon: 2
                            });
                        }
                    });
                    layer.close(index);
                });
                break;
        }
    });
});
