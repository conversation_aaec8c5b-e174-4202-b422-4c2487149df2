package com.hongru.controller.order;

import com.hongru.base.BaseController;
import com.hongru.common.result.HrPageResult;
import com.hongru.common.result.HrResult;
import com.hongru.common.security.AuthorizingUser;
import com.hongru.common.util.BusinessExcelExportUtil;
import com.hongru.common.util.TemplateExcelExportUtil;
import com.hongru.common.util.DateUtils;
import com.hongru.common.util.ServletUtils;
import com.hongru.common.util.SingletonLoginUtils;
import com.hongru.common.util.StringUtil;
import com.hongru.constant.CommonReturnCode;
import com.hongru.entity.admin.User;
import com.hongru.entity.businessOps.SalesAmount;
import com.hongru.entity.businessOps.SalesAmountDetail;
import com.hongru.entity.sumitomo.Customers;
import com.hongru.pojo.dto.SalesLoginListDTO;
import com.hongru.pojo.dto.SalesAmountExportDTO;
import com.hongru.pojo.vo.UserVO;
import com.hongru.service.admin.IUserService;
import com.hongru.service.businessOps.IOrderService;
import com.hongru.service.businessOps.ISalesLoginService;
import com.hongru.service.sumitomo.ISumitomoService;
import com.hongru.service.businessOps.ISalesCommonService;
import com.hongru.entity.businessOps.CopperContract;
import com.hongru.entity.businessOps.CopperConditions;
import com.hongru.support.page.PageInfo;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;

@Controller
@RequestMapping(value = "/order/salesLogin")
public class SalesLoginController extends BaseController {

    @Autowired
    private IOrderService orderService;

    @Autowired
    private IUserService userService;

    @Autowired
    private ISumitomoService sumitomoService;

    @Autowired
    private ISalesLoginService salesLoginService;

    @Autowired
    private ISalesCommonService salesCommonService;

    /**
     * 销售额登录列表页面
     * 
     * @param model
     * @throws Exception
     * @return
     */
    @GetMapping("/list/view")
    public String listView(Model model) throws Exception {
        // 获取客户列表
        List<Customers> customersList = sumitomoService.listCustomersList();
        model.addAttribute("customersList", customersList);
        return "/modules/order/salesLogin_list";
    }

    /**
     * 销售额登录列表数据
     *
     * @param page                 页码
     * @param limit                每页数量
     * @param customerCode         客户代码
     * @param salesAmountDateRange 销售额日期范围
     * @throws Exception
     * @return
     */
    @PostMapping("/list")
    @ResponseBody
    public Object list(Integer page, Integer limit, String customerCode, String salesAmountDateRange) throws Exception {
        try {
            // 验证用户登录状态
            AuthorizingUser authorizingUser = SingletonLoginUtils.getUser();
            if (authorizingUser == null) {
                return new HrResult(CommonReturnCode.FAILED, "登录超时，请重新登录");
            }

            PageInfo pageInfo = new PageInfo(limit, page);

            UserVO userVO = userService.getById(authorizingUser.getUserId());
            if (userVO == null) {
                return new HrResult(CommonReturnCode.FAILED, "用户信息不存在");
            }

            // 解析日期范围
            String startDate = null;
            String endDate = null;
            if (salesAmountDateRange != null && !salesAmountDateRange.trim().isEmpty()) {
                String[] dateRange = salesAmountDateRange.split(" - ");
                if (dateRange.length == 2) {
                    startDate = dateRange[0].trim();
                    endDate = dateRange[1].trim();
                }
            }

            // 查询销售额登录列表数据（按当前用户过滤）
            List<SalesLoginListDTO> salesLoginList = salesLoginService.listSalesLoginByPageWithUser(pageInfo,
                    customerCode, startDate, endDate, userVO.getUserName());
            Integer totalCount = salesLoginService.listSalesLoginByPageCountWithUser(customerCode, startDate, endDate,
                    userVO.getUserName());

            return new HrPageResult(salesLoginList, totalCount);
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("查询销售额登录列表数据异常：", e);
            return new HrResult(CommonReturnCode.FAILED.getCode(), "系统异常");
        }
    }

    /**
     * 销售额登录添加页面
     * 
     * @param model
     * @throws Exception
     * @return
     */
    @GetMapping("/add/view")
    public String addView(Model model) throws Exception {
        // 获取客户列表
        List<Customers> customersList = sumitomoService.listCustomersList();
        model.addAttribute("customersList", customersList);
        return "/modules/order/salesLogin_add";
    }

    /**
     * 销售额登录详情页面
     *
     * @param model
     * @param id            销售额登录ID
     * @param salesAmountNo 销售额NO
     * @throws Exception
     * @return
     */
    @GetMapping("/detail/view")
    public String detailView(Model model, String id, String salesAmountNo) throws Exception {
        model.addAttribute("id", id);
        model.addAttribute("salesAmountNo", salesAmountNo);
        return "/modules/order/salesLogin_detail";
    }

    /**
     * 销售额登录编辑页面
     *
     * @param model
     * @param id            销售额登录ID（已废弃）
     * @param salesAmountNo 销售额NO
     * @throws Exception
     * @return
     */
    @GetMapping("/edit/view")
    public String editView(Model model, String id, String salesAmountNo) throws Exception {
        // 获取客户列表
        List<Customers> customersList = sumitomoService.listCustomersList();
        model.addAttribute("customersList", customersList);
        model.addAttribute("id", id);
        model.addAttribute("salesAmountNo", salesAmountNo);
        return "/modules/order/salesLogin_edit";
    }

    /**
     * 保存销售额登录数据
     *
     * @param requestBody 请求体
     * @throws Exception
     * @return
     */
    @PostMapping("/save")
    @ResponseBody
    public Object save(@RequestBody Map<String, Object> requestBody) throws Exception {
        try {
            // 验证用户登录状态
            AuthorizingUser authorizingUser = SingletonLoginUtils.getUser();
            if (authorizingUser == null) {
                return new HrResult(CommonReturnCode.FAILED, "登录超时，请重新登录");
            }

            // 打印接收到的请求体
            logger.info("接收到的请求体: " + requestBody);

            // 获取参数
            String customerCode = (String) requestBody.get("customerCode");
            String salesAmountDate = (String) requestBody.get("salesAmountDate");
            String currency = (String) requestBody.get("currency");
            String taxCode = (String) requestBody.get("taxCode");
            String salesDetails = (String) requestBody.get("salesDetails");

            // 获取客户相关条件字段
            String tradeCondition = (String) requestBody.get("tradeCondition");
            String payCondition = (String) requestBody.get("payCondition");
            String transportCondition = (String) requestBody.get("transportCondition");

            // 打印参数
            logger.info("客户代码: " + customerCode);
            logger.info("销售额日: " + salesAmountDate);
            logger.info("货币: " + currency);
            logger.info("税代码: " + taxCode);
            logger.info("交易条件: " + tradeCondition);
            logger.info("付款条件: " + payCondition);
            logger.info("运输条件: " + transportCondition);
            logger.info("销售明细数据长度: " + (salesDetails != null ? salesDetails.length() : 0));

            // 参数验证
            if (StringUtil.isStringEmpty(customerCode)) {
                logger.warn("客户代码为空");
                return new HrResult(CommonReturnCode.FAILED, "客户代码不能为空");
            }
            if (StringUtil.isStringEmpty(salesAmountDate)) {
                return new HrResult(CommonReturnCode.FAILED, "销售额日不能为空");
            }

            User user = userService.getById(authorizingUser.getUserId());

            // 获取客户详情信息
            Map<String, Object> customerInfo = salesLoginService.getCustomerDetailsInfo(customerCode);
            if (customerInfo == null) {
                return new HrResult(CommonReturnCode.FAILED, "未找到客户详情信息");
            }

            // 生成销售额NO
            String salesAmountNo = salesLoginService.generateSalesAmountNo();

            // 构建销售额主表数据
            SalesAmount salesAmount = new SalesAmount();
            salesAmount.setSalesAmountNo(salesAmountNo);
            salesAmount.setSalesAmountDate(salesAmountDate);
            salesAmount.setCustomerCode(customerCode);

            // 从客户信息中获取客户简称
            Customers customer = sumitomoService.listCustomersList().stream()
                    .filter(c -> customerCode.equals(c.getCustomerCode()))
                    .findFirst().orElse(null);
            if (customer != null) {
                salesAmount.setCustomerAlias(customer.getCustomerAlias());
            }

            // 使用前端传入的货币和税代码
            salesAmount.setSettlementCurrency(currency);
            salesAmount.setTaxCode(taxCode);

            // 设置客户相关条件字段
            salesAmount.setTradeCondition(tradeCondition);
            salesAmount.setPayCondition(payCondition);
            salesAmount.setTransportCondition(transportCondition);

            // 构建销售额明细数据
            List<SalesAmountDetail> salesAmountDetails = new ArrayList<>();

            // 如果前端传入了明细数据，则解析处理
            if (!StringUtil.isStringEmpty(salesDetails)) {
                try {
                    // HTML实体解码
                    String decodedSalesDetails = unescapeHtml(salesDetails);
                    logger.debug("解码后的销售明细数据: " + decodedSalesDetails);

                    // 将JSON字符串转换为List<Map>
                    ObjectMapper mapper = new ObjectMapper();
                    List<Map<String, Object>> detailList = null;
                    try {
                        detailList = mapper.readValue(decodedSalesDetails,
                                new TypeReference<List<Map<String, Object>>>() {
                                });
                    } catch (Exception e) {
                        logger.error("JSON解析异常，尝试修复格式: " + e.getMessage());
                        // 尝试修复可能的JSON格式问题
                        if (decodedSalesDetails.startsWith("[[],")) {
                            // 修复空数组问题
                            decodedSalesDetails = decodedSalesDetails.replace("[[],", "[");
                            detailList = mapper.readValue(decodedSalesDetails,
                                    new TypeReference<List<Map<String, Object>>>() {
                                    });
                        } else {
                            throw e; // 如果无法修复，则重新抛出异常
                        }
                    }

                    // 处理每一条明细数据
                    for (Map<String, Object> item : detailList) {
                        SalesAmountDetail detail = new SalesAmountDetail();
                        detail.setSalesAmountNo(salesAmountNo);

                        // 设置各个字段值
                        if (item.containsKey("productCode")) {
                            detail.setProductCode(String.valueOf(item.get("productCode")));
                        }

                        // 处理日期字段
                        if (item.containsKey("arrivalDate")) {
                            Object dateObj = item.get("arrivalDate");
                            if (dateObj instanceof Long) {
                                // 处理前端传入的时间戳
                                Date date = new Date((Long) dateObj);
                                detail.setArrivalDate(DateUtils.formatDate(date));
                            } else {
                                detail.setArrivalDate(String.valueOf(dateObj));
                            }
                        }

                        // 设置其他字段
                        if (item.containsKey("copperContractType")) {
                            detail.setCopperContractType(String.valueOf(item.get("copperContractType")));
                        }
                        if (item.containsKey("copperContractNo")) {
                            detail.setCopperContractNo(String.valueOf(item.get("copperContractNo")));
                        }
                        if (item.containsKey("copperCondition")) {
                            detail.setCopperCondition(String.valueOf(item.get("copperCondition")));
                        }
                        if (item.containsKey("conversionRate")) {
                            detail.setConversionRate(new BigDecimal(String.valueOf(item.get("conversionRate"))));
                        }
                        if (item.containsKey("quantity")) {
                            detail.setQuantity(new BigDecimal(String.valueOf(item.get("quantity"))));
                        }
                        if (item.containsKey("copperBase")) {
                            detail.setCopperBase(new BigDecimal(String.valueOf(item.get("copperBase"))));
                        }
                        if (item.containsKey("premium")) {
                            detail.setPremium(new BigDecimal(String.valueOf(item.get("premium"))));
                        }
                        if (item.containsKey("zeroBase")) {
                            detail.setZeroBase(new BigDecimal(String.valueOf(item.get("zeroBase"))));
                        }
                        if (item.containsKey("salesAmount")) {
                            detail.setSalesAmount(new BigDecimal(String.valueOf(item.get("salesAmount"))));
                        }
                        if (item.containsKey("productMiddleCategory")) {
                            detail.setProductMiddleCategory(String.valueOf(item.get("productMiddleCategory")));
                        }
                        if (item.containsKey("shipmentPlanNo")) {
                            detail.setShipmentPlanNo(String.valueOf(item.get("shipmentPlanNo")));
                        }
                        // 处理明细备注字段
                        if (item.containsKey("remark")) {
                            String remarkValue = String.valueOf(item.get("remark"));
                            detail.setDetailRemark(remarkValue);
                            logger.info("设置明细备注字段: " + remarkValue);
                        } else {
                            logger.warn("明细数据中缺少remark字段");
                        }

                        // 处理送货单号字段
                        if (item.containsKey("deliveryNoteNo")) {
                            String deliveryNoteNo = String.valueOf(item.get("deliveryNoteNo"));
                            detail.setDeliveryOrderNo(deliveryNoteNo);
                            logger.info("设置送货单号字段: " + deliveryNoteNo);
                        }

                        // 处理送货单序号字段
                        if (item.containsKey("deliveryNoteSeq")) {
                            String deliveryNoteSeq = String.valueOf(item.get("deliveryNoteSeq"));
                            detail.setDeliveryOrderSeq(deliveryNoteSeq);
                            logger.info("设置送货单序号字段: " + deliveryNoteSeq);
                        }

                        // 处理品目C字段
                        if (item.containsKey("productCategoryCode")) {
                            String productCategoryCode = String.valueOf(item.get("productCategoryCode"));
                            detail.setProductCategoryCode(productCategoryCode);
                            logger.info("设置品目C字段: " + productCategoryCode);
                        }

                        // 处理理由字段
                        if (item.containsKey("reason")) {
                            String reason = String.valueOf(item.get("reason"));
                            detail.setReason(reason);
                            logger.info("设置理由字段: " + reason);
                        }

                        // 处理线盘字段
                        if (item.containsKey("reelName")) {
                            String reelName = String.valueOf(item.get("reelName"));
                            detail.setReelName(reelName);
                            logger.info("设置线盘字段: " + reelName);
                        }

                        // 处理出货日字段
                        if (item.containsKey("outboundDate")) {
                            String outboundDate = String.valueOf(item.get("outboundDate"));
                            detail.setShipmentDate(outboundDate);
                            logger.info("设置出库日期字段: " + outboundDate);
                        } else if (item.containsKey("shipmentDate")) {
                            String shipmentDate = String.valueOf(item.get("shipmentDate"));
                            detail.setShipmentDate(shipmentDate);
                            logger.info("设置出库日期字段(从shipmentDate): " + shipmentDate);
                        } else {
                            logger.warn("明细数据中缺少出库日期字段");
                        }

                        // 处理客户订单号字段
                        if (item.containsKey("customerOrderNo")) {
                            String customerOrderNo = String.valueOf(item.get("customerOrderNo"));
                            detail.setCustomerOrderNo(customerOrderNo);
                            logger.info("设置客户订单号字段: " + customerOrderNo);
                        } else if (item.containsKey("客户订单No")) {
                            String customerOrderNo = String.valueOf(item.get("客户订单No"));
                            detail.setCustomerOrderNo(customerOrderNo);
                            logger.info("设置客户订单号字段(从客户订单No): " + customerOrderNo);
                        } else {
                            logger.warn("明细数据中缺少客户订单号字段");
                        }

                        // 处理品名字段（使用产品代码）
                        if (item.containsKey("productName")) {
                            String productName = String.valueOf(item.get("productName"));
                            // 不需要特殊处理，因为实体类中已经将productName映射到productCode
                            detail.setProductName(productName);
                            logger.info("设置品名字段(使用产品代码): " + productName);
                        }

                        // 处理客户品目C字段（使用客户代码）
                        if (item.containsKey("customerProductCode")) {
                            String customerProductCode = String.valueOf(item.get("customerProductCode"));
                            // 这里可能需要特殊处理，因为实体类中没有直接对应的字段
                            detail.setCustomerProductCode(customerProductCode);
                            logger.info("设置客户品目C字段(使用客户代码): " + customerProductCode);
                        }

                        salesAmountDetails.add(detail);
                    }
                } catch (Exception e) {
                    logger.error("解析销售额明细数据异常：", e);
                    return new HrResult(CommonReturnCode.FAILED, "解析销售额明细数据异常：" + e.getMessage());
                }
            }

            // 保存数据
            int result = salesLoginService.saveSalesLoginData(salesAmount, salesAmountDetails, user.getUserName());

            if (result > 0) {
                return new HrResult(CommonReturnCode.SUCCESS, "保存成功");
            } else {
                return new HrResult(CommonReturnCode.FAILED, "保存失败");
            }
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("保存销售额登录数据异常：", e);
            return new HrResult(CommonReturnCode.FAILED.getCode(), "系统异常：" + e.getMessage());
        }
    }

    /**
     * 获取销售额登录详情数据
     *
     * @param id 销售额登录ID
     * @throws Exception
     * @return
     */
    @PostMapping("/detail")
    @ResponseBody
    public Object detail(String id) throws Exception {
        try {
            // 验证用户登录状态
            AuthorizingUser authorizingUser = SingletonLoginUtils.getUser();
            if (authorizingUser == null) {
                return new HrResult(CommonReturnCode.FAILED, "登录超时，请重新登录");
            }

            if (StringUtil.isStringEmpty(id)) {
                return new HrResult(CommonReturnCode.FAILED, "ID不能为空");
            }

            // 获取销售额主表数据
            SalesAmount salesAmount = salesLoginService.getSalesAmountByNo(id);
            if (salesAmount == null) {
                return new HrResult(CommonReturnCode.FAILED, "未找到销售额数据");
            }

            // 获取销售额明细数据
            List<SalesAmountDetail> salesAmountDetails = salesLoginService
                    .getSalesAmountDetailsByNo(salesAmount.getSalesAmountNo());

            Map<String, Object> result = new HashMap<>();
            result.put("salesAmount", salesAmount);
            result.put("salesAmountDetails", salesAmountDetails);

            return new HrResult(CommonReturnCode.SUCCESS, result);
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("获取销售额登录详情数据异常：", e);
            return new HrResult(CommonReturnCode.FAILED.getCode(), "系统异常：" + e.getMessage());
        }
    }

    /**
     * 根据销售额NO获取销售额登录详情数据
     *
     * @param salesAmountNo 销售额NO
     * @throws Exception
     * @return
     */
    @PostMapping("/detailByNo")
    @ResponseBody
    public Object detailByNo(String salesAmountNo) throws Exception {
        try {
            // 验证用户登录状态
            AuthorizingUser authorizingUser = SingletonLoginUtils.getUser();
            if (authorizingUser == null) {
                return new HrResult(CommonReturnCode.FAILED, "登录超时，请重新登录");
            }

            if (StringUtil.isStringEmpty(salesAmountNo)) {
                return new HrResult(CommonReturnCode.FAILED, "销售额NO不能为空");
            }

            // 获取销售额主表数据
            logger.info("开始根据销售额NO获取数据: " + salesAmountNo);
            SalesAmount salesAmount = salesLoginService.getSalesAmountByNo(salesAmountNo);
            logger.info("获取到的销售额数据: " + (salesAmount != null ? salesAmount.getSalesAmountNo() : "null"));
            if (salesAmount == null) {
                return new HrResult(CommonReturnCode.FAILED, "未找到销售额数据");
            }

            // 获取销售额明细数据
            List<SalesAmountDetail> salesAmountDetails = salesLoginService
                    .getSalesAmountDetailsByNo(salesAmountNo);

            Map<String, Object> result = new HashMap<>();
            result.put("salesAmount", salesAmount);
            result.put("salesAmountDetails", salesAmountDetails);

            return new HrResult(CommonReturnCode.SUCCESS, result);
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("根据销售额NO获取销售额登录详情数据异常：", e);
            return new HrResult(CommonReturnCode.FAILED.getCode(), "系统异常：" + e.getMessage());
        }
    }

    /**
     * 更新销售额登录数据
     *
     * @param id              销售额ID
     * @param salesAmountDate 销售额日
     * @param taxCode         税代码
     * @throws Exception
     * @return
     */
    @PostMapping("/update")
    @ResponseBody
    public Object update(String id, String salesAmountDate, String taxCode) throws Exception {
        try {
            // 验证用户登录状态
            AuthorizingUser authorizingUser = SingletonLoginUtils.getUser();
            if (authorizingUser == null) {
                return new HrResult(CommonReturnCode.FAILED, "登录超时，请重新登录");
            }

            if (StringUtil.isStringEmpty(id)) {
                return new HrResult(CommonReturnCode.FAILED, "ID不能为空");
            }

            User user = userService.getById(authorizingUser.getUserId());

            // 获取现有销售额数据
            SalesAmount salesAmount = salesLoginService.getSalesAmountById(Integer.parseInt(id));
            if (salesAmount == null) {
                return new HrResult(CommonReturnCode.FAILED, "未找到销售额数据");
            }

            // 检查是否已被支付请求登录使用
            if (salesLoginService.isUsedByPaymentRequest(salesAmount.getSalesAmountNo())) {
                return new HrResult(CommonReturnCode.FAILED, "该销售额数据已被支付请求登录使用，不能编辑");
            }

            // 更新销售额主表数据
            if (!StringUtil.isStringEmpty(salesAmountDate)) {
                salesAmount.setSalesAmountDate(salesAmountDate);
            }
            if (!StringUtil.isStringEmpty(taxCode)) {
                salesAmount.setTaxCode(taxCode);
            }

            // 获取销售额明细数据（这里可以根据需要进行更新）
            List<SalesAmountDetail> salesAmountDetails = salesLoginService
                    .getSalesAmountDetailsByNo(salesAmount.getSalesAmountNo());

            // 更新数据
            int result = salesLoginService.updateSalesLoginData(salesAmount, salesAmountDetails, user.getUserName());

            if (result > 0) {
                return new HrResult(CommonReturnCode.SUCCESS, "更新成功");
            } else {
                return new HrResult(CommonReturnCode.FAILED, "更新失败");
            }
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("更新销售额登录数据异常：", e);
            return new HrResult(CommonReturnCode.FAILED.getCode(), "系统异常：" + e.getMessage());
        }
    }

    /**
     * 根据销售额NO更新销售额登录数据
     *
     * @param requestBody 请求体
     * @throws Exception
     * @return
     */
    @PostMapping("/updateByNo")
    @ResponseBody
    public Object updateByNo(@RequestBody Map<String, Object> requestBody) throws Exception {
        try {
            // 验证用户登录状态
            AuthorizingUser authorizingUser = SingletonLoginUtils.getUser();
            if (authorizingUser == null) {
                return new HrResult(CommonReturnCode.FAILED, "登录超时，请重新登录");
            }

            // 打印接收到的请求体
            logger.info("更新接收到的请求体: " + requestBody);

            // 获取参数
            String salesAmountNo = (String) requestBody.get("salesAmountNo");
            String salesAmountDate = (String) requestBody.get("salesAmountDate");
            String currency = (String) requestBody.get("currency");
            String taxCode = (String) requestBody.get("taxRate"); // 前端使用taxRate字段
            String salesDetails = (String) requestBody.get("salesDetails");

            // 获取客户相关条件字段
            String tradeCondition = (String) requestBody.get("tradeCondition");
            String payCondition = (String) requestBody.get("payCondition");
            String transportCondition = (String) requestBody.get("transportCondition");

            // 打印参数
            logger.info("销售额NO: " + salesAmountNo);
            logger.info("销售额日: " + salesAmountDate);
            logger.info("货币: " + currency);
            logger.info("税代码: " + taxCode);
            logger.info("交易条件: " + tradeCondition);
            logger.info("付款条件: " + payCondition);
            logger.info("运输条件: " + transportCondition);
            logger.info("销售明细数据长度: " + (salesDetails != null ? salesDetails.length() : 0));

            if (StringUtil.isStringEmpty(salesAmountNo)) {
                return new HrResult(CommonReturnCode.FAILED, "销售额NO不能为空");
            }

            User user = userService.getById(authorizingUser.getUserId());

            // 获取现有销售额数据
            SalesAmount salesAmount = salesLoginService.getSalesAmountByNo(salesAmountNo);
            if (salesAmount == null) {
                return new HrResult(CommonReturnCode.FAILED, "未找到销售额数据");
            }

            // 检查是否已被支付请求登录使用
            if (salesLoginService.isUsedByPaymentRequest(salesAmountNo)) {
                return new HrResult(CommonReturnCode.FAILED, "该销售额数据已被支付请求登录使用，不能编辑");
            }

            // 更新销售额主表数据
            if (!StringUtil.isStringEmpty(salesAmountDate)) {
                salesAmount.setSalesAmountDate(salesAmountDate);
            }
            if (!StringUtil.isStringEmpty(taxCode)) {
                salesAmount.setTaxCode(taxCode);
            }
            if (!StringUtil.isStringEmpty(currency)) {
                salesAmount.setSettlementCurrency(currency);
            }

            // 更新客户相关条件字段
            if (!StringUtil.isStringEmpty(tradeCondition)) {
                salesAmount.setTradeCondition(tradeCondition);
            }
            if (!StringUtil.isStringEmpty(payCondition)) {
                salesAmount.setPayCondition(payCondition);
            }
            if (!StringUtil.isStringEmpty(transportCondition)) {
                salesAmount.setTransportCondition(transportCondition);
            }

            // 构建销售额明细数据
            List<SalesAmountDetail> salesAmountDetails = new ArrayList<>();

            // 如果前端传入了明细数据，则解析处理
            if (!StringUtil.isStringEmpty(salesDetails)) {
                try {
                    // HTML实体解码
                    String decodedSalesDetails = unescapeHtml(salesDetails);
                    logger.debug("解码后的销售明细数据: " + decodedSalesDetails);

                    // 将JSON字符串转换为List<Map>
                    ObjectMapper mapper = new ObjectMapper();
                    List<Map<String, Object>> detailList = null;
                    try {
                        detailList = mapper.readValue(decodedSalesDetails,
                                new TypeReference<List<Map<String, Object>>>() {
                                });
                    } catch (Exception e) {
                        logger.error("JSON解析异常，尝试修复格式: " + e.getMessage());
                        // 尝试修复可能的JSON格式问题
                        if (decodedSalesDetails.startsWith("[[],")) {
                            // 修复空数组问题
                            decodedSalesDetails = decodedSalesDetails.replace("[[],", "[");
                            detailList = mapper.readValue(decodedSalesDetails,
                                    new TypeReference<List<Map<String, Object>>>() {
                                    });
                        } else {
                            throw e; // 如果无法修复，则重新抛出异常
                        }
                    }

                    // 处理每一条明细数据
                    for (Map<String, Object> item : detailList) {
                        SalesAmountDetail detail = new SalesAmountDetail();
                        detail.setSalesAmountNo(salesAmountNo);

                        // 设置各个字段值
                        if (item.containsKey("productCode")) {
                            detail.setProductCode(String.valueOf(item.get("productCode")));
                        }

                        // 处理日期字段
                        if (item.containsKey("arrivalDate")) {
                            Object dateObj = item.get("arrivalDate");
                            if (dateObj instanceof Long) {
                                // 处理前端传入的时间戳
                                Date date = new Date((Long) dateObj);
                                detail.setArrivalDate(DateUtils.formatDate(date));
                            } else {
                                detail.setArrivalDate(String.valueOf(dateObj));
                            }
                        }

                        // 设置其他字段
                        if (item.containsKey("copperContractType")) {
                            detail.setCopperContractType(String.valueOf(item.get("copperContractType")));
                        }
                        if (item.containsKey("copperContractNo")) {
                            detail.setCopperContractNo(String.valueOf(item.get("copperContractNo")));
                        }
                        if (item.containsKey("copperCondition")) {
                            detail.setCopperCondition(String.valueOf(item.get("copperCondition")));
                        }
                        if (item.containsKey("conversionRate")) {
                            detail.setConversionRate(new BigDecimal(String.valueOf(item.get("conversionRate"))));
                        }
                        if (item.containsKey("quantity")) {
                            detail.setQuantity(new BigDecimal(String.valueOf(item.get("quantity"))));
                        }
                        if (item.containsKey("copperBase")) {
                            detail.setCopperBase(new BigDecimal(String.valueOf(item.get("copperBase"))));
                        }
                        if (item.containsKey("premium")) {
                            detail.setPremium(new BigDecimal(String.valueOf(item.get("premium"))));
                        }
                        if (item.containsKey("zeroBase")) {
                            detail.setZeroBase(new BigDecimal(String.valueOf(item.get("zeroBase"))));
                        }
                        if (item.containsKey("salesAmount")) {
                            detail.setSalesAmount(new BigDecimal(String.valueOf(item.get("salesAmount"))));
                        }
                        if (item.containsKey("productMiddleCategory")) {
                            detail.setProductMiddleCategory(String.valueOf(item.get("productMiddleCategory")));
                        } else if (item.containsKey("productCategory")) {
                            detail.setProductMiddleCategory(String.valueOf(item.get("productCategory")));
                        }
                        if (item.containsKey("shipmentPlanNo")) {
                            detail.setShipmentPlanNo(String.valueOf(item.get("shipmentPlanNo")));
                        }
                        // 处理明细备注字段
                        if (item.containsKey("remark")) {
                            String remarkValue = String.valueOf(item.get("remark"));
                            detail.setDetailRemark(remarkValue);
                            logger.info("设置明细备注字段: " + remarkValue);
                        } else {
                            logger.warn("明细数据中缺少remark字段");
                        }

                        // 处理送货单号字段
                        if (item.containsKey("deliveryNoteNo")) {
                            String deliveryNoteNo = String.valueOf(item.get("deliveryNoteNo"));
                            detail.setDeliveryOrderNo(deliveryNoteNo);
                            logger.info("设置送货单号字段: " + deliveryNoteNo);
                        }

                        // 处理送货单序号字段
                        if (item.containsKey("deliveryNoteSeq")) {
                            String deliveryNoteSeq = String.valueOf(item.get("deliveryNoteSeq"));
                            detail.setDeliveryOrderSeq(deliveryNoteSeq);
                            logger.info("设置送货单序号字段: " + deliveryNoteSeq);
                        }

                        // 处理品目C字段
                        if (item.containsKey("productCategoryCode")) {
                            String productCategoryCode = String.valueOf(item.get("productCategoryCode"));
                            detail.setProductCategoryCode(productCategoryCode);
                            logger.info("设置品目C字段: " + productCategoryCode);
                        }

                        // 处理理由字段
                        if (item.containsKey("reason")) {
                            String reason = String.valueOf(item.get("reason"));
                            detail.setReason(reason);
                            logger.info("设置理由字段: " + reason);
                        }

                        // 处理线盘字段
                        if (item.containsKey("reelName")) {
                            String reelName = String.valueOf(item.get("reelName"));
                            detail.setReelName(reelName);
                            logger.info("设置线盘字段: " + reelName);
                        }

                        // 处理出货日字段
                        if (item.containsKey("outboundDate")) {
                            String outboundDate = String.valueOf(item.get("outboundDate"));
                            detail.setShipmentDate(outboundDate);
                            logger.info("设置出库日期字段: " + outboundDate);
                        } else if (item.containsKey("shipmentDate")) {
                            String shipmentDate = String.valueOf(item.get("shipmentDate"));
                            detail.setShipmentDate(shipmentDate);
                            logger.info("设置出库日期字段(从shipmentDate): " + shipmentDate);
                        } else {
                            logger.warn("明细数据中缺少出库日期字段");
                        }

                        // 处理客户订单号字段
                        if (item.containsKey("customerOrderNo")) {
                            String customerOrderNo = String.valueOf(item.get("customerOrderNo"));
                            detail.setCustomerOrderNo(customerOrderNo);
                            logger.info("设置客户订单号字段: " + customerOrderNo);
                        } else if (item.containsKey("客户订单No")) {
                            String customerOrderNo = String.valueOf(item.get("客户订单No"));
                            detail.setCustomerOrderNo(customerOrderNo);
                            logger.info("设置客户订单号字段(从客户订单No): " + customerOrderNo);
                        } else {
                            logger.warn("明细数据中缺少客户订单号字段");
                        }

                        // 处理品名字段（使用产品代码）
                        if (item.containsKey("productName")) {
                            String productName = String.valueOf(item.get("productName"));
                            // 不需要特殊处理，因为实体类中已经将productName映射到productCode
                            detail.setProductName(productName);
                            logger.info("设置品名字段(使用产品代码): " + productName);
                        }

                        // 处理客户品目C字段（使用客户代码）
                        if (item.containsKey("customerProductCode")) {
                            String customerProductCode = String.valueOf(item.get("customerProductCode"));
                            // 这里可能需要特殊处理，因为实体类中没有直接对应的字段
                            detail.setCustomerProductCode(customerProductCode);
                            logger.info("设置客户品目C字段(使用客户代码): " + customerProductCode);
                        }

                        salesAmountDetails.add(detail);
                    }
                } catch (Exception e) {
                    logger.error("解析销售额明细数据异常：", e);
                    return new HrResult(CommonReturnCode.FAILED, "解析销售额明细数据异常：" + e.getMessage());
                }
            }

            // 更新数据
            int result = salesLoginService.updateSalesLoginData(salesAmount, salesAmountDetails, user.getUserName());

            if (result > 0) {
                return new HrResult(CommonReturnCode.SUCCESS, "更新成功");
            } else {
                return new HrResult(CommonReturnCode.FAILED, "更新失败");
            }
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("根据销售额NO更新销售额登录数据异常：", e);
            return new HrResult(CommonReturnCode.FAILED.getCode(), "系统异常：" + e.getMessage());
        }
    }

    /**
     * 删除销售额登录数据
     *
     * @param salesAmountNo 销售额NO
     * @return
     */
    @PostMapping("/delete")
    @ResponseBody
    public Object delete(String salesAmountNo) throws Exception {
        try {
            System.out.println("接收到删除请求，salesAmountNo: " + salesAmountNo);

            if (StringUtil.isStringEmpty(salesAmountNo)) {
                System.out.println("销售额NO为空");
                return new HrResult(CommonReturnCode.FAILED, "销售额NO不能为空");
            }

            // 检查是否已被支付请求登录使用
            if (salesLoginService.isUsedByPaymentRequest(salesAmountNo)) {
                return new HrResult(CommonReturnCode.FAILED, "该销售额数据已被支付请求登录使用，不能删除");
            }

            // 删除销售额数据
            int result = salesLoginService.deleteSalesLoginDataByNo(salesAmountNo);

            if (result > 0) {
                return new HrResult(CommonReturnCode.SUCCESS, "删除成功");
            } else {
                return new HrResult(CommonReturnCode.FAILED, "删除失败");
            }
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("删除销售额登录数据异常：", e);
            return new HrResult(CommonReturnCode.FAILED.getCode(), "系统异常：" + e.getMessage());
        }
    }

    /**
     * 获取客户详情信息
     *
     * @param customerCode 客户代码
     * @throws Exception
     * @return
     */
    @PostMapping("/getCustomerInfo")
    @ResponseBody
    public Object getCustomerInfo(String customerCode) throws Exception {
        try {
            // 验证用户登录状态
            AuthorizingUser authorizingUser = SingletonLoginUtils.getUser();
            if (authorizingUser == null) {
                return new HrResult(CommonReturnCode.FAILED, "登录超时，请重新登录");
            }

            if (StringUtil.isStringEmpty(customerCode)) {
                return new HrResult(CommonReturnCode.FAILED, "客户代码不能为空");
            }

            // 获取客户详情信息
            Map<String, Object> customerInfo = salesLoginService.getCustomerDetailsInfo(customerCode);
            if (customerInfo == null) {
                // 返回空对象而不是错误信息
                return new HrResult(CommonReturnCode.SUCCESS, new HashMap<String, Object>());
            }

            return new HrResult(CommonReturnCode.SUCCESS, customerInfo);
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("获取客户详情信息异常：", e);
            return new HrResult(CommonReturnCode.FAILED.getCode(), "系统异常：" + e.getMessage());
        }
    }

    /**
     * 获取交易条件列表
     *
     * @throws Exception
     * @return
     */
    @PostMapping("/getTradeConditions")
    @ResponseBody
    public Object getTradeConditions() throws Exception {
        try {
            // 验证用户登录状态
            AuthorizingUser authorizingUser = SingletonLoginUtils.getUser();
            if (authorizingUser == null) {
                return new HrResult(CommonReturnCode.FAILED, "登录超时，请重新登录");
            }

            // 获取交易条件列表
            List<Map<String, Object>> tradeConditions = salesLoginService.getTradeConditions();
            return new HrResult(CommonReturnCode.SUCCESS, tradeConditions);
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("获取交易条件列表异常：", e);
            return new HrResult(CommonReturnCode.FAILED.getCode(), "系统异常：" + e.getMessage());
        }
    }

    /**
     * 获取付款条件列表
     *
     * @throws Exception
     * @return
     */
    @PostMapping("/getPayConditions")
    @ResponseBody
    public Object getPayConditions() throws Exception {
        try {
            // 验证用户登录状态
            AuthorizingUser authorizingUser = SingletonLoginUtils.getUser();
            if (authorizingUser == null) {
                return new HrResult(CommonReturnCode.FAILED, "登录超时，请重新登录");
            }

            // 获取付款条件列表
            List<Map<String, Object>> payConditions = salesLoginService.getPayConditions();
            return new HrResult(CommonReturnCode.SUCCESS, payConditions);
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("获取付款条件列表异常：", e);
            return new HrResult(CommonReturnCode.FAILED.getCode(), "系统异常：" + e.getMessage());
        }
    }

    /**
     * 获取运输条件列表
     *
     * @throws Exception
     * @return
     */
    @PostMapping("/getTransportConditions")
    @ResponseBody
    public Object getTransportConditions() throws Exception {
        try {
            // 验证用户登录状态
            AuthorizingUser authorizingUser = SingletonLoginUtils.getUser();
            if (authorizingUser == null) {
                return new HrResult(CommonReturnCode.FAILED, "登录超时，请重新登录");
            }

            // 获取运输条件列表
            List<Map<String, Object>> transportConditions = salesLoginService.getTransportConditions();
            return new HrResult(CommonReturnCode.SUCCESS, transportConditions);
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("获取运输条件列表异常：", e);
            return new HrResult(CommonReturnCode.FAILED.getCode(), "系统异常：" + e.getMessage());
        }
    }

    /**
     * 获取出库数据
     *
     * @param customerCode 客户代码
     * @param startDate    开始日期
     * @param endDate      结束日期
     * @throws Exception
     * @return
     */
    @PostMapping("/getOutboundData")
    @ResponseBody
    public Object getOutboundData(String customerCode, String startDate, String endDate) throws Exception {
        try {
            // 验证用户登录状态
            AuthorizingUser authorizingUser = SingletonLoginUtils.getUser();
            if (authorizingUser == null) {
                return new HrResult(CommonReturnCode.FAILED, "登录超时，请重新登录");
            }

            if (StringUtil.isStringEmpty(customerCode)) {
                return new HrResult(CommonReturnCode.FAILED, "客户代码不能为空");
            }

            // 获取出库数据（支持日期范围）
            List<Map<String, Object>> outboundDataList = salesLoginService.getOutboundDataForSalesAmountWithDateRange(
                    customerCode,
                    startDate, endDate);
            if (outboundDataList == null || outboundDataList.isEmpty()) {
                // 返回空列表而不是错误信息
                return new HrResult(CommonReturnCode.SUCCESS, outboundDataList);
            }

            return new HrResult(CommonReturnCode.SUCCESS, outboundDataList);
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("获取出库数据异常：", e);
            return new HrResult(CommonReturnCode.FAILED.getCode(), "系统异常：" + e.getMessage());
        }
    }

    /**
     * 根据出货单No.获取明细数据
     *
     * @param shipmentNo 出货单No.
     * @throws Exception
     * @return
     */
    @PostMapping("/getDetailsByShipmentNo")
    @ResponseBody
    public Object getDetailsByShipmentNo(String shipmentNo) throws Exception {
        try {
            // 验证用户登录状态
            AuthorizingUser authorizingUser = SingletonLoginUtils.getUser();
            if (authorizingUser == null) {
                return new HrResult(CommonReturnCode.FAILED, "登录超时，请重新登录");
            }

            if (StringUtil.isStringEmpty(shipmentNo)) {
                return new HrResult(CommonReturnCode.FAILED, "出货单No.不能为空");
            }

            // 根据出货单No.获取明细数据
            List<Map<String, Object>> detailsList = salesLoginService.getDetailsByShipmentNo(shipmentNo);
            if (detailsList == null || detailsList.isEmpty()) {
                return new HrResult(CommonReturnCode.SUCCESS, detailsList);
            }

            return new HrResult(CommonReturnCode.SUCCESS, detailsList);
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("根据出货单No.获取明细数据异常：", e);
            return new HrResult(CommonReturnCode.FAILED.getCode(), "系统异常：" + e.getMessage());
        }
    }

    /**
     * 解码HTML实体
     * 
     * @param input 包含HTML实体的字符串
     * @return 解码后的字符串
     */
    private String unescapeHtml(String input) {
        if (input == null) {
            return null;
        }

        return input.replace("&quot;", "\"")
                .replace("&amp;", "&")
                .replace("&lt;", "<")
                .replace("&gt;", ">")
                .replace("&apos;", "'")
                .replace("&#39;", "'")
                .replace("&#x27;", "'")
                .replace("&#x2F;", "/")
                .replace("&#x60;", "`")
                .replace("&#x3D;", "=");
    }

    /**
     * 获取铜合同列表
     *
     * @param customerCode       客户代码
     * @param copperContractType 铜合同类别
     * @param useMonth           使用月（可以是完整日期YYYY-MM-DD或年月格式YYYY-MM）
     * @param status             状态
     * @throws Exception
     * @return
     */
    @PostMapping("/getCopperContractList")
    @ResponseBody
    public Object getCopperContractList(String customerCode, String copperContractType, String useMonth, String status)
            throws Exception {
        try {
            // 验证用户登录状态
            AuthorizingUser authorizingUser = SingletonLoginUtils.getUser();
            if (authorizingUser == null) {
                return new HrResult(CommonReturnCode.FAILED, "登录超时，请重新登录");
            }

            logger.info("获取铜合同列表原始参数: customerCode=" + customerCode + ", copperContractType=" + copperContractType +
                    ", useMonth=" + useMonth + ", status=" + status);

            // 格式化useMonth参数：如果是完整日期格式（YYYY-MM-DD），则截取年月部分（YYYY-MM）
            String formattedUseMonth = useMonth;
            if (useMonth != null && useMonth.length() > 7 && useMonth.matches("\\d{4}-\\d{2}-\\d{2}")) {
                formattedUseMonth = useMonth.substring(0, 7);
                logger.info("日期格式化: " + useMonth + " -> " + formattedUseMonth);
            }

            logger.info("获取铜合同列表格式化后参数: customerCode=" + customerCode + ", copperContractType=" + copperContractType +
                    ", useMonth=" + formattedUseMonth + ", status=" + status);

            // 调用服务获取铜合同列表
            List<CopperContract> copperContractList = salesCommonService.getCopperContractList(
                    customerCode, copperContractType, formattedUseMonth, status);

            logger.info("获取到铜合同列表数量: " + (copperContractList != null ? copperContractList.size() : 0));

            return new HrResult(CommonReturnCode.SUCCESS, copperContractList);

        } catch (Exception e) {
            e.printStackTrace();
            logger.error("获取铜合同列表异常：", e);
            return new HrResult(CommonReturnCode.FAILED.getCode(), "系统异常：" + e.getMessage());
        }
    }

    /**
     * 获取铜条件列表
     *
     * @throws Exception
     * @return
     */
    @PostMapping("/getCopperConditionList")
    @ResponseBody
    public Object getCopperConditionList() throws Exception {
        try {
            // 验证用户登录状态
            AuthorizingUser authorizingUser = SingletonLoginUtils.getUser();
            if (authorizingUser == null) {
                return new HrResult(CommonReturnCode.FAILED, "登录超时，请重新登录");
            }

            logger.info("获取铜条件列表");

            // 调用服务获取铜条件列表
            List<CopperConditions> copperConditionsList = salesCommonService.listCopperConditions(null, null);

            logger.info("获取到铜条件列表数量: " + (copperConditionsList != null ? copperConditionsList.size() : 0));

            return new HrResult(CommonReturnCode.SUCCESS, copperConditionsList);

        } catch (Exception e) {
            e.printStackTrace();
            logger.error("获取铜条件列表异常：", e);
            return new HrResult(CommonReturnCode.FAILED.getCode(), "系统异常：" + e.getMessage());
        }
    }

    /**
     * 导出销售额Excel
     *
     * @param customerCode         客户代码
     * @param salesAmountDateRange 销售额日期范围
     * @param response             HTTP响应
     * @throws Exception
     */
    @PostMapping("/export")
    @ResponseBody
    public void exportSalesAmountExcel(String customerCode, String salesAmountDateRange,
            HttpServletResponse response) throws Exception {
        try {
            // 验证用户登录状态
            AuthorizingUser authorizingUser = SingletonLoginUtils.getUser();
            if (authorizingUser == null) {
                response.getWriter().write("{\"code\":0,\"msg\":\"登录超时，请重新登录\"}");
                return;
            }

            // 获取导出数据
            List<SalesAmountExportDTO> exportData = salesLoginService.getSalesAmountExportData(
                    customerCode, salesAmountDateRange);

            if (exportData == null || exportData.isEmpty()) {
                response.getWriter().write("{\"code\":0,\"msg\":\"没有可导出的数据\"}");
                return;
            }

            // 设置响应头
            String fileName = "销售额登录_" + DateUtils.convertIntToDateString(DateUtils.getCurrentTime(), "yyyyMMddHHmmss")
                    + ".xls";
            ServletUtils.setFileDownloadHeader(response, fileName);
            response.setContentType(ServletUtils.EXCEL_TYPE);

            // 导出Excel - 使用模板
            String companyInfo = "住友电工运营株式会社";
            TemplateExcelExportUtil.exportSalesAmountExcel(response.getOutputStream(), exportData, companyInfo);

        } catch (Exception e) {
            logger.error("导出销售额Excel异常：", e);
            response.getWriter().write("{\"code\":0,\"msg\":\"导出失败：" + e.getMessage() + "\"}");
        }
    }

    /**
     * 根据销售额NO导出Excel
     *
     * @param salesAmountNo 销售额NO
     * @param response      HTTP响应
     * @throws Exception
     */
    @PostMapping("/exportByNo")
    @ResponseBody
    public void exportSalesAmountExcelByNo(String salesAmountNo, HttpServletResponse response) throws Exception {
        try {
            // 验证用户登录状态
            AuthorizingUser authorizingUser = SingletonLoginUtils.getUser();
            if (authorizingUser == null) {
                response.getWriter().write("{\"code\":0,\"msg\":\"登录超时，请重新登录\"}");
                return;
            }

            if (StringUtil.isStringEmpty(salesAmountNo)) {
                response.getWriter().write("{\"code\":0,\"msg\":\"销售额NO不能为空\"}");
                return;
            }

            // 获取导出数据
            List<SalesAmountExportDTO> exportData = salesLoginService.getSalesAmountExportDataByNo(salesAmountNo);

            if (exportData == null || exportData.isEmpty()) {
                response.getWriter().write("{\"code\":0,\"msg\":\"没有可导出的数据\"}");
                return;
            }

            // 设置响应头
            String fileName = "销售额登录_" + salesAmountNo + "_" +
                    DateUtils.convertIntToDateString(DateUtils.getCurrentTime(), "yyyyMMddHHmmss") + ".xls";
            ServletUtils.setFileDownloadHeader(response, fileName);
            response.setContentType(ServletUtils.EXCEL_TYPE);

            // 导出Excel - 使用模板
            String companyInfo = "住友电工运营株式会社";
            TemplateExcelExportUtil.exportSalesAmountExcel(response.getOutputStream(), exportData, companyInfo);

        } catch (Exception e) {
            logger.error("导出销售额Excel异常：", e);
            response.getWriter().write("{\"code\":0,\"msg\":\"导出失败：" + e.getMessage() + "\"}");
        }
    }
}