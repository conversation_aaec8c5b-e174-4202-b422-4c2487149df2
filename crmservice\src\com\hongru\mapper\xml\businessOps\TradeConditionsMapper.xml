<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hongru.mapper.businessOps.TradeConditionsMapper">
    <sql id="tradeConditions_sql">
		pc.[流水号] AS id,pc.[交易条件] AS tradeCondition,pc.[交易条件名] AS conditionName,pc.[备注] AS remark,
		pc.[创建人姓名] AS creatorName,pc.[创建时间] AS createdTime,
		pc.[更新人姓名] AS updaterName,pc.[更新时间] AS updatedTime
	</sql>

	<insert id="insertTradeConditions" parameterType="com.hongru.entity.businessOps.TradeConditions">
		INSERT INTO [businessOps].[dbo].[交易条件表]
		(
		[交易条件],
		[交易条件名],
		[备注],
		[创建人姓名],
		[创建时间]
		)VALUES(
		#{tradeConditions.tradeCondition},
		#{tradeConditions.conditionName},
		#{tradeConditions.remark},
		#{tradeConditions.creatorName},
		#{tradeConditions.createdTime}
		)
	</insert>

	<select id="selectTradeConditionsById" resultType="com.hongru.entity.businessOps.TradeConditions">
		SELECT
		<include refid="tradeConditions_sql"/>
		FROM [businessOps].[dbo].[交易条件表] pc
		<where>
			<if test="id != null">
				AND pc.[流水号] = #{id}
			</if>
		</where>
	</select>

	<select id="listTradeConditions" resultType="com.hongru.entity.businessOps.TradeConditions">
		SELECT
		<include refid="tradeConditions_sql"/>
		FROM [businessOps].[dbo].[交易条件表] pc
		<where>
			<if test="tradeCondition != null and tradeCondition != ''">
				AND pc.[交易条件] = #{tradeCondition}
			</if>
			<if test="conditionName != null and conditionName != 0">
				AND pc.[交易条件名] = #{conditionName}
			</if>
		</where>
		ORDER BY pc.[交易条件]
	</select>

	<update id="updateTradeConditions">
		UPDATE [businessOps].[dbo].[交易条件表]
		<set>
			<if test="tradeConditions.tradeCondition != null and tradeConditions.tradeCondition != ''">
				[交易条件] = #{tradeConditions.tradeCondition},
			</if>
			<if test="tradeConditions.conditionName != null and tradeConditions.conditionName != ''">
				[交易条件名] = #{tradeConditions.conditionName},
			</if>
			<if test="tradeConditions.remark != null">
				[备注] = #{tradeConditions.remark},
			</if>
			<if test="tradeConditions.updaterName != null and tradeConditions.updaterName != ''">
				[更新人姓名] = #{tradeConditions.updaterName},
			</if>
			<if test="tradeConditions.updatedTime != null">
				[更新时间] = #{tradeConditions.updatedTime},
			</if>
		</set>
		WHERE [流水号] = #{tradeConditions.id}
	</update>
	
	<delete id="deleteTradeConditions">
		DELETE [businessOps].[dbo].[交易条件表] WHERE [流水号] = #{id}
	</delete>
</mapper>