package com.hongru.pojo.dto;

import java.math.BigDecimal;
import java.util.List;

import com.hongru.support.page.PageInfo;

/**
 * 出货和销售额差DTO
 */
public class ShipmentSalesDiffDTO {

    private String customerAlias; // 客户简称
    private String productCode; // 产品代码
    private String productMiddleCategory; // 产品中分类
    private BigDecimal shipmentQuantity; // 出货数
    private BigDecimal salesQuantity; // 销售额数
    private BigDecimal salesAmount; // 销售金额（不含税）
    private BigDecimal copperAmount; // 铜金额
    private String accountingYearMonth; // 会计年月

    // 分页信息
    private PageInfo pageInfo;

    // 查询结果列表
    private List<ShipmentSalesDiffDTO> shipmentSalesDiffList;

    public String getCustomerAlias() {
        return customerAlias;
    }

    public void setCustomerAlias(String customerAlias) {
        this.customerAlias = customerAlias;
    }

    public String getProductCode() {
        return productCode;
    }

    public void setProductCode(String productCode) {
        this.productCode = productCode;
    }

    public String getProductMiddleCategory() {
        return productMiddleCategory;
    }

    public void setProductMiddleCategory(String productMiddleCategory) {
        this.productMiddleCategory = productMiddleCategory;
    }

    public BigDecimal getShipmentQuantity() {
        return shipmentQuantity;
    }

    public void setShipmentQuantity(BigDecimal shipmentQuantity) {
        this.shipmentQuantity = shipmentQuantity;
    }

    public BigDecimal getSalesQuantity() {
        return salesQuantity;
    }

    public void setSalesQuantity(BigDecimal salesQuantity) {
        this.salesQuantity = salesQuantity;
    }

    public BigDecimal getSalesAmount() {
        return salesAmount;
    }

    public void setSalesAmount(BigDecimal salesAmount) {
        this.salesAmount = salesAmount;
    }

    public BigDecimal getCopperAmount() {
        return copperAmount;
    }

    public void setCopperAmount(BigDecimal copperAmount) {
        this.copperAmount = copperAmount;
    }

    public String getAccountingYearMonth() {
        return accountingYearMonth;
    }

    public void setAccountingYearMonth(String accountingYearMonth) {
        this.accountingYearMonth = accountingYearMonth;
    }

    public PageInfo getPageInfo() {
        return pageInfo;
    }

    public void setPageInfo(PageInfo pageInfo) {
        this.pageInfo = pageInfo;
    }

    public List<ShipmentSalesDiffDTO> getShipmentSalesDiffList() {
        return shipmentSalesDiffList;
    }

    public void setShipmentSalesDiffList(List<ShipmentSalesDiffDTO> shipmentSalesDiffList) {
        this.shipmentSalesDiffList = shipmentSalesDiffList;
    }
}