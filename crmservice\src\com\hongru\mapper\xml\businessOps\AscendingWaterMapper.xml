<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hongru.mapper.businessOps.AscendingWaterMapper">

	<insert id="insertAscendingWater" parameterType="com.hongru.entity.businessOps.AscendingWater">
		INSERT INTO [businessOps].[dbo].[升水表]
		(
		[客户代码],
		[铜条件],
		[适用开始日期],
		[适用结束日期],
		[升水单价],
		[创建人姓名],
		[创建时间]
		)VALUES(
		#{ascendingWater.customerCode},
		#{ascendingWater.copperCondition},
		#{ascendingWater.applyDateStart},
		#{ascendingWater.applyDateEnd},
		#{ascendingWater.ascendingWaterPrice},
		#{ascendingWater.creatorName},
		#{ascendingWater.createdTime}
		)
	</insert>

	<select id="selectAscendingWaterById" resultType="com.hongru.entity.businessOps.AscendingWater">
		SELECT a.[流水号] AS id, a.[客户代码] AS customerCode, b.[客户简称] AS customerAlias,a.[铜条件] AS copperCondition,
			   c.[铜条件名] AS copperConditionName, c.[货币] AS currency,a.[适用开始日期] AS applyDateStart,
			   a.[适用结束日期] AS applyDateEnd, a.[升水单价] AS ascendingWaterPrice, a.[创建人姓名] AS creatorName,
			   a.[创建时间] AS createdTime
		FROM [businessOps].[dbo].[升水表] a
		LEFT JOIN [sumitomo].[dbo].[客户表] b ON a.[客户代码] = b.[客户代码]
		LEFT JOIN [businessOps].[dbo].[铜条件表] c ON a.[铜条件] = c.[铜条件]
		<where>
			<if test="id != null">
				AND a.[流水号] = #{id}
			</if>
		</where>
	</select>
	
	<select id="ascendingWaterListByPage" resultType="com.hongru.entity.businessOps.AscendingWater">
		SELECT a.[流水号] AS id, a.[客户代码] AS customerCode, b.[客户简称] AS customerAlias,a.[铜条件] AS copperCondition,
			   c.[铜条件名] AS copperConditionName, c.[货币] AS currency,a.[适用开始日期] AS applyDateStart,
			   a.[适用结束日期] AS applyDateEnd, a.[升水单价] AS ascendingWaterPrice, a.[创建人姓名] AS creatorName,
			   a.[创建时间] AS createdTime
		FROM [businessOps].[dbo].[升水表] a
		LEFT JOIN [sumitomo].[dbo].[客户表] b ON a.[客户代码] COLLATE Chinese_PRC_CI_AS = b.[客户代码] COLLATE Chinese_PRC_CI_AS
		LEFT JOIN [businessOps].[dbo].[铜条件表] c ON a.[铜条件] = c.[铜条件]
		<where>
			<if test="customerCode != null and customerCode != ''">
				AND a.[客户代码] = #{customerCode}
			</if>
			<if test="copperCondition != null and copperCondition != ''">
				AND a.[铜条件] = #{copperCondition}
			</if>
			<if test="applyDateStart != null">
				AND a.[适用开始日期] &gt;=#{applyDateStart}
			</if>
			<if test="applyDateEnd != null">
				AND a.[适用结束日期] &lt;= #{applyDateEnd}
			</if>
		</where>
		ORDER BY a.[客户代码]
		<if test="pageInfo">
			OFFSET ${pageInfo.offset} ROWS
			FETCH NEXT ${pageInfo.limit} ROWS ONLY
		</if>
	</select>
	
	<select id="ascendingWaterListByPageCount" resultType="integer">
		SELECT
		 	COUNT(1)
		FROM [businessOps].[dbo].[升水表] a
		LEFT JOIN [sumitomo].[dbo].[客户表] b ON a.[客户代码] COLLATE Chinese_PRC_CI_AS = b.[客户代码] COLLATE Chinese_PRC_CI_AS
		LEFT JOIN [businessOps].[dbo].[铜条件表] c ON a.[铜条件] = c.[铜条件]
		<where>
			<if test="customerCode != null and customerCode != ''">
				AND a.[客户代码] = #{customerCode}
			</if>
			<if test="copperCondition != null and copperCondition != ''">
				AND a.[铜条件] = #{copperCondition}
			</if>
			<if test="applyDateStart != null">
				AND a.[适用开始日期] &gt;=#{applyDateStart}
			</if>
			<if test="applyDateEnd != null">
				AND a.[适用结束日期] &lt;= #{applyDateEnd}
			</if>
		</where>
	</select>
	
	<update id="updateAscendingWater">
		UPDATE [businessOps].[dbo].[升水表]
		<set>
			<if test="ascendingWater.customerCode != null and ascendingWater.customerCode != ''">
				[客户代码] = #{ascendingWater.customerCode},
			</if>
			<if test="ascendingWater.copperCondition != null and ascendingWater.copperCondition != ''">
				[铜条件 ] = #{ascendingWater.copperCondition},
			</if>
			<if test="ascendingWater.applyDateStart != null">
				[适用开始日期] = #{ascendingWater.applyDateStart},
			</if>
			<if test="ascendingWater.applyDateEnd != null">
				[适用结束日期] = #{ascendingWater.applyDateEnd},
			</if>
			<if test="ascendingWater.ascendingWaterPrice != null">
				[升水单价] = #{ascendingWater.ascendingWaterPrice},
			</if>
			<if test="ascendingWater.updaterName != null and ascendingWater.updaterName != ''">
				[更新人姓名] = #{ascendingWater.updaterName},
			</if>
			<if test="ascendingWater.updatedTime != null">
				[更新时间] = #{ascendingWater.updatedTime},
			</if>
		</set>
		WHERE [流水号] = #{ascendingWater.id}
	</update>
	
	<delete id="deleteAscendingWater">
		DELETE [businessOps].[dbo].[升水表] WHERE [流水号] = #{id}
	</delete>

	<!-- 根据客户代码和到货日期查询升水表数据（用于一般铜的铜条件和升水单价获取） -->
	<select id="getAscendingWaterByCustomerAndDate" resultType="com.hongru.entity.businessOps.AscendingWater">
		SELECT a.[流水号] AS id, a.[客户代码] AS customerCode, a.[铜条件] AS copperCondition,
			   a.[适用开始日期] AS applyDateStart, a.[适用结束日期] AS applyDateEnd,
			   a.[升水单价] AS ascendingWaterPrice,
			   c.[铜条件名] AS copperConditionName, c.[货币] AS currency
		FROM [businessOps].[dbo].[升水表] a
		LEFT JOIN [businessOps].[dbo].[铜条件表] c ON a.[铜条件] = c.[铜条件]
		WHERE a.[客户代码] = #{customerCode}
		AND a.[适用开始日期] &lt;= #{arrivalDate}
		AND a.[适用结束日期] &gt;= #{arrivalDate}
		ORDER BY a.[适用开始日期] DESC
	</select>
</mapper>