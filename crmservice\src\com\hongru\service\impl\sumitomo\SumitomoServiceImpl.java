package com.hongru.service.impl.sumitomo;

import com.hongru.entity.sumitomo.Customers;
import com.hongru.entity.sumitomo.Product;
import com.hongru.mapper.sumitomo.CustomersMapper;
import com.hongru.mapper.sumitomo.ProductMapper;
import com.hongru.service.sumitomo.ISumitomoService;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.List;

@Service
public class SumitomoServiceImpl implements ISumitomoService {
	@Autowired
	private CustomersMapper customersMapper;
	@Autowired
	private ProductMapper productMapper;

	/* ======================客户表====================== */
	/**
	 * 客户表详情list
	 * 
	 * @param
	 * @throws
	 */
	public List<Customers> listCustomersList() throws Exception {
		return customersMapper.selectCustomersList();
	}

	/**
	 * 根据客户代码获取客户信息
	 * 
	 * @param customerCode 客户代码
	 * @throws
	 * @return 客户信息
	 */
	public Customers getCustomerByCode(String customerCode) throws Exception {
		return customersMapper.selectByCustomerCode(customerCode);
	}

	/* ======================产品表====================== */
	@Override
	public List<String> getProductListByCustomerCode(String customerCode, String printShield) {

		return productMapper.getProductListByCustomerCode(customerCode, printShield);
	}

	/**
	 * 根据产品代码获取产品信息
	 * 
	 * @param productCode 产品代码
	 * @param printShield 打印屏蔽
	 * @throws
	 * @return
	 */
	@Override
	public Product getProductInfo(String productCode, String printShield) {
		return productMapper.getProductInfo(productCode, printShield);
	}

	/**
	 * 获取产品尺寸列表
	 * 
	 * @param customerCode 客户代码
	 * @param productCode  产品代码
	 * @param printShield  打印屏蔽
	 * @throws
	 * @return
	 */
	@Override
	public List<String> getProductSizeList(String customerCode, String productCode, String printShield) {
		return productMapper.getProductSizeList(customerCode, productCode, printShield);
	}

	/**
	 * 获取产品线盘名称列表
	 * 
	 * @param customerCode 客户代码
	 * @param productCode  产品代码
	 * @param size         尺寸
	 * @param printShield  打印屏蔽
	 * @throws
	 * @return
	 */
	@Override
	public List<String> getProductWireReelNameList(String customerCode, String productCode, String size,
			String printShield) {
		return productMapper.getProductWireReelNameList(customerCode, productCode, size, printShield);
	}
}