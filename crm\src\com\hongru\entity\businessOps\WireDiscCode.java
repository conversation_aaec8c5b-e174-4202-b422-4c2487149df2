package com.hongru.entity.businessOps;

import com.baomidou.mybatisplus.annotations.TableId;
import com.baomidou.mybatisplus.annotations.TableName;
import com.baomidou.mybatisplus.enums.IdType;

@TableName("线盘成本编码表")//CostPrice
public class WireDiscCode {
	/* 状态-正常 */
	public static final short STATE_NORMAL = 0;
	/* 状态-删除 */
	public static final short STATE_DELETED = 9;

	/* 流水号 */
	@TableId(value="流水号", type= IdType.AUTO)
	protected int codeId;
	/* 状态 */
	protected short state;
	/* 线盘名称 */
	protected String wireDiscName;
	/* 成本编码 */
	protected String paintCode;

	public int getCodeId() {
		return codeId;
	}

	public void setCodeId(int codeId) {
		this.codeId = codeId;
	}

	public short getState() {
		return state;
	}

	public void setState(short state) {
		this.state = state;
	}

	public String getWireDiscName() {
		return wireDiscName;
	}

	public void setWireDiscName(String wireDiscName) {
		this.wireDiscName = wireDiscName;
	}

	public String getPaintCode() {
		return paintCode;
	}

	public void setPaintCode(String paintCode) {
		this.paintCode = paintCode;
	}
}