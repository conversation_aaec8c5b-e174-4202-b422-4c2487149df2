package com.hongru.controller.salesReport;

import com.hongru.base.BaseController;
import com.hongru.common.result.HrPageResult;
import com.hongru.common.result.HrResult;
import com.hongru.common.security.AuthorizingUser;
import com.hongru.common.util.DateUtils;
import com.hongru.common.util.SingletonLoginUtils;
import com.hongru.common.util.StringUtil;
import com.hongru.constant.CommonReturnCode;
import com.hongru.entity.admin.User;
import com.hongru.pojo.dto.CuBaseDTO;
import com.hongru.pojo.dto.CustomerSalesRevenueDTO;
import com.hongru.pojo.dto.DepartmentalSalesRevenueDTO;
import com.hongru.pojo.dto.MonthlyShipmentDetailDTO;
import com.hongru.pojo.dto.MonthlyShipmentPerformanceDTO;
import com.hongru.pojo.dto.ShipmentSalesDiffDTO;
import com.hongru.pojo.dto.UserMarginDTO;
import com.hongru.pojo.dto.ZeroBaseReportDTO;
import com.hongru.service.admin.IUserService;
import com.hongru.service.businessOps.ISalesReportService;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

@Controller
@RequestMapping(value = "/salesReport")
public class SalesReportController extends BaseController {

    @Autowired
    private ISalesReportService salesReportService;

    @Autowired
    private IUserService userService;

    /**
     * 部门销售额页面
     * 
     * @param model
     * @throws Exception
     * @return
     */
    @GetMapping("/departmentalSalesRevenue")
    public String departmentalSalesRevenueView(Model model) throws Exception {
        return "/modules/salesReport/departmentalSalesRevenue";
    }

    /**
     * 客户销售额页面
     * 
     * @param model
     * @throws Exception
     * @return
     */
    @GetMapping("/customerSalesRevenue")
    public String customerSalesRevenueView(Model model) throws Exception {
        return "/modules/salesReport/customerSalesRevenue";
    }

    /**
     * 月出货明细页面
     * 
     * @param model
     * @throws Exception
     * @return
     */
    @GetMapping("/monthlyShipmentDetails")
    public String monthlyShipmentDetailsView(Model model) throws Exception {
        return "/modules/salesReport/monthlyShipmentDetails";
    }

    /**
     * 查询部门销售额数据
     * 
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @throws Exception
     * @return
     */
    @PostMapping("/departmentalSalesRevenue/list")
    @ResponseBody
    public Object departmentalSalesRevenueList(String startDate, String endDate) throws Exception {
        try {
            // 验证用户登录状态
            AuthorizingUser authorizingUser = SingletonLoginUtils.getUser();
            if (authorizingUser == null) {
                return new HrResult(CommonReturnCode.FAILED, "登录超时，请重新登录");
            }

            // 查询数据
            List<DepartmentalSalesRevenueDTO> dataList = salesReportService.listDepartmentalSalesRevenue(startDate,
                    endDate);

            return new HrPageResult(dataList, dataList.size());
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("查询部门销售额数据异常：", e);
            return new HrResult(CommonReturnCode.FAILED.getCode(), "系统异常");
        }
    }

    /**
     * 查询月出货明细数据
     * 
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @throws Exception
     * @return
     */
    @PostMapping("/monthlyShipmentDetails/list")
    @ResponseBody
    public Object monthlyShipmentDetailsList(String startDate, String endDate) throws Exception {
        try {
            // 验证用户登录状态
            AuthorizingUser authorizingUser = SingletonLoginUtils.getUser();
            if (authorizingUser == null) {
                return new HrResult(CommonReturnCode.FAILED, "登录超时，请重新登录");
            }

            // 打印日志，确认参数是否正确
            logger.info("月出货明细查询参数 - startDate: [" + startDate + "], endDate: [" + endDate + "]");

            // 确保日期参数为null或有效值（而不是空字符串）
            if (startDate != null && startDate.trim().isEmpty()) {
                startDate = null;
            }
            if (endDate != null && endDate.trim().isEmpty()) {
                endDate = null;
            }

            // 查询数据
            List<MonthlyShipmentDetailDTO> dataList = salesReportService.listMonthlyShipmentDetail(startDate, endDate);

            return new HrPageResult(dataList, dataList.size());
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("查询月出货明细数据异常：", e);
            return new HrResult(CommonReturnCode.FAILED.getCode(), "系统异常");
        }
    }

    /**
     * 导出月出货明细数据
     * 
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @param response  HTTP响应
     * @throws Exception
     */
    @GetMapping("/monthlyShipmentDetails/export")
    public void exportMonthlyShipmentDetails(String startDate, String endDate, HttpServletResponse response)
            throws Exception {
        try {
            // 验证用户登录状态
            AuthorizingUser authorizingUser = SingletonLoginUtils.getUser();
            if (authorizingUser == null) {
                return;
            }

            // 打印日志，确认参数是否正确
            logger.info("月出货明细导出参数 - startDate: [" + startDate + "], endDate: [" + endDate + "]");

            // 确保日期参数为null或有效值（而不是空字符串）
            if (startDate != null && startDate.trim().isEmpty()) {
                startDate = null;
            }
            if (endDate != null && endDate.trim().isEmpty()) {
                endDate = null;
            }

            salesReportService.exportMonthlyShipmentDetail(startDate, endDate, response);
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("导出月出货明细数据异常：", e);
        }
    }

    /**
     * 查询客户销售额数据
     * 
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @throws Exception
     * @return
     */
    @PostMapping("/customerSalesRevenue/list")
    @ResponseBody
    public Object customerSalesRevenueList(String startDate, String endDate) throws Exception {
        try {
            // 验证用户登录状态
            AuthorizingUser authorizingUser = SingletonLoginUtils.getUser();
            if (authorizingUser == null) {
                return new HrResult(CommonReturnCode.FAILED, "登录超时，请重新登录");
            }

            // 查询数据
            List<CustomerSalesRevenueDTO> dataList = salesReportService.listCustomerSalesRevenue(startDate, endDate);

            return new HrPageResult(dataList, dataList.size());
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("查询客户销售额数据异常：", e);
            return new HrResult(CommonReturnCode.FAILED.getCode(), "系统异常");
        }
    }

    /**
     * 导出部门销售额数据
     * 
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @param response  HTTP响应
     * @throws Exception
     */
    @PostMapping("/departmentalSalesRevenue/export")
    public void departmentalSalesRevenueExport(String startDate, String endDate, HttpServletResponse response)
            throws Exception {
        try {
            // 验证用户登录状态
            AuthorizingUser authorizingUser = SingletonLoginUtils.getUser();
            if (authorizingUser == null) {
                response.setContentType("text/html;charset=utf-8");
                response.getWriter().write("登录超时，请重新登录");
                return;
            }

            // 导出数据
            salesReportService.exportDepartmentalSalesRevenue(startDate, endDate, response);
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("导出部门销售额数据异常：", e);
            response.setContentType("text/html;charset=utf-8");
            response.getWriter().write("导出失败：" + e.getMessage());
        }
    }

    /**
     * 导出客户销售额数据
     * 
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @param response  HTTP响应
     * @throws Exception
     */
    @PostMapping("/customerSalesRevenue/export")
    public void customerSalesRevenueExport(String startDate, String endDate, HttpServletResponse response)
            throws Exception {
        try {
            // 验证用户登录状态
            AuthorizingUser authorizingUser = SingletonLoginUtils.getUser();
            if (authorizingUser == null) {
                response.setContentType("text/html;charset=utf-8");
                response.getWriter().write("登录超时，请重新登录");
                return;
            }

            // 导出数据
            salesReportService.exportCustomerSalesRevenue(startDate, endDate, response);
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("导出客户销售额数据异常：", e);
            response.setContentType("text/html;charset=utf-8");
            response.getWriter().write("导出失败：" + e.getMessage());
        }
    }

    /**
     * 出货和销售额差页面
     * 
     * @param model
     * @throws Exception
     * @return
     */
    @GetMapping("/diffByShipmentAndSalesRevenue")
    public String diffByShipmentAndSalesRevenueView(Model model) throws Exception {
        return "/modules/salesReport/diffByShipmentAndSalesRevenue";
    }

    /**
     * 查询出货和销售额差数据
     *
     * @param yearMonth 年月（格式：yyyy-MM）
     * @throws Exception
     * @return
     */
    @PostMapping("/diffByShipmentAndSalesRevenue/list")
    @ResponseBody
    public Object diffByShipmentAndSalesRevenueList(String yearMonth) throws Exception {
        try {
            // 验证用户登录状态
            AuthorizingUser authorizingUser = SingletonLoginUtils.getUser();
            if (authorizingUser == null) {
                return new HrResult(CommonReturnCode.FAILED, "登录超时，请重新登录");
            }

            if (StringUtil.isStringEmpty(yearMonth)) {
                return new HrResult(CommonReturnCode.FAILED, "年月参数不能为空");
            }

            // 查询数据：计算选择年月的上个月销售额登录数据与当前年月支付请求数据的差额
            List<ShipmentSalesDiffDTO> dataList = salesReportService.listShipmentSalesDiffByYearMonth(yearMonth);

            return new HrPageResult(dataList, dataList.size());
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("查询出货和销售额差数据异常：", e);
            return new HrResult(CommonReturnCode.FAILED.getCode(), "系统异常");
        }
    }

    /**
     * 导出出货和销售额差数据
     *
     * @param yearMonth 年月（格式：yyyy-MM）
     * @param response  HTTP响应
     * @throws Exception
     */
    @PostMapping("/diffByShipmentAndSalesRevenue/export")
    public void diffByShipmentAndSalesRevenueExport(String yearMonth, HttpServletResponse response)
            throws Exception {
        try {
            // 验证用户登录状态
            AuthorizingUser authorizingUser = SingletonLoginUtils.getUser();
            if (authorizingUser == null) {
                response.setContentType("text/html;charset=utf-8");
                response.getWriter().write("登录超时，请重新登录");
                return;
            }

            if (StringUtil.isStringEmpty(yearMonth)) {
                response.setContentType("text/html;charset=utf-8");
                response.getWriter().write("年月参数不能为空");
                return;
            }

            // 导出数据
            salesReportService.exportShipmentSalesDiffByYearMonth(yearMonth, response);
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("导出出货和销售额差数据异常：", e);
            response.setContentType("text/html;charset=utf-8");
            response.getWriter().write("导出失败：" + e.getMessage());
        }
    }

    /**
     * 月出货实绩页面
     * 
     * @param model
     * @throws Exception
     * @return
     */
    @GetMapping("/monthlyShipmentPerformance")
    public String monthlyShipmentPerformanceView(Model model) throws Exception {
        return "/modules/salesReport/monthlyShipmentPerformance";
    }

    /**
     * 查询月出货实绩数据
     * 
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @throws Exception
     * @return
     */
    @PostMapping("/monthlyShipmentPerformance/list")
    @ResponseBody
    public Object monthlyShipmentPerformanceList(String startDate, String endDate) throws Exception {
        try {
            // 验证用户登录状态
            AuthorizingUser authorizingUser = SingletonLoginUtils.getUser();
            if (authorizingUser == null) {
                return new HrResult(CommonReturnCode.FAILED, "登录超时，请重新登录");
            }

            // 打印日志，确认参数是否正确
            logger.info("月出货实绩查询参数 - startDate: [" + startDate + "], endDate: [" + endDate + "]");

            // 确保日期参数为null或有效值（而不是空字符串）
            if (startDate != null && startDate.trim().isEmpty()) {
                startDate = null;
            }
            if (endDate != null && endDate.trim().isEmpty()) {
                endDate = null;
            }

            // 查询数据
            List<MonthlyShipmentPerformanceDTO> dataList = salesReportService.listMonthlyShipmentPerformance(startDate,
                    endDate);

            // 添加日志，输出查询结果中的国内/国外值，用于调试编码问题
            if (dataList != null && !dataList.isEmpty()) {
                logger.info("查询结果示例 - 第一条记录的国内/国外值: [" + dataList.get(0).getDomesticOrForeign() + "]");
            }

            return new HrPageResult(dataList, dataList.size());
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("查询月出货实绩数据异常：", e);
            return new HrResult(CommonReturnCode.FAILED.getCode(), "系统异常");
        }
    }

    /**
     * 导出月出货实绩数据
     * 
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @param response  HTTP响应
     * @throws Exception
     */
    @PostMapping("/monthlyShipmentPerformance/export")
    public void exportMonthlyShipmentPerformance(String startDate, String endDate, HttpServletResponse response)
            throws Exception {
        try {
            // 导出Excel
            salesReportService.exportMonthlyShipmentPerformance(startDate, endDate, response);
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("导出月出货实绩数据异常：", e);
            throw e;
        }
    }

    /**
     * Cu Base页面
     * 
     * @param model
     * @throws Exception
     * @return
     */
    @GetMapping("/cuBase")
    public String cuBaseView(Model model) throws Exception {
        return "/modules/salesReport/cuBase";
    }

    /**
     * 查询Cu Base数据
     * 
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @throws Exception
     * @return
     */
    @PostMapping("/cuBase/list")
    @ResponseBody
    public Object cuBaseList(String startDate, String endDate) throws Exception {
        try {
            // 验证用户登录状态
            AuthorizingUser authorizingUser = SingletonLoginUtils.getUser();
            if (authorizingUser == null) {
                return new HrResult(CommonReturnCode.FAILED, "登录超时，请重新登录");
            }

            // 打印日志，确认参数是否正确
            logger.info("Cu Base查询参数 - startDate: [" + startDate + "], endDate: [" + endDate + "]");

            // 确保参数为null或有效值（而不是空字符串）
            if (startDate != null && startDate.trim().isEmpty()) {
                startDate = null;
            }
            if (endDate != null && endDate.trim().isEmpty()) {
                endDate = null;
            }

            // 查询数据，无需判断是否为空
            List<CuBaseDTO> dataList = salesReportService.listCuBase(startDate, endDate);

            return new HrPageResult(dataList, dataList.size());
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("查询Cu Base数据异常：", e);
            return new HrResult(CommonReturnCode.FAILED.getCode(), "系统异常");
        }
    }

    /**
     * 导出Cu Base数据
     * 
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @param response  HTTP响应
     * @throws Exception
     */
    @PostMapping("/cuBase/export")
    public void cuBaseExport(String startDate, String endDate, HttpServletResponse response) throws Exception {
        try {
            // 验证用户登录状态
            AuthorizingUser authorizingUser = SingletonLoginUtils.getUser();
            if (authorizingUser == null) {
                return;
            }

            // 打印日志，确认参数是否正确
            logger.info("Cu Base导出参数 - startDate: [" + startDate + "], endDate: [" + endDate + "]");

            // 确保参数为null或有效值（而不是空字符串）
            if (startDate != null && startDate.trim().isEmpty()) {
                startDate = null;
            }
            if (endDate != null && endDate.trim().isEmpty()) {
                endDate = null;
            }

            // 导出数据，无需判断是否为空
            salesReportService.exportCuBase(startDate, endDate, response);
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("导出Cu Base数据异常：", e);
        }
    }

    /**
     * 0-base报表页面
     * 
     * @param model
     * @throws Exception
     * @return
     */
    @GetMapping("/zeroBase")
    public String zeroBaseView(Model model) throws Exception {
        return "/modules/salesReport/zeroBase";
    }

    /**
     * 查询0-base报表数据
     * 
     * @param startAccountingYearMonth 开始会计年月
     * @param endAccountingYearMonth   结束会计年月
     * @throws Exception
     * @return
     */
    @PostMapping("/zeroBase/list")
    @ResponseBody
    public Object zeroBaseList(String startAccountingYearMonth, String endAccountingYearMonth) throws Exception {
        try {
            // 验证用户登录状态
            AuthorizingUser authorizingUser = SingletonLoginUtils.getUser();
            if (authorizingUser == null) {
                return new HrResult(CommonReturnCode.FAILED, "登录超时，请重新登录");
            }

            // 打印日志，确认参数是否正确
            logger.info("0-base报表查询参数 - startAccountingYearMonth: [" + startAccountingYearMonth +
                    "], endAccountingYearMonth: [" + endAccountingYearMonth + "]");

            // 确保参数为null或有效值（而不是空字符串）
            if (startAccountingYearMonth != null && startAccountingYearMonth.trim().isEmpty()) {
                startAccountingYearMonth = null;
            }
            if (endAccountingYearMonth != null && endAccountingYearMonth.trim().isEmpty()) {
                endAccountingYearMonth = null;
            }

            // 查询数据
            List<ZeroBaseReportDTO> dataList = salesReportService.listZeroBaseReport(startAccountingYearMonth,
                    endAccountingYearMonth);

            return new HrPageResult(dataList, dataList.size());
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("查询0-base报表数据异常：", e);
            return new HrResult(CommonReturnCode.FAILED.getCode(), "系统异常");
        }
    }

    /**
     * 导出0-base报表数据
     * 
     * @param startAccountingYearMonth 开始会计年月
     * @param endAccountingYearMonth   结束会计年月
     * @param response                 HTTP响应
     * @throws Exception
     */
    @PostMapping("/zeroBase/export")
    public void zeroBaseExport(String startAccountingYearMonth, String endAccountingYearMonth,
            HttpServletResponse response) throws Exception {
        try {
            // 验证用户登录状态
            AuthorizingUser authorizingUser = SingletonLoginUtils.getUser();
            if (authorizingUser == null) {
                return;
            }

            // 打印日志，确认参数是否正确
            logger.info("0-base报表导出参数 - startAccountingYearMonth: [" + startAccountingYearMonth +
                    "], endAccountingYearMonth: [" + endAccountingYearMonth + "]");

            // 确保参数为null或有效值（而不是空字符串）
            if (startAccountingYearMonth != null && startAccountingYearMonth.trim().isEmpty()) {
                startAccountingYearMonth = null;
            }
            if (endAccountingYearMonth != null && endAccountingYearMonth.trim().isEmpty()) {
                endAccountingYearMonth = null;
            }

            // 导出数据
            salesReportService.exportZeroBaseReport(startAccountingYearMonth, endAccountingYearMonth, response);
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("导出0-base报表数据异常：", e);
            response.setContentType("text/html;charset=utf-8");
            response.getWriter().write("导出失败：" + e.getMessage());
        }
    }

    /**
     * User-Margin报表页面
     * 
     * @param model
     * @throws Exception
     * @return
     */
    @GetMapping("/userMargin")
    public String userMarginView(Model model) throws Exception {
        return "/modules/salesReport/userMargin";
    }

    /**
     * 查询User-Margin报表数据
     * 
     * @param accountingYearMonth 会计年月
     * @throws Exception
     * @return
     */
    @PostMapping("/userMargin/list")
    @ResponseBody
    public Object userMarginList(String accountingYearMonth) throws Exception {
        try {
            // 验证用户登录状态
            AuthorizingUser authorizingUser = SingletonLoginUtils.getUser();
            if (authorizingUser == null) {
                return new HrResult(CommonReturnCode.FAILED, "登录超时，请重新登录");
            }

            // 打印日志，确认参数是否正确
            logger.info("User-Margin报表查询参数 - accountingYearMonth: [" + accountingYearMonth + "]");

            // 确保参数为null或有效值（而不是空字符串）
            if (accountingYearMonth != null && accountingYearMonth.trim().isEmpty()) {
                accountingYearMonth = null;
            }

            // 查询数据
            List<UserMarginDTO> dataList = salesReportService.listUserMargin(accountingYearMonth);

            return new HrPageResult(dataList, dataList.size());
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("查询User-Margin报表数据异常：", e);
            return new HrResult(CommonReturnCode.FAILED.getCode(), "系统异常");
        }
    }

    /**
     * 导出User-Margin报表数据
     * 
     * @param accountingYearMonth 会计年月
     * @param response            HTTP响应
     * @throws Exception
     */
    @PostMapping("/userMargin/export")
    public void userMarginExport(String accountingYearMonth, HttpServletResponse response) throws Exception {
        try {
            // 验证用户登录状态
            AuthorizingUser authorizingUser = SingletonLoginUtils.getUser();
            if (authorizingUser == null) {
                return;
            }

            // 打印日志，确认参数是否正确
            logger.info("User-Margin报表导出参数 - accountingYearMonth: [" + accountingYearMonth + "]");

            // 确保参数为null或有效值（而不是空字符串）
            if (accountingYearMonth != null && accountingYearMonth.trim().isEmpty()) {
                accountingYearMonth = null;
            }

            // 导出数据
            salesReportService.exportUserMargin(accountingYearMonth, response);
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("导出User-Margin报表数据异常：", e);
            response.setContentType("text/html;charset=utf-8");
            response.getWriter().write("导出失败：" + e.getMessage());
        }
    }
}
