package com.hongru.pojo.dto;

import com.hongru.entity.businessOps.AuxiliaryMaterialSummary;
import com.hongru.support.page.PageInfo;

import java.util.List;

public class AuxiliaryMaterialSummaryDTO {

    private PageInfo pageInfo;

    private List<AuxiliaryMaterialSummary> materialSummaryList;

    public AuxiliaryMaterialSummaryDTO(PageInfo pageInfo, List<AuxiliaryMaterialSummary> materialSummaryList) {
        super();
        this.pageInfo = pageInfo;
        this.materialSummaryList = materialSummaryList;
    }

    public PageInfo getPageInfo() {
        return pageInfo;
    }

    public void setPageInfo(PageInfo pageInfo) {
        this.pageInfo = pageInfo;
    }

    public List<AuxiliaryMaterialSummary> getMaterialSummaryList() {
        return materialSummaryList;
    }

    public void setMaterialSummaryList(List<AuxiliaryMaterialSummary> materialSummaryList) {
        this.materialSummaryList = materialSummaryList;
    }
}
