package com.hongru.common.util;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;

/**
 * 日本财年计算工具类
 * 日本财年：4月1日-次年3月31日
 * 规则：以查询开始日期所在财年为准
 */
public class JapaneseFiscalYearUtils {
    
    private static final SimpleDateFormat DATE_FORMAT = new SimpleDateFormat("yyyy-MM-dd");
    private static final SimpleDateFormat YEAR_FORMAT = new SimpleDateFormat("yyyy");
    
    /**
     * 根据日期获取日本财年
     * @param date 日期字符串 (格式: yyyy-MM-dd)
     * @return 财年字符串 (格式: yyyy)
     * @throws ParseException
     */
    public static String getFiscalYear(String date) throws ParseException {
        if (StringUtil.isStringEmpty(date)) {
            return null;
        }
        
        Date inputDate = DATE_FORMAT.parse(date);
        return getFiscalYear(inputDate);
    }
    
    /**
     * 根据日期获取日本财年
     * @param date 日期对象
     * @return 财年字符串 (格式: yyyy)
     */
    public static String getFiscalYear(Date date) {
        if (date == null) {
            return null;
        }
        
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        
        int year = cal.get(Calendar.YEAR);
        int month = cal.get(Calendar.MONTH) + 1; // Calendar.MONTH 从0开始
        
        // 如果是1-3月，属于上一年度的财年
        if (month >= 1 && month <= 3) {
            return String.valueOf(year - 1);
        } else {
            // 如果是4-12月，属于当年的财年
            return String.valueOf(year);
        }
    }
    
    /**
     * 获取财年的开始日期
     * @param fiscalYear 财年 (格式: yyyy)
     * @return 财年开始日期 (格式: yyyy-MM-dd)
     */
    public static String getFiscalYearStartDate(String fiscalYear) {
        if (StringUtil.isStringEmpty(fiscalYear)) {
            return null;
        }
        
        return fiscalYear + "-04-01";
    }
    
    /**
     * 获取财年的结束日期
     * @param fiscalYear 财年 (格式: yyyy)
     * @return 财年结束日期 (格式: yyyy-MM-dd)
     */
    public static String getFiscalYearEndDate(String fiscalYear) {
        if (StringUtil.isStringEmpty(fiscalYear)) {
            return null;
        }
        
        int year = Integer.parseInt(fiscalYear);
        return String.valueOf(year + 1) + "-03-31";
    }
    
    /**
     * 判断两个日期是否在同一财年
     * @param date1 日期1 (格式: yyyy-MM-dd)
     * @param date2 日期2 (格式: yyyy-MM-dd)
     * @return true: 同一财年, false: 不同财年
     * @throws ParseException
     */
    public static boolean isSameFiscalYear(String date1, String date2) throws ParseException {
        String fiscalYear1 = getFiscalYear(date1);
        String fiscalYear2 = getFiscalYear(date2);
        
        return fiscalYear1 != null && fiscalYear1.equals(fiscalYear2);
    }
    
    /**
     * 根据查询开始日期获取财年范围
     * @param startDate 查询开始日期 (格式: yyyy-MM-dd)
     * @return 财年范围数组 [开始日期, 结束日期]
     * @throws ParseException
     */
    public static String[] getFiscalYearRange(String startDate) throws ParseException {
        String fiscalYear = getFiscalYear(startDate);
        if (fiscalYear == null) {
            return null;
        }
        
        String[] range = new String[2];
        range[0] = getFiscalYearStartDate(fiscalYear);
        range[1] = getFiscalYearEndDate(fiscalYear);
        
        return range;
    }
    
    /**
     * 检查日期范围是否跨财年
     * @param startDate 开始日期 (格式: yyyy-MM-dd)
     * @param endDate 结束日期 (格式: yyyy-MM-dd)
     * @return true: 跨财年, false: 不跨财年
     * @throws ParseException
     */
    public static boolean isCrossFiscalYear(String startDate, String endDate) throws ParseException {
        return !isSameFiscalYear(startDate, endDate);
    }
    
    /**
     * 获取当前财年
     * @return 当前财年字符串 (格式: yyyy)
     */
    public static String getCurrentFiscalYear() {
        return getFiscalYear(new Date());
    }
    
    /**
     * 根据年月获取财年
     * @param yearMonth 年月字符串 (格式: yyyy-MM)
     * @return 财年字符串 (格式: yyyy)
     * @throws ParseException
     */
    public static String getFiscalYearByYearMonth(String yearMonth) throws ParseException {
        if (StringUtil.isStringEmpty(yearMonth)) {
            return null;
        }
        
        // 将年月转换为该月的第一天
        String dateStr = yearMonth + "-01";
        return getFiscalYear(dateStr);
    }
    
    /**
     * 格式化财年显示
     * @param fiscalYear 财年 (格式: yyyy)
     * @return 格式化后的财年显示 (格式: yyyy年度)
     */
    public static String formatFiscalYear(String fiscalYear) {
        if (StringUtil.isStringEmpty(fiscalYear)) {
            return null;
        }
        
        return fiscalYear + "年度";
    }
    
    /**
     * 获取财年的年度标识（用于成本表匹配）
     * @param date 日期字符串 (格式: yyyy-MM-dd)
     * @return 年度标识 (格式: yyyy)
     * @throws ParseException
     */
    public static String getFiscalYearForCostMatching(String date) throws ParseException {
        return getFiscalYear(date);
    }
}
