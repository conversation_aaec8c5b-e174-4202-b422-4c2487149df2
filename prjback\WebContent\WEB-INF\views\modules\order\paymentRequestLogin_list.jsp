<%@ page language="java" import="java.util.*"
         contentType="text/html; charset=utf-8"%>
<%@ include file="/WEB-INF/layouts/base.jsp"%>
<html>
<head>
    <title>支付请求登录</title>
    <style>
        /*设置数据表表头字体*/
        .layui-table th .layui-table-cell{
            text-align: center;
        }
    </style>
</head>
<body class="gray-bg">
<div class="ibox float-e-margins">
    <div class="ibox-content">
        <div class="layui-collapse">
            <div class="layui-colla-item">
                <div class="layui-colla-content layui-show">
                    <form id="formSearch" class="layui-form hr-form-search toolbar table-tool-mini" method="post" action="">
                        <div class="layui-form-item">
                            <div class="layui-inline layui-col-md3">
                                <label class="layui-form-label">需求方:</label>
                                <div class="layui-input-block">
                                    <select class="layui-select" id="customerCode" name="customerCode" lay-search="true">
                                        <option value="">请选择</option>
                                        <c:forEach items="${customersList}" var="customers">
                                            <option value="${customers.customerCode}">${customers.customerCode} - ${customers.customerAlias}</option>
                                        </c:forEach>
                                    </select>
                                </div>
                            </div>
                            <div class="layui-inline layui-col-md3">
                                <label class="layui-form-label">支付请求日:</label>
                                <div class="layui-input-block">
                                    <input type="text" name="paymentRequestDate" autocomplete="off" class="layui-input"
                                           id="paymentRequestDate">
                                </div>
                            </div>
                            <div class="layui-inline layui-col-md1 hr-div-btn">
                                <button type="button" id="btn_query" onclick="search();" class="layui-btn layui-bg-blue"><i class="layui-icon layui-icon-search"></i>检索</button>
                            </div>
                            <div class="layui-inline layui-col-md1 hr-div-btn">
                                <button type="button" id="btn_export" onclick="exportExcel();" class="layui-btn layui-bg-green"><i class="layui-icon layui-icon-download-circle"></i>导出</button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        <%--初始化table--%>
        <table class="layui-hide" id="demo" lay-filter="test"></table>
        <!-- 自定义表中工具栏 -->
        <script type="text/html" id="barDemo">
            <a class="layui-btn layui-btn-xs layui-bg-blue" lay-event="detail"><i class="layui-icon layui-icon-search"></i>详情</a>
            <a class="layui-btn layui-btn-xs layui-bg-orange" lay-event="export"><i class="layui-icon layui-icon-download-circle"></i>导出</a>
            {{# if(d.paymentRequestConfirm != '1') { }}
            <a class="layui-btn layui-btn-xs layui-bg-green" lay-event="edit"><i class="layui-icon layui-icon-edit"></i>编辑</a>
            <a class="layui-btn layui-btn-xs layui-bg-red" lay-event="remove"><i class="layui-icon layui-icon-delete"></i>删除</a>
            {{# } }}
        </script>
        <!-- 自定义头部工具栏 -->
        <script type="text/html" id="toolbarDemo">
            <div class="layui-btn-container">
                <button class="layui-btn layui-btn-sm layui-bg-blue" lay-event="toAdd"><i class="layui-icon layui-icon-add-1"></i>新增</button>
                <button class="layui-btn layui-btn-primary layui-btn-sm" lay-event="refresh"><i class="layui-icon layui-icon-refresh"></i>刷新</button>
            </div>
        </script>
    </div>
</div>

<myfooter>
    <script src="${ctxsta}/hongru/js/order/paymentRequestLogin_list.js?v=1.0.23"></script>
</myfooter>
</body>
</html>
