<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hongru.mapper.businessOps.CustomerDetailsMapper">

	<insert id="insertCustomerDetails" parameterType="com.hongru.entity.businessOps.CustomerDetails">
		INSERT INTO [businessOps].[dbo].[客户详情表]
		(
		[状态],
		[客户代码],
		[客户类别代码],
		[客户组代码],
		[分野代码],
		[国家],
		[货币],
		[税率代码],
		[交易条件],
		[付款条件],
		[运输条件],
		[创建人姓名],
		[创建时间]
		)VALUES(
		#{customerDetails.status},
		#{customerDetails.customerCode},
		#{customerDetails.customerTypeCode},
		#{customerDetails.customerGroupCode},
		#{customerDetails.fieldCode},
		#{customerDetails.country},
		#{customerDetails.currency},
		#{customerDetails.taxRateCode},
		#{customerDetails.tradeCondition},
		#{customerDetails.payCondition},
		#{customerDetails.transportCondition},
		#{customerDetails.creatorName},
		#{customerDetails.createdTime}
		)
	</insert>

	<select id="selectCustomerDetailsById" resultType="com.hongru.entity.businessOps.CustomerDetails">
		SELECT a.[流水号] AS id, a.[状态] AS status, a.[客户代码] AS customerCode, b.[客户简称] AS customerAlias,a.[客户类别代码] AS customerTypeCode,
			   c.[客户类别名] AS customerCategory, a.[客户组代码] AS customerGroupCode, a.[分野代码] AS fieldCode, a.[国家] AS country, a.[货币] AS currency,
			   a.[税率代码] AS taxRateCode, a.[交易条件] AS tradeCondition, a.[付款条件] AS payCondition,  a.[运输条件] AS transportCondition,
			   a.[创建人姓名] AS creatorName, a.[创建时间] AS createdTime, a.[更新人姓名] AS updaterName, a.[更新时间] AS updatedTime
		FROM [businessOps].[dbo].[客户详情表] a
		LEFT JOIN [sumitomo].[dbo].[客户表] b ON a.[客户代码] COLLATE Chinese_PRC_CI_AS = b.[客户代码] COLLATE Chinese_PRC_CI_AS
		LEFT JOIN [businessOps].[dbo].[客户分类表] c ON a.[客户类别代码] = c.[客户类别代码]
		<where>
			<if test="id != null">
				AND a.[流水号] = #{id}
			</if>
		</where>
	</select>
	
	<select id="customerDetailsListByPage" resultType="com.hongru.entity.businessOps.CustomerDetails">
		SELECT a.[流水号] AS id, a.[状态] AS status, a.[客户代码] AS customerCode, b.[客户简称] AS customerAlias,a.[客户类别代码] AS customerTypeCode,
			   c.[客户类别名] AS customerCategory, a.[客户组代码] AS customerGroupCode, a.[分野代码] AS fieldCode, a.[国家] AS country, a.[货币] AS currency,
			   a.[税率代码] AS taxRateCode, a.[交易条件] AS tradeCondition, a.[付款条件] AS payCondition,  a.[运输条件] AS transportCondition,
			   a.[创建人姓名] AS creatorName, a.[创建时间] AS createdTime, a.[更新人姓名] AS updaterName, a.[更新时间] AS updatedTime
		FROM [businessOps].[dbo].[客户详情表] a
		LEFT JOIN [sumitomo].[dbo].[客户表] b ON a.[客户代码] COLLATE Chinese_PRC_CI_AS = b.[客户代码] COLLATE Chinese_PRC_CI_AS
		LEFT JOIN [businessOps].[dbo].[客户分类表] c ON a.[客户类别代码] = c.[客户类别代码]
		<where>
			<if test="customerCode != null and customerCode != ''">
				AND a.[客户代码] = #{customerCode}
			</if>
			<if test="status != null and status != ''">
				AND a.[状态] = #{status}
			</if>
		</where>
		ORDER BY a.[客户代码]
		<if test="pageInfo">
			OFFSET ${pageInfo.offset} ROWS
			FETCH NEXT ${pageInfo.limit} ROWS ONLY
		</if>
	</select>
	
	<select id="customerDetailsListByPageCount" resultType="integer">
		SELECT COUNT(1) FROM [businessOps].[dbo].[客户详情表]
		<where>
			<if test="customerCode != null and customerCode != ''">
				AND [客户代码] = #{customerCode}
			</if>
			<if test="status != null and status != ''">
				AND [状态] = #{status}
			</if>
		</where>
	</select>
	
	<update id="updateCustomerDetails">
		UPDATE [businessOps].[dbo].[客户详情表]
		<set>
			<if test="customerDetails.customerTypeCode != null and customerDetails.customerTypeCode != ''">
				[客户类别代码] = #{customerDetails.customerCode},
			</if>
			<if test="customerDetails.customerGroupCode != null and customerDetails.customerGroupCode != ''">
				[客户组代码] = #{customerDetails.customerGroupCode},
			</if>
			<if test="customerDetails.fieldCode != null and customerDetails.fieldCode != ''">
				[分野代码] = #{customerDetails.fieldCode},
			</if>
			<if test="customerDetails.country != null and customerDetails.country != ''">
				[国家] = #{customerDetails.country},
			</if>
			<if test="customerDetails.currency != null and customerDetails.currency != ''">
				[货币] = #{customerDetails.currency},
			</if>
			<if test="customerDetails.taxRateCode != null and customerDetails.taxRateCode != ''">
				[税率代码] = #{customerDetails.taxRateCode},
			</if>
			<if test="customerDetails.tradeCondition != null and customerDetails.tradeCondition != ''">
				[交易条件] = #{customerDetails.tradeCondition},
			</if>
			<if test="customerDetails.payCondition != null and customerDetails.payCondition != ''">
				[付款条件] = #{customerDetails.payCondition},
			</if>
			<if test="customerDetails.transportCondition != null and customerDetails.transportCondition != ''">
				[付款条件] = #{customerDetails.transportCondition},
			</if>
			<if test="customerDetails.updaterName != null and customerDetails.updaterName != ''">
				[更新人姓名] = #{customerDetails.updaterName},
			</if>
			<if test="customerDetails.updatedTime != null">
				[更新时间] = #{customerDetails.updatedTime},
			</if>
		</set>
		WHERE [流水号] = #{customerDetails.id}
	</update>
	
	<update id="updateCustomerDetailsStatus">
		UPDATE [businessOps].[dbo].[客户详情表] SET [状态] = '9' WHERE [流水号] = #{id}
	</update>	
	
</mapper>