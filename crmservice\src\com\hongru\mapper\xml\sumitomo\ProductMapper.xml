<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hongru.mapper.sumitomo.ProductMapper">

    <sql id="product_Sql">
		p.[产品代码] AS productCode,p.[条码] AS barCode,p.[标签尺寸名称] AS labelSizeName
	</sql>
<!--     <sql id="product_Sql"> -->
<!-- 		p.[流水号] AS productId,p.[产品代码] AS productCode,p.[条码] AS barCode,p.[尺寸] AS size,p.[平角线尺寸] AS pjxSize -->
<!-- 		,p.[最大重量] AS maximumWeight,p.[标准重量] AS standardWeight,p.[最小重量] AS minimumWeight,p.[包装物重量] AS weightOfPackage,p.[木托容量] AS woodenPalletCapacity -->
<!-- 		,p.[皮重] AS tare,p.[用户名] AS userName,p.[上行] AS upstream,p.[字体1] AS font1,p.[DOT] AS dot -->
<!-- 		,p.[下口线标记] AS bottomLineMark,p.[下行] AS down,p.[字体2] AS font2,p.[线盘名称] AS wireReelName,p.[标签尺寸名称] AS labelSizeName -->
<!-- 		,p.[木托标签] AS woodenPalletLabel,p.[部品编号] AS partNo,p.[标记] AS mark,p.[量少品重量] AS weightOfLessProduct,p.[量少品重量2] AS weightOfLessProduct2 -->
<!-- 		,p.[量少品数量] AS quantityOfLessProduct,p.[在库量] AS quantityInStock,p.[需求量] AS requirement,p.[FBT] AS fbt,p.[混入率] AS mixingRate -->
<!-- 		,p.[混入率2] AS mixingRate2,p.[汽车客户] AS automobileCustomer,p.[打印屏蔽] AS printShield,p.[包装要求] AS packagingRequirement,p.[包装方式] AS packagingMethod -->
<!-- 		,p.[机型] AS model,p.[备注] AS remark,p.[标签数量] AS numberOfLabel,p.[创建人姓名] AS creatName,p.[创建时间] AS creatTime,p.[最后修改人姓名] AS modifieName,p.[最后修改时间] AS modifieTime -->
<!-- 	</sql> -->

    <select id="getProductListByCustomerCode" resultType="String">
        SELECT DISTINCT([产品代码])
          FROM [sumitomo].[dbo].[产品表]
        <where>
            <if test="customerCode != null and customerCode != ''">
                AND RTRIM([用户名]) = #{customerCode}
            </if>
            <if test="printShield != null and printShield != ''">
                AND [打印屏蔽] = #{printShield}
            </if>
        </where>
        ORDER BY [产品代码]
    </select>

    <select id="getProductInfo" resultType="com.hongru.entity.sumitomo.Product">
        SELECT TOP 1
            [尺寸] AS size,
            [条码] AS barCode,
            [线盘名称] AS wireReelName,
            [标签尺寸名称] AS labelSizeName,
            [部品编号] AS partNo
        FROM 
            [sumitomo].[dbo].[产品表]
        <where>
            <if test="productCode != null and productCode != ''">
                AND [产品代码] = #{productCode}
            </if>
            <if test="printShield != null and printShield != ''">
                AND [打印屏蔽] = #{printShield}
            </if>
        </where>
        ORDER BY [流水号] DESC
    </select>

    <select id="getProductSizeList" resultType="String">
        SELECT DISTINCT([尺寸])
          FROM [sumitomo].[dbo].[产品表]
        <where>
            <if test="customerCode != null and customerCode != ''">
                AND RTRIM([用户名]) = #{customerCode}
            </if>
            <if test="productCode != null and productCode != ''">
                AND [产品代码] = #{productCode}
            </if>
            <if test="printShield != null and printShield != ''">
                AND [打印屏蔽] = #{printShield}
            </if>
        </where>
        ORDER BY [尺寸]
    </select>

    <select id="getProductWireReelNameList" resultType="String">
        SELECT DISTINCT([线盘名称])
          FROM [sumitomo].[dbo].[产品表]
        <where>
            <if test="customerCode != null and customerCode != ''">
                AND RTRIM([用户名]) = #{customerCode}
            </if>
            <if test="productCode != null and productCode != ''">
                AND [产品代码] = #{productCode}
            </if>
            <if test="size != null and size != ''">
                AND [尺寸] = #{size}
            </if>
            <if test="printShield != null and printShield != ''">
                AND [打印屏蔽] = #{printShield}
            </if>
        </where>
        ORDER BY [线盘名称]
    </select>

<!--     <select id="getProductById" resultType="com.hongru.entity.sumitomo.Product"> -->
<!--         SELECT -->
<!--             <include refid="product_Sql"/> -->
<!--         FROM [sumitomo].[dbo].[产品表] p -->
<!--         WHERE [流水号] = #{productId} -->
<!--     </select> -->

<!--     <select id="getProductByProductCode" resultType="com.hongru.entity.sumitomo.Product"> -->
<!--         SELECT TOP 1 -->
<!--             <include refid="product_Sql"/> -->
<!--         FROM [sumitomo].[dbo].[产品表] p -->
<!--         WHERE p.[产品代码] = #{productCode} -->
<!--         ORDER BY p.[流水号] DESC -->
<!--     </select> -->

<!--     <select id="getProductByBarCode" resultType="com.hongru.entity.sumitomo.Product"> -->
<!--         SELECT TOP 1 -->
<!--             <include refid="product_Sql"/> -->
<!--         FROM [sumitomo].[dbo].[产品表] p -->
<!--         WHERE p.[条码] = #{barCode} -->
<!--         ORDER BY p.[流水号] DESC -->
<!--     </select> -->

<!--     <select id="listWireReelNameFromProduct" resultType="string"> -->
<!--         SELECT -->
<!--             DISTINCT([线盘名称]) -->
<!--         FROM [sumitomo].[dbo].[产品表] -->
<!--         <where> -->
<!--             <if test="printShield != null and printShield != ''"> -->
<!--                 AND [打印屏蔽] = #{printShield} -->
<!--             </if> -->
<!--             <if test="userName != null and userName != ''"> -->
<!--                 AND [用户名] = #{userName} -->
<!--             </if> -->
<!--         </where> -->
<!--         ORDER BY [线盘名称] ASC -->
<!--     </select> -->

<!--     <select id="listExclusionProduceCode" resultType="string"> -->
<!--         SELECT -->
<!--             DISTINCT([产品代码]) -->
<!--         FROM [sumitomo].[dbo].[排除代码] -->
<!--         ORDER BY [产品代码] ASC -->
<!--     </select> -->

<!--     <select id="selectMaxBarCodeForProductByParam" resultType="string"> -->
<!--         SELECT -->
<!--         MAX(条码) -->
<!--         FROM [sumitomo].[dbo].[产品表] -->
<!--     </select> -->
    
<!--    <select id="listProductCodeAByParam" resultType="string"> -->
<!--         SELECT -->
<!--             DISTINCT([产品代码A]) -->
<!--         FROM [sumitomo].[dbo].[QC复绕检查品] WHERE [复绕扫描日期] IS NOT NULL -->
<!--         ORDER BY [产品代码A] ASC -->
<!--     </select> -->
    
<!--     <select id="listBatchNoAByParam" resultType="string"> -->
<!--         SELECT -->
<!--             DISTINCT([批号A]) -->
<!--         FROM [sumitomo].[dbo].[QC复绕检查品] WHERE [复绕扫描日期] IS NOT NULL AND [产品代码A] = #{productCodeA} -->
<!--         ORDER BY [批号A] ASC -->
<!--     </select> -->
    
<!--     <select id="listSerialNoAByParam" resultType="string"> -->
<!--         SELECT -->
<!--             DISTINCT([序列号A]) -->
<!--         FROM [sumitomo].[dbo].[QC复绕检查品] -->
<!--         WHERE [复绕扫描日期] IS NOT NULL AND [产品代码A] = #{productCodeA} AND [批号A] = #{batchNoA} -->
<!--         ORDER BY [序列号A] ASC -->
<!--     </select> -->
    
<!--     <select id="listRewindProdByParam" resultType="com.hongru.entity.store.QcRewindCheckProd"> -->
<!--         SELECT RTRIM([产品代码A]) AS productCodeA, RTRIM([条码A]) AS barcodeA, RTRIM([批号A]) AS batchNoA, RTRIM([序列号A]) AS serialNoA, RTRIM([客户代码A]) AS customerCodeA, RTRIM([设备代码]) AS equipmentCode, -->
<!--         			RTRIM([产品代码B]) AS productCodeB, RTRIM([条码B]) AS barcodeB, RTRIM([批号B]) AS batchNoB, RTRIM([序列号B]) AS serialNoB, RTRIM([客户代码B]) AS customerCodeB, RTRIM([设备代码B]) AS equipmentCodeB, RTRIM([复绕扫描日期]) AS rewindScanDate		 -->
<!--         FROM [sumitomo].[dbo].[QC复绕检查品] -->
<!--       <where> -->
<!--             <if test="productCodeA != null and productCodeA != ''"> -->
<!--                 AND [产品代码A] = #{productCodeA}  -->
<!--             </if> -->
<!--             <if test="batchNoA != null and batchNoA != ''"> -->
<!--                 AND [批号A] = #{batchNoA} -->
<!--             </if> -->
<!--             <if test="serialNoA != null and serialNoA != ''"> -->
<!--                 AND [序列号A] = #{serialNoA} -->
<!--             </if> -->
<!--              	AND [复绕扫描日期] IS NOT NULL -->
<!--         </where> -->
<!--     </select> -->
    
<!--     <select id="findByBarCode" resultType="com.hongru.entity.sumitomo.Product"> -->
<!--     SELECT -->
<!--     		<include refid="product_Sql"/> -->
<!--         FROM [sumitomo].[dbo].[产品表] p -->
<!--         WHERE [条码] = #{barcode}  -->
<!--     </select> -->
</mapper>