package com.hongru.common.util;

import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.util.List;

import org.apache.poi.hssf.usermodel.*;
import org.apache.poi.ss.usermodel.*;

import com.hongru.pojo.dto.PaymentRequestExportDTO;
import com.hongru.pojo.dto.SalesAmountExportDTO;

/**
 * 基于模板的Excel导出工具类
 * 使用预定义的Excel模板填充数据
 */
public class TemplateExcelExportUtil {

    /**
     * 导出支付请求书Excel - 基于模板
     * 
     * @param out         输出流
     * @param exportData  导出数据
     * @param companyInfo 公司信息
     * @throws IOException
     */
    public static void exportPaymentRequestExcel(OutputStream out, List<PaymentRequestExportDTO> exportData,
            String companyInfo) throws IOException {

        // 加载模板文件
        InputStream templateStream = null;
        HSSFWorkbook workbook = null;

        try {
            // 尝试从多个路径加载模板
            String[] templatePaths = {
                    "prjback/src/com/hongru/common/exportTemplate/请求书模板.xls",
                    "src/com/hongru/common/exportTemplate/请求书模板.xls",
                    "com/hongru/common/exportTemplate/请求书模板.xls"
            };

            for (String templatePath : templatePaths) {
                try {
                    // 先尝试从类路径加载
                    templateStream = TemplateExcelExportUtil.class.getClassLoader().getResourceAsStream(templatePath);
                    if (templateStream == null) {
                        // 再尝试从文件系统加载
                        templateStream = new FileInputStream(templatePath);
                    }
                    if (templateStream != null) {
                        System.out.println("模板加载成功: " + templatePath);
                        break;
                    }
                } catch (Exception e) {
                    // 继续尝试下一个路径
                }
            }

            if (templateStream == null) {
                throw new IOException("找不到模板文件: 请求书模板.xls");
            }

            // 加载模板工作簿
            workbook = new HSSFWorkbook(templateStream);
            HSSFSheet sheet = workbook.getSheetAt(0);

            // 填充数据
            fillPaymentRequestData(sheet, exportData, companyInfo);

            // 写入输出流
            workbook.write(out);

        } finally {
            if (templateStream != null) {
                try {
                    templateStream.close();
                } catch (IOException e) {
                    // 忽略关闭异常
                }
            }
            if (workbook != null) {
                try {
                    workbook.close();
                } catch (IOException e) {
                    // 忽略关闭异常
                }
            }
        }
    }

    /**
     * 填充支付请求书数据
     */
    private static void fillPaymentRequestData(HSSFSheet sheet, List<PaymentRequestExportDTO> exportData,
            String companyInfo) {
        // 根据更新的模板，第8行（0-based索引7）是唯一的数据行模板
        int templateDataRow = 7; // 第8行是数据行模板
        int insertPosition = 8; // 在第9行位置开始插入新行

        BigDecimal totalQuantity = BigDecimal.ZERO;
        BigDecimal totalAmount = BigDecimal.ZERO;
        BigDecimal totalTax = BigDecimal.ZERO;
        BigDecimal totalWithTax = BigDecimal.ZERO;

        // 如果有多条数据，需要在第8行和第9行之间插入新行
        if (exportData.size() > 1) {
            insertRowsForData(sheet, insertPosition, exportData.size() - 1, templateDataRow);
        }

        // 填充数据行
        for (int i = 0; i < exportData.size(); i++) {
            PaymentRequestExportDTO data = exportData.get(i);
            int rowIndex = templateDataRow + i; // 从模板行开始填充

            HSSFRow row = sheet.getRow(rowIndex);
            if (row == null) {
                row = sheet.createRow(rowIndex);
            }

            // 根据实际模板列顺序填充数据（出货日期在B列）
            setCellValue(row, 1, data.getShipmentDate()); // B列：出货日
            setCellValue(row, 2, data.getArrivalDate()); // C列：到货日
            setCellValue(row, 3, data.getProductCode()); // D列：型号、尺寸
            setCellValue(row, 4, data.getCustomerPartCode()); // E列：客户部品编码（从sumitomo库产品表获取）
            setCellValue(row, 5, data.getCustomerOrderNo()); // F列：客户单号
            setCellValue(row, 6, data.getQuantity()); // G列：数量(kg)
            setCellValue(row, 7, data.getUnitPrice()); // H列：单价(RMB/kg)
            setCellValue(row, 8, data.getAmount()); // I列：金额(RMB)
            setCellValue(row, 9, data.getTax()); // J列：税(RMB)
            // K列：换算后铜单价 - 使用正确的字段
            if (data.getConvertedCopperPrice() != null) {
                setCellValue(row, 10, data.getConvertedCopperPrice());
            } else {
                setCellValue(row, 10, ""); // 显示空白
            }

            // 累计计算
            if (data.getQuantity() != null) {
                totalQuantity = totalQuantity.add(data.getQuantity());
            }
            if (data.getAmount() != null) {
                totalAmount = totalAmount.add(data.getAmount());
            }
            if (data.getTax() != null) {
                totalTax = totalTax.add(data.getTax());
            }
            if (data.getTotalPriceWithTax() != null) {
                totalWithTax = totalWithTax.add(data.getTotalPriceWithTax());
            }
        }

        // 找到合计行（原来的第9行，现在可能位置发生了变化）
        int totalRowIndex = templateDataRow + exportData.size();
        HSSFRow totalRow = sheet.getRow(totalRowIndex);
        if (totalRow != null) {
            // 根据模板填充合计数据
            setCellValue(totalRow, 5, "合计:"); // F列显示"合计:"
            setCellValue(totalRow, 6, totalQuantity); // G列：总数量
            // 注意：H列单价(RMB/kg)和K列换算后铜单价不需要合计值，保持空白
            setCellValue(totalRow, 8, totalAmount); // I列：总金额
            setCellValue(totalRow, 9, totalTax); // J列：总税额
            // K列换算后铜单价不需要合计值，保持空白
            setCellValue(totalRow, 10, ""); // K列合计显示空白
        }

        // 在金额(RMB)合计值的正下方添加新的合计值：合计金额 + 合计税额
        int additionalTotalRowIndex = totalRowIndex + 1;
        HSSFRow additionalTotalRow = sheet.getRow(additionalTotalRowIndex);
        if (additionalTotalRow == null) {
            additionalTotalRow = sheet.createRow(additionalTotalRowIndex);
        }
        BigDecimal totalAmountPlusTax = totalAmount.add(totalTax);
        setCellValue(additionalTotalRow, 8, totalAmountPlusTax); // I列：合计金额+合计税额

        // 填充其他动态数据
        fillDynamicData(sheet, exportData);
    }

    /**
     * 在指定位置插入行来容纳多条数据
     * 
     * @param sheet            Excel工作表
     * @param insertPosition   插入位置（从这个位置开始插入）
     * @param rowsToInsert     要插入的行数
     * @param templateRowIndex 模板行索引（用于复制格式）
     */
    private static void insertRowsForData(HSSFSheet sheet, int insertPosition, int rowsToInsert, int templateRowIndex) {
        // 先将现有行向下移动，为新行腾出空间
        sheet.shiftRows(insertPosition, sheet.getLastRowNum(), rowsToInsert);

        // 获取模板行用于复制格式
        HSSFRow templateRow = sheet.getRow(templateRowIndex);

        // 在腾出的空间中创建新行并复制模板格式
        for (int i = 0; i < rowsToInsert; i++) {
            int newRowIndex = insertPosition + i;
            HSSFRow newRow = sheet.createRow(newRowIndex);

            // 复制模板行的格式
            if (templateRow != null) {
                copyRowFormat(templateRow, newRow);
            }
        }
    }

    /**
     * 复制行格式（高度和单元格样式）
     */
    private static void copyRowFormat(HSSFRow sourceRow, HSSFRow targetRow) {
        // 复制行高
        targetRow.setHeight(sourceRow.getHeight());

        // 复制每个单元格的格式
        for (int i = 0; i < 15; i++) { // A到O列
            HSSFCell sourceCell = sourceRow.getCell(i);
            if (sourceCell != null) {
                HSSFCell targetCell = targetRow.createCell(i);
                // 复制单元格样式
                targetCell.setCellStyle(sourceCell.getCellStyle());
                // 复制单元格类型 - 使用新的API
                targetCell.setCellType(sourceCell.getCellTypeEnum());
            }
        }
    }

    /**
     * 填充动态数据（日期、签字等）
     */
    private static void fillDynamicData(HSSFSheet sheet, List<PaymentRequestExportDTO> exportData) {
        if (exportData.isEmpty()) {
            return;
        }

        PaymentRequestExportDTO firstData = exportData.get(0);

        // 计算动态位置：数据行数量会影响后续内容的位置
        int dataRowCount = exportData.size();
        int templateDataRow = 7; // 第8行是模板数据行
        int baseOffsetAfterData = templateDataRow + dataRowCount + 1; // 合计行后的基础偏移

        // 根据原模板，付款请求日和作成日在合计行下方的固定相对位置
        // 假设在原模板中，付款请求日在第12行（合计行下方2行），作成日在第16行（合计行下方6行）
        int paymentRequestDateRow = baseOffsetAfterData + 2; // 合计行下方2行
        int creationDateRow = baseOffsetAfterData + 6; // 合计行下方6行

        // 1. 填充付款请求日（动态位置）- 向下移动一个单元格
        if (firstData.getPaymentRequestDate() != null && !firstData.getPaymentRequestDate().trim().isEmpty()) {
            String formattedDate = formatDateToChineseStyle(firstData.getPaymentRequestDate());
            // 向下移动一个单元格：第8列（I列）
            setCellValueAt(sheet, paymentRequestDateRow, 8, "付款请求日：" + formattedDate);
        }

        // 2. 填充作成日（动态位置）- 使用导出日期
        if (firstData.getExportDate() != null && !firstData.getExportDate().trim().isEmpty()) {
            String formattedExportDate = formatDateToChineseStyle(firstData.getExportDate());
            // 向下移动一个单元格：第8列（I列）
            setCellValueAt(sheet, creationDateRow + 1, 8, "作成日：" + formattedExportDate);
        }

        // 3. 填充客户信息（这些在数据行上方，位置固定）
        // 使用全称优先，如果没有全称则使用简称
        String contractorName = firstData.getContractorFullName() != null ? firstData.getContractorFullName()
                : firstData.getCustomerAlias();
        String customerName = firstData.getCustomerFullName() != null ? firstData.getCustomerFullName()
                : firstData.getCustomerAlias();

        if (contractorName != null) {
            setCellValueAt(sheet, 4, 1, "签约方：" + contractorName);
        }
        if (customerName != null) {
            setCellValueAt(sheet, 5, 1, "需求方：" + customerName);
        }

        // 4. 其他可能需要动态调整位置的内容
        // 例如：签字区域等，都需要根据数据行数进行相对位置调整
    }

    /**
     * 在指定位置设置单元格值
     */
    private static void setCellValueAt(HSSFSheet sheet, int rowIndex, int columnIndex, String value) {
        HSSFRow row = sheet.getRow(rowIndex);
        if (row == null) {
            row = sheet.createRow(rowIndex);
        }

        HSSFCell cell = row.getCell(columnIndex);
        if (cell == null) {
            cell = row.createCell(columnIndex);
        }

        cell.setCellValue(value);
    }

    /**
     * 设置单元格值的通用方法
     */
    private static void setCellValue(HSSFRow row, int columnIndex, Object value) {
        HSSFCell cell = row.getCell(columnIndex);
        if (cell == null) {
            cell = row.createCell(columnIndex);
        }

        if (value == null) {
            cell.setCellValue("");
        } else if (value instanceof String) {
            cell.setCellValue((String) value);
        } else if (value instanceof BigDecimal) {
            cell.setCellValue(((BigDecimal) value).doubleValue());
        } else if (value instanceof Number) {
            cell.setCellValue(((Number) value).doubleValue());
        } else {
            cell.setCellValue(value.toString());
        }
    }

    /**
     * 导出销售额Excel - 基于模板
     *
     * @param out         输出流
     * @param exportData  导出数据
     * @param companyInfo 公司信息
     * @throws IOException
     */
    public static void exportSalesAmountExcel(OutputStream out, List<SalesAmountExportDTO> exportData,
            String companyInfo) throws IOException {

        InputStream templateStream = null;
        HSSFWorkbook workbook = null;

        try {
            // 尝试加载销售额模板文件，如果没有则使用支付请求模板
            String[] templatePaths = {
                    "prjback/src/com/hongru/common/exportTemplate/销售额模板.xls",
                    "src/com/hongru/common/exportTemplate/销售额模板.xls",
                    "com/hongru/common/exportTemplate/销售额模板.xls",
                    "prjback/src/com/hongru/common/exportTemplate/请求书模板.xls",
                    "src/com/hongru/common/exportTemplate/请求书模板.xls",
                    "com/hongru/common/exportTemplate/请求书模板.xls"
            };

            for (String templatePath : templatePaths) {
                try {
                    // 先尝试从类路径加载
                    templateStream = TemplateExcelExportUtil.class.getClassLoader().getResourceAsStream(templatePath);
                    if (templateStream == null) {
                        // 再尝试从文件系统加载
                        templateStream = new FileInputStream(templatePath);
                    }
                    if (templateStream != null) {
                        System.out.println("销售额模板加载成功: " + templatePath);
                        break;
                    }
                } catch (Exception e) {
                    // 继续尝试下一个路径
                }
            }

            if (templateStream == null) {
                throw new IOException("找不到模板文件: 销售额模板.xls 或 请求书模板.xls");
            }

            // 加载模板工作簿
            workbook = new HSSFWorkbook(templateStream);
            HSSFSheet sheet = workbook.getSheetAt(0);

            // 填充销售额数据
            fillSalesAmountData(sheet, exportData, companyInfo);

            // 写入输出流
            workbook.write(out);

        } finally {
            if (templateStream != null) {
                try {
                    templateStream.close();
                } catch (IOException e) {
                    // 忽略关闭异常
                }
            }
            if (workbook != null) {
                try {
                    workbook.close();
                } catch (IOException e) {
                    // 忽略关闭异常
                }
            }
        }
    }

    /**
     * 填充销售额数据
     */
    private static void fillSalesAmountData(HSSFSheet sheet, List<SalesAmountExportDTO> exportData,
            String companyInfo) {
        // 根据模板，第8行（0-based索引7）是唯一的数据行模板
        int templateDataRow = 7; // 第8行是数据行模板
        int insertPosition = 8; // 在第9行位置开始插入新行

        BigDecimal totalQuantity = BigDecimal.ZERO;
        BigDecimal totalAmount = BigDecimal.ZERO;
        BigDecimal totalTax = BigDecimal.ZERO;
        // totalCost不再需要，因为L列不显示任何数据

        // 如果有多条数据，需要在第8行和第9行之间插入新行
        if (exportData.size() > 1) {
            insertRowsForData(sheet, insertPosition, exportData.size() - 1, templateDataRow);
        }

        // 填充数据行
        for (int i = 0; i < exportData.size(); i++) {
            SalesAmountExportDTO data = exportData.get(i);
            int rowIndex = templateDataRow + i; // 从模板行开始填充

            HSSFRow row = sheet.getRow(rowIndex);
            if (row == null) {
                row = sheet.createRow(rowIndex);
            }

            // 根据销售额模板列顺序填充数据
            setCellValue(row, 1, data.getShipmentDate()); // B列：出货日
            setCellValue(row, 2, data.getArrivalDate()); // C列：到货日
            setCellValue(row, 3, data.getProductCode()); // D列：型号、尺寸
            setCellValue(row, 4, data.getCustomerPartCode()); // E列：客户部品编码（从sumitomo库产品表获取）
            setCellValue(row, 5, data.getCustomerOrderNo()); // F列：客户单号
            setCellValue(row, 6, data.getQuantity()); // G列：数量(kg)
            setCellValue(row, 7, data.getUnitPrice()); // H列：单价(RMB/kg)
            setCellValue(row, 8, data.getAmount()); // I列：金额(RMB)
            setCellValue(row, 9, data.getTax()); // J列：税(RMB)
            // K列：换算后铜单价 - 使用正确的字段
            if (data.getConvertedCopperPrice() != null) {
                setCellValue(row, 10, data.getConvertedCopperPrice());
            } else {
                setCellValue(row, 10, ""); // 显示空白
            }
            // L列：什么也不要，始终显示空白
            setCellValue(row, 11, "");

            // 累计计算
            if (data.getQuantity() != null) {
                totalQuantity = totalQuantity.add(data.getQuantity());
            }
            if (data.getAmount() != null) {
                totalAmount = totalAmount.add(data.getAmount());
            }
            if (data.getTax() != null) {
                totalTax = totalTax.add(data.getTax());
            }
            // 不再累计totalCost，因为L列不显示任何数据
        }

        // 找到合计行（原来的第9行，现在可能位置发生了变化）
        int totalRowIndex = templateDataRow + exportData.size();
        HSSFRow totalRow = sheet.getRow(totalRowIndex);
        if (totalRow != null) {
            // 根据销售额模板填充合计数据
            setCellValue(totalRow, 5, "合计:"); // F列显示"合计:"
            setCellValue(totalRow, 6, totalQuantity); // G列：总数量
            // 注意：H列单价(RMB/kg)不需要合计值，保持空白
            setCellValue(totalRow, 8, totalAmount); // I列：总金额
            setCellValue(totalRow, 9, totalTax); // J列：总税额
            // K列：换算后铜单价 - 单价性质，不需要合计值，保持空白
            setCellValue(totalRow, 10, ""); // K列合计显示空白
            // L列：什么也不要，始终显示空白
            setCellValue(totalRow, 11, ""); // L列合计显示空白
        }

        // 在金额(RMB)合计值的正下方添加新的合计值：合计金额 + 合计税额
        int additionalTotalRowIndex = totalRowIndex + 1;
        HSSFRow additionalTotalRow = sheet.getRow(additionalTotalRowIndex);
        if (additionalTotalRow == null) {
            additionalTotalRow = sheet.createRow(additionalTotalRowIndex);
        }
        java.math.BigDecimal totalAmountPlusTax = totalAmount.add(totalTax);
        setCellValue(additionalTotalRow, 8, totalAmountPlusTax); // I列：合计金额+合计税额

        // 填充其他动态数据
        fillSalesAmountDynamicData(sheet, exportData);
    }

    /**
     * 填充销售额动态数据（日期、签字等）
     */
    private static void fillSalesAmountDynamicData(HSSFSheet sheet, List<SalesAmountExportDTO> exportData) {
        if (exportData.isEmpty()) {
            return;
        }

        SalesAmountExportDTO firstData = exportData.get(0);

        // 计算动态位置：数据行数量会影响后续内容的位置
        int dataRowCount = exportData.size();
        int templateDataRow = 7; // 第8行是模板数据行
        int baseOffsetAfterData = templateDataRow + dataRowCount + 1; // 合计行后的基础偏移

        // 根据原模板，销售额登录日和作成日在合计行下方的固定相对位置
        int salesAmountDateRow = baseOffsetAfterData + 2; // 合计行下方2行
        int creationDateRow = baseOffsetAfterData + 6; // 合计行下方6行

        // 1. 填充销售额日（动态位置）- 向下移动一个单元格
        if (firstData.getSalesAmountDate() != null) {
            setCellValueAt(sheet, salesAmountDateRow, 8,
                    "销售额日：" + formatDateToChineseStyle(firstData.getSalesAmountDate()));
        }

        // 2. 填充作成日（动态位置）- 使用导出日期
        if (firstData.getExportDate() != null && !firstData.getExportDate().trim().isEmpty()) {
            String formattedExportDate = formatDateToChineseStyle(firstData.getExportDate());
            setCellValueAt(sheet, creationDateRow + 1, 8, "作成日：" + formattedExportDate);
        } else {
            setCellValueAt(sheet, creationDateRow + 1, 8, "作成日：");
        }

        // 3. 设置E5标题为"销售请求书"
        setCellValueAt(sheet, 4, 4, "销售请求书"); // E5位置（0-based: row=4, col=4）

        // 4. 填充客户信息（这些在数据行上方，位置固定）
        // 使用全称优先，如果没有全称则使用简称
        String contractorName = firstData.getContractorFullName() != null ? firstData.getContractorFullName()
                : firstData.getCustomerAlias();
        String customerName = firstData.getCustomerFullName() != null ? firstData.getCustomerFullName()
                : firstData.getCustomerAlias();

        if (contractorName != null) {
            setCellValueAt(sheet, 4, 1, "签约方：" + contractorName);
        }
        if (customerName != null) {
            setCellValueAt(sheet, 5, 1, "需求方：" + customerName);
        }

        // 4. 其他可能需要动态调整位置的内容
        // 例如：签字区域等，都需要根据数据行数进行相对位置调整
    }

    /**
     * 将日期字符串格式化为中文格式：2025年07月30日
     */
    private static String formatDateToChineseStyle(String dateStr) {
        if (dateStr == null || dateStr.trim().isEmpty()) {
            return "";
        }

        try {
            // 尝试解析不同的日期格式
            String result = dateStr;

            // 如果是 yyyy-MM-dd 格式
            if (dateStr.matches("\\d{4}-\\d{2}-\\d{2}")) {
                String[] parts = dateStr.split("-");
                result = parts[0] + "年" + parts[1] + "月" + parts[2] + "日";
            }
            // 如果是 yyyy-MM-dd HH:mm:ss 格式，只取日期部分
            else if (dateStr.matches("\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}:\\d{2}")) {
                String datePart = dateStr.substring(0, 10);
                String[] parts = datePart.split("-");
                result = parts[0] + "年" + parts[1] + "月" + parts[2] + "日";
            }
            // 如果是 yyyy-MM-dd HH:mm:ss.SSS 格式，只取日期部分
            else if (dateStr.matches("\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}:\\d{2}\\.\\d{3}")) {
                String datePart = dateStr.substring(0, 10);
                String[] parts = datePart.split("-");
                result = parts[0] + "年" + parts[1] + "月" + parts[2] + "日";
            }
            // 如果是 yyyy/MM/dd 格式
            else if (dateStr.matches("\\d{4}/\\d{2}/\\d{2}")) {
                String[] parts = dateStr.split("/");
                result = parts[0] + "年" + parts[1] + "月" + parts[2] + "日";
            }
            // 如果是 yyyy/MM/dd HH:mm:ss 格式，只取日期部分
            else if (dateStr.matches("\\d{4}/\\d{2}/\\d{2} \\d{2}:\\d{2}:\\d{2}")) {
                String datePart = dateStr.substring(0, 10);
                String[] parts = datePart.split("/");
                result = parts[0] + "年" + parts[1] + "月" + parts[2] + "日";
            }
            // 如果是 yyyy-M-d 格式（单数字月日）
            else if (dateStr.matches("\\d{4}-\\d{1,2}-\\d{1,2}")) {
                String[] parts = dateStr.split("-");
                String year = parts[0];
                String month = parts[1].length() == 1 ? "0" + parts[1] : parts[1];
                String day = parts[2].length() == 1 ? "0" + parts[2] : parts[2];
                result = year + "年" + month + "月" + day + "日";
            }
            // 如果是时间戳格式，尝试提取日期部分
            else if (dateStr.contains("-") && dateStr.contains(":")) {
                // 通用处理：提取日期部分
                String datePart = dateStr.split(" ")[0];
                if (datePart.matches("\\d{4}-\\d{1,2}-\\d{1,2}")) {
                    String[] parts = datePart.split("-");
                    String year = parts[0];
                    String month = parts[1].length() == 1 ? "0" + parts[1] : parts[1];
                    String day = parts[2].length() == 1 ? "0" + parts[2] : parts[2];
                    result = year + "年" + month + "月" + day + "日";
                }
            }

            return result;
        } catch (Exception e) {
            // 如果格式化失败，返回原字符串
            return dateStr;
        }
    }
}
