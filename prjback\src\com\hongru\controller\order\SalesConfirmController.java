package com.hongru.controller.order;

import com.hongru.base.BaseController;
import com.hongru.common.result.HrPageResult;
import com.hongru.common.result.HrResult;
import com.hongru.common.security.AuthorizingUser;
import com.hongru.common.util.SingletonLoginUtils;
import com.hongru.common.util.StringUtil;
import com.hongru.constant.CommonReturnCode;
import com.hongru.entity.businessOps.PaymentAmount;
import com.hongru.entity.businessOps.PaymentAmountDetail;
import com.hongru.entity.sumitomo.Customers;
import com.hongru.pojo.dto.SalesConfirmListDTO;
import com.hongru.pojo.vo.UserVO;
import com.hongru.service.admin.IUserService;
import com.hongru.service.businessOps.ISalesConfirmService;
import com.hongru.service.sumitomo.ISumitomoService;
import com.hongru.support.page.PageInfo;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Controller
@RequestMapping(value = "/order/salesConfirm")
public class SalesConfirmController extends BaseController {

    private static final Logger logger = LoggerFactory.getLogger(SalesConfirmController.class);

    @Autowired
    private IUserService userService;

    @Autowired
    private ISumitomoService sumitomoService;

    @Autowired
    private ISalesConfirmService salesConfirmService;

    /**
     * 销售额确认列表页面
     * 
     * @param model
     * @throws Exception
     * @return
     */
    @GetMapping("/list/view")
    public String listView(Model model) throws Exception {
        // 获取客户列表
        List<Customers> customersList = sumitomoService.listCustomersList();
        model.addAttribute("customersList", customersList);
        return "/modules/order/salesConfirm_list";
    }

    /**
     * 销售额确认列表数据
     *
     * @param page               页码
     * @param limit              每页数量
     * @param customerCode       客户代码
     * @param paymentRequestDate 支付请求日
     * @throws Exception
     * @return
     */
    @PostMapping("/list")
    @ResponseBody
    public Object list(Integer page, Integer limit, String customerCode, String paymentRequestDate) throws Exception {
        try {
            // 验证用户登录状态
            AuthorizingUser authorizingUser = SingletonLoginUtils.getUser();
            if (authorizingUser == null) {
                return new HrResult(CommonReturnCode.FAILED, "登录超时，请重新登录");
            }

            PageInfo pageInfo = new PageInfo(limit, page);
            List<SalesConfirmListDTO> salesConfirmList = salesConfirmService.listSalesConfirmByPage(pageInfo,
                    customerCode, paymentRequestDate);
            Integer totalCount = salesConfirmService.listSalesConfirmByPageCount(customerCode, paymentRequestDate);

            return new HrPageResult(salesConfirmList, totalCount);
        } catch (Exception e) {
            logger.error("查询销售额确认列表失败", e);
            return new HrResult(CommonReturnCode.FAILED, "查询失败：" + e.getMessage());
        }
    }

    /**
     * 销售额确认详情页面
     *
     * @param model
     * @param paymentRequestNo 支付请求NO
     * @throws Exception
     * @return
     */
    @GetMapping("/detail/view")
    public String detailView(Model model, String paymentRequestNo) throws Exception {
        model.addAttribute("paymentRequestNo", paymentRequestNo);
        return "/modules/order/salesConfirm_detail";
    }

    /**
     * 根据支付请求NO获取详情数据
     *
     * @param paymentRequestNo 支付请求NO
     * @throws Exception
     * @return
     */
    @PostMapping("/detail")
    @ResponseBody
    public Object detail(String paymentRequestNo) throws Exception {
        try {
            // 验证用户登录状态
            AuthorizingUser authorizingUser = SingletonLoginUtils.getUser();
            if (authorizingUser == null) {
                return new HrResult(CommonReturnCode.FAILED, "登录超时，请重新登录");
            }

            if (StringUtil.isStringEmpty(paymentRequestNo)) {
                return new HrResult(CommonReturnCode.FAILED, "支付请求NO不能为空");
            }

            // 获取支付额主表数据
            PaymentAmount paymentAmount = salesConfirmService.getPaymentAmountByNo(paymentRequestNo);
            if (paymentAmount == null) {
                return new HrResult(CommonReturnCode.FAILED, "未找到支付请求数据");
            }

            // 获取支付额明细数据
            List<PaymentAmountDetail> paymentAmountDetails = salesConfirmService
                    .getPaymentAmountDetailsByNo(paymentRequestNo);

            Map<String, Object> result = new HashMap<>();
            result.put("paymentAmount", paymentAmount);
            result.put("paymentAmountDetails", paymentAmountDetails);

            return new HrResult(CommonReturnCode.SUCCESS, result);
        } catch (Exception e) {
            logger.error("获取销售额确认详情失败", e);
            return new HrResult(CommonReturnCode.FAILED, "获取详情失败：" + e.getMessage());
        }
    }

    /**
     * 确认销售额
     *
     * @param paymentRequestNo 支付请求NO
     * @throws Exception
     * @return
     */
    @PostMapping("/confirm")
    @ResponseBody
    public Object confirm(String paymentRequestNo) throws Exception {
        try {
            // 验证用户登录状态
            AuthorizingUser authorizingUser = SingletonLoginUtils.getUser();
            if (authorizingUser == null) {
                return new HrResult(CommonReturnCode.FAILED, "登录超时，请重新登录");
            }

            UserVO userVO = userService.getById(authorizingUser.getUserId());
            if (userVO == null) {
                return new HrResult(CommonReturnCode.FAILED, "用户信息不存在");
            }

            if (StringUtil.isStringEmpty(paymentRequestNo)) {
                return new HrResult(CommonReturnCode.FAILED, "支付请求NO不能为空");
            }

            // 确认销售额
            int result = salesConfirmService.confirmSalesAmount(paymentRequestNo, userVO.getUserName());

            if (result > 0) {
                return new HrResult(CommonReturnCode.SUCCESS, "确认成功");
            } else {
                return new HrResult(CommonReturnCode.FAILED, "确认失败");
            }
        } catch (Exception e) {
            logger.error("确认销售额失败", e);
            return new HrResult(CommonReturnCode.FAILED, "确认失败：" + e.getMessage());
        }
    }

    /**
     * 取消确认销售额
     *
     * @param paymentRequestNo 支付请求NO
     * @throws Exception
     * @return
     */
    @PostMapping("/cancelConfirm")
    @ResponseBody
    public Object cancelConfirm(String paymentRequestNo) throws Exception {
        try {
            // 验证用户登录状态
            AuthorizingUser authorizingUser = SingletonLoginUtils.getUser();
            if (authorizingUser == null) {
                return new HrResult(CommonReturnCode.FAILED, "登录超时，请重新登录");
            }

            UserVO userVO = userService.getById(authorizingUser.getUserId());
            if (userVO == null) {
                return new HrResult(CommonReturnCode.FAILED, "用户信息不存在");
            }

            if (StringUtil.isStringEmpty(paymentRequestNo)) {
                return new HrResult(CommonReturnCode.FAILED, "支付请求NO不能为空");
            }

            // 取消确认销售额
            int result = salesConfirmService.cancelConfirmSalesAmount(paymentRequestNo, userVO.getUserName());

            if (result > 0) {
                return new HrResult(CommonReturnCode.SUCCESS, "取消确认成功");
            } else {
                return new HrResult(CommonReturnCode.FAILED, "取消确认失败");
            }
        } catch (Exception e) {
            logger.error("取消确认销售额失败", e);
            return new HrResult(CommonReturnCode.FAILED, "取消确认失败：" + e.getMessage());
        }
    }
}
