package com.hongru.pojo.dto;

import com.hongru.entity.businessOps.HumanHourCost;
import com.hongru.support.page.PageInfo;

import java.util.List;

public class HumanHourCostDTO {

    private PageInfo pageInfo;

    private List<HumanHourCost> humanHourCostList;

    public HumanHourCostDTO(PageInfo pageInfo, List<HumanHourCost> humanHourCostList) {
        super();
        this.pageInfo = pageInfo;
        this.humanHourCostList = humanHourCostList;
    }

    public PageInfo getPageInfo() {
        return pageInfo;
    }

    public void setPageInfo(PageInfo pageInfo) {
        this.pageInfo = pageInfo;
    }

    public List<HumanHourCost> getHumanHourCostList() {
        return humanHourCostList;
    }

    public void setHumanHourCostList(List<HumanHourCost> humanHourCostList) {
        this.humanHourCostList = humanHourCostList;
    }
}
