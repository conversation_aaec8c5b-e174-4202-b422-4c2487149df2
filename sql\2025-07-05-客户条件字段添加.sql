-- 为销售额表和支付额表添加客户相关条件字段
-- 需求: 共通字段对应第五点 - 客户相关条件字段

USE [BusinessOps]
GO

PRINT '开始为销售额表添加客户相关条件字段...';

-- 为销售额表添加字段
IF NOT EXISTS (SELECT 1 FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[销售额表]') AND name = N'交易条件')
    ALTER TABLE [dbo].[销售额表] ADD [交易条件] varchar(10) COLLATE SQL_Latin1_General_CP1_CI_AS NULL;

IF NOT EXISTS (SELECT 1 FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[销售额表]') AND name = N'付款条件')
    ALTER TABLE [dbo].[销售额表] ADD [付款条件] varchar(10) COLLATE SQL_Latin1_General_CP1_CI_AS NULL;

IF NOT EXISTS (SELECT 1 FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[销售额表]') AND name = N'运输条件')
    ALTER TABLE [dbo].[销售额表] ADD [运输条件] char(3) COLLATE SQL_Latin1_General_CP1_CI_AS NULL;

PRINT '销售额表字段添加完成';

PRINT '开始检查支付额表字段...';

-- 检查支付额表是否已有这些字段（根据您提供的表结构，支付额表已经有这些字段）
IF EXISTS (SELECT 1 FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[支付额表]') AND name = N'交易条件')
    PRINT '支付额表已存在交易条件字段';
ELSE
    PRINT '支付额表缺少交易条件字段';

IF EXISTS (SELECT 1 FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[支付额表]') AND name = N'付款条件')
    PRINT '支付额表已存在付款条件字段';
ELSE
    PRINT '支付额表缺少付款条件字段';

IF EXISTS (SELECT 1 FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[支付额表]') AND name = N'运输条件')
    PRINT '支付额表已存在运输条件字段';
ELSE
    PRINT '支付额表缺少运输条件字段';

PRINT '客户相关条件字段添加脚本执行完成';
