<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hongru.mapper.businessOps.PaymentAmountMapper">
  <sql id="paymentAmount_sql">
    pa.[支付额ID] AS id, pa.[支付请求NO] AS paymentRequestNo, pa.[据点] AS stronghold,
    pa.[支付请求日] AS paymentRequestDate, pa.[支付日期] AS paymentDate, pa.[支付金额] AS paymentAmount,
    pa.[签约方代码] AS contractorCode, pa.[客户代码] AS customerCode,
    ISNULL(c.[客户简称], pa.[客户简称]) AS customerAlias, pa.[交易条件] AS tradeCondition, pa.[付款条件] AS paymentCondition,
    pa.[运输条件] AS transportCondition, pa.[结算货币] AS settlementCurrency, pa.[税代码] AS taxCode,
    ISNULL(CAST(t.[税率] AS NVARCHAR(50)), pa.[税代码]) AS taxRate,
    pa.[支付请求确认] AS paymentRequestConfirm,
    pa.[创建人] AS creatorName, pa.[创建时间] AS createdTime,
    pa.[更新人] AS updaterName, pa.[更新时间] AS updatedTime,
    pa.[确认者] AS confirmer, pa.[确认时间] AS confirmTime
  </sql>

  <!-- 分页查询支付请求登录列表 -->
  <select id="listPaymentRequestLoginByPage" resultType="com.hongru.pojo.dto.PaymentRequestLoginListDTO">
    SELECT
      a.[支付额ID] AS id,
      a.[支付请求NO] AS paymentRequestNo,
      a.[支付请求日] AS paymentRequestDate,
      a.[客户代码] AS customerCode,
      ISNULL(c.[客户简称], a.[客户简称]) AS customerAlias,
      a.[结算货币] AS currency,
      ISNULL(a.[支付金额], 0) AS requestAmount,
      a.[税代码] AS taxCode,
      ISNULL(CAST(d.[税率] AS NVARCHAR(50)), a.[税代码]) AS taxRate,
      a.[创建人] AS creatorName,
      a.[创建时间] AS createdTime,
      a.[更新人] AS updaterName,
      a.[更新时间] AS updatedTime,
      a.[支付请求确认] AS paymentRequestConfirm
    FROM [支付额表] a
    LEFT JOIN [sumitomo].[dbo].[客户表] c ON a.[客户代码] COLLATE Chinese_PRC_CI_AS = c.[客户代码] COLLATE Chinese_PRC_CI_AS
    LEFT JOIN [businessOps].[dbo].[税率计算表] d ON CAST(a.[税代码] AS INT) = d.[税率代码]
    <where>
      <if test="customerCode != null and customerCode != ''">
        AND a.[客户代码] = #{customerCode}
      </if>
      <if test="paymentRequestDate != null and paymentRequestDate != ''">
        AND a.[支付请求日] = #{paymentRequestDate}
      </if>
    </where>
    ORDER BY a.[支付请求日] DESC, a.[支付请求NO] DESC
    <if test="pageInfo != null">
      OFFSET #{pageInfo.offset} ROWS FETCH NEXT #{pageInfo.limit} ROWS ONLY
    </if>
  </select>

  <!-- 查询支付请求登录列表总数 -->
  <select id="listPaymentRequestLoginByPageCount" resultType="java.lang.Integer">
    SELECT COUNT(*)
    FROM [支付额表] a
    <where>
      <if test="customerCode != null and customerCode != ''">
        AND a.[客户代码] = #{customerCode}
      </if>
      <if test="paymentRequestDate != null and paymentRequestDate != ''">
        AND a.[支付请求日] = #{paymentRequestDate}
      </if>
    </where>
  </select>

  <!-- 分页查询支付请求登录列表（按用户过滤） -->
  <select id="listPaymentRequestLoginByPageWithUser" resultType="com.hongru.pojo.dto.PaymentRequestLoginListDTO">
    SELECT
      a.[支付额ID] AS id,
      a.[支付请求NO] AS paymentRequestNo,
      a.[支付请求日] AS paymentRequestDate,
      a.[客户代码] AS customerCode,
      ISNULL(c.[客户简称], a.[客户简称]) AS customerAlias,
      a.[结算货币] AS currency,
      ISNULL(a.[支付金额], 0) AS requestAmount,
      a.[税代码] AS taxCode,
      ISNULL(CAST(d.[税率] AS NVARCHAR(50)), a.[税代码]) AS taxRate,
      a.[创建人] AS creatorName,
      a.[创建时间] AS createdTime,
      a.[更新人] AS updaterName,
      a.[更新时间] AS updatedTime,
      a.[支付请求确认] AS paymentRequestConfirm
    FROM [支付额表] a
    LEFT JOIN [sumitomo].[dbo].[客户表] c ON a.[客户代码] COLLATE Chinese_PRC_CI_AS = c.[客户代码] COLLATE Chinese_PRC_CI_AS
    LEFT JOIN [businessOps].[dbo].[税率计算表] d ON CAST(a.[税代码] AS INT) = d.[税率代码]
    <where>
      <if test="customerCode != null and customerCode != ''">
        AND a.[客户代码] = #{customerCode}
      </if>
      <if test="paymentRequestDate != null and paymentRequestDate != ''">
        AND a.[支付请求日] = #{paymentRequestDate}
      </if>
      <if test="creatorName != null and creatorName != ''">
        AND a.[创建人] = #{creatorName}
      </if>
    </where>
    ORDER BY a.[支付请求日] DESC, a.[支付请求NO] DESC
    <if test="pageInfo != null">
      OFFSET #{pageInfo.offset} ROWS FETCH NEXT #{pageInfo.limit} ROWS ONLY
    </if>
  </select>

  <!-- 查询支付请求登录列表总数（按用户过滤） -->
  <select id="listPaymentRequestLoginByPageCountWithUser" resultType="java.lang.Integer">
    SELECT COUNT(*)
    FROM [支付额表] a
    <where>
      <if test="customerCode != null and customerCode != ''">
        AND a.[客户代码] = #{customerCode}
      </if>
      <if test="paymentRequestDate != null and paymentRequestDate != ''">
        AND a.[支付请求日] = #{paymentRequestDate}
      </if>
      <if test="creatorName != null and creatorName != ''">
        AND a.[创建人] = #{creatorName}
      </if>
    </where>
  </select>

  <!-- 根据支付请求NO获取支付额数据 -->
  <select id="selectByPaymentRequestNo" resultType="com.hongru.entity.businessOps.PaymentAmount">
    SELECT <include refid="paymentAmount_sql"/>
    FROM [支付额表] pa
    LEFT JOIN [sumitomo].[dbo].[客户表] c ON pa.[客户代码] COLLATE Chinese_PRC_CI_AS = c.[客户代码] COLLATE Chinese_PRC_CI_AS
    LEFT JOIN [businessOps].[dbo].[税率计算表] t ON CAST(pa.[税代码] AS INT) = t.[税率代码]
    WHERE pa.[支付请求NO] = #{paymentRequestNo}
  </select>

  <!-- 根据支付请求NO删除支付额数据 -->
  <delete id="deleteByPaymentRequestNo">
    DELETE FROM [支付额表] WHERE [支付请求NO] = #{paymentRequestNo}
  </delete>

  <!-- 插入支付额数据 -->
  <insert id="insert" parameterType="com.hongru.entity.businessOps.PaymentAmount">
    INSERT INTO [支付额表] (
      [据点], [支付请求NO], [支付请求日], [支付日期], [支付金额], [签约方代码], [客户代码], [客户简称],
      [交易条件], [付款条件], [运输条件], [结算货币], [税代码], [支付请求确认],
      [创建人], [创建时间], [更新人], [更新时间]
    ) VALUES (
      #{stronghold}, #{paymentRequestNo}, #{paymentRequestDate}, #{paymentDate}, #{paymentAmount}, #{contractorCode}, #{customerCode}, #{customerAlias},
      #{tradeCondition}, #{paymentCondition}, #{transportCondition}, #{settlementCurrency}, #{taxCode}, #{paymentRequestConfirm},
      #{creatorName}, #{createdTime}, #{updaterName}, #{updatedTime}
    )
  </insert>

  <!-- 生成支付请求NO -->
  <select id="generatePaymentRequestNo" resultType="java.lang.String">
    DECLARE @maxSeq INT;
    SELECT @maxSeq = ISNULL(MAX(CAST(RIGHT([支付请求NO], 2) AS INT)), 0) + 1
    FROM [支付额表]
    WHERE [支付请求NO] LIKE 'F' + #{dateStr} + '%';

    SELECT 'F' + #{dateStr} + RIGHT('00' + CAST(@maxSeq AS VARCHAR(2)), 2) AS paymentRequestNo;
  </select>

  <!-- 分页查询销售额确认列表 -->
  <select id="listSalesConfirmByPage" resultType="com.hongru.pojo.dto.SalesConfirmListDTO">
    SELECT
      a.[支付请求NO] AS paymentRequestNo,
      a.[支付请求日] AS paymentRequestDate,
      a.[签约方代码] AS contractorCode,
      ISNULL(c1.[客户简称], a.[签约方代码]) AS contractorAlias,
      a.[客户代码] AS customerCode,
      ISNULL(c2.[客户简称], a.[客户简称]) AS customerAlias,
      a.[结算货币] AS settlementCurrency,
      ISNULL(SUM(b.[请求金额]), 0) AS salesAmount,
      a.[确认者] AS confirmer,
      a.[确认时间] AS confirmTime,
      CAST(a.[支付请求确认] AS VARCHAR) AS paymentRequestConfirm
    FROM [支付额表] a
    LEFT JOIN [支付额明细表] b ON a.[支付请求NO] = b.[支付请求NO]
    LEFT JOIN [sumitomo].[dbo].[客户表] c1 ON a.[签约方代码] COLLATE Chinese_PRC_CI_AS = c1.[客户代码] COLLATE Chinese_PRC_CI_AS
    LEFT JOIN [sumitomo].[dbo].[客户表] c2 ON a.[客户代码] COLLATE Chinese_PRC_CI_AS = c2.[客户代码] COLLATE Chinese_PRC_CI_AS
    <where>
      <if test="customerCode != null and customerCode != ''">
        AND a.[客户代码] = #{customerCode}
      </if>
      <if test="paymentRequestDate != null and paymentRequestDate != ''">
        AND a.[支付请求日] = #{paymentRequestDate}
      </if>
    </where>
    GROUP BY a.[支付请求NO], a.[支付请求日], a.[签约方代码], c1.[客户简称], a.[客户代码],
             ISNULL(c2.[客户简称], a.[客户简称]), a.[结算货币], a.[确认者], a.[确认时间], a.[支付请求确认]
    ORDER BY a.[支付请求日] DESC, a.[支付请求NO] DESC
    OFFSET #{pageInfo.offset} ROWS FETCH NEXT #{pageInfo.limit} ROWS ONLY
  </select>

  <!-- 查询销售额确认列表总数 -->
  <select id="listSalesConfirmByPageCount" resultType="Integer">
    SELECT COUNT(DISTINCT a.[支付请求NO])
    FROM [支付额表] a
    LEFT JOIN [支付额明细表] b ON a.[支付请求NO] = b.[支付请求NO]
    <where>
      <if test="customerCode != null and customerCode != ''">
        AND a.[客户代码] = #{customerCode}
      </if>
      <if test="paymentRequestDate != null and paymentRequestDate != ''">
        AND a.[支付请求日] = #{paymentRequestDate}
      </if>
    </where>
  </select>

  <!-- 更新支付请求确认状态 -->
  <update id="updatePaymentRequestConfirmStatus">
    UPDATE [支付额表]
    SET [支付请求确认] = #{confirmStatus},
        [确认者] = #{confirmer},
        [确认时间] = #{confirmTime}
    WHERE [支付请求NO] = #{paymentRequestNo}
  </update>

</mapper>
