<%@ page language="java" import="java.util.*"
         contentType="text/html; charset=utf-8"%>
<%@ include file="/WEB-INF/layouts/base.jsp"%>
<html>
<head>
    <title>客户详情添加</title>
    <style>
        /*设置数据表表头字体*/
        .layui-table th .layui-table-cell{
            text-align: center;
        }
    </style>
</head>
<body>
<form class="layui-form hr-form-add" action="${ctx}/salesCommon/customerDetails/add" method="post" id="submitForm">
    <div class="layui-fluid hr-layui-fluid">
        <div class="layui-card">
            <div class="layui-card-body">
                <div class="layui-form-item layui-row">
                    <div class="layui-inline layui-col-md5">
		                <label class="layui-form-label"><span class="star">*</span>客户简称:</label>
		                <div class="layui-input-block">
		                    <select class="layui-select" id="customerCode" name="customerCode" lay-search="true" lay-verify="required" required>
		                        <option value="">请选择</option>
		                        <c:forEach items="${customersList}" var="customers">
		                             <option value="${customers.customerCode}" >${customers.customerCode} - ${customers.customerAlias}</option>
		                        </c:forEach>
		                    </select>
		                 </div>
                    </div>
                    <div class="layui-inline layui-col-md5">
		                <label class="layui-form-label"><span class="star">*</span>客户分类:</label>
		                <div class="layui-input-block">
		                    <select class="layui-select" id="customerTypeCode" name="customerTypeCode" lay-search="true" lay-verify="required" required>
		                        <option value="">请选择</option>
		                        <c:forEach items="${customerClassifyList}" var="customerClassify">
		                             <option value="${customerClassify.customerCategoryCode}" >${customerClassify.customerCategoryCode} - ${customerClassify.customerCategory}</option>
		                        </c:forEach>
		                    </select>
		                 </div>
                    </div>
                    <div class="layui-inline layui-col-md5">
                        <label class="layui-form-label"><span class="star">*</span>客户组代码:</label>
                        <div class="layui-input-block">
                             <input type="text" class="layui-input" id="customerGroupCode" name="customerGroupCode" lay-verify="required" required/>
                        </div>
                    </div>
                    <div class="layui-inline layui-col-md5">
                        <label class="layui-form-label"><span class="star">*</span>分野代码:</label>
                        <div class="layui-input-block">
                             <input type="text" class="layui-input" id="fieldCode" name="fieldCode"  lay-verify="required" />
                        </div>
                    </div>
                    <div class="layui-inline layui-col-md5">
                        <label class="layui-form-label">国内外分类:</label>
                        <div class="layui-input-block"  style="display: flex;align-items: center;align-content: center;">
                             <input type="radio" name="country" id="country" value="1" title="国内" checked="checked"/>
                             <input type="radio" name="country" id="country" value="2" title="国外" >
                        </div>
                    </div>
                    <div class="layui-inline layui-col-md5">
		                <label class="layui-form-label"><span class="star">*</span>标准货币:</label>
		                <div class="layui-input-block">
		                    <select class="layui-select" id="currency" name="currency" lay-search="true" lay-verify="required" required>
		                        <option value="">请选择</option>
		                        <c:forEach items="${currencySettingList}" var="currencySetting">
		                             <option value="${currencySetting.currency}" >${currencySetting.currency} - ${currencySetting.currencyName}</option>
		                        </c:forEach>
		                    </select>
		                 </div>
                    </div>
                    <div class="layui-inline layui-col-md5">
		                <label class="layui-form-label"><span class="star">*</span>税率计算:</label>
		                <div class="layui-input-block" style="display: flex;justify-content: space-between;align-items: center;align-content: center">
		                    <select class="layui-select" id="taxRateCode" name="taxRateCode"  lay-filter="taxRateCodeFun" lay-search="true" style="width:100%" lay-verify="required" required>
		                        <option value="">请选择</option>
		                        <c:forEach items="${taxRateCalculationList}" var="taxRateCalculation">
		                             <option value="${taxRateCalculation.taxCode}" >${taxRateCalculation.taxCode} - ${taxRateCalculation.taxCalcName}</option>
		                        </c:forEach>
		                    </select>
		                    <label class="layui-form-label" style="width: 20%">税率(%):</label>
		                    <input type="text" name="taxRate"  class="layui-input" id="taxRate" style="width: 20%"  readonly/>
		                 </div>
                    </div>
                    <div class="layui-inline layui-col-md5">
		                <label class="layui-form-label"><span class="star">*</span>交易条件:</label>
		                <div class="layui-input-block">
		                    <select class="layui-select" id="tradeCondition" name="tradeCondition" lay-search="true" lay-verify="required" required>
		                        <option value="">请选择</option>
		                        <c:forEach items="${tradeConditionsList}" var="tradeConditions">
		                             <option value="${tradeConditions.tradeCondition}" >${tradeConditions.tradeCondition} - ${tradeConditions.conditionName}</option>
		                        </c:forEach>
		                    </select>
		                 </div>
                    </div>
                    <div class="layui-inline layui-col-md5">
		                <label class="layui-form-label"><span class="star">*</span>请求条件:</label>
		                <div class="layui-input-block">
		                    <select class="layui-select" id="payCondition" name="payCondition" lay-search="true" lay-verify="required" required>
		                        <option value="">请选择</option>
		                        <c:forEach items="${payConditionsList}" var="payConditions">
		                             <option value="${payConditions.payCondition}" >${payConditions.payCondition} - ${payConditions.payConditionName}</option>
		                        </c:forEach>
		                    </select>
		                 </div>
                    </div>
                    <div class="layui-inline layui-col-md5">
		                <label class="layui-form-label"><span class="star">*</span>运输条件:</label>
		                <div class="layui-input-block">
		                    <select class="layui-select" id="transportCondition" name="transportCondition" lay-search="true" lay-verify="required" required>
		                        <option value="">请选择</option>
		                        <c:forEach items="${transportConditionsList}" var="transportConditions">
		                             <option value="${transportConditions.transportCondition}" >${transportConditions.transportCondition} - ${transportConditions.transportConditionName}</option>
		                        </c:forEach>
		                    </select>
		                 </div>
                    </div>
                </div>
                <div class="layui-form-item layui-row">
                    <div class="layui-inline layui-col-md12">
                        <label class="layui-form-label"></label>
                        <div class="layui-input-block">
                            <button type="button" class="layui-btn layui-bg-blue" lay-submit lay-filter="formDemo">保存</button>
                            <button type="button" class="layui-btn layui-btn-primary" onclick="closeAll();">取消</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</form>
<myfooter>
    <script src="${ctxsta}/hongru/js/salesCommon/customerDetails_add.js?time=1"></script>
</myfooter>
</body>
</html>
