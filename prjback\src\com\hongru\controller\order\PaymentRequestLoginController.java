package com.hongru.controller.order;

import com.hongru.base.BaseController;
import com.hongru.common.result.HrPageResult;
import com.hongru.common.result.HrResult;
import com.hongru.common.security.AuthorizingUser;
import com.hongru.common.util.BusinessExcelExportUtil;
import com.hongru.common.util.TemplateExcelExportUtil;
import com.hongru.common.util.DateUtils;
import com.hongru.common.util.ServletUtils;
import com.hongru.common.util.SingletonLoginUtils;
import com.hongru.common.util.StringUtil;
import com.hongru.constant.CommonReturnCode;
import com.hongru.entity.admin.User;
import com.hongru.entity.businessOps.PaymentAmount;
import com.hongru.entity.businessOps.PaymentAmountDetail;
import com.hongru.entity.sumitomo.Customers;
import com.hongru.pojo.dto.PaymentRequestLoginListDTO;
import com.hongru.pojo.dto.PaymentRequestExportDTO;
import com.hongru.pojo.vo.UserVO;
import com.hongru.service.admin.IUserService;
import com.hongru.service.businessOps.IPaymentRequestLoginService;
import com.hongru.service.sumitomo.ISumitomoService;
import com.hongru.support.page.PageInfo;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.List;
import java.util.List;
import java.util.Map;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.math.BigDecimal;

@Controller
@RequestMapping(value = "/order/paymentRequestLogin")
public class PaymentRequestLoginController extends BaseController {

    private static final Logger logger = LoggerFactory.getLogger(PaymentRequestLoginController.class);

    @Autowired
    private IUserService userService;

    @Autowired
    private ISumitomoService sumitomoService;

    @Autowired
    private IPaymentRequestLoginService paymentRequestLoginService;

    /**
     * 支付请求登录列表页面
     * 
     * @param model
     * @throws Exception
     * @return
     */
    @GetMapping("/list/view")
    public String listView(Model model) throws Exception {
        // 获取客户列表
        List<Customers> customersList = sumitomoService.listCustomersList();
        model.addAttribute("customersList", customersList);
        return "/modules/order/paymentRequestLogin_list";
    }

    /**
     * 支付请求登录列表数据
     *
     * @param page               页码
     * @param limit              每页数量
     * @param customerCode       客户代码
     * @param paymentRequestDate 支付请求日
     * @throws Exception
     * @return
     */
    @PostMapping("/list")
    @ResponseBody
    public Object list(Integer page, Integer limit, String customerCode, String paymentRequestDate) throws Exception {
        try {
            // 验证用户登录状态
            AuthorizingUser authorizingUser = SingletonLoginUtils.getUser();
            if (authorizingUser == null) {
                return new HrResult(CommonReturnCode.FAILED, "登录超时，请重新登录");
            }

            PageInfo pageInfo = new PageInfo(limit, page);

            UserVO userVO = userService.getById(authorizingUser.getUserId());
            if (userVO == null) {
                return new HrResult(CommonReturnCode.FAILED, "用户信息不存在");
            }

            // 查询支付请求登录列表数据（按当前用户过滤）
            List<PaymentRequestLoginListDTO> list = paymentRequestLoginService.listPaymentRequestLoginByPageWithUser(
                    pageInfo, customerCode, paymentRequestDate, userVO.getUserName());
            Integer total = paymentRequestLoginService.listPaymentRequestLoginByPageCountWithUser(customerCode,
                    paymentRequestDate, userVO.getUserName());

            return new HrPageResult(list, total);
        } catch (Exception e) {
            logger.error("查询支付请求登录列表异常：", e);
            return new HrResult(CommonReturnCode.FAILED, "查询失败：" + e.getMessage());
        }
    }

    /**
     * 支付请求登录添加页面
     * 
     * @param model
     * @throws Exception
     * @return
     */
    @GetMapping("/add/view")
    public String addView(Model model) throws Exception {
        // 获取客户列表
        List<Customers> customersList = sumitomoService.listCustomersList();
        model.addAttribute("customersList", customersList);
        return "/modules/order/paymentRequestLogin_add";
    }

    /**
     * 支付请求登录详情页面
     *
     * @param model
     * @param id               支付请求登录ID
     * @param paymentRequestNo 支付请求NO
     * @throws Exception
     * @return
     */
    @GetMapping("/detail/view")
    public String detailView(Model model, String id, String paymentRequestNo) throws Exception {
        model.addAttribute("id", id);
        model.addAttribute("paymentRequestNo", paymentRequestNo);
        return "/modules/order/paymentRequestLogin_detail";
    }

    /**
     * 支付请求登录编辑页面
     *
     * @param model
     * @param id               支付请求登录ID（已废弃）
     * @param paymentRequestNo 支付请求NO
     * @throws Exception
     * @return
     */
    @GetMapping("/edit/view")
    public String editView(Model model, String id, String paymentRequestNo) throws Exception {
        // 获取客户列表
        List<Customers> customersList = sumitomoService.listCustomersList();
        model.addAttribute("customersList", customersList);
        model.addAttribute("id", id);
        model.addAttribute("paymentRequestNo", paymentRequestNo);
        return "/modules/order/paymentRequestLogin_edit";
    }

    /**
     * 根据客户代码获取销售额数据用于支付请求
     *
     * @param customerCode 客户代码
     * @throws Exception
     * @return
     */
    @PostMapping("/getSalesDataForPaymentRequest")
    @ResponseBody
    public Object getSalesDataForPaymentRequest(String customerCode) throws Exception {
        try {
            // 验证用户登录状态
            AuthorizingUser authorizingUser = SingletonLoginUtils.getUser();
            if (authorizingUser == null) {
                return new HrResult(CommonReturnCode.FAILED, "登录超时，请重新登录");
            }

            if (StringUtil.isStringEmpty(customerCode)) {
                return new HrResult(CommonReturnCode.FAILED, "客户代码不能为空");
            }

            List<PaymentAmountDetail> salesData = paymentRequestLoginService
                    .getSalesDataForPaymentRequest(customerCode);

            Map<String, Object> resultData = new HashMap<>();
            resultData.put("salesData", salesData);

            return new HrResult(CommonReturnCode.SUCCESS, resultData);
        } catch (Exception e) {
            logger.error("获取销售额数据异常：", e);
            return new HrResult(CommonReturnCode.FAILED, "获取销售额数据失败：" + e.getMessage());
        }
    }

    /**
     * 更新销售额明细的支付请求确认状态
     */
    @PostMapping("/updateSalesDetailPaymentRequestConfirm")
    @ResponseBody
    public Object updateSalesDetailPaymentRequestConfirm(
            @RequestParam String salesAmountNo,
            @RequestParam String detailSeq,
            @RequestParam String confirmStatus) throws Exception {
        try {
            AuthorizingUser authorizingUser = SingletonLoginUtils.getUser();
            if (authorizingUser == null) {
                return new HrResult(CommonReturnCode.FAILED, "登录超时，请重新登录");
            }

            UserVO userVO = userService.getById(authorizingUser.getUserId());
            if (userVO == null) {
                return new HrResult(CommonReturnCode.FAILED, "用户信息不存在");
            }

            int result = paymentRequestLoginService.updateSalesDetailPaymentRequestConfirm(
                    salesAmountNo, detailSeq, confirmStatus, userVO.getUserName());

            if (result > 0) {
                return new HrResult(CommonReturnCode.SUCCESS, "更新成功");
            } else {
                return new HrResult(CommonReturnCode.FAILED, "更新失败");
            }
        } catch (Exception e) {
            logger.error("更新支付请求确认状态失败", e);
            return new HrResult(CommonReturnCode.FAILED, "更新失败: " + e.getMessage());
        }
    }

    /**
     * 保存支付请求登录数据
     *
     * @param request HTTP请求对象
     * @throws Exception
     * @return
     */
    @PostMapping("/save")
    @ResponseBody
    public Object save(HttpServletRequest request) throws Exception {
        try {
            // 验证用户登录状态
            AuthorizingUser authorizingUser = SingletonLoginUtils.getUser();
            if (authorizingUser == null) {
                return new HrResult(CommonReturnCode.FAILED, "登录超时，请重新登录");
            }

            UserVO userVO = userService.getById(authorizingUser.getUserId());
            if (userVO == null) {
                return new HrResult(CommonReturnCode.FAILED, "用户信息不存在");
            }
            User user = new User();
            user.setUserName(userVO.getUserName());

            // 获取表单数据
            String paymentRequestDate = request.getParameter("paymentRequestDate");
            String paymentDate = request.getParameter("paymentDate");
            String customerCode = request.getParameter("customerCode");
            String currency = request.getParameter("currency");
            String taxCode = request.getParameter("taxRate");
            String detailsJson = request.getParameter("details");

            // 获取客户相关条件字段
            String tradeCondition = request.getParameter("tradeCondition");
            String payCondition = request.getParameter("payCondition");
            String transportCondition = request.getParameter("transportCondition");

            // 验证必填字段
            if (StringUtil.isStringEmpty(paymentRequestDate)) {
                return new HrResult(CommonReturnCode.FAILED, "支付请求日不能为空");
            }
            if (StringUtil.isStringEmpty(paymentDate)) {
                return new HrResult(CommonReturnCode.FAILED, "支付日不能为空");
            }
            if (StringUtil.isStringEmpty(customerCode)) {
                return new HrResult(CommonReturnCode.FAILED, "客户代码不能为空");
            }

            // 根据客户代码获取客户简称
            List<Customers> customersList = sumitomoService.listCustomersList();
            String customerAlias = "";
            for (Customers customer : customersList) {
                if (customerCode.equals(customer.getCustomerCode())) {
                    customerAlias = customer.getCustomerAlias();
                    break;
                }
            }

            // 构建支付额主表数据
            PaymentAmount paymentAmount = new PaymentAmount();
            paymentAmount.setStronghold("CW");
            paymentAmount.setPaymentRequestDate(paymentRequestDate);
            paymentAmount.setPaymentDate(paymentDate);
            paymentAmount.setCustomerCode(customerCode);
            paymentAmount.setCustomerAlias(customerAlias);
            paymentAmount.setSettlementCurrency(currency);
            paymentAmount.setTaxCode(taxCode);
            paymentAmount.setPaymentRequestConfirm("0");

            // 设置客户相关条件字段
            paymentAmount.setTradeCondition(tradeCondition);
            paymentAmount.setPaymentCondition(payCondition);
            paymentAmount.setTransportCondition(transportCondition);

            // 解析明细数据
            List<PaymentAmountDetail> paymentAmountDetails = null;
            if (!StringUtil.isStringEmpty(detailsJson)) {
                try {
                    logger.info("接收到的明细JSON数据：" + detailsJson);
                    ObjectMapper objectMapper = new ObjectMapper();
                    paymentAmountDetails = objectMapper.readValue(detailsJson,
                            new TypeReference<List<PaymentAmountDetail>>() {
                            });

                    // 调试：打印解析后的数据
                    if (paymentAmountDetails != null && !paymentAmountDetails.isEmpty()) {
                        for (int i = 0; i < paymentAmountDetails.size(); i++) {
                            PaymentAmountDetail detail = paymentAmountDetails.get(i);
                            logger.info("明细[" + i + "] - deliveryNoteNo: " + detail.getDeliveryNoteNo() +
                                    ", deliveryNoteSeq: " + detail.getDeliveryNoteSeq() +
                                    ", wireDrum: " + detail.getWireDrum() +
                                    ", shipmentPlanNo: " + detail.getShipmentPlanNo() +
                                    ", detailRemark: " + detail.getDetailRemark());
                        }
                    }
                } catch (Exception e) {
                    logger.error("解析支付请求明细数据异常：", e);
                    return new HrResult(CommonReturnCode.FAILED, "解析支付请求明细数据异常：" + e.getMessage());
                }
            }

            // 保存数据
            int result = paymentRequestLoginService.savePaymentRequestLoginData(paymentAmount, paymentAmountDetails,
                    user.getUserName());

            if (result > 0) {
                return new HrResult(CommonReturnCode.SUCCESS, "保存成功");
            } else {
                return new HrResult(CommonReturnCode.FAILED, "保存失败");
            }
        } catch (Exception e) {
            logger.error("保存支付请求登录数据异常：", e);
            return new HrResult(CommonReturnCode.FAILED, "保存失败：" + e.getMessage());
        }
    }

    /**
     * 获取支付请求登录详情数据
     *
     * @param id 支付请求登录ID
     * @throws Exception
     * @return
     */
    @PostMapping("/detail")
    @ResponseBody
    public Object detail(String id) throws Exception {
        try {
            // 验证用户登录状态
            AuthorizingUser authorizingUser = SingletonLoginUtils.getUser();
            if (authorizingUser == null) {
                return new HrResult(CommonReturnCode.FAILED, "登录超时，请重新登录");
            }

            if (StringUtil.isStringEmpty(id)) {
                return new HrResult(CommonReturnCode.FAILED, "ID不能为空");
            }

            // 获取支付额主表数据
            PaymentAmount paymentAmount = paymentRequestLoginService.getPaymentAmountByNo(id);
            if (paymentAmount == null) {
                return new HrResult(CommonReturnCode.FAILED, "未找到支付请求数据");
            }

            // 获取支付额明细数据
            List<PaymentAmountDetail> paymentAmountDetails = paymentRequestLoginService
                    .getPaymentAmountDetailsByNo(paymentAmount.getPaymentRequestNo());

            Map<String, Object> resultData = new HashMap<>();
            resultData.put("paymentAmount", paymentAmount);
            resultData.put("paymentAmountDetails", paymentAmountDetails);

            return new HrResult(CommonReturnCode.SUCCESS, resultData);
        } catch (Exception e) {
            logger.error("获取支付请求登录详情异常：", e);
            return new HrResult(CommonReturnCode.FAILED, "获取详情失败：" + e.getMessage());
        }
    }

    /**
     * 根据支付请求NO获取详情数据
     *
     * @param paymentRequestNo 支付请求NO
     * @throws Exception
     * @return
     */
    @PostMapping("/detailByNo")
    @ResponseBody
    public Object detailByNo(String paymentRequestNo) throws Exception {
        try {
            // 验证用户登录状态
            AuthorizingUser authorizingUser = SingletonLoginUtils.getUser();
            if (authorizingUser == null) {
                return new HrResult(CommonReturnCode.FAILED, "登录超时，请重新登录");
            }

            if (StringUtil.isStringEmpty(paymentRequestNo)) {
                return new HrResult(CommonReturnCode.FAILED, "支付请求NO不能为空");
            }

            // 获取支付额主表数据
            PaymentAmount paymentAmount = paymentRequestLoginService.getPaymentAmountByNo(paymentRequestNo);
            if (paymentAmount == null) {
                return new HrResult(CommonReturnCode.FAILED, "未找到支付请求数据");
            }

            // 获取支付额明细数据
            List<PaymentAmountDetail> paymentAmountDetails = paymentRequestLoginService
                    .getPaymentAmountDetailsByNo(paymentRequestNo);

            Map<String, Object> resultData = new HashMap<>();
            resultData.put("paymentAmount", paymentAmount);
            resultData.put("paymentAmountDetails", paymentAmountDetails);

            return new HrResult(CommonReturnCode.SUCCESS, resultData);
        } catch (Exception e) {
            logger.error("根据支付请求NO获取详情异常：", e);
            return new HrResult(CommonReturnCode.FAILED, "获取详情失败：" + e.getMessage());
        }
    }

    /**
     * 更新支付请求登录数据
     *
     * @param request HTTP请求对象
     * @throws Exception
     * @return
     */
    @PostMapping("/update")
    @ResponseBody
    public Object update(HttpServletRequest request) throws Exception {
        try {
            // 验证用户登录状态
            AuthorizingUser authorizingUser = SingletonLoginUtils.getUser();
            if (authorizingUser == null) {
                return new HrResult(CommonReturnCode.FAILED, "登录超时，请重新登录");
            }

            UserVO userVO = userService.getById(authorizingUser.getUserId());
            if (userVO == null) {
                return new HrResult(CommonReturnCode.FAILED, "用户信息不存在");
            }
            User user = new User();
            user.setUserName(userVO.getUserName());

            // 获取表单数据
            String paymentRequestNo = request.getParameter("paymentRequestNo");
            String paymentRequestDate = request.getParameter("paymentRequestDate");
            String paymentDate = request.getParameter("paymentDate");
            String currency = request.getParameter("currency");
            String taxCode = request.getParameter("taxCode");
            String detailsJson = request.getParameter("details");

            // 获取客户相关条件字段
            String tradeCondition = request.getParameter("tradeCondition");
            String payCondition = request.getParameter("payCondition");
            String transportCondition = request.getParameter("transportCondition");

            // 验证必填字段
            if (StringUtil.isStringEmpty(paymentRequestNo)) {
                return new HrResult(CommonReturnCode.FAILED, "支付请求NO不能为空");
            }

            // 获取现有的支付额数据
            PaymentAmount paymentAmount = paymentRequestLoginService.getPaymentAmountByNo(paymentRequestNo);
            if (paymentAmount == null) {
                return new HrResult(CommonReturnCode.FAILED, "未找到支付请求数据");
            }

            // 更新支付额主表数据
            paymentAmount.setPaymentRequestDate(paymentRequestDate);
            paymentAmount.setPaymentDate(paymentDate);
            paymentAmount.setSettlementCurrency(currency);
            paymentAmount.setTaxCode(taxCode);

            // 更新客户相关条件字段
            paymentAmount.setTradeCondition(tradeCondition);
            paymentAmount.setPaymentCondition(payCondition);
            paymentAmount.setTransportCondition(transportCondition);

            // 解析明细数据
            List<PaymentAmountDetail> paymentAmountDetails = null;
            if (!StringUtil.isStringEmpty(detailsJson)) {
                try {
                    logger.info("更新时接收到的明细JSON数据：" + detailsJson);
                    ObjectMapper objectMapper = new ObjectMapper();
                    paymentAmountDetails = objectMapper.readValue(detailsJson,
                            new TypeReference<List<PaymentAmountDetail>>() {
                            });

                    // 调试：打印解析后的数据
                    if (paymentAmountDetails != null && !paymentAmountDetails.isEmpty()) {
                        for (int i = 0; i < paymentAmountDetails.size(); i++) {
                            PaymentAmountDetail detail = paymentAmountDetails.get(i);
                            logger.info("更新明细[" + i + "] - deliveryNoteNo: " + detail.getDeliveryNoteNo() +
                                    ", deliveryNoteSeq: " + detail.getDeliveryNoteSeq() +
                                    ", wireDrum: " + detail.getWireDrum() +
                                    ", shipmentPlanNo: " + detail.getShipmentPlanNo() +
                                    ", detailRemark: " + detail.getDetailRemark());
                        }
                    }
                } catch (Exception e) {
                    logger.error("解析支付请求明细数据异常：", e);
                    return new HrResult(CommonReturnCode.FAILED, "解析支付请求明细数据异常：" + e.getMessage());
                }
            }

            // 更新数据
            int result = paymentRequestLoginService.updatePaymentRequestLoginData(paymentAmount, paymentAmountDetails,
                    user.getUserName());

            if (result > 0) {
                return new HrResult(CommonReturnCode.SUCCESS, "更新成功");
            } else {
                return new HrResult(CommonReturnCode.FAILED, "更新失败");
            }
        } catch (Exception e) {
            logger.error("更新支付请求登录数据异常：", e);
            return new HrResult(CommonReturnCode.FAILED, "更新失败：" + e.getMessage());
        }
    }

    /**
     * 删除支付请求登录数据
     *
     * @param paymentRequestNo 支付请求NO
     * @throws Exception
     * @return
     */
    @PostMapping("/delete")
    @ResponseBody
    public Object delete(String paymentRequestNo) throws Exception {
        try {
            // 验证用户登录状态
            AuthorizingUser authorizingUser = SingletonLoginUtils.getUser();
            if (authorizingUser == null) {
                return new HrResult(CommonReturnCode.FAILED, "登录超时，请重新登录");
            }

            if (StringUtil.isStringEmpty(paymentRequestNo)) {
                return new HrResult(CommonReturnCode.FAILED, "支付请求NO不能为空");
            }

            // 删除数据
            int result = paymentRequestLoginService.deletePaymentRequestLoginData(paymentRequestNo);

            if (result > 0) {
                return new HrResult(CommonReturnCode.SUCCESS, "删除成功");
            } else {
                return new HrResult(CommonReturnCode.FAILED, "删除失败");
            }
        } catch (Exception e) {
            logger.error("删除支付请求登录数据异常：", e);
            return new HrResult(CommonReturnCode.FAILED, "删除失败：" + e.getMessage());
        }
    }

    /**
     * 导出支付请求Excel
     *
     * @param customerCode       客户代码
     * @param paymentRequestDate 支付请求日期范围
     * @param response           HTTP响应
     * @throws Exception
     */
    @PostMapping("/export")
    @ResponseBody
    public void exportPaymentRequestExcel(String customerCode, String paymentRequestDate,
            HttpServletResponse response) throws Exception {
        try {
            // 验证用户登录状态
            AuthorizingUser authorizingUser = SingletonLoginUtils.getUser();
            if (authorizingUser == null) {
                response.getWriter().write("{\"code\":0,\"msg\":\"登录超时，请重新登录\"}");
                return;
            }

            // 获取导出数据
            List<PaymentRequestExportDTO> exportData = paymentRequestLoginService.getPaymentRequestExportData(
                    customerCode, paymentRequestDate);

            if (exportData == null || exportData.isEmpty()) {
                response.getWriter().write("{\"code\":0,\"msg\":\"没有可导出的数据\"}");
                return;
            }

            // 设置响应头
            String fileName = "支付请求书_" + DateUtils.convertIntToDateString(DateUtils.getCurrentTime(), "yyyyMMddHHmmss")
                    + ".xls";
            ServletUtils.setFileDownloadHeader(response, fileName);
            response.setContentType(ServletUtils.EXCEL_TYPE);

            // 导出Excel - 使用模板
            String companyInfo = "住友电工运营株式会社";
            TemplateExcelExportUtil.exportPaymentRequestExcel(response.getOutputStream(), exportData, companyInfo);

        } catch (Exception e) {
            logger.error("导出支付请求Excel异常：", e);
            response.getWriter().write("{\"code\":0,\"msg\":\"导出失败：" + e.getMessage() + "\"}");
        }
    }

    /**
     * 根据支付请求NO导出Excel
     *
     * @param paymentRequestNo 支付请求NO
     * @param response         HTTP响应
     * @throws Exception
     */
    @PostMapping("/exportByNo")
    @ResponseBody
    public void exportPaymentRequestExcelByNo(String paymentRequestNo, HttpServletResponse response) throws Exception {
        try {
            // 验证用户登录状态
            AuthorizingUser authorizingUser = SingletonLoginUtils.getUser();
            if (authorizingUser == null) {
                response.getWriter().write("{\"code\":0,\"msg\":\"登录超时，请重新登录\"}");
                return;
            }

            if (StringUtil.isStringEmpty(paymentRequestNo)) {
                response.getWriter().write("{\"code\":0,\"msg\":\"支付请求NO不能为空\"}");
                return;
            }

            // 获取导出数据
            List<PaymentRequestExportDTO> exportData = paymentRequestLoginService.getPaymentRequestExportDataByNo(
                    paymentRequestNo);

            if (exportData == null || exportData.isEmpty()) {
                response.getWriter().write("{\"code\":0,\"msg\":\"没有可导出的数据\"}");
                return;
            }

            // 设置响应头
            String fileName = "支付请求书_" + paymentRequestNo + "_" +
                    DateUtils.convertIntToDateString(DateUtils.getCurrentTime(), "yyyyMMddHHmmss") + ".xls";
            ServletUtils.setFileDownloadHeader(response, fileName);
            response.setContentType(ServletUtils.EXCEL_TYPE);

            // 导出Excel - 使用模板
            String companyInfo = "住友电工运营株式会社";
            TemplateExcelExportUtil.exportPaymentRequestExcel(response.getOutputStream(), exportData, companyInfo);

        } catch (Exception e) {
            logger.error("导出支付请求Excel异常：", e);
            response.getWriter().write("{\"code\":0,\"msg\":\"导出失败：" + e.getMessage() + "\"}");
        }
    }

    /**
     * 根据出货单No.获取明细数据
     *
     * @param shipmentNo 出货单No.
     * @throws Exception
     * @return
     */
    @PostMapping("/getDetailsByShipmentNo")
    @ResponseBody
    public Object getDetailsByShipmentNo(String shipmentNo) throws Exception {
        try {
            // 验证用户登录状态
            AuthorizingUser authorizingUser = SingletonLoginUtils.getUser();
            if (authorizingUser == null) {
                return new HrResult(CommonReturnCode.FAILED, "登录超时，请重新登录");
            }

            if (StringUtil.isStringEmpty(shipmentNo)) {
                return new HrResult(CommonReturnCode.FAILED, "出货单No.不能为空");
            }

            // 根据出货单No.获取明细数据
            List<Map<String, Object>> detailsList = paymentRequestLoginService.getDetailsByShipmentNo(shipmentNo);
            if (detailsList == null || detailsList.isEmpty()) {
                return new HrResult(CommonReturnCode.SUCCESS, detailsList);
            }

            return new HrResult(CommonReturnCode.SUCCESS, detailsList);
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("根据出货单No.获取支付请求明细数据异常：", e);
            return new HrResult(CommonReturnCode.FAILED.getCode(), "系统异常：" + e.getMessage());
        }
    }
}
