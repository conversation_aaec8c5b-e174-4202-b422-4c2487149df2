/**
 * 订单详情页面JS
 */
layui.use(['form', 'layer'], function() {
    var form = layui.form;
    var layer = layui.layer;

    // 页面初始化
    $(function() {
        // 初始化附件预览
        initAttachmentPreview();

        // 预加载SheetJS库，用于Excel文件预览
        preloadSheetJSLibrary();
    });
});

/**
 * 初始化附件预览
 */
function initAttachmentPreview() {
    var attachmentId = $('#attachmentId').val();
    if (!attachmentId) return;

    // 处理可能的逗号问题
    if (attachmentId.indexOf(',') !== -1) {
        console.log('附件ID包含逗号，原始值:', attachmentId);
        attachmentId = attachmentId.split(',')[0];
        console.log('处理后的附件ID:', attachmentId);
    }

    // 处理可能的空格问题
    attachmentId = attachmentId.trim();

    // 记录日志，便于调试
    console.log('最终使用的附件ID:', attachmentId);

    // 从服务器获取文件信息
    $.ajax({
        url: baselocation + '/common/file/info',
        type: 'GET',
        data: { id: attachmentId },
        dataType: 'json',
        success: function(res) {
            console.log('获取文件信息成功:', res);
            if (res.code === 1 && res.data) {
                updateAttachmentPreview(res.data);
            } else {
                console.error('获取文件信息失败:', res.message || '未知错误');
                // 使用默认预览
                updateAttachmentPreview({
                    id: attachmentId,
                    fileName: '附件文件'
                });
            }
        },
        error: function(xhr, _, error) {
            console.error('获取文件信息请求失败:', error, xhr.responseText);
            // 使用默认预览
            updateAttachmentPreview({
                id: attachmentId,
                fileName: '附件文件'
            });
        }
    });
}

/**
 * 更新附件预览
 * @param {Object} fileInfo 文件信息
 */
function updateAttachmentPreview(fileInfo) {
    if (!fileInfo) return;

    console.log('更新附件预览:', fileInfo);

    var $previewHeader = $('#attachmentTitle');
    var $previewContent = $('#previewContent');

    // 获取原始文件名 - 修复：使用与其他页面相同的文件名获取逻辑，确保一致性
    var originalFileName = fileInfo.fileName || fileInfo.originalFileName || '未知文件';

    // 如果文件名是附件_ID的形式，尝试从文件系统中获取原始文件名
    if (originalFileName.startsWith('附件_') && fileInfo.newFileName) {
        originalFileName = fileInfo.newFileName;
        // 如果有时间戳前缀，去除它
        if (originalFileName.indexOf('_') !== -1) {
            originalFileName = originalFileName.substring(originalFileName.indexOf('_') + 1);
        }
    }

    // 更新附件ID和文件名显示
    $previewHeader.html('附件预览: ' + originalFileName);

    // 根据文件类型显示不同的预览
    var fileType = getFileType(originalFileName);

    // 判断是否是Excel文件
    if (fileType === 'excel' || /\.(xls|xlsx|csv)$/i.test(originalFileName)) {
        // 使用与单行录入页面相同的Excel预览方式 - 更紧凑的样式
        $previewContent.html(
            '<div style="padding:10px; text-align:center;" id="excel-loading">' +
            '  <i class="layui-icon layui-icon-loading layui-anim layui-anim-rotate layui-anim-loop" style="font-size:24px;"></i>' +
            '  <p style="margin-top:5px; font-size:13px;">正在加载Excel内容...</p>' +
            '</div>' +
            '<div id="excel-preview-container" style="width:100%; overflow-x:auto; display:none;"></div>'
        );

        // 获取文件并预览
        var downloadUrl = baselocation + '/common/download?id=' + fileInfo.id;
        previewExcelFile(downloadUrl, originalFileName);
    }
    // 判断是否是图片文件
    else if (fileType === 'image' || /\.(jpg|jpeg|png|gif)$/i.test(originalFileName)) {
        // 图片文件预览 - 更紧凑的样式
        var imageUrl = fileInfo.fileUrl || (baselocation + '/common/download?id=' + fileInfo.id);
        $previewContent.html(
            '<div style="font-size:14px; font-weight:bold; margin-bottom:5px;"><i class="layui-icon layui-icon-picture" style="margin-right:5px;"></i>图片预览</div>' +
            '<div style="width:100%; text-align:center; padding:5px;">' +
            '  <img src="' + imageUrl + '" style="max-width:100%; max-height:350px; border:1px solid #e6e6e6;" />' +
            '</div>'
        );
    }
    // 判断是否是PDF文件
    else if (fileType === 'pdf' || /\.pdf$/i.test(originalFileName)) {
        // 直接在线预览PDF文件 - 更紧凑的样式
        var fileUrl = baselocation + '/common/download?id=' + fileInfo.id + '&iframe=true';

        $previewContent.html(
            '<div style="width:100%; height:450px; border:1px solid #e6e6e6;">' +
            '  <iframe src="' + fileUrl + '" width="100%" height="100%" style="border:none;" allowfullscreen></iframe>' +
            '</div>'
        );
    }
    // 其他文件类型
    else {
        // 显示图标和下载按钮 - 更紧凑的样式
        var iconClass = getFileIconClass(fileType);
        $previewContent.html(
            '<div style="padding:10px; text-align:center; display:flex; align-items:center; justify-content:center;">' +
            '  <i class="layui-icon ' + iconClass + '" style="font-size:24px; color:#1E9FFF; margin-right:10px;"></i>' +
            '  <span style="font-size:14px;">' + originalFileName + '</span>' +
            '  <button class="layui-btn layui-btn-sm" onclick="downloadAttachment(\'' + fileInfo.id + '\')" style="margin-left:15px;">' +
            '    <i class="layui-icon layui-icon-download-circle"></i> 下载' +
            '  </button>' +
            '</div>'
        );
    }
}

/**
 * 获取文件类型
 * @param {string} fileName 文件名
 * @returns {string} 文件类型
 */
function getFileType(fileName) {
    if (!fileName) return 'unknown';

    var ext = fileName.split('.').pop().toLowerCase();

    if (/^(jpg|jpeg|png|gif|bmp)$/.test(ext)) {
        return 'image';
    } else if (/^(doc|docx|rtf)$/.test(ext)) {
        return 'word';
    } else if (/^(xls|xlsx|csv)$/.test(ext)) {
        return 'excel';
    } else if (ext === 'pdf') {
        return 'pdf';
    } else if (/^(txt|json|xml|html|css|js)$/.test(ext)) {
        return 'text';
    } else {
        return 'unknown';
    }
}

/**
 * 获取文件图标样式
 * @param {string} fileType 文件类型
 * @returns {string} 图标样式类
 */
function getFileIconClass(fileType) {
    switch (fileType) {
        case 'word':
            return 'layui-icon-file-b';
        case 'excel':
            return 'layui-icon-table';
        case 'pdf':
            return 'layui-icon-read';
        case 'text':
            return 'layui-icon-file-b';
        default:
            return 'layui-icon-file';
    }
}

/**
 * 下载附件
 * @param {string} fileId 文件ID
 */
function downloadAttachment(fileId) {
    // 打印调试信息
    console.log('下载文件ID:', fileId);
    console.log('文件ID类型:', typeof fileId);

    // 去除可能的空格和特殊字符
    if (typeof fileId === 'string') {
        fileId = fileId.trim();
        // 检查是否包含逗号，如果有则只取第一部分
        if (fileId.indexOf(',') !== -1) {
            console.log('文件ID包含逗号，原始值:', fileId);
            fileId = fileId.split(',')[0];
            console.log('处理后的文件ID:', fileId);
        }
        // 再次去除可能的空格
        fileId = fileId.trim();
        console.log('最终使用的文件ID:', fileId);
    }

    if (!fileId || fileId === '') {
        layer.msg('未找到附件', {icon: 2});
        return;
    }

    // 显示加载中提示
    var loadingIndex = layer.load(1, {
        shade: [0.1, '#fff']
    });

    // 先获取文件信息
    $.ajax({
        url: baselocation + '/common/file/info',
        type: 'GET',
        data: { id: fileId },
        dataType: 'json',
        success: function(res) {
            layer.close(loadingIndex);
            console.log('获取文件信息成功:', res);
            if (res.code === 1 && res.data) {
                // 打开下载链接（显式不包含iframe参数以确保是下载而非预览）
                var downloadUrl = baselocation + '/common/download?id=' + fileId + '&download=true';
                console.log('下载链接:', downloadUrl);
                window.open(downloadUrl);
            } else {
                layer.msg('获取文件信息失败: ' + (res.message || '未知错误'), {icon: 2});
            }
        },
        error: function(xhr, _, error) {
            layer.close(loadingIndex);
            console.error('获取文件信息请求失败:', error, xhr.responseText);
            layer.msg('获取文件信息失败: ' + error, {icon: 2});
        }
    });
}

/**
 * 格式化文件大小
 * @param {number} size 文件大小（字节）
 * @returns {string} 格式化后的文件大小
 */
function formatFileSize(size) {
    if (!size || size === 0) return '0 B';

    var units = ['B', 'KB', 'MB', 'GB', 'TB'];
    var i = Math.floor(Math.log(size) / Math.log(1024));

    return (size / Math.pow(1024, i)).toFixed(2) + ' ' + units[i];
}

/**
 * 关闭页面
 */
function closeAll() {
    var index = parent.layer.getFrameIndex(window.name);
    parent.layer.close(index);
}

/**
 * 预加载SheetJS库
 */
function preloadSheetJSLibrary() {
    if (typeof XLSX === 'undefined') {
        console.log('预加载SheetJS库');
        var script = document.createElement('script');
        script.src = 'https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js';
        script.async = true;
        script.onload = function() {
            console.log('SheetJS库加载完成');
        };
        script.onerror = function() {
            console.error('SheetJS库加载失败');
        };
        document.head.appendChild(script);
    }
}

/**
 * 预览Excel文件
 * @param {string} fileUrl 文件URL
 * @param {string} fileName 文件名
 */
function previewExcelFile(fileUrl, fileName) {
    // 检查是否已加载SheetJS库
    if (typeof XLSX === 'undefined') {
        // 动态加载SheetJS库
        var script = document.createElement('script');
        script.src = 'https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js';
        script.onload = function() {
            fetchAndParseExcel(fileUrl, fileName);
        };
        script.onerror = function() {
            $('#excel-loading').html(
                '<i class="layui-icon layui-icon-close-fill" style="font-size:30px;color:#FF5722"></i>' +
                '<p style="margin-top:10px">无法加载Excel预览组件，请直接下载文件查看</p>'
            );
        };
        document.head.appendChild(script);
    } else {
        fetchAndParseExcel(fileUrl, fileName);
    }
}

/**
 * 获取并解析Excel文件
 * @param {string} fileUrl 文件URL
 * @param {string} fileName 文件名
 */
function fetchAndParseExcel(fileUrl, fileName) {
    // 记录文件名信息
    console.log('开始解析Excel文件:', fileName);
    // 使用XMLHttpRequest获取文件
    var xhr = new XMLHttpRequest();
    xhr.open('GET', fileUrl, true);
    xhr.responseType = 'arraybuffer';

    xhr.onload = function() {
        if (xhr.status === 200) {
            try {
                var arrayBuffer = xhr.response;
                var data = new Uint8Array(arrayBuffer);
                var workbook = XLSX.read(data, {type: 'array'});

                // 表格样式 - 更紧凑的样式
                var tableStyles = '<style>' +
                    '.excel-table { width: 100%; border-collapse: collapse; border: 1px solid #ddd; font-size: 13px; }' +
                    '.excel-table th, .excel-table td { border: 1px solid #ddd; padding: 4px; text-align: left; }' +
                    '.excel-table tr:nth-child(even) { background-color: #f2f2f2; }' +
                    '.excel-table th { padding: 6px 4px; background-color: #4CAF50; color: white; font-weight: bold; }' +
                    '.excel-table td, .excel-table th { min-width: 70px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap; }' +
                    // 直接应用到原生表格元素，确保样式覆盖
                    'table { border-collapse: collapse !important; }' +
                    'th, td { border: 1px solid #ddd !important; padding: 4px !important; font-size: 13px !important; }' +
                    'th { background-color: #f2f2f2 !important; font-weight: bold !important; }' +
                    '</style>';

                // 如果工作簿有多个工作表，添加选项卡
                if (workbook.SheetNames.length > 1) {
                    var sheetTabs = '<div class="layui-tab layui-tab-brief"><ul class="layui-tab-title">';

                    for (var i = 0; i < workbook.SheetNames.length; i++) {
                        var sheetName = workbook.SheetNames[i];
                        var activeClass = (i === 0) ? 'layui-this' : '';
                        sheetTabs += '<li class="' + activeClass + '" data-sheet="' + sheetName + '">' + sheetName + '</li>';
                    }

                    sheetTabs += '</ul></div>';

                    // 添加工作表选项卡
                    $('#excel-preview-container').before(sheetTabs);

                    // 绑定选项卡切换事件
                    $('.layui-tab-title li').click(function() {
                        var sheetName = $(this).data('sheet');
                        var worksheet = workbook.Sheets[sheetName];
                        var htmlTable = XLSX.utils.sheet_to_html(worksheet, {
                            id: 'excel-table',
                            className: 'excel-table',
                            editable: false,
                            header: tableStyles
                        });

                        $('#excel-preview-container').html(htmlTable);
                        setTimeout(function() {
                            applyTableEnhancements();
                        }, 100);
                        $('.layui-tab-title li').removeClass('layui-this');
                        $(this).addClass('layui-this');
                    });
                }

                // 默认显示第一个工作表
                var firstSheet = workbook.SheetNames[0];
                var worksheet = workbook.Sheets[firstSheet];

                // 将工作表转换为HTML表格
                var htmlTable = XLSX.utils.sheet_to_html(worksheet, {
                    id: 'excel-table',
                    className: 'excel-table',
                    editable: false,
                    header: tableStyles
                });

                // 显示HTML表格
                $('#excel-preview-container').html(htmlTable).show();
                $('#excel-loading').hide();

                // 应用额外的样式强化
                setTimeout(function() {
                    applyTableEnhancements();
                }, 100);

            } catch (e) {
                console.error('解析Excel文件失败:', e);
                $('#excel-loading').html(
                    '<i class="layui-icon layui-icon-close-fill" style="font-size:30px;color:#FF5722"></i>' +
                    '<p style="margin-top:10px">解析Excel文件失败: ' + e.message + '</p>'
                );
            }
        } else {
            console.error('获取Excel文件失败:', xhr.statusText);
            $('#excel-loading').html(
                '<i class="layui-icon layui-icon-close-fill" style="font-size:30px;color:#FF5722"></i>' +
                '<p style="margin-top:10px">获取Excel文件失败: ' + xhr.statusText + '</p>'
            );
        }
    };

    xhr.onerror = function() {
        console.error('网络错误，无法获取Excel文件');
        $('#excel-loading').html(
            '<i class="layui-icon layui-icon-close-fill" style="font-size:30px;color:#FF5722"></i>' +
            '<p style="margin-top:10px">网络错误，无法获取Excel文件</p>'
        );
    };

    xhr.send();
}

/**
 * 应用表格增强样式 - 更紧凑的样式
 */
function applyTableEnhancements() {
    var $table = $('#excel-table');
    if (!$table.length) return;

    // 确保表格有边框
    $table.css({
        'border': '1px solid #ddd',
        'font-size': '13px'
    });

    // 确保表格单元格有边框和填充 - 更紧凑的样式
    $table.find('th, td').css({
        'border': '1px solid #ddd',
        'padding': '4px',
        'font-size': '13px'
    });

    // 为表头添加样式
    $table.find('th').css({
        'background-color': '#f2f2f2',
        'font-weight': 'bold',
        'text-align': 'center',
        'padding': '6px 4px'
    });

    // 为偶数行添加背景色
    $table.find('tr:even').css('background-color', '#f9f9f9');

    // 设置表格行高更小
    $table.find('tr').css('line-height', '24px');

    // 如果表格过宽，添加水平滚动
    var tableWidth = $table.width();
    var containerWidth = $('#excel-preview-container').width();
    if (tableWidth > containerWidth) {
        $('#excel-preview-container').css('overflow-x', 'auto');
    }

    // 添加表头固定功能（如果表格很长）
    if ($table.find('tr').length > 10) {
        $table.find('tr:first-child th').css({
            'position': 'sticky',
            'top': '0',
            'z-index': '10',
            'background-color': '#e6e6e6'
        });
    }

    // 输出确认信息
    console.log('表格样式增强已应用');
}

/**
 * 在新窗口中打开PDF文件
 * @param {string} fileId 文件ID
 */
function openPdfInNewWindow(fileId) {
    // 去除可能的空格和特殊字符
    if (typeof fileId === 'string') {
        fileId = fileId.trim();
        // 检查是否包含逗号，如果有则只取第一部分
        if (fileId.indexOf(',') !== -1) {
            console.log('文件ID包含逗号，原始值:', fileId);
            fileId = fileId.split(',')[0];
            console.log('处理后的文件ID:', fileId);
        }
        // 再次去除可能的空格
        fileId = fileId.trim();
        console.log('最终使用的文件ID:', fileId);
    }

    if (!fileId || fileId === '') {
        layer.msg('未找到附件', {icon: 2});
        return;
    }

    // 使用iframe参数确保在新窗口中以内联方式打开PDF，而不是下载
    var pdfUrl = baselocation + '/common/download?id=' + fileId + '&iframe=true';

    // 打开新窗口
    var pdfWindow = window.open(pdfUrl, '_blank');
    if (pdfWindow) {
        pdfWindow.focus();
    } else {
        layer.msg('浏览器阻止了新窗口的打开，请检查您的浏览器设置', {icon: 0});
    }
}
