<%@ page language="java" import="java.util.*" contentType="text/html; charset=utf-8"%>
<%@ include file="/WEB-INF/layouts/base.jsp"%>
<!DOCTYPE HTML>
<html>
<head>
  <title>0-base报表</title>
</head>
<body>
<div class="ibox float-e-margins">
  <div class="ibox-content">
    <div class="layui-card">
      <div class="layui-card-header">检索条件</div>
      <div class="layui-card-body">
        <div class="layui-form">
          <form id="formSearch" class="layui-form layui-form-pane">
            <div class="layui-form-item">
              <div class="layui-inline layui-col-md3">
                <label class="layui-form-label">会计年月（开始）</label>
                <div class="layui-input-block">
                  <input type="text" name="startAccountingYearMonth" id="startAccountingYearMonth" placeholder="请选择开始会计年月" autocomplete="off" class="layui-input">
                </div>
              </div>
              <div class="layui-inline layui-col-md3">
                <label class="layui-form-label">会计年月（结束）</label>
                <div class="layui-input-block">
                  <input type="text" name="endAccountingYearMonth" id="endAccountingYearMonth" placeholder="请选择结束会计年月" autocomplete="off" class="layui-input">
                </div>
              </div>
              <div class="layui-inline layui-col-md1 hr-div-btn">
                <button type="button" id="btn_query" onclick="search();" class="layui-btn layui-bg-blue"><i class="layui-icon layui-icon-search"></i>检索</button>
              </div>
              <div class="layui-inline layui-col-md1 hr-div-btn">
                <button type="button" id="btn_export" onclick="exportData();" class="layui-btn layui-bg-green"><i class="layui-icon layui-icon-export"></i>导出</button>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>
    <%--初始化table--%>
    <table class="layui-hide" id="demo" lay-filter="test"></table>
    <!-- 自定义头部工具栏 -->
    <script type="text/html" id="toolbarDemo">
      <div class="layui-btn-container">
        <button class="layui-btn layui-btn-primary layui-btn-sm" lay-event="refresh"><i class="layui-icon layui-icon-refresh"></i>刷新</button>
      </div>
    </script>
  </div>
</div>
<myfooter>
  <script src="${ctxsta}/hongru/js/salesReport/zeroBase.js?time=4"></script>
</myfooter>
</body>
</html>
