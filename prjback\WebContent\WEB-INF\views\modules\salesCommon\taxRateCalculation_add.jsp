<%@ page language="java" import="java.util.*"
         contentType="text/html; charset=utf-8"%>
<%@ include file="/WEB-INF/layouts/base.jsp"%>
<html>
<head>
    <title>税率计算添加</title>
    <style>
        /*设置数据表表头字体*/
        .layui-table th .layui-table-cell{
            text-align: center;
        }
    </style>
</head>
<body>
<form class="layui-form hr-form-add" action="${ctx}/salesCommon/taxRateCalculation/add" method="post" id="submitForm">
    <div class="layui-fluid hr-layui-fluid">
        <div class="layui-card">
            <div class="layui-card-body">
                <div class="layui-form-item layui-row">
                    <div class="layui-inline layui-col-md5">
                        <label class="layui-form-label">税率代码:</label>
                        <div class="layui-input-block">
                             <input type="text" class="layui-input" id="taxCode" name="taxCode" value="${taxCode}" readonly/>
                        </div>
                    </div>
                    <div class="layui-inline layui-col-md5">
                        <label class="layui-form-label">税率计算名:</label>
                        <div class="layui-input-block"  style="display: flex;align-items: center;align-content: center;">
                             <input type="radio" name="taxCalcName" id="taxCalcName" value="含税" title="含税" checked="checked"/>
                             <input type="radio" name="taxCalcName" id="taxCalcName" value="不含税" title="不含税" >
                             <input type="radio" name="taxCalcName" id="taxCalcName" value="无税" title="无税" >
                        </div>
                    </div>
                    <div class="layui-inline layui-col-md5">
		                <label class="layui-form-label">税率(%):</label>
		                <div class="layui-input-block">
							<input type="text" class="layui-input" id="taxRate" name="taxRate" value="0"  onKeyUp="amount(this)" onBlur="overFormat(this)" />
		                </div>
                    </div>
                </div>
                <div class="layui-form-item layui-row">
                    <div class="layui-inline layui-col-md12">
                        <label class="layui-form-label"></label>
                        <div class="layui-input-block">
                            <button type="button" class="layui-btn layui-bg-blue" lay-submit lay-filter="formDemo">保存</button>
                            <button type="button" class="layui-btn layui-btn-primary" onclick="closeAll();">取消</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</form>
<myfooter>
    <script src="${ctxsta}/hongru/js/salesCommon/taxRateCalculation_add.js?time=1"></script>
</myfooter>
</body>
</html>
