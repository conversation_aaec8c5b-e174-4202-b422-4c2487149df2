package com.hongru.entity.businessOps;

import com.baomidou.mybatisplus.annotations.TableField;
import com.baomidou.mybatisplus.annotations.TableId;
import com.baomidou.mybatisplus.annotations.TableName;
import com.baomidou.mybatisplus.enums.IdType;

import java.math.BigDecimal;

@TableName("预定直接部门回收计算表")//DirectRecycling
public class DirectRecycling {
	/* 流水号 */
	@TableId(value="流水号", type= IdType.AUTO)
	protected int costId;
	/* 导入标识 */
	protected int importId;
	/* 年月 */
	protected String yearMonth;
	/* 年 */
	protected int year;
	/* 月 */
	protected int month;
	/* 区分 */
	protected String directCode;
	/* 部门 */
	protected String departmentCode;
	/* SMCH */
	protected BigDecimal sMCHNum;
	/* 操作工人件费SH */
	protected BigDecimal operatorCostSH;
	/* 操作工人件费费用 */
	protected BigDecimal operatorCost;
	/* 劳务工人件费SH */
	protected BigDecimal laborCostSH;
	/* 劳务工人件费费用 */
	protected BigDecimal laborCost;
	/* 电费SH */
	protected BigDecimal electricCostSH;
	/* 电费费用 */
	protected BigDecimal electricCost;
	/* 煤气SH */
	protected BigDecimal gasCostSH;
	/* 煤气费费用 */
	protected BigDecimal gasCost;
	/* 水费SH */
	protected BigDecimal waterCostSH;
	/* 水费费用 */
	protected BigDecimal waterCost;
	/* 氮气费SH */
	protected BigDecimal nitrogenCostSH;
	/* 氮气费费用 */
	protected BigDecimal nitrogenCost;
	/* 保全操作工人件费SH */
	protected BigDecimal securityCostSH;
	/* 保全操作工人件费费用 */
	protected BigDecimal securityCost;
	/* 补修费SH */
	protected BigDecimal repairCostSH;
	/* 补修费费用 */
	protected BigDecimal repairCost;
	/* 辅材费SH */
	protected BigDecimal materialCostSH;
	/* 辅材费费用 */
	protected BigDecimal materialCost;
	/* 创建人标识 */
	protected int creatorId;
	/* 创建人姓名 */
	protected String creatorName;
	/* 创建时间 */
	protected String createdTime;

	/* 部门名称 */
	@TableField(exist = false)
	protected String departmentName;

	public int getCostId() {
		return costId;
	}

	public void setCostId(int costId) {
		this.costId = costId;
	}

	public int getImportId() {
		return importId;
	}

	public void setImportId(int importId) {
		this.importId = importId;
	}

	public String getYearMonth() {
		return yearMonth;
	}

	public void setYearMonth(String yearMonth) {
		this.yearMonth = yearMonth;
	}

	public int getYear() {
		return year;
	}

	public void setYear(int year) {
		this.year = year;
	}

	public int getMonth() {
		return month;
	}

	public void setMonth(int month) {
		this.month = month;
	}

	public String getDirectCode() {
		return directCode;
	}

	public void setDirectCode(String directCode) {
		this.directCode = directCode;
	}

	public String getDepartmentCode() {
		return departmentCode;
	}

	public void setDepartmentCode(String departmentCode) {
		this.departmentCode = departmentCode;
	}

	public BigDecimal getsMCHNum() {
		return sMCHNum;
	}

	public void setsMCHNum(BigDecimal sMCHNum) {
		this.sMCHNum = sMCHNum;
	}

	public BigDecimal getOperatorCostSH() {
		return operatorCostSH;
	}

	public void setOperatorCostSH(BigDecimal operatorCostSH) {
		this.operatorCostSH = operatorCostSH;
	}

	public BigDecimal getOperatorCost() {
		return operatorCost;
	}

	public void setOperatorCost(BigDecimal operatorCost) {
		this.operatorCost = operatorCost;
	}

	public BigDecimal getLaborCostSH() {
		return laborCostSH;
	}

	public void setLaborCostSH(BigDecimal laborCostSH) {
		this.laborCostSH = laborCostSH;
	}

	public BigDecimal getLaborCost() {
		return laborCost;
	}

	public void setLaborCost(BigDecimal laborCost) {
		this.laborCost = laborCost;
	}

	public BigDecimal getElectricCostSH() {
		return electricCostSH;
	}

	public void setElectricCostSH(BigDecimal electricCostSH) {
		this.electricCostSH = electricCostSH;
	}

	public BigDecimal getElectricCost() {
		return electricCost;
	}

	public void setElectricCost(BigDecimal electricCost) {
		this.electricCost = electricCost;
	}

	public BigDecimal getGasCostSH() {
		return gasCostSH;
	}

	public void setGasCostSH(BigDecimal gasCostSH) {
		this.gasCostSH = gasCostSH;
	}

	public BigDecimal getGasCost() {
		return gasCost;
	}

	public void setGasCost(BigDecimal gasCost) {
		this.gasCost = gasCost;
	}

	public BigDecimal getWaterCostSH() {
		return waterCostSH;
	}

	public void setWaterCostSH(BigDecimal waterCostSH) {
		this.waterCostSH = waterCostSH;
	}

	public BigDecimal getWaterCost() {
		return waterCost;
	}

	public void setWaterCost(BigDecimal waterCost) {
		this.waterCost = waterCost;
	}

	public BigDecimal getNitrogenCostSH() {
		return nitrogenCostSH;
	}

	public void setNitrogenCostSH(BigDecimal nitrogenCostSH) {
		this.nitrogenCostSH = nitrogenCostSH;
	}

	public BigDecimal getNitrogenCost() {
		return nitrogenCost;
	}

	public void setNitrogenCost(BigDecimal nitrogenCost) {
		this.nitrogenCost = nitrogenCost;
	}

	public BigDecimal getSecurityCostSH() {
		return securityCostSH;
	}

	public void setSecurityCostSH(BigDecimal securityCostSH) {
		this.securityCostSH = securityCostSH;
	}

	public BigDecimal getSecurityCost() {
		return securityCost;
	}

	public void setSecurityCost(BigDecimal securityCost) {
		this.securityCost = securityCost;
	}

	public BigDecimal getRepairCostSH() {
		return repairCostSH;
	}

	public void setRepairCostSH(BigDecimal repairCostSH) {
		this.repairCostSH = repairCostSH;
	}

	public BigDecimal getRepairCost() {
		return repairCost;
	}

	public void setRepairCost(BigDecimal repairCost) {
		this.repairCost = repairCost;
	}

	public BigDecimal getMaterialCostSH() {
		return materialCostSH;
	}

	public void setMaterialCostSH(BigDecimal materialCostSH) {
		this.materialCostSH = materialCostSH;
	}

	public BigDecimal getMaterialCost() {
		return materialCost;
	}

	public void setMaterialCost(BigDecimal materialCost) {
		this.materialCost = materialCost;
	}

	public int getCreatorId() {
		return creatorId;
	}

	public void setCreatorId(int creatorId) {
		this.creatorId = creatorId;
	}

	public String getCreatorName() {
		return creatorName;
	}

	public void setCreatorName(String creatorName) {
		this.creatorName = creatorName;
	}

	public String getCreatedTime() {
		return createdTime;
	}

	public void setCreatedTime(String createdTime) {
		this.createdTime = createdTime;
	}

	public String getDepartmentName() {
		return departmentName;
	}

	public void setDepartmentName(String departmentName) {
		this.departmentName = departmentName;
	}
}