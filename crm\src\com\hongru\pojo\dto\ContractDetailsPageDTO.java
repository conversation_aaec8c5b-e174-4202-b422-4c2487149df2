package com.hongru.pojo.dto;

import java.util.List;

import com.hongru.entity.businessOps.ContractDetails;
import com.hongru.support.page.PageInfo;

public class ContractDetailsPageDTO{

	/**
	 * 合同方详情信息
	 */
	private List<ContractDetails> contractDetailsList;
	
	/**
	 * 分页信息
	 */
	private PageInfo pageInfo;
	
	public ContractDetailsPageDTO(PageInfo pageInfo, List<ContractDetails> contractDetailsList) {
		super();
		this.contractDetailsList = contractDetailsList;
		this.pageInfo = pageInfo;
	}

	public List<ContractDetails> getContractDetailsList() {
		return contractDetailsList;
	}

	public void setContractDetailsList(List<ContractDetails> contractDetailsList) {
		this.contractDetailsList = contractDetailsList;
	}

	public PageInfo getPageInfo() {
		return pageInfo;
	}

	public void setPageInfo(PageInfo pageInfo) {
		this.pageInfo = pageInfo;
	}

}
