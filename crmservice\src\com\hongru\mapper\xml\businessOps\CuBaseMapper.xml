<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hongru.mapper.businessOps.CuBaseMapper">
  <!-- 查询Cu Base数据 -->
  <select id="listCuBase" resultType="com.hongru.pojo.dto.CuBaseDTO">
    SELECT
      a.[客户简称] AS customerAlias,
      a.[结算货币] AS settlementCurrency,
      b.[铜base] AS copperPrice,
      SUM(b.[数量]) AS salesQuantity,
      SUM(b.[销售金额]) AS salesAmount,
      SUM(b.[铜base] * b.[销售金额]) AS copperAmount,
      LEFT(a.[销售额日], 7) AS accountingYearMonth
    FROM [销售额表] a
    LEFT JOIN [销售额明细表] b ON a.[销售额NO] = b.[销售额NO]
    <where>
      <if test="startDate != null and startDate != ''">
        AND a.[销售额日] &gt;= #{startDate}
      </if>
      <if test="endDate != null and endDate != ''">
        AND a.[销售额日] &lt;= #{endDate}
      </if>
    </where>
    GROUP BY a.[客户简称], a.[结算货币], b.[铜base], LEFT(a.[销售额日], 7)
    ORDER BY a.[客户简称], b.[铜base]
  </select>
</mapper> 