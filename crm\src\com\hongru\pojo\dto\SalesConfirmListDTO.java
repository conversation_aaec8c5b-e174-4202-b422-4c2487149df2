package com.hongru.pojo.dto;

import java.math.BigDecimal;

/**
 * 销售额确认列表DTO
 */
public class SalesConfirmListDTO {
    
    /* 支付请求NO */
    private String paymentRequestNo;
    /* 支付请求日 */
    private String paymentRequestDate;
    /* 签约方代码 */
    private String contractorCode;
    /* 签约方简称 */
    private String contractorAlias;
    /* 客户代码 */
    private String customerCode;
    /* 客户简称 */
    private String customerAlias;
    /* 结算货币 */
    private String settlementCurrency;
    /* 销售金额 */
    private BigDecimal salesAmount;
    /* 确认者 */
    private String confirmer;
    /* 确认时间 */
    private String confirmTime;
    /* 支付请求确认状态 */
    private String paymentRequestConfirm;

    public String getPaymentRequestNo() {
        return paymentRequestNo;
    }

    public void setPaymentRequestNo(String paymentRequestNo) {
        this.paymentRequestNo = paymentRequestNo;
    }

    public String getPaymentRequestDate() {
        return paymentRequestDate;
    }

    public void setPaymentRequestDate(String paymentRequestDate) {
        this.paymentRequestDate = paymentRequestDate;
    }

    public String getContractorCode() {
        return contractorCode;
    }

    public void setContractorCode(String contractorCode) {
        this.contractorCode = contractorCode;
    }

    public String getContractorAlias() {
        return contractorAlias;
    }

    public void setContractorAlias(String contractorAlias) {
        this.contractorAlias = contractorAlias;
    }

    public String getCustomerCode() {
        return customerCode;
    }

    public void setCustomerCode(String customerCode) {
        this.customerCode = customerCode;
    }

    public String getCustomerAlias() {
        return customerAlias;
    }

    public void setCustomerAlias(String customerAlias) {
        this.customerAlias = customerAlias;
    }

    public String getSettlementCurrency() {
        return settlementCurrency;
    }

    public void setSettlementCurrency(String settlementCurrency) {
        this.settlementCurrency = settlementCurrency;
    }

    public BigDecimal getSalesAmount() {
        return salesAmount;
    }

    public void setSalesAmount(BigDecimal salesAmount) {
        this.salesAmount = salesAmount;
    }

    public String getConfirmer() {
        return confirmer;
    }

    public void setConfirmer(String confirmer) {
        this.confirmer = confirmer;
    }

    public String getConfirmTime() {
        return confirmTime;
    }

    public void setConfirmTime(String confirmTime) {
        this.confirmTime = confirmTime;
    }

    public String getPaymentRequestConfirm() {
        return paymentRequestConfirm;
    }

    public void setPaymentRequestConfirm(String paymentRequestConfirm) {
        this.paymentRequestConfirm = paymentRequestConfirm;
    }
}
