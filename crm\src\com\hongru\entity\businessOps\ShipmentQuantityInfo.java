package com.hongru.entity.businessOps;

import com.baomidou.mybatisplus.annotations.TableField;
import com.baomidou.mybatisplus.annotations.TableId;
import com.baomidou.mybatisplus.annotations.TableName;
import com.baomidou.mybatisplus.enums.IdType;

import java.math.BigDecimal;
import java.util.List;


@TableName("出库数量表")//BusinessOps.dbo.出库数量表
public class ShipmentQuantityInfo {
	@TableId(value = "流水号", type = IdType.AUTO)
	private Integer id;
	
	@TableField("出库日期")
	private String outboundDate;
	
	@TableField("客户代码")
	private String customerCode;
	
	@TableField("客户简称")
	private String customerAlias;
	
	@TableField("产品代码")
	private String productCode;
	
	@TableField("尺寸")
	private String size;
	
	@TableField("出库数量")
	private BigDecimal shipmentQuantity;
	
	@TableField("送货单号")
	private String shipmentOrderNo;

	@TableField("送货单序号")
	private Integer shipmentOrderSerial;

	@TableField("客户订单号")
	private String customerOrderNo;
	
	@TableField("产品中分类")
	private String productCategory;
	
	@TableField("出库计划番号")
	private String shipmentPlanNo;
	
	@TableField("创建者")
	private String creator;
	
	@TableField("创建时间")
	private String createTime;
	
	// Getters and Setters
	public Integer getId() {
		return id;
	}
	
	public void setId(Integer id) {
		this.id = id;
	}
	
	public String getOutboundDate() {
		return outboundDate;
	}
	
	public void setOutboundDate(String outboundDate) {
		this.outboundDate = outboundDate;
	}
	
	public String getCustomerCode() {
		return customerCode;
	}
	
	public void setCustomerCode(String customerCode) {
		this.customerCode = customerCode;
	}
	
	public String getCustomerAlias() {
		return customerAlias;
	}
	
	public void setCustomerAlias(String customerAlias) {
		this.customerAlias = customerAlias;
	}
	
	public String getProductCode() {
		return productCode;
	}
	
	public void setProductCode(String productCode) {
		this.productCode = productCode;
	}
	
	public String getSize() {
		return size;
	}
	
	public void setSize(String size) {
		this.size = size;
	}
	
	public BigDecimal getShipmentQuantity() {
		return shipmentQuantity;
	}
	
	public void setShipmentQuantity(BigDecimal shipmentQuantity) {
		this.shipmentQuantity = shipmentQuantity;
	}
	
	public String getShipmentOrderNo() {
		return shipmentOrderNo;
	}
	
	public void setShipmentOrderNo(String shipmentOrderNo) {
		this.shipmentOrderNo = shipmentOrderNo;
	}
	
	public String getCustomerOrderNo() {
		return customerOrderNo;
	}
	
	public void setCustomerOrderNo(String customerOrderNo) {
		this.customerOrderNo = customerOrderNo;
	}
	
	public String getProductCategory() {
		return productCategory;
	}
	
	public void setProductCategory(String productCategory) {
		this.productCategory = productCategory;
	}
	
	public String getShipmentPlanNo() {
		return shipmentPlanNo;
	}
	
	public void setShipmentPlanNo(String shipmentPlanNo) {
		this.shipmentPlanNo = shipmentPlanNo;
	}

	public Integer getShipmentOrderSerial() {
		return shipmentOrderSerial;
	}

	public void setShipmentOrderSerial(Integer shipmentOrderSerial) {
		this.shipmentOrderSerial = shipmentOrderSerial;
	}

	public String getCreator() {
		return creator;
	}
	
	public void setCreator(String creator) {
		this.creator = creator;
	}
	
	public String getCreateTime() {
		return createTime;
	}
	
	public void setCreateTime(String createTime) {
		this.createTime = createTime;
	}
	
}