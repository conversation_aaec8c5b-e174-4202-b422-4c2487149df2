#===============================#
#======  数据库连接的配置     ======#
#===============================#

#住友sumitom数据库
jdbc.driver=com.microsoft.sqlserver.jdbc.SQLServerDriver
jdbc.url=************************************************************
#定义连接数据库账号
jdbc.username=sa
#定义连接数据库密码
jdbc.password=1q2w3E#$

#定义初始连接数
jdbc.pool.init=1
#定义最大连接数
jdbc.pool.maxActive=20
#定义最大空闲
jdbc.pool.minIdle=3
#定义最小空闲
minIdle=1
#定义最长等待时间
jdbc.maxWait=60000
#定义间隔检查时间
jdbc.timeBetweenEvictionRunsMillis=60000
#最小存活时间
jdbc.minEvictableIdleTimeMillis=300000
#jdbc.testSql=SELECT 'x'
#jdbc.testSql=SELECT 'x' FROM DUAL
jdbc.testSql=SELECT 1