package com.hongru.service.businessOps;

import com.hongru.entity.businessOps.*;
import com.hongru.entity.sumitomo.Customers;
import com.hongru.pojo.dto.AscendingWaterPageDTO;
import com.hongru.pojo.dto.ContractDetailsPageDTO;
import com.hongru.pojo.dto.CopperContractPageDTO;
import com.hongru.pojo.dto.CopperPricesPageDTO;
import com.hongru.pojo.dto.CurrencyExchangeRatePageDTO;
import com.hongru.pojo.dto.CustomerDetailsPageDTO;
import com.hongru.pojo.dto.ProductsPricePageDTO;
import com.hongru.support.page.PageInfo;

import java.math.BigDecimal;
import java.util.List;

public interface ISalesCommonService {

	/* ======================客户详情表====================== */
	/**
	 * 客户详情
	 * 
	 * @param
	 * @throws
	 */
	CustomerDetails getCustomerDetailsById(Integer id) throws Exception;

	/**
	 * 客户详情列表
	 * 
	 * @param
	 * @throws
	 * @return
	 */
	CustomerDetailsPageDTO customerDetailsListByPage(PageInfo pageInfo, String customerCode, String status)
			throws Exception;

	/**
	 * 新增客户详情
	 * 
	 * @param
	 * @throws
	 * @return int
	 */
	int insertCustomerDetails(CustomerDetails customerDetails) throws Exception;

	/**
	 * 更新客户详情
	 * 
	 * @param
	 * @throws
	 * @return
	 */
	void updateCustomerDetails(CustomerDetails customerDetails) throws Exception;

	/**
	 * 删除客户详情
	 * 
	 * @param
	 * @throws
	 * @return
	 */
	void updateCustomerDetailsStatus(Integer id) throws Exception;

	/* ======================合同方详情表====================== */
	/**
	 * 取得最大合同方代码
	 * 
	 * @throws
	 * @return String
	 */
	String getMaxContractCode() throws Exception;

	/**
	 * 合同方详情list
	 * 
	 * @throws
	 * @return java.util.List<com.hongru.entity.businessOps.ContractDetails>
	 */
	List<ContractDetails> listContractDetailsList() throws Exception;

	/**
	 * 合同方详情
	 * 
	 * @param
	 * @throws
	 */
	ContractDetails getContractDetailsById(Integer id) throws Exception;

	/**
	 * 合同方详情列表
	 * 
	 * @param
	 * @throws
	 * @return
	 */
	ContractDetailsPageDTO contractDetailsListByPage(PageInfo pageInfo, String contractCode) throws Exception;

	/**
	 * 新增合同方
	 * 
	 * @param
	 * @throws
	 * @return int
	 */
	int insertContractDetails(ContractDetails contractDetails) throws Exception;

	/**
	 * 更新合同方
	 * 
	 * @param
	 * @throws
	 * @return
	 */
	void updateContractDetails(ContractDetails contractDetails) throws Exception;

	/**
	 * 删除合同方
	 * 
	 * @param
	 * @throws
	 * @return
	 */
	void deleteContractDetailsById(Integer id);

	/**
	 * 根据合同方代码获取合同方详情
	 * 
	 * @param contractCode 合同方代码
	 * @throws
	 * @return 合同方详情
	 */
	ContractDetails getContractDetailsByCode(String contractCode) throws Exception;

	/* ======================客户分类表====================== */
	/**
	 * 客户分类详情
	 * 
	 * @param
	 * @throws
	 */
	CustomerClassify getCustomerClassifyById(Integer id) throws Exception;

	/**
	 * 客户分类列表
	 * 
	 * @param
	 * @throws
	 * @return java.util.List<com.hongru.entity.businessOps.CustomerClassify>
	 */
	List<CustomerClassify> listCustomerClassify(String customerCategoryCode, String customerCategory) throws Exception;

	/**
	 * 新增客户分类表
	 * 
	 * @param
	 * @throws
	 * @return int
	 */
	int insertCustomerClassify(CustomerClassify customerClassify) throws Exception;

	/**
	 * 更新客户分类表
	 * 
	 * @param
	 * @throws
	 * @return
	 */
	void updateCustomerClassify(CustomerClassify customerClassify) throws Exception;

	/**
	 * 删除客户分类表
	 * 
	 * @param
	 * @throws
	 * @return
	 */
	void deleteCustomerClassify(Integer id) throws Exception;

	/* ======================产品价格表====================== */
	/**
	 * 产品价格详情
	 * 
	 * @param
	 * @throws
	 */
	ProductsPrice getProductsPriceById(Integer id) throws Exception;

	/**
	 * 产品价格详情列表
	 * 
	 * @param
	 * @throws
	 * @return
	 */
	ProductsPricePageDTO productsPriceListByPage(PageInfo pageInfo, String customerCode, String productCode,
			String applyDateStart, String applyDateEnd) throws Exception;

	/**
	 * 新增产品价格
	 * 
	 * @param
	 * @throws
	 * @return int
	 */
	int insertProductsPrice(ProductsPrice productsPrice) throws Exception;

	/**
	 * 更新产品价格
	 * 
	 * @param
	 * @throws
	 * @return
	 */
	void updateProductsPrice(ProductsPrice productsPrice) throws Exception;

	/**
	 * 删除产品价格
	 * 
	 * @param
	 * @throws
	 * @return
	 */
	void deleteProductsPrice(Integer id) throws Exception;

	/**
	 * 根据客户代码、产品代码、到货日期查询产品单价
	 * 
	 * @param customerCode 客户代码
	 * @param productCode  产品代码
	 * @param arrivalDate  到货日期
	 * @throws Exception
	 * @return 产品价格信息
	 */
	ProductsPrice getProductPriceByConditions(String customerCode, String productCode, String arrivalDate)
			throws Exception;

	/* ======================交易条件表====================== */
	/**
	 * 交易条件详情
	 * 
	 * @param
	 * @throws
	 */
	TradeConditions getTradeConditionsById(Integer id) throws Exception;

	/**
	 * 交易条件列表
	 * 
	 * @param
	 * @throws
	 * @return java.util.List<com.hongru.entity.businessOps.TradeConditions>
	 */
	List<TradeConditions> listTradeConditions(String tradeCondition, String conditionName) throws Exception;

	/**
	 * 新增交易条件
	 * 
	 * @param
	 * @throws
	 * @return int
	 */
	int insertTradeConditions(TradeConditions tradeConditions) throws Exception;

	/**
	 * 更新交易条件
	 * 
	 * @param
	 * @throws
	 * @return
	 */
	void updateTradeConditions(TradeConditions tradeConditions) throws Exception;

	/**
	 * 删除交易条件表
	 * 
	 * @param
	 * @throws
	 * @return
	 */
	void deleteTradeConditions(Integer id) throws Exception;

	/* ======================付款条件表====================== */
	/**
	 * 付款条件详情
	 * 
	 * @param
	 * @throws
	 */
	PayConditions getPayConditionsById(Integer id) throws Exception;

	/**
	 * 付款条件列表
	 * 
	 * @param
	 * @throws
	 * @return java.util.List<com.hongru.entity.businessOps.PayConditions>
	 */
	List<PayConditions> listPayConditions(String payCondition, String payConditionName) throws Exception;

	/**
	 * 新增付款条件
	 * 
	 * @param
	 * @throws
	 * @return int
	 */
	int insertPayConditions(PayConditions payConditions) throws Exception;

	/**
	 * 更新付款条件
	 * 
	 * @param
	 * @throws
	 * @return
	 */
	void updatePayConditions(PayConditions payConditions) throws Exception;

	/**
	 * 删除付款条件表
	 * 
	 * @param
	 * @throws
	 * @return
	 */
	void deletePayConditions(Integer id) throws Exception;

	/* ======================运输条件表====================== */
	/**
	 * 运输条件详情
	 * 
	 * @param
	 * @throws
	 */
	TransportConditions getTransportConditionsById(Integer id) throws Exception;

	/**
	 * 运输条件列表
	 * 
	 * @param
	 * @throws
	 * @return java.util.List<com.hongru.entity.businessOps.TransportConditions>
	 */
	List<TransportConditions> listTransportConditions(String transportCondition, String transportConditionName)
			throws Exception;

	/**
	 * 新增运输条件
	 * 
	 * @param
	 * @throws
	 * @return int
	 */
	int insertTransportConditions(TransportConditions transportConditions) throws Exception;

	/**
	 * 更新运输条件
	 * 
	 * @param
	 * @throws
	 * @return
	 */
	void updateTransportConditions(TransportConditions transportConditions) throws Exception;

	/**
	 * 删除运输条件表
	 * 
	 * @param
	 * @throws
	 * @return
	 */
	void deleteTransportConditions(Integer id) throws Exception;

	/* ======================国家设定表====================== */
	/**
	 * 国家设定详情
	 * 
	 * @param
	 * @throws
	 */
	CountrySetting getCountrySettingById(Integer id) throws Exception;

	/**
	 * 国家设定详情列表
	 * 
	 * @param
	 * @throws
	 * @return java.util.List<com.hongru.entity.businessOps.CountrySetting>
	 */
	List<CountrySetting> listCountrySetting(String country, String countryName) throws Exception;

	/**
	 * 新增国家
	 * 
	 * @param
	 * @throws
	 * @return int
	 */
	int insertCountrySetting(CountrySetting countrySetting) throws Exception;

	/**
	 * 更新国家设定表
	 * 
	 * @param
	 * @throws
	 * @return
	 */
	void updateCountrySetting(CountrySetting countrySetting) throws Exception;

	/**
	 * 删除国家设定表
	 * 
	 * @param
	 * @throws
	 * @return
	 */
	void deleteCountrySetting(Integer id) throws Exception;

	/* ======================仓库表====================== */
	/**
	 * 仓库详情
	 * 
	 * @param
	 * @throws
	 */
	WareHouse getWareHouseById(Integer id) throws Exception;

	/**
	 * 仓库详情列表
	 * 
	 * @param
	 * @throws
	 * @return java.util.List<com.hongru.entity.businessOps.WareHouse>
	 */
	List<WareHouse> listWareHouse(String wareHouseCode, String wareHouseName) throws Exception;

	/**
	 * 新增仓库详情
	 * 
	 * @param
	 * @throws
	 * @return int
	 */
	int insertWareHouse(WareHouse wareHouse) throws Exception;

	/**
	 * 更新仓库表
	 * 
	 * @param
	 * @throws
	 * @return
	 */
	void updateWareHouse(WareHouse wareHouse) throws Exception;

	/**
	 * 删除仓库表
	 * 
	 * @param
	 * @throws
	 * @return
	 */
	void deleteWareHouse(Integer id) throws Exception;

	/* ======================货币设定表====================== */
	/**
	 * 货币种类详情
	 * 
	 * @param
	 * @throws
	 */
	CurrencySetting getCurrencySettingById(Integer id) throws Exception;

	/**
	 * 货币种类详情列表
	 * 
	 * @param
	 * @throws
	 * @return java.util.List<com.hongru.entity.businessOps.CurrencySetting>
	 */
	List<CurrencySetting> listCurrencySetting(String currency, String currencyName) throws Exception;

	/**
	 * 新增货币详情
	 * 
	 * @param
	 * @throws
	 * @return int
	 */
	int insertCurrencySetting(CurrencySetting currencySetting) throws Exception;

	/**
	 * 更新货币设定表
	 * 
	 * @param
	 * @throws
	 * @return
	 */
	void updateCurrencySetting(CurrencySetting currencySetting) throws Exception;

	/**
	 * 删除仓库表
	 * 
	 * @param
	 * @throws
	 * @return
	 */
	void deleteCurrencySetting(Integer id) throws Exception;

	/**
	 * 取得货币列表（去重）
	 * 
	 * @param
	 * @throws
	 * @return
	 */
	List<String> selectDistinctCurrency() throws Exception;

	/* ======================货币汇率换算表====================== */
	/**
	 * 货币汇率换算详情
	 * 
	 * @param
	 * @throws
	 */
	CurrencyExchangeRate getCurrencyExchangeRateById(Integer id) throws Exception;

	/**
	 * 货币汇率换算列表(分页)
	 * 
	 * @param pageInfo 分页信息
	 * @param search   搜索内容
	 * @return
	 */
	CurrencyExchangeRatePageDTO currencyExchangeRateListByPage(PageInfo pageInfo, String originalCurrency,
			String targetCurrency, String applyDateStart, String applyDateEnd) throws Exception;

	/**
	 * 新增货币汇率换算详情
	 * 
	 * @param
	 * @throws
	 */
	int insertCurrencyExchangeRate(CurrencyExchangeRate currencyExchangeRate) throws Exception;

	/**
	 * 更新货币汇率换算表
	 * 
	 * @param
	 * @throws
	 * @return
	 */
	void updateCurrencyExchangeRate(CurrencyExchangeRate currencyExchangeRate) throws Exception;

	/**
	 * 删除货币汇率换算
	 * 
	 * @param
	 * @throws
	 * @return
	 */
	void deleteCurrencyExchangeRate(Integer id) throws Exception;

	/**
	 * 根据换算原货币和适用日期查询汇率
	 * 
	 * @param originalCurrency 换算原货币
	 * @param arrivalDate      到货日期
	 * @return 货币换算汇率
	 * @throws Exception
	 */
	CurrencyExchangeRate getCurrencyExchangeRateByOriginalCurrencyAndDate(String originalCurrency, String arrivalDate)
			throws Exception;

	/* ======================税率计算表====================== */
	/**
	 * 税率计算详情
	 * 
	 * @param
	 * @throws
	 */
	TaxRateCalculation getTaxRateCalculationById(Integer id) throws Exception;

	/**
	 * 税率计算详情列表
	 * 
	 * @param
	 * @throws
	 * @return java.util.List<com.hongru.entity.businessOps.TaxRateCalculation>
	 */
	List<TaxRateCalculation> listTaxRateCalculation(String taxCalcName, BigDecimal taxRate) throws Exception;

	/**
	 * 新增税率计算
	 * 
	 * @param
	 * @throws
	 */
	int insertTaxRateCalculation(TaxRateCalculation taxRateCalculation) throws Exception;

	/**
	 * 更新税率计算
	 * 
	 * @param
	 * @throws
	 * @return
	 */
	void updateTaxRateCalculation(TaxRateCalculation taxRateCalculation) throws Exception;

	/**
	 * 删除税率计算
	 * 
	 * @param
	 * @throws
	 * @return
	 */
	void deleteTaxRateCalculation(Integer id) throws Exception;

	/**
	 * 取得当前最大税率代码
	 * 
	 * @param
	 * @throws
	 * @return
	 */
	int selectMaxTaxCode() throws Exception;

	/**
	 * 根据税率代码取得税率
	 * 
	 * @param
	 * @throws
	 */
	TaxRateCalculation getTaxRateByTaxCode(String taxRateCode) throws Exception;

	/* ======================铜条件表====================== */
	/**
	 * 铜条件详情
	 * 
	 * @param
	 * @throws
	 */
	CopperConditions getCopperConditionsById(Integer id) throws Exception;

	/**
	 * 铜条件列表
	 * 
	 * @param
	 * @throws
	 * @return java.util.List<com.hongru.entity.businessOps.CopperConditions>
	 */
	List<CopperConditions> listCopperConditions(String copperCondition, String copperConditionName) throws Exception;

	/**
	 * 新增铜条件
	 * 
	 * @param
	 * @throws
	 * @return int
	 */
	int insertCopperConditions(CopperConditions copperConditions) throws Exception;

	/**
	 * 更新铜条件
	 * 
	 * @param
	 * @throws
	 * @return
	 */
	void updateCopperConditions(CopperConditions copperConditions) throws Exception;

	/**
	 * 删除铜条件表
	 * 
	 * @param
	 * @throws
	 * @return
	 */
	void deleteCopperConditions(Integer id) throws Exception;

	/**
	 * 根据铜条件获取铜条件详情
	 * 
	 * @param
	 * @throws
	 * @return
	 */
	CopperConditions findCopperConditions(String copperCondition) throws Exception;

	/**
	 * 获取铜条件列表
	 * 
	 * @param
	 * @throws
	 * @return
	 */
	List<String> selectCopperCondition() throws Exception;

	/* ======================铜价表====================== */
	/**
	 * 铜价详情
	 * 
	 * @param
	 * @throws
	 */
	CopperPrices getCopperPricesById(Integer id) throws Exception;

	/**
	 * 铜价详情列表
	 * 
	 * @param pageInfo 分页信息
	 * @param search   搜索内容
	 * @throws
	 * @return java.util.List<com.hongru.entity.businessOps.CopperPrices>
	 */
	CopperPricesPageDTO copperPricesListByPage(PageInfo pageInfo, String copperCondition, String applyDateStart,
			String applyDateEnd) throws Exception;

	/**
	 * 新增铜价详情
	 * 
	 * @param
	 * @throws
	 * @return int
	 */
	int insertCopperPrices(CopperPrices copperPrices) throws Exception;

	/**
	 * 更新铜价详情
	 * 
	 * @param
	 * @throws
	 * @return
	 */
	void updateCopperPrices(CopperPrices copperPrices) throws Exception;

	/**
	 * 删除铜价
	 * 
	 * @param
	 * @throws
	 * @return
	 */
	void deleteCopperPrices(Integer id) throws Exception;

	/**
	 * 根据铜条件和到货日期查询铜基本
	 * 
	 * @param copperCondition 铜条件
	 * @param arrivalDate     到货日期
	 * @throws Exception
	 * @return CopperPrices
	 */
	CopperPrices getCopperBaseByConditionAndDate(String copperCondition, String arrivalDate) throws Exception;

	/* ======================铜合同表====================== */
	/**
	 * 铜合同详情
	 * 
	 * @param
	 * @throws
	 */
	CopperContract getCopperContractById(Integer id) throws Exception;

	/**
	 * 铜合同详情列表
	 * 
	 * @param
	 * @throws
	 * @return
	 */
	CopperContractPageDTO copperContractListByPage(PageInfo pageInfo, String customerCode, String copperSignNo,
			String copperSignType, String copperCondition, String appointmentDate, String useMonth, String status)
			throws Exception;

	/**
	 * 新增铜合同详情
	 * 
	 * @param
	 * @throws
	 */
	int insertCopperContract(CopperContract copperContract) throws Exception;

	/**
	 * 更新铜合同
	 * 
	 * @param
	 * @throws
	 * @return
	 */
	void updateCopperContract(CopperContract copperContract) throws Exception;

	/**
	 * 更新铜合同状态
	 * 
	 * @param
	 * @throws
	 * @return
	 */
	void updateCopperContractStatus(Short status, Integer id) throws Exception;

	/**
	 * 根据条件获取铜合同列表（用于销售额登录）
	 * 
	 * @param customerCode       客户代码
	 * @param copperContractType 铜合同类别
	 * @param useMonth           使用月
	 * @param status             状态
	 * @throws Exception
	 * @return
	 */
	List<CopperContract> getCopperContractList(String customerCode, String copperContractType, String useMonth,
			String status) throws Exception;

	/**
	 * 根据铜签约NO和使用月查询铜合同详情（关联铜条件表）
	 * 
	 * @param copperSignNo 铜签约NO
	 * @param useMonth     使用月
	 * @throws Exception
	 * @return CopperContract
	 */
	CopperContract getCopperContractDetailWithCondition(String copperSignNo, String useMonth) throws Exception;

	/**
	 * 更新铜合同的实际数量和剩余数量
	 * 
	 * @param copperSignNo      铜签约NO
	 * @param actualQuantity    实际数量
	 * @param remainingQuantity 剩余数量
	 * @throws Exception
	 */
	void updateCopperContractQuantity(String copperSignNo, java.math.BigDecimal actualQuantity,
			java.math.BigDecimal remainingQuantity) throws Exception;

	/* ======================升水表====================== */
	/**
	 * 升水价格详情
	 * 
	 * @param
	 * @throws
	 */
	AscendingWater getAscendingWaterById(Integer id) throws Exception;

	/**
	 * 升水价格详情列表
	 * 
	 * @param
	 * @throws
	 * @return
	 */
	AscendingWaterPageDTO ascendingWaterListByPage(PageInfo pageInfo, String customerCode, String copperCondition,
			String applyDateStart, String applyDateEnd) throws Exception;

	/**
	 * 新增升水价格
	 * 
	 * @param
	 * @throws
	 * @return int
	 */
	int insertAscendingWater(AscendingWater ascendingWater) throws Exception;

	/**
	 * 更新升水价格详情
	 * 
	 * @param
	 * @throws
	 * @return
	 */
	void updateAscendingWater(AscendingWater ascendingWater) throws Exception;

	/**
	 * 删除升水价格
	 * 
	 * @param
	 * @throws
	 * @return
	 */
	void deleteAscendingWater(Integer id) throws Exception;

	/**
	 * 根据客户代码和到货日期查询升水表数据（用于一般铜的铜条件和升水单价获取）
	 * 
	 * @param customerCode 客户代码
	 * @param arrivalDate  到货日期
	 * @throws Exception
	 * @return 升水表数据列表
	 */
	List<AscendingWater> getAscendingWaterByCustomerAndDate(String customerCode, String arrivalDate) throws Exception;

}