package com.hongru.mapper.businessOps;

import com.baomidou.mybatisplus.mapper.BaseMapper;
import com.hongru.entity.businessOps.SalesAmount;
import com.hongru.pojo.dto.CustomerSalesRevenueDTO;
import com.hongru.pojo.dto.DepartmentalSalesRevenueDTO;
import com.hongru.pojo.dto.MonthlyShipmentDetailDTO;
import com.hongru.pojo.dto.MonthlyShipmentPerformanceDTO;
import com.hongru.pojo.dto.SalesLoginListDTO;
import com.hongru.pojo.dto.ShipmentSalesDiffDTO;
import com.hongru.pojo.dto.UserMarginDTO;
import com.hongru.pojo.dto.ZeroBaseReportDTO;
import com.hongru.support.page.PageInfo;

import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface SalesAmountMapper extends BaseMapper<SalesAmount> {

        /**
         * 查询部门销售额数据
         * 
         * @param startDate 开始日期
         * @param endDate   结束日期
         * @return 部门销售额数据列表
         */
        List<DepartmentalSalesRevenueDTO> listDepartmentalSalesRevenue(
                        @Param("startDate") String startDate,
                        @Param("endDate") String endDate);

        /**
         * 查询客户销售额数据
         * 
         * @param startDate 开始日期
         * @param endDate   结束日期
         * @return 客户销售额数据列表
         */
        List<CustomerSalesRevenueDTO> listCustomerSalesRevenue(
                        @Param("startDate") String startDate,
                        @Param("endDate") String endDate);

        /**
         * 查询所有产品中分类
         * 
         * @return 产品中分类列表
         */
        List<String> selectDistinctProductMiddleCategory();

        /**
         * 查询出货和销售额差数据
         *
         * @param startDate 开始日期
         * @param endDate   结束日期
         * @return 出货和销售额差数据列表
         */
        List<ShipmentSalesDiffDTO> listShipmentSalesDiff(
                        @Param("startDate") String startDate,
                        @Param("endDate") String endDate);

        /**
         * 根据年月查询出货和销售额差数据
         * 计算选择年月的上个月销售额登录数据与当前年月支付请求数据的差额
         *
         * @param yearMonth 年月（格式：yyyy-MM）
         * @return 出货和销售额差数据列表
         */
        List<ShipmentSalesDiffDTO> listShipmentSalesDiffByYearMonth(@Param("yearMonth") String yearMonth);

        /**
         * 查询月出货明细数据
         * 
         * @param startDate 开始日期
         * @param endDate   结束日期
         * @return 月出货明细数据列表
         */
        List<MonthlyShipmentDetailDTO> listMonthlyShipmentDetail(
                        @Param("startDate") String startDate,
                        @Param("endDate") String endDate);

        /**
         * 查询月出货实绩数据
         *
         * @param startDate 开始日期
         * @param endDate   结束日期
         * @return 月出货实绩数据列表
         */
        List<MonthlyShipmentPerformanceDTO> listMonthlyShipmentPerformance(
                        @Param("startDate") String startDate,
                        @Param("endDate") String endDate);

        /**
         * 分页查询销售额登录列表
         *
         * @param pageInfo        分页信息
         * @param customerCode    客户代码
         * @param salesAmountDate 销售额日
         * @return 销售额登录列表
         */
        List<SalesLoginListDTO> listSalesLoginByPage(
                        @Param("pageInfo") PageInfo pageInfo,
                        @Param("customerCode") String customerCode,
                        @Param("salesAmountDate") String salesAmountDate);

        /**
         * 查询销售额登录列表总数
         *
         * @param customerCode    客户代码
         * @param salesAmountDate 销售额日
         * @return 总数
         */
        Integer listSalesLoginByPageCount(
                        @Param("customerCode") String customerCode,
                        @Param("salesAmountDate") String salesAmountDate);

        /**
         * 分页查询销售额登录列表（按用户过滤）
         *
         * @param pageInfo     分页信息
         * @param customerCode 客户代码
         * @param startDate    开始日期
         * @param endDate      结束日期
         * @param creatorName  创建者名称
         * @return 销售额登录列表
         */
        List<SalesLoginListDTO> listSalesLoginByPageWithUser(
                        @Param("pageInfo") PageInfo pageInfo,
                        @Param("customerCode") String customerCode,
                        @Param("startDate") String startDate,
                        @Param("endDate") String endDate,
                        @Param("creatorName") String creatorName);

        /**
         * 查询销售额登录列表总数（按用户过滤）
         *
         * @param customerCode 客户代码
         * @param startDate    开始日期
         * @param endDate      结束日期
         * @param creatorName  创建者名称
         * @return 总数
         */
        Integer listSalesLoginByPageCountWithUser(
                        @Param("customerCode") String customerCode,
                        @Param("startDate") String startDate,
                        @Param("endDate") String endDate,
                        @Param("creatorName") String creatorName);

        /**
         * 根据客户代码获取客户详情信息（货币、税率等）
         *
         * @param customerCode 客户代码
         * @return 客户详情信息
         */
        Map<String, Object> getCustomerDetailsInfo(@Param("customerCode") String customerCode);

        /**
         * 根据客户代码获取出库数据用于生成销售额
         *
         * @param customerCode 客户代码
         * @return 出库数据列表
         */
        List<Map<String, Object>> getOutboundDataForSalesAmount(@Param("customerCode") String customerCode);

        /**
         * 根据客户代码和到货日期获取出库数据用于生成销售额
         *
         * @param customerCode 客户代码
         * @param arrivalDate  到货日期
         * @return 出库数据列表
         */
        List<Map<String, Object>> getOutboundDataForSalesAmountWithArrivalDate(
                        @Param("customerCode") String customerCode,
                        @Param("arrivalDate") String arrivalDate);

        /**
         * 根据客户代码和日期范围获取出库数据用于生成销售额
         *
         * @param customerCode 客户代码
         * @param startDate    开始日期
         * @param endDate      结束日期
         * @return 出库数据列表
         */
        List<Map<String, Object>> getOutboundDataForSalesAmountWithDateRange(
                        @Param("customerCode") String customerCode,
                        @Param("startDate") String startDate,
                        @Param("endDate") String endDate);

        /**
         * 根据出货单No.获取明细数据
         *
         * @param shipmentNo 出货单No.
         * @return 明细数据列表
         */
        List<Map<String, Object>> getDetailsByShipmentNo(@Param("shipmentNo") String shipmentNo);

        /**
         * 根据前缀获取最大销售额NO
         *
         * @param prefix 前缀（如：D250423）
         * @return 最大销售额NO
         */
        String getMaxSalesAmountNoByPrefix(@Param("prefix") String prefix);

        /**
         * 根据销售额NO获取销售额主表数据
         *
         * @param salesAmountNo 销售额NO
         * @return 销售额主表数据
         */
        SalesAmount getSalesAmountByNo(@Param("salesAmountNo") String salesAmountNo);

        /**
         * 根据销售额NO删除销售额数据
         *
         * @param salesAmountNo 销售额NO
         * @return 删除结果
         */
        int deleteBySalesAmountNo(@Param("salesAmountNo") String salesAmountNo);

        /**
         * 检查指定客户在指定到货日是否已登录
         *
         * @param customerCode 客户代码
         * @param arrivalDate  到货日
         * @return 记录数量
         */
        int checkCustomerExists(@Param("customerCode") String customerCode, @Param("arrivalDate") String arrivalDate);

        /**
         * 根据客户代码查询销售额数据用于支付请求
         *
         * @param customerCode 客户代码
         * @return 销售额数据列表
         */
        List<com.hongru.entity.businessOps.PaymentAmountDetail> getSalesDataForPaymentRequest(
                        @Param("customerCode") String customerCode);

        /**
         * 更新销售额明细表的支付请求确认状态
         *
         * @param salesAmountNo 销售额NO
         * @return 更新结果
         */
        int updatePaymentRequestConfirm(@Param("salesAmountNo") String salesAmountNo);

        /**
         * 更新指定明细行的支付请求确认状态
         *
         * @param detailSeq     明细ID
         * @param confirmStatus 确认状态
         * @return 更新结果
         */
        int updatePaymentRequestConfirmByDetail(@Param("detailSeq") String detailSeq,
                        @Param("confirmStatus") String confirmStatus);

        /**
         * 获取交易条件列表
         * 
         * @return 交易条件列表
         */
        List<Map<String, Object>> getTradeConditions();

        /**
         * 获取付款条件列表
         * 
         * @return 付款条件列表
         */
        List<Map<String, Object>> getPayConditions();

        /**
         * 获取运输条件列表
         *
         * @return 运输条件列表
         */
        List<Map<String, Object>> getTransportConditions();

        /**
         * 检查销售额是否已被支付请求登录使用
         *
         * @param salesAmountNo 销售额NO
         * @return true-已被使用，false-未被使用
         */
        boolean isUsedByPaymentRequest(@Param("salesAmountNo") String salesAmountNo);

        /**
         * 查询0-base报表数据
         *
         * @param startAccountingYearMonth 开始会计年月
         * @param endAccountingYearMonth   结束会计年月
         * @return 0-base报表数据列表
         */
        List<ZeroBaseReportDTO> listZeroBaseReport(
                        @Param("startAccountingYearMonth") String startAccountingYearMonth,
                        @Param("endAccountingYearMonth") String endAccountingYearMonth);

        /**
         * 查询User-Margin报表数据
         *
         * @param accountingYearMonth 会计年月
         * @return User-Margin报表数据列表
         */
        List<UserMarginDTO> listUserMargin(@Param("accountingYearMonth") String accountingYearMonth);
}
