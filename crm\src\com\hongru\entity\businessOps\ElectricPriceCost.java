package com.hongru.entity.businessOps;

import java.math.BigDecimal;

import com.baomidou.mybatisplus.annotations.TableField;
import com.baomidou.mybatisplus.annotations.TableId;
import com.baomidou.mybatisplus.annotations.TableName;
import com.baomidou.mybatisplus.enums.IdType;

@TableName("电费用表")//CostPrice
public class ElectricPriceCost {
	/* 状态-正常 */
	public static final short STATE_NORMAL = 0;
	/* 状态-删除 */
	public static final short STATE_DELETED = 9;

	/* 电费计算标记-不分时电费 */
	public static final short CALCULATE_1 = 1;
	/* 电费计算标记-分时电费（峰） */
	public static final short CALCULATE_2 = 2;
	/* 电费计算标记-分时电费（平） */
	public static final short CALCULATE_3 = 3;
	/* 电费计算标记-分时电费（谷） */
	public static final short CALCULATE_4 = 4;

	/* 流水号 */
	@TableId(value="流水号", type= IdType.AUTO)
	protected int costId;
	/* 状态 */
	protected short state;
	/* 年月 */
	protected String yearMonth;
	/* 电费计算标记 */
	protected String calculateTag;
	/* 电费单价 */
	protected BigDecimal eleSinglePrice;
	/* 用电量 */
	protected BigDecimal eleCost;
	/* 电费单价实际 */
	protected BigDecimal eleSinglePriceAct;
	/* 用电量实际 */
	protected BigDecimal eleCostAct;
	/* 年 */
	protected int year;
	/* 月 */
	protected int month;
	/* 创建人标识 */
	protected int creatorId;
	/* 创建人姓名 */
	protected String creatorName;
	/* 创建时间 */
	protected String createdTime;
	/* 最后修改人标识 */
	protected int lastModifierId;
	/* 最后修改人姓名 */
	protected String lastModifierName;
	/* 最后修改时间 */
	protected String lastModifiedTime;

	/* 金额 */
	@TableField(exist = false)
	protected BigDecimal totalPrice;

	/* 电费计算标记Str */
	@TableField(exist = false)
	protected String calculateTagStr;

	public int getCostId() {
		return costId;
	}
	public void setCostId(int costId) {
		this.costId = costId;
	}
	public short getState() {
		return state;
	}
	public void setState(short state) {
		this.state = state;
	}
	public String getYearMonth() {
		return yearMonth;
	}
	public void setYearMonth(String yearMonth) {
		this.yearMonth = yearMonth;
	}
	public String getCalculateTag() {
		return calculateTag;
	}
	public void setCalculateTag(String calculateTag) {
		this.calculateTag = calculateTag;
	}
	public BigDecimal getEleSinglePrice() {
		return eleSinglePrice;
	}
	public void setEleSinglePrice(BigDecimal eleSinglePrice) {
		this.eleSinglePrice = eleSinglePrice;
	}
	public BigDecimal getEleCost() {
		return eleCost;
	}
	public void setEleCost(BigDecimal eleCost) {
		this.eleCost = eleCost;
	}
	public BigDecimal getEleSinglePriceAct() {
		return eleSinglePriceAct;
	}
	public void setEleSinglePriceAct(BigDecimal eleSinglePriceAct) {
		this.eleSinglePriceAct = eleSinglePriceAct;
	}
	public BigDecimal getEleCostAct() {
		return eleCostAct;
	}
	public void setEleCostAct(BigDecimal eleCostAct) {
		this.eleCostAct = eleCostAct;
	}
	public int getYear() {
		return year;
	}
	public void setYear(int year) {
		this.year = year;
	}
	public int getMonth() {
		return month;
	}
	public void setMonth(int month) {
		this.month = month;
	}
	public int getCreatorId() {
		return creatorId;
	}
	public void setCreatorId(int creatorId) {
		this.creatorId = creatorId;
	}
	public String getCreatorName() {
		return creatorName;
	}
	public void setCreatorName(String creatorName) {
		this.creatorName = creatorName;
	}
	public String getCreatedTime() {
		return createdTime;
	}
	public void setCreatedTime(String createdTime) {
		this.createdTime = createdTime;
	}
	public int getLastModifierId() {
		return lastModifierId;
	}
	public void setLastModifierId(int lastModifierId) {
		this.lastModifierId = lastModifierId;
	}
	public String getLastModifierName() {
		return lastModifierName;
	}
	public void setLastModifierName(String lastModifierName) {
		this.lastModifierName = lastModifierName;
	}
	public String getLastModifiedTime() {
		return lastModifiedTime;
	}
	public void setLastModifiedTime(String lastModifiedTime) {
		this.lastModifiedTime = lastModifiedTime;
	}
	public BigDecimal getTotalPrice() {
		return totalPrice;
	}
	public void setTotalPrice(BigDecimal totalPrice) {
		this.totalPrice = totalPrice;
	}

	public String getCalculateTagStr() {
		return calculateTagStr;
	}

	public void setCalculateTagStr(String calculateTagStr) {
		this.calculateTagStr = calculateTagStr;
	}
}