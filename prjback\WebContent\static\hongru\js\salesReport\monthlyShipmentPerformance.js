layui.use(['laydate', 'layer', 'table', 'element', 'form'], function(){
    var laydate = layui.laydate //日期
        ,layer = layui.layer //弹层
        ,table = layui.table //表格
        ,element = layui.element //元素操作
    var form = layui.form;
    
    // 日期范围选择器
    laydate.render({
        elem: '#salesDateRange'
        ,type: 'date'
        ,range: true
    });
    
    // 执行一个 table 实例
    var url = baselocation+'/salesReport/monthlyShipmentPerformance/list';
    table.render({
        elem: '#demo'
        ,height: 'full-170'
        ,url: url //数据接口
        ,cellMinWidth: 80 //全局定义常规单元格的最小宽度
        ,parseData:function(res){ //res 即为原始返回的数据
            return {
                "code": 0, //解析接口状态
                "msg": '', //解析提示文本
                "count": res.total, //解析数据长度
                "data": res.rows //解析数据列表
            };
        }
        ,method:'post'
        ,title: '月出货实绩'
        ,page: false //关闭分页
        ,where:$("#formSearch").serializeJsonObject()
        ,toolbar: '#toolbarDemo' //开启工具栏，此处显示默认图标，可以自定义模板，详见文档
        ,defaultToolbar: ['filter']
        ,totalRow: true //开启合计行
        ,cols: [[ //表头
            {field: 'domesticOrForeign', title: '国内/国外', align:'center', fixed: 'left', templet: function(d){
                // 确保中文显示正常，避免出现乱码
                return d.domesticOrForeign || '';
            }}
            ,{field: 'customerAlias', title: '需求方略称', align:'center'}
            ,{field: 'productMiddleCategory', title: '品目中分类名', align:'center'}
            ,{field: 'productCode', title: '品名', align:'center'}
            ,{field: 'shipmentQuantity', title: '出货数', align:'right', totalRow: true, templet: function(d){
                if(d.shipmentQuantity != null){
                    return parseFloat(d.shipmentQuantity).toFixed(3);
                }else{
                    return "0.000";
                }
            }}
        ]]
    });
    
    //监听头工具栏事件
    table.on('toolbar(test)', function(obj){
        var checkStatus = table.checkStatus(obj.config.id);
        switch(obj.event){
            case 'refresh':
                search();
                break;
        };
    });
});

// 检索
function search(){
    var dateRange = $("#salesDateRange").val();
    var startDate = "";
    var endDate = "";
    
    if (dateRange) {
        var dates = dateRange.split(" - ");
        if (dates.length == 2) {
            startDate = dates[0];
            endDate = dates[1];
        }
    }
    
    layui.table.reload('demo', {
        where: {
            startDate: startDate,
            endDate: endDate
        }
    });
}

// 导出
function exportData(){
    var dateRange = $("#salesDateRange").val();
    var startDate = "";
    var endDate = "";
    
    if (dateRange) {
        var dates = dateRange.split(" - ");
        if (dates.length == 2) {
            startDate = dates[0];
            endDate = dates[1];
        }
    }
    
    // 输出日期范围，用于调试
    console.log("导出数据 - 开始日期: " + startDate + ", 结束日期: " + endDate);
    
    // 创建表单
    var form = $("<form>");
    form.attr('style', 'display:none');
    form.attr('target', '');
    form.attr('method', 'post');
    form.attr('action', baselocation + '/salesReport/monthlyShipmentPerformance/export');
    
    // 添加参数
    var startDateInput = $('<input>');
    startDateInput.attr('type', 'hidden');
    startDateInput.attr('name', 'startDate');
    startDateInput.attr('value', startDate);
    form.append(startDateInput);
    
    var endDateInput = $('<input>');
    endDateInput.attr('type', 'hidden');
    endDateInput.attr('name', 'endDate');
    endDateInput.attr('value', endDate);
    form.append(endDateInput);
    
    // 提交表单
    $('body').append(form);
    form.submit();
    form.remove();
} 