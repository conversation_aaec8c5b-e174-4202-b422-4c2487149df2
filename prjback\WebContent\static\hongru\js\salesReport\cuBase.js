layui.use(['laydate', 'layer', 'table', 'element', 'form'], function(){
    var laydate = layui.laydate //日期
        ,layer = layui.layer //弹层
        ,table = layui.table //表格
        ,element = layui.element //元素操作
    var form = layui.form;
    
    // 日期范围选择器
    laydate.render({
        elem: '#salesDateRange'
        ,type: 'date'
        ,range: true
    });
    
    // 执行一个 table 实例
    var url = baselocation+'/salesReport/cuBase/list';
    table.render({
        elem: '#demo'
        ,height: 'full-170'
        ,url: url //数据接口
        ,cellMinWidth: 80 //全局定义常规单元格的最小宽度
        ,parseData:function(res){ //res 即为原始返回的数据
            return {
                "code": 0, //解析接口状态
                "msg": '', //解析提示文本
                "count": res.total, //解析数据长度
                "data": res.rows //解析数据列表
            };
        }
        ,method:'post'
        ,title: 'Cu Base'
        ,page: false //关闭分页
        ,where:$("#formSearch").serializeJsonObject()
        ,toolbar: '#toolbarDemo' //开启工具栏，此处显示默认图标，可以自定义模板，详见文档
        ,defaultToolbar: ['filter']
        ,totalRow: true //开启合计行
        ,cols: [[ //表头
            {field: 'customerAlias', title: '需求方略称', align:'center', fixed: 'left'}
            ,{field: 'settlementCurrency', title: '标准货币C', align:'center'}
            ,{field: 'copperPrice', title: '铜单价（不含税・功能货币）', align:'right', templet: function(d){
                if(d.copperPrice != null){
                    return parseFloat(d.copperPrice).toFixed(4);
                }else{
                    return "0.0000";
                }
            }}
            ,{field: 'salesQuantity', title: '销售额数', align:'right', totalRow: true, templet: function(d){
                if(d.salesQuantity != null){
                    return parseFloat(d.salesQuantity).toFixed(3);
                }else{
                    return "0.000";
                }
            }}
            ,{field: 'salesAmount', title: '销售额金额（不含税）', align:'right', totalRow: true, templet: function(d){
                if(d.salesAmount != null){
                    return parseFloat(d.salesAmount).toFixed(2);
                }else{
                    return "0.00";
                }
            }}
            ,{field: 'copperAmount', title: '铜金额（不含税・功能货币）', align:'right', totalRow: true, templet: function(d){
                if(d.copperAmount != null){
                    return parseFloat(d.copperAmount).toFixed(2);
                }else{
                    return "0.00";
                }
            }}
        ]]
    });
    
    //监听头工具栏事件
    table.on('toolbar(test)', function(obj){
        var checkStatus = table.checkStatus(obj.config.id);
        switch(obj.event){
            case 'refresh':
                search();
                break;
        };
    });
});

// 检索
function search(){
    var salesDateRange = $("#salesDateRange").val();
    var startDate = '';
    var endDate = '';
    
    // 如果选择了日期范围，则进行拆分
    if (salesDateRange) {
        var dates = salesDateRange.split(' - ');
        startDate = dates[0];
        endDate = dates[1];
    }
    
    // 无论是否选择日期范围，都执行查询
    layui.table.reload('demo', {
        where: {
            startDate: startDate,
            endDate: endDate
        }
    });
}

// 导出
function exportData(){
    var salesDateRange = $("#salesDateRange").val();
    var startDate = '';
    var endDate = '';
    
    // 如果选择了日期范围，则进行拆分
    if (salesDateRange) {
        var dates = salesDateRange.split(' - ');
        startDate = dates[0];
        endDate = dates[1];
    }
    
    // 创建表单
    var form = $("<form>");
    form.attr('style', 'display:none');
    form.attr('target', '');
    form.attr('method', 'post');
    form.attr('action', baselocation + '/salesReport/cuBase/export');
    
    // 添加开始日期参数
    var startDateInput = $('<input>');
    startDateInput.attr('type', 'hidden');
    startDateInput.attr('name', 'startDate');
    startDateInput.attr('value', startDate);
    form.append(startDateInput);
    
    // 添加结束日期参数
    var endDateInput = $('<input>');
    endDateInput.attr('type', 'hidden');
    endDateInput.attr('name', 'endDate');
    endDateInput.attr('value', endDate);
    form.append(endDateInput);
    
    // 提交表单
    $('body').append(form);
    form.submit();
    form.remove();
} 