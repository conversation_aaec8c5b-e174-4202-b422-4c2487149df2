<%@ page language="java" import="java.util.*"
         contentType="text/html; charset=utf-8"%>
<%@ include file="/WEB-INF/layouts/base.jsp"%>
<html>
<head>
    <title>销售额确认详情</title>
    <style>
        .layui-form-label {
            width: 120px;
            padding: 8px 5px;
            text-align: right;
            box-sizing: border-box;
        }
        .layui-input-block {
            margin-left: 130px;
            box-sizing: border-box;
        }
        .layui-form-item {
            display: table;
            width: 100%;
            table-layout: fixed;
            margin-bottom: 15px;
        }
        .layui-inline {
            display: table-cell;
            width: 25%;
            padding-right: 10px;
            box-sizing: border-box;
            float: none;
        }
        .hr-line {
            margin: 15px 0;
            border-top: 1px solid #e6e6e6;
        }
        /* 明细信息部分的特殊处理 */
        .layui-block {
            width: 100%;
            display: block;
        }

        /* 表格容器样式 */
        .detail-table-container {
            width: 100%;
            padding: 0;
            margin-left: 0;
        }

        /* 确保表格宽度100% */
        .layui-table {
            width: 100% !important;
            margin: 0;
        }

        /* 底部按钮居中对齐 */
        .btn-container {
            text-align: center;
            margin-top: 20px;
        }
        .btn-container .layui-btn {
            margin: 0 10px;
        }

        /* 移动端适配 */
        @media screen and (max-width: 768px) {
            .layui-form-item {
                display: block;
            }
            .layui-inline {
                display: block;
                width: 100%;
                padding-right: 0;
                margin-bottom: 10px;
            }
        }

        /* 确保下拉框父元素可见 */
        .layui-table-cell {
            overflow: visible !important;
            height: auto !important;
            white-space: normal;
        }

        .readonly-input {
            background-color: #f5f5f5;
            border: 1px solid #e6e6e6;
        }
    </style>
</head>
<body class="gray-bg">
<div class="wrapper wrapper-content animated fadeInRight">
    <div class="row">
        <div class="col-sm-12">
            <div class="ibox float-e-margins">
                <div class="ibox-content">
                    <form class="layui-form" id="form" action="">
                        <input type="hidden" name="paymentRequestNo" id="paymentRequestNo" value="${paymentRequestNo}">

                        <!-- 基本信息 -->
                        <div class="layui-form-item">
                            <div class="layui-inline layui-col-md3">
                                <label class="layui-form-label">支付请求NO:</label>
                                <div class="layui-input-block">
                                    <input type="text" name="paymentRequestNoDisplay" id="paymentRequestNoDisplay" readonly class="layui-input readonly-input">
                                </div>
                            </div>
                            <div class="layui-inline layui-col-md3">
                                <label class="layui-form-label">支付请求日:</label>
                                <div class="layui-input-block">
                                    <input type="text" name="paymentRequestDate" id="paymentRequestDate" readonly class="layui-input readonly-input">
                                </div>
                            </div>
                            <div class="layui-inline layui-col-md3">
                                <label class="layui-form-label">支付日:</label>
                                <div class="layui-input-block">
                                    <input type="text" name="paymentDate" id="paymentDate" readonly class="layui-input readonly-input">
                                </div>
                            </div>
                            <div class="layui-inline layui-col-md3">
                                <label class="layui-form-label">需求方:</label>
                                <div class="layui-input-block">
                                    <input type="text" name="customerAlias" id="customerAlias" readonly class="layui-input readonly-input">
                                </div>
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <div class="layui-inline layui-col-md3">
                                <label class="layui-form-label">货币:</label>
                                <div class="layui-input-block">
                                    <input type="text" name="currency" id="currency" readonly class="layui-input readonly-input">
                                </div>
                            </div>
                            <div class="layui-inline layui-col-md3">
                                <label class="layui-form-label">税率(%):</label>
                                <div class="layui-input-block">
                                    <input type="text" name="taxCode" id="taxCode" readonly class="layui-input readonly-input">
                                </div>
                            </div>
                            <div class="layui-inline layui-col-md3">
                                <label class="layui-form-label">总请求金额:</label>
                                <div class="layui-input-block">
                                    <input type="text" name="totalAmount" id="totalAmount" readonly class="layui-input readonly-input">
                                </div>
                            </div>
                            <div class="layui-inline layui-col-md3">
                                <label class="layui-form-label">确认状态:</label>
                                <div class="layui-input-block">
                                    <input type="text" name="confirmStatus" id="confirmStatus" readonly class="layui-input readonly-input">
                                </div>
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <div class="layui-inline layui-col-md3">
                                <label class="layui-form-label">确认者:</label>
                                <div class="layui-input-block">
                                    <input type="text" name="confirmer" id="confirmer" readonly class="layui-input readonly-input">
                                </div>
                            </div>
                            <div class="layui-inline layui-col-md3">
                                <label class="layui-form-label">确认时间:</label>
                                <div class="layui-input-block">
                                    <input type="text" name="confirmTime" id="confirmTime" readonly class="layui-input readonly-input">
                                </div>
                            </div>
                        </div>

                        <div class="hr-line"></div>

                        <!-- 明细信息 -->
                        <div class="layui-form-item" style="display: block;">
                            <div class="layui-block" style="width: 100%;">
                                <div class="layui-input-block detail-table-container">
                                    <table class="layui-table" id="detailTable" lay-filter="detailTable"></table>
                                </div>
                            </div>
                        </div>

                        <!-- 操作按钮 -->
                        <div class="btn-container">
                            <button type="button" class="layui-btn layui-btn-normal" id="confirmBtn" style="display: none;">确认</button>
                            <button type="button" class="layui-btn layui-btn-warm" id="cancelConfirmBtn" style="display: none;">取消确认</button>
                            <button type="button" class="layui-btn layui-btn-primary" onclick="closeLayer()">关闭</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<myfooter>
    <script>
        var baselocation = '${ctx}';
    </script>
    <script src="${ctxsta}/hongru/js/order/salesConfirm_detail.js?v=1.1.5"></script>
</myfooter>
</body>
</html>
