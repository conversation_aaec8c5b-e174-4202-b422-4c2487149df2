package com.hongru.controller.order;

import com.hongru.base.BaseController;
import com.hongru.common.result.HrPageResult;
import com.hongru.common.result.HrResult;
import com.hongru.common.security.AuthorizingUser;
import com.hongru.common.util.DateUtils;
import com.hongru.common.util.SingletonLoginUtils;
import com.hongru.common.util.StringUtil;
import com.hongru.constant.CommonReturnCode;
import com.hongru.entity.admin.User;
import com.hongru.entity.businessOps.*;
import com.hongru.entity.sumitomo.Customers;
import com.hongru.entity.sumitomo.Product;
import com.hongru.pojo.dto.*;
import com.hongru.service.admin.IUserService;
import com.hongru.service.businessOps.IOrderService;
import com.hongru.service.businessOps.ISalesCommonService;
import com.hongru.service.sumitomo.ISumitomoService;
import com.hongru.support.page.PageInfo;

import org.apache.commons.collections.CollectionUtils;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileOutputStream;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import static com.hongru.common.util.ServletUtils.getRequest;

@Controller
@RequestMapping(value = "/order")
public class OrderController extends BaseController {
    @Autowired
    private IOrderService orderService;
    @Autowired
    private ISalesCommonService salesCommonService;
    @Autowired
    private IUserService userService;
    @Autowired
    private ISumitomoService sumitomoService;

    /*=================================订单录入页面======================================*/
    /**
     * 跳转订单录入列表页面
     * @param model
     * @throws Exception
     * @return
     */
    @GetMapping("/orderEntry/list/view")
    public String orderEntryListView(Model model) throws Exception{
    	//获取客户列表（需求方）
    	List<Customers> customersList = sumitomoService.listCustomersList();
        //获取合同方信息列表
        List<Customers> contractPartyCustomerList = orderService.getContractPartyCustomerList();

        model.addAttribute("customersList",customersList);
        model.addAttribute("contractPartyCustomerList",contractPartyCustomerList);
        return "/modules/order/orderEntry_list";
    }

    /**
     * 检索订单录入列表
     * @param
     * @throws Exception
     * @return
     */
    @PostMapping("/orderEntry/list")
    @ResponseBody
    public Object orderEntryList(Integer page, Integer limit, String orderStartDate, String orderEndDate, String prepareStartDate, String prepareEndDate, String outboundStartDate, String outboundEndDate, String demandCustomerCode, String contractPartyCustomerCode, String orderType, String status) throws Exception{
    	PageInfo pageInfo = new PageInfo(limit, page);

        // 只能查询本人录入的订单
        AuthorizingUser authorizingUser = SingletonLoginUtils.getUser();
        if (authorizingUser == null) {
            return new HrResult(CommonReturnCode.FAILED, "登录超时，请重新登录");
        }
        
        User user = userService.getById(authorizingUser.getUserId());

        OrderEntryDetailsPageDTO orderEntryDetailsPageDTO = orderService.orderEntryDetailsListByPage(pageInfo, orderStartDate, orderEndDate, prepareStartDate, prepareEndDate, outboundStartDate, outboundEndDate, demandCustomerCode, contractPartyCustomerCode, orderType, status, user.getUserName());

    	return new HrPageResult(orderEntryDetailsPageDTO.getOrderEntryDetailsList(), orderEntryDetailsPageDTO.getPageInfo().getTotal());
    }


    /**
     * 根据订单号前缀获取最大订单号
     * @param orderNoPrefix 订单号前缀 (如：D250423)
     * @throws Exception
     * @return
     */
    @PostMapping("/orderEntry/getMaxOrderNoByOrderNoPrefix")
    @ResponseBody
    public Object getMaxOrderNoByOrderNoPrefix(String orderNoPrefix) throws Exception{
        String maxOrderNo = "";
        try {
            maxOrderNo = orderService.getMaxOrderNoByOrderNoPrefix(orderNoPrefix);
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("根据订单号前缀获取最大订单号异常信息：", e);
            return new HrResult(0, e.getMessage());
        }

        return new HrResult(CommonReturnCode.SUCCESS, maxOrderNo);
    }


    /**
     * 订单录入页面
     *
     * @param model
     * @param mode  模式参数：batch(批量) 或 single(单行)
     * @throws Exception
     * @return
     */
    @GetMapping("/orderEntry/add/view")
    public String orderEntryAddView(Model model, String mode) throws Exception {
    	//获取客户列表（需求方）
    	List<Customers> customersList = sumitomoService.listCustomersList();
        //获取合同方信息列表
        List<Customers> contractPartyCustomerList = orderService.getContractPartyCustomerList();

    	model.addAttribute("customersList",customersList);
        model.addAttribute("contractPartyCustomerList",contractPartyCustomerList);

        // 如果mode为null或为空，默认为batch模式
        if (mode == null || mode.trim().isEmpty()) {
            mode = "batch";
        }

        // 添加mode参数到模型中，用于区分批量新增和单行新增模式
        model.addAttribute("mode", mode);

        // 记录日志，便于调试
        logger.info("订单录入页面 - 模式: " + mode);

        return "/modules/order/orderEntry_add";
    }

    /**
     * 添加订单信息
     * @param orderEntryInfo
     * @throws Exception
     * @return
     */
    @PostMapping("/orderEntry/add")
    @ResponseBody
    public Object orderEntryAdd(OrderEntryDetails orderEntryInfo) throws Exception{
    	try {
            AuthorizingUser authorizingUser = SingletonLoginUtils.getUser();

            if(authorizingUser == null ) {
                return new HrResult(CommonReturnCode.FAILED,"登录超时，请重新登录");
            }

            User user = userService.getById(authorizingUser.getUserId());

            // 获取当前日期年月日字符串
            String nowDate = DateUtils.convertIntToDateString(DateUtils.getCurrentTime(),"yyyy-MM-dd");
            String nowDate2 = DateUtils.convertIntToDateString(DateUtils.getCurrentTime(),"yyyyMMdd");

            // 获取当前日期前缀的最大订单号
            String maxOrderNo = orderService.getMaxOrderNoByOrderNoPrefix("D" + nowDate2.substring(2));

            // 生成当前订单号
            String currentOrderNo = "";
            if (!StringUtil.isStringEmpty(maxOrderNo)) {
                // 如果maxOrderNo不为空，则当前订单号为maxOrderNo加1 (例：D25042301 -> D25042302)
                currentOrderNo = "D" + nowDate2.substring(2) + String.format("%02d",(Integer.parseInt(maxOrderNo.substring(7)) + 1));
            } else {
                // 如果maxOrderNo为空，则当前订单号为D加上当前日期和01 (例：D25042301)
                currentOrderNo = "D" + nowDate2.substring(2) + "01";
            }

            orderEntryInfo.setOrderNo(currentOrderNo);
            orderEntryInfo.setStatus("0");
            orderEntryInfo.setOrderType("0");
            orderEntryInfo.setStronghold("CW");
            orderEntryInfo.setCreatorName(user.getUserName());
            orderEntryInfo.setOrderDate(nowDate);

            orderService.insertOrderEntryInfo(orderEntryInfo);

            // 整合子订单信息
            if (!StringUtil.isStringEmpty(orderEntryInfo.getOrderSerialNum())) {
                List<OrderEntryDetails> subOrders = new ArrayList<>();
                String[] orderSerialNums = orderEntryInfo.getOrderSerialNum().split(",");
                String[] outboundDates = StringUtil.isStringEmpty(orderEntryInfo.getOutboundDate()) ? new String[0] : orderEntryInfo.getOutboundDate().split(",");
                String[] arrivalDates = StringUtil.isStringEmpty(orderEntryInfo.getArrivalDate()) ? new String[0] : orderEntryInfo.getArrivalDate().split(",");
                String[] prepareDates = StringUtil.isStringEmpty(orderEntryInfo.getPrepareDate()) ? new String[0] : orderEntryInfo.getPrepareDate().split(",");
                String[] modelNumbers = StringUtil.isStringEmpty(orderEntryInfo.getModelNumber()) ? new String[0] : orderEntryInfo.getModelNumber().split(",");
                String[] sizes = StringUtil.isStringEmpty(orderEntryInfo.getSize()) ? new String[0] : orderEntryInfo.getSize().split(",");
                String[] wireSpools = StringUtil.isStringEmpty(orderEntryInfo.getWireSpool()) ? new String[0] : orderEntryInfo.getWireSpool().split(",");
                String[] partNumbers = StringUtil.isStringEmpty(orderEntryInfo.getPartNumber()) ? new String[0] : orderEntryInfo.getPartNumber().split(",");
                String[] orderQuantities = StringUtil.isStringEmpty(orderEntryInfo.getOrderQuantity()) ? new String[0] : orderEntryInfo.getOrderQuantity().split(",");
                String[] deliveredQuantities = StringUtil.isStringEmpty(orderEntryInfo.getDeliveredQuantity()) ? new String[0] : orderEntryInfo.getDeliveredQuantity().split(",");
                String[] remainingQuantities = StringUtil.isStringEmpty(orderEntryInfo.getRemainingQuantity()) ? new String[0] : orderEntryInfo.getRemainingQuantity().split(",");
                String[] detailRemarks = StringUtil.isStringEmpty(orderEntryInfo.getDetailRemark()) ? new String[0] : orderEntryInfo.getDetailRemark().split(",");

                for (int i = 0; i < orderSerialNums.length; i++) {
                    OrderEntryDetails subOrder = new OrderEntryDetails();
                    subOrder.setOrderNo(currentOrderNo);
                    // 检查modelNumbers数组是否有足够的元素
                    if (modelNumbers.length > i) {
                        subOrder.setModelNumber(modelNumbers[i]);
                    } else {
                        // 如果没有对应的型号，设置为空字符串
                        subOrder.setModelNumber("");
                    }
                    subOrder.setStatus(OrderEntryDetails.STATE_NORMAL);
                    // TODO 条码逻辑待确认，暂时默认0
                    subOrder.setBarcode("0");
                    subOrder.setCreatorName(user.getUserName());

                    subOrder.setOrderSerialNum(orderSerialNums[i]);

                    if (outboundDates.length >= i + 1) {
                        subOrder.setOutboundDate(outboundDates[i]);
                    }

                    if (arrivalDates.length >= i + 1) {
                        subOrder.setArrivalDate(arrivalDates[i]);
                    }

                    if (prepareDates.length >= i + 1) {
                        subOrder.setPrepareDate(prepareDates[i]);
                    }

                    if (modelNumbers.length >= i + 1) {
                        subOrder.setModelNumber(modelNumbers[i]);
                    }

                    if (sizes.length >= i + 1) {
                        subOrder.setSize(sizes[i]);
                    }

                    if (wireSpools.length >= i + 1) {
                        subOrder.setWireSpool(wireSpools[i]);
                    }

                    if (partNumbers.length >= i + 1) {
                        subOrder.setPartNumber(partNumbers[i]);
                    }

                    if (orderQuantities.length >= i + 1) {
                        subOrder.setOrderQuantity(orderQuantities[i]);
                    }

                    if (deliveredQuantities.length >= i + 1) {
                        subOrder.setDeliveredQuantity(deliveredQuantities[i]);
                    }

                    if (remainingQuantities.length >= i + 1) {
                        subOrder.setRemainingQuantity(remainingQuantities[i]);
                    }

                    if (detailRemarks.length >= i + 1) {
                        subOrder.setDetailRemark(detailRemarks[i]);
                    }

                    subOrders.add(subOrder);
                }

                // 批量新增子订单信息
                orderService.batchInsertOrderEntryDetails(subOrders);
            }

		} catch (Exception e) {
			e.printStackTrace();
			logger.error("订单详情新增异常信息：", e);
			return new HrResult(0, e.getMessage());
		}
		return new HrResult(CommonReturnCode.SUCCESS);
	}

    /**
     * 保存单行订单详情
     *
     * @param orderEntryDetails
     * @throws Exception
     * @return
     */
    @PostMapping("/orderEntry/saveSingleRow")
    @ResponseBody
    public Object saveSingleRow(OrderEntryDetails orderEntryDetails) throws Exception {
        try {
            AuthorizingUser authorizingUser = SingletonLoginUtils.getUser();
            if (authorizingUser == null) {
                return new HrResult(CommonReturnCode.FAILED, "登录超时，请重新登录");
            }
            User user = userService.getById(authorizingUser.getUserId());

            List<OrderEntryDetails> orderEntryDetailsList = Optional.ofNullable(orderService.orderEntryDetailsByOrderNo(orderEntryDetails.getOrderNo())).orElse(new ArrayList<>());

            // 检查是否已有订单号，如果前端传递了有效的订单号，则使用该订单号，不再创建新的订单
            boolean hasValidOrderNo = orderEntryDetails.getOrderNo() != null
                    && !orderEntryDetails.getOrderNo().trim().isEmpty();

            // 新增第一条子订单信息时，同步新增基础订单信息
            if (orderEntryDetailsList.isEmpty() && !hasValidOrderNo) {
                // 获取当前日期年月日字符串
                String nowDate = DateUtils.convertIntToDateString(DateUtils.getCurrentTime(),"yyyy-MM-dd");
                String nowDate2 = DateUtils.convertIntToDateString(DateUtils.getCurrentTime(),"yyyyMMdd");
    
                // 获取当前日期前缀的最大订单号
                String maxOrderNo = orderService.getMaxOrderNoByOrderNoPrefix("D" + nowDate2.substring(2));
    
                // 生成当前订单号
                String currentOrderNo = "";
                if (!StringUtil.isStringEmpty(maxOrderNo)) {
                    // 如果maxOrderNo不为空，则当前订单号为maxOrderNo加1 (例：D25042301 -> D25042302)
                    currentOrderNo = "D" + nowDate2.substring(2) + String.format("%02d",(Integer.parseInt(maxOrderNo.substring(7)) + 1));
                } else {
                    // 如果maxOrderNo为空，则当前订单号为D加上当前日期和01 (例：D25042301)
                    currentOrderNo = "D" + nowDate2.substring(2) + "01";
                }

                orderEntryDetails.setOrderNo(currentOrderNo);

                // 设置基础订单信息
                OrderEntryDetails orderEntryInfo = new OrderEntryDetails();
                BeanUtils.copyProperties(orderEntryDetails, orderEntryInfo);
    
                orderEntryInfo.setOrderNo(currentOrderNo);
                orderEntryInfo.setStatus("0");
                orderEntryInfo.setStronghold("CW");
                orderEntryInfo.setCreatorName(user.getUserName());
                orderEntryInfo.setOrderDate(nowDate);
    
                orderService.insertOrderEntryInfo(orderEntryInfo);

                logger.info("创建新订单: " + currentOrderNo);
            } else if (!orderEntryDetailsList.isEmpty()) {
                orderEntryDetails.setId(orderEntryDetailsList.get(0).getId());
                // 设置更新者姓名
                orderEntryDetails.setUpdaterName(user.getUserName());

                // 如果是修改现有记录，更新订单信息
                orderService.updateOrderEntryInfo(orderEntryDetails);
                logger.info("更新订单信息: " + orderEntryDetails.getOrderNo());
            } else if (hasValidOrderNo) {
                // 如果是添加新明细但已有订单号，不需要创建新订单
                logger.info("使用现有订单号添加明细: " + orderEntryDetails.getOrderNo());
            }

            // 没有详情信息，直接返回结果
            if (StringUtil.isStringEmpty(orderEntryDetails.getOrderSerialNum())) {
                return new HrResult(CommonReturnCode.SUCCESS, orderEntryDetails.getOrderNo());
            }

            if (orderEntryDetails.getOrderNo() == null || orderEntryDetails.getOrderNo().trim().isEmpty()) {
                return new HrResult(CommonReturnCode.FAILED, "订单号不能为空");
            }

//            if (orderEntryDetails.getOrderQuantity() == null || orderEntryDetails.getOrderQuantity() <= 0) {
//                return new HrResult(CommonReturnCode.FAILED, "订单数量必须大于0");
//            }

            // 模拟数据库操作延迟，让用户能感受到保存过程
            // Thread.sleep(500);

            // 模拟数据处理
//            orderEntryDetails.setId(UUID.randomUUID().toString().replace("-", ""));

            if (StringUtil.isStringEmpty(orderEntryDetails.getArrivalDate())) {
                orderEntryDetails.setArrivalDate(null);
            }

            if (StringUtil.isStringEmpty(orderEntryDetails.getOutboundDate())) {
                orderEntryDetails.setOutboundDate(null);
            }
            
            if (StringUtil.isStringEmpty(orderEntryDetails.getPrepareDate())) {
                orderEntryDetails.setPrepareDate(null);
            }

            if (!orderEntryDetailsList.isEmpty() && !StringUtil.isStringEmpty(orderEntryDetails.getOrderSerialNum()) && orderEntryDetailsList.stream().anyMatch(o -> orderEntryDetails.getOrderSerialNum().equals(o.getOrderSerialNum()))) {
                orderService.updateOrderEntryDetail(orderEntryDetails);
            } else {
                orderEntryDetails.setStatus("0"); // 有效
                orderEntryDetails.setCreatorName(user.getUserName());
    
                // 实际保存数据到数据库
                orderService.batchInsertOrderEntryDetails(Arrays.asList(orderEntryDetails));
            }

            return new HrResult(CommonReturnCode.SUCCESS, orderEntryDetails.getOrderNo());
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("单行订单详情保存异常信息：", e);
            return new HrResult(CommonReturnCode.FAILED, "保存失败：" + e.getMessage());
        }
    }

    /**
     * 编辑订单详情页面
     * @param model
     * @param id 订单ID
     * @param status 订单状态
     * @throws Exception
     * @return
     */
    @GetMapping("/orderEntry/edit/view")
    public String orderEntryEditView(Model model,Integer id,String status) throws Exception{
    	//获取客户列表（需求方）
    	List<Customers> customersList = sumitomoService.listCustomersList();
        //获取合同方信息列表
        List<Customers> contractPartyCustomerList = orderService.getContractPartyCustomerList();

    	model.addAttribute("customersList",customersList);
        model.addAttribute("contractPartyCustomerList",contractPartyCustomerList);

        // 获取订单详情
        List<OrderEntryDetails> orderEntryDetails = Optional.ofNullable(orderService.getOrderEntryDetailsById(id, status)).orElse(new ArrayList<>());

        if (!orderEntryDetails.isEmpty()) {
            OrderEntryDetails orderEntryInfo = orderEntryDetails.get(0);

            // 整合子订单信息
            List<OrderEntryDetails> subOrders = new ArrayList<>();
            for(OrderEntryDetails orderEntryDetail : orderEntryDetails) {
                if (!StringUtil.isStringEmpty(orderEntryDetail.getOrderSerialNum())) {
                    BigDecimal orderQuantity = StringUtil.isStringEmpty(orderEntryDetail.getOrderQuantity()) ? BigDecimal.ZERO : new BigDecimal(orderEntryDetail.getOrderQuantity());
                    BigDecimal deliveredQuantity = StringUtil.isStringEmpty(orderEntryDetail.getDeliveredQuantity()) ? BigDecimal.ZERO : new BigDecimal(orderEntryDetail.getDeliveredQuantity());

                    orderEntryDetail.setRemainingQuantity(orderQuantity.subtract(deliveredQuantity).toString());

                    subOrders.add(orderEntryDetail);
                }
            }

            // 根据订单序号排序
            subOrders.sort(Comparator.comparing(OrderEntryDetails::getOrderSerialNum));

            orderEntryInfo.setSubOrders(subOrders);

            model.addAttribute("orderEntryDetails",orderEntryInfo);
        }
        return "/modules/order/orderEntry_modify";
    }

    /**
     * 编辑订单信息
     * @param model
     * @param orderEntryInfo
     * @throws Exception
     * @return
     */
    @PostMapping("/orderEntry/edit")
    @ResponseBody
    public Object orderEntryEdit(OrderEntryDetails orderEntryInfo) throws Exception{
        try {
        	 AuthorizingUser authorizingUser = SingletonLoginUtils.getUser();
	         if(authorizingUser == null ) {
	             return new HrResult(CommonReturnCode.FAILED,"登录超时，请重新登录");
	         }

             User user = userService.getById(authorizingUser.getUserId());

             List<OrderEntryDetails> orderEntryDetailsList = Optional.ofNullable(orderService.getOrderEntryDetailsById(orderEntryInfo.getId(), null)).orElse(new ArrayList<>());

             // 设置更新者姓名
             orderEntryInfo.setUpdaterName(user.getUserName());

             // 添加调试日志
             logger.info("更新订单信息 - ID: " + orderEntryInfo.getId() +
                     ", 需求方: " + orderEntryInfo.getDemandCustomerCode() +
                     ", 签约方: " + orderEntryInfo.getContractPartyCustomerCode() +
                     ", 客户订单号: " + orderEntryInfo.getCustomerOrderNo());

             // 检查必填字段
             if (orderEntryInfo.getDemandCustomerCode() == null
                     || orderEntryInfo.getDemandCustomerCode().trim().isEmpty()) {
                 logger.error("需求方字段为空，无法更新订单信息");
                 return new HrResult(CommonReturnCode.FAILED, "需求方不能为空");
             }

             if (orderEntryInfo.getContractPartyCustomerCode() == null
                     || orderEntryInfo.getContractPartyCustomerCode().trim().isEmpty()) {
                 logger.error("签约方字段为空，无法更新订单信息");
                 return new HrResult(CommonReturnCode.FAILED, "签约方不能为空");
             }

             orderService.updateOrderEntryInfo(orderEntryInfo);

              // 整合子订单信息
            if (!StringUtil.isStringEmpty(orderEntryInfo.getOrderSerialNum())) {
                List<OrderEntryDetails> subOrders = new ArrayList<>();
                String[] orderSerialNums = orderEntryInfo.getOrderSerialNum().split(",");
                String[] outboundDates = StringUtil.isStringEmpty(orderEntryInfo.getOutboundDate()) ? new String[0] : orderEntryInfo.getOutboundDate().split(",");
                String[] arrivalDates = StringUtil.isStringEmpty(orderEntryInfo.getArrivalDate()) ? new String[0] : orderEntryInfo.getArrivalDate().split(",");
                String[] prepareDates = StringUtil.isStringEmpty(orderEntryInfo.getPrepareDate()) ? new String[0] : orderEntryInfo.getPrepareDate().split(",");
                String[] modelNumbers = StringUtil.isStringEmpty(orderEntryInfo.getModelNumber()) ? new String[0] : orderEntryInfo.getModelNumber().split(",");
                String[] sizes = StringUtil.isStringEmpty(orderEntryInfo.getSize()) ? new String[0] : orderEntryInfo.getSize().split(",");
                String[] wireSpools = StringUtil.isStringEmpty(orderEntryInfo.getWireSpool()) ? new String[0] : orderEntryInfo.getWireSpool().split(",");
                String[] partNumbers = StringUtil.isStringEmpty(orderEntryInfo.getPartNumber()) ? new String[0] : orderEntryInfo.getPartNumber().split(",");
                String[] orderQuantities = StringUtil.isStringEmpty(orderEntryInfo.getOrderQuantity()) ? new String[0] : orderEntryInfo.getOrderQuantity().split(",");
                String[] deliveredQuantities = StringUtil.isStringEmpty(orderEntryInfo.getDeliveredQuantity()) ? new String[0] : orderEntryInfo.getDeliveredQuantity().split(",");
                String[] remainingQuantities = StringUtil.isStringEmpty(orderEntryInfo.getRemainingQuantity()) ? new String[0] : orderEntryInfo.getRemainingQuantity().split(",");
                String[] detailRemarks = StringUtil.isStringEmpty(orderEntryInfo.getDetailRemark()) ? new String[0] : orderEntryInfo.getDetailRemark().split(",");

                for (int i = 0; i < orderSerialNums.length; i++) {
                    OrderEntryDetails subOrder = new OrderEntryDetails();
                    subOrder.setOrderNo(orderEntryInfo.getOrderNo());
                    // 检查modelNumbers数组是否有足够的元素
                    if (modelNumbers.length > i) {
                        subOrder.setModelNumber(modelNumbers[i]);
                    } else {
                        // 如果没有对应的型号，设置为空字符串
                        subOrder.setModelNumber("");
                    }
                    subOrder.setStatus(OrderEntryDetails.STATE_NORMAL);
                    // TODO 条码逻辑待确认，暂时默认0
                    subOrder.setBarcode("0");
                    subOrder.setCreatorName(user.getUserName());
                    subOrder.setUpdaterName(user.getUserName());

                    subOrder.setOrderSerialNum(orderSerialNums[i]);

                    if (outboundDates.length >= i + 1) {
                        subOrder.setOutboundDate(outboundDates[i]);
                    }

                    if (arrivalDates.length >= i + 1) {
                        subOrder.setArrivalDate(arrivalDates[i]);
                    }

                    if (prepareDates.length >= i + 1) {
                        subOrder.setPrepareDate(prepareDates[i]);
                    }

                    if (modelNumbers.length >= i + 1) {
                        subOrder.setModelNumber(modelNumbers[i]);
                    }

                    if (sizes.length >= i + 1) {
                        subOrder.setSize(sizes[i]);
                    }

                    if (wireSpools.length >= i + 1) {
                        subOrder.setWireSpool(wireSpools[i]);
                    }

                    if (partNumbers.length >= i + 1) {
                        subOrder.setPartNumber(partNumbers[i]);
                    }

                    if (orderQuantities.length >= i + 1) {
                        subOrder.setOrderQuantity(orderQuantities[i]);
                    }

                    if (deliveredQuantities.length >= i + 1) {
                        subOrder.setDeliveredQuantity(deliveredQuantities[i]);
                    }

                    if (remainingQuantities.length >= i + 1) {
                        subOrder.setRemainingQuantity(remainingQuantities[i]);
                    }

                    if (detailRemarks.length >= i + 1) {
                        subOrder.setDetailRemark(detailRemarks[i]);
                    }

                    subOrders.add(subOrder);
                }

                List<OrderEntryDetails> newSubOrders = new ArrayList<>();
                for (OrderEntryDetails subOrder : subOrders) {
                    if (!orderEntryDetailsList.isEmpty() && !StringUtil.isStringEmpty(subOrder.getOrderSerialNum()) && orderEntryDetailsList.stream().anyMatch(o -> subOrder.getOrderSerialNum().equals(o.getOrderSerialNum()))) {
                        // 设置更新者姓名
                        subOrder.setUpdaterName(user.getUserName());
                        orderService.updateOrderEntryDetail(subOrder);
                    } else {
                        newSubOrders.add(subOrder);
                    }
                }

                if (!newSubOrders.isEmpty()) {
                    // TODO 临时测试用 处理必填字段，确保其不为空
//                    for (OrderEntryDetails detail : newSubOrders) {
//                        // 当前日期字符串
//                        String currentDate = DateUtils.convertIntToDateString(DateUtils.getCurrentTime(), "yyyy-MM-dd");
//
//                        // 如果出库日期为空，设置为当前日期
//                        if (detail.getOutboundDate() == null || detail.getOutboundDate().trim().isEmpty()) {
//                            detail.setOutboundDate(currentDate);
//                        }
//
//                        // 如果到货日期为空，设置为当前日期
//                        if (detail.getArrivalDate() == null || detail.getArrivalDate().trim().isEmpty()) {
//                            detail.setArrivalDate(currentDate);
//                        }
//
//                        // 如果准备日期为空，设置为当前日期
//                        if (detail.getPrepareDate() == null || detail.getPrepareDate().trim().isEmpty()) {
//                            detail.setPrepareDate(currentDate);
//                        }
//
//                        // 如果尺寸为空，设置为默认值
//                        if (detail.getSize() == null || detail.getSize().trim().isEmpty()) {
//                            detail.setSize("0");
//                        }
//
//                        // 如果线盘名称为空，设置为默认值
//                        if (detail.getWireSpool() == null || detail.getWireSpool().trim().isEmpty()) {
//                            detail.setWireSpool("0");
//                        }
//
//                        // 注意：OrderEntryDetails类中没有productCode属性，但数据库表中有产品代码字段
//                        // 这里不需要设置产品代码，因为在SQL语句中会直接使用默认值
//                    }

                    // 批量新增子订单信息
                    orderService.batchInsertOrderEntryDetails(newSubOrders);
                }

                // 获取所有最新的订单序号
                List<String> orderSerialNumList = subOrders.stream()
                        .map(OrderEntryDetails::getOrderSerialNum)
                        .filter(serialNum -> serialNum != null && !serialNum.isEmpty())
                        .collect(Collectors.toList());

                // 整合需要删除的子订单信息
                orderEntryDetailsList.removeIf(detail -> detail.getOrderSerialNum() != null && orderSerialNumList.contains(detail.getOrderSerialNum()));

                // 批量删除
                for (OrderEntryDetails detail : orderEntryDetailsList) {
                    orderService.deleteOrderEntryDetail(detail.getOrderNo(), detail.getOrderSerialNum());
                }
            }
        }catch (Exception e){
            e.printStackTrace();
            logger.error("订单详情修改异常信息：", e);
            return new HrResult(0,e.getMessage());
        }
        return new HrResult(CommonReturnCode.SUCCESS);
    }

    /**
     * 订单详情页面
     * @param id 订单ID
     * @param status 订单状态
     * @throws Exception
     * @return
     */
    @GetMapping("/orderEntry/detail/view")
    public String orderEntryDetailView(Model model,Integer id,String status) throws Exception{
        //获取客户列表（需求方）
    	List<Customers> customersList = sumitomoService.listCustomersList();
        //获取合同方信息列表
        List<Customers> contractPartyCustomerList = orderService.getContractPartyCustomerList();

    	model.addAttribute("customersList",customersList);
        model.addAttribute("contractPartyCustomerList",contractPartyCustomerList);

        // 获取订单详情
        List<OrderEntryDetails> orderEntryDetails = Optional.ofNullable(orderService.getOrderEntryDetailsById(id, status)).orElse(new ArrayList<>());

        if (!orderEntryDetails.isEmpty()) {
            OrderEntryDetails orderEntryInfo = orderEntryDetails.get(0);

            // 整合子订单信息
            List<OrderEntryDetails> subOrders = new ArrayList<>();
            for(OrderEntryDetails orderEntryDetail : orderEntryDetails) {
                // 确保只添加有效的子订单记录（订单序号不为空且不为空字符串）
                if (orderEntryDetail.getOrderSerialNum() != null && !orderEntryDetail.getOrderSerialNum().trim().isEmpty()) {
                    BigDecimal orderQuantity = StringUtil.isStringEmpty(orderEntryDetail.getOrderQuantity()) ? BigDecimal.ZERO : new BigDecimal(orderEntryDetail.getOrderQuantity());
                    BigDecimal deliveredQuantity = StringUtil.isStringEmpty(orderEntryDetail.getDeliveredQuantity()) ? BigDecimal.ZERO : new BigDecimal(orderEntryDetail.getDeliveredQuantity());

                    orderEntryDetail.setRemainingQuantity(orderQuantity.subtract(deliveredQuantity).toString());

                    subOrders.add(orderEntryDetail);
                }
            }

            // 根据订单序号排序
            subOrders.sort(Comparator.comparing(OrderEntryDetails::getOrderSerialNum));

            // 记录日志，便于调试
            logger.info("订单详情页面 - 订单ID: " + id + ", 子订单数量: " + subOrders.size());

            orderEntryInfo.setSubOrders(subOrders);

            model.addAttribute("orderEntryDetails",orderEntryInfo);
        }

        return "/modules/order/orderEntry_detail";
    }

    /**
     * 删除订单基础信息及详情信息
     * @param id
     * @throws Exception
     * @return
     */
    @PostMapping("/orderEntry/remove")
    @ResponseBody
    public Object orderEntryRemove(Integer id) throws Exception{
        try {
            List<OrderEntryDetails> orderEntryDetailsList = Optional.ofNullable(orderService.getOrderEntryDetailsById(id, null)).orElse(new ArrayList<>());

            if (!orderEntryDetailsList.isEmpty()) {
                OrderEntryDetails orderEntryDetails = orderEntryDetailsList.get(0);

                orderService.deleteOrderEntryInfo(id);
                orderService.deleteOrderEntryDetail(orderEntryDetails.getOrderNo(), null);
            }
        }catch (Exception e){
            e.printStackTrace();
            return new HrResult(0,e.getMessage());
        }
        return new HrResult(CommonReturnCode.SUCCESS);
    }

    /**
     * 删除子订单
     * @param orderNo 订单号
     * @param orderSerialNum 订单序号
     * @throws Exception
     * @return
     */
    @PostMapping("/orderEntry/removeSubOrder")
    @ResponseBody
    public Object orderEntryRemoveSubOrder(String orderNo, String orderSerialNum) throws Exception{
        try {
            orderService.deleteOrderEntryDetail(orderNo, orderSerialNum);
        }catch (Exception e){
            e.printStackTrace();
            return new HrResult(0,e.getMessage());
        }
        return new HrResult(CommonReturnCode.SUCCESS);
    }

    /**
     * 获取铜合同列表
     * 
     * @param customerCode       客户代码
     * @param copperContractType 铜合同类别
     * @return
     */
    @PostMapping("/orderEntry/getCopperContractList")
    @ResponseBody
    public Object getCopperContractList(String customerCode, String copperContractType) throws Exception {
        try {
            List<String> contractList = orderService.getCopperContractList(customerCode, copperContractType);
            return new HrResult(CommonReturnCode.SUCCESS, contractList);
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("获取铜合同列表异常：", e);
            return new HrResult(CommonReturnCode.FAILED, "获取铜合同列表失败：" + e.getMessage());
        }
    }

    /**
     * 获取铜合同详情
     * 
     * @param copperContractNo 铜合同No
     * @return
     */
    @PostMapping("/orderEntry/getCopperContractDetail")
    @ResponseBody
    public Object getCopperContractDetail(String copperContractNo) throws Exception {
        try {
            OrderEntryDetails contractDetail = orderService.getCopperContractDetail(copperContractNo);
            return new HrResult(CommonReturnCode.SUCCESS, contractDetail);
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("获取铜合同详情异常：", e);
            return new HrResult(CommonReturnCode.FAILED, "获取铜合同详情失败：" + e.getMessage());
        }
    }

    /**
     * 获取铜条件列表
     *
     * @return
     */
    @PostMapping("/orderEntry/getCopperConditionList")
    @ResponseBody
    public Object getCopperConditionList() throws Exception {
        try {
            List<Map<String, Object>> conditionList = orderService.getCopperConditionList();
            return new HrResult(CommonReturnCode.SUCCESS, conditionList);
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("获取铜条件列表异常：", e);
            return new HrResult(CommonReturnCode.FAILED, "获取铜条件列表失败：" + e.getMessage());
        }
    }

    /**
     * 计算接单金额
     * 
     * @param conversionRate 换算率
     * @param copperBase     铜基本
     * @param premium        升水
     * @param zeroBase       零基础
     * @param orderQuantity  订单数量
     * @return
     */
    @PostMapping("/orderEntry/calculateOrderAmount")
    @ResponseBody
    public Object calculateOrderAmount(String conversionRate, String copperBase, String premium, String zeroBase,
            String orderQuantity) throws Exception {
        try {
            Map<String, Object> result = new HashMap<>();

            if (StringUtil.isStringEmpty(conversionRate) || StringUtil.isStringEmpty(copperBase) ||
                    StringUtil.isStringEmpty(premium) || StringUtil.isStringEmpty(zeroBase) ||
                    StringUtil.isStringEmpty(orderQuantity)) {
                return new HrResult(CommonReturnCode.FAILED, "计算参数不能为空");
            }

            BigDecimal rate = new BigDecimal(conversionRate);
            BigDecimal base = new BigDecimal(copperBase);
            BigDecimal premiumValue = new BigDecimal(premium);
            BigDecimal zero = new BigDecimal(zeroBase);
            BigDecimal quantity = new BigDecimal(orderQuantity);

            // 换算后铜单价 = （铜base + 升水）* 换算率
            BigDecimal convertedCopperPrice = base.add(premiumValue).multiply(rate);

            // 接单单价 = 换算后铜单价 + 0base
            BigDecimal orderUnitPrice = convertedCopperPrice.add(zero);

            // 接单金额 = 接单单价 * 订单数量
            BigDecimal orderAmount = orderUnitPrice.multiply(quantity);

            result.put("convertedCopperPrice", convertedCopperPrice);
            result.put("orderUnitPrice", orderUnitPrice);
            result.put("orderAmount", orderAmount);

            return new HrResult(CommonReturnCode.SUCCESS, result);
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("计算接单金额异常：", e);
            return new HrResult(CommonReturnCode.FAILED, "计算接单金额失败：" + e.getMessage());
        }
    }

    /**
     * 导出订单详情
     * @param id
     * @throws Exception
     * @return
     */
    @PostMapping("/orderEntry/export")
    @ResponseBody
    public Object orderEntryExport(HttpServletResponse response) throws Exception{
        try {
            // // 获取订单详情
            // List<OrderEntryDetails> orderEntryDetails = Optional.ofNullable(orderService.getOrderEntryDetailsById(id)).orElse(new ArrayList<>());

            // if (orderEntryDetails.isEmpty()) {
            //     return new HrResult(0, "没有需要导出的数据");
            //}

            // 导出excel
            String fileName = "订单记录_110.xlsx";
            String filePath = "D:/" + fileName;

            // 创建工作簿
            Workbook workbook = new XSSFWorkbook();
            Sheet sheet = workbook.createSheet("订单记录");

            // 设置默认行数
            sheet.setDefaultRowHeight((short) 20);


            // 前三行的右上角需要显示「记录号、修改号、文件号」
            Row headerRow = sheet.createRow(0);
            headerRow.setHeight((short) 600);
            // 在右上角显示记录号、修改号、文件号
            int lastColumnIndex = 13;
            headerRow.createCell(lastColumnIndex).setCellValue("记录号：20251101");

            Row headerRow1 = sheet.createRow(1);
            headerRow1.setHeight((short) 600);
            // 修改号
            headerRow1.createCell(lastColumnIndex).setCellValue("修改号：1");

            Row headerRow2 = sheet.createRow(2);
            headerRow2.setHeight((short) 600);
            // 文件号
            headerRow2.createCell(lastColumnIndex).setCellValue("文件号：1");

            // 第四行需要居中显示一个加粗字体的标题
            Row titleRow = sheet.createRow(3);
            titleRow.setHeight((short) 600);
            titleRow.setHeightInPoints(20);

            // 创建单元格并设置值
            Cell titleCell = titleRow.createCell(0);
            titleCell.setCellValue("订单记录");

            // 创建居中加粗样式
            CellStyle centerStyle = workbook.createCellStyle();
            centerStyle.setAlignment(HorizontalAlignment.CENTER);  // 水平居中
            centerStyle.setVerticalAlignment(VerticalAlignment.CENTER);  // 垂直居中

            // 创建居中加粗样式
            CellStyle centerBoldStyle = workbook.createCellStyle();
            centerBoldStyle.setAlignment(HorizontalAlignment.CENTER);  // 水平居中
            centerBoldStyle.setVerticalAlignment(VerticalAlignment.CENTER);  // 垂直居中

            // 创建加粗字体
            Font boldFont = workbook.createFont();
            boldFont.setBold(true);
            boldFont.setFontHeightInPoints((short) 22);
            centerBoldStyle.setFont(boldFont);

            // 合并单元格使标题跨整个表格
            sheet.addMergedRegion(new CellRangeAddress(3, 3, 0, 13));

            // 应用样式
            titleCell.setCellStyle(centerBoldStyle);

            // 创建表头（文字居中）
            Row headerRow4 = sheet.createRow(4);
            headerRow4.setHeight((short) 600);
            // 创建单元格并设置值
            Cell headerCell = headerRow4.createCell(0);
            headerCell.setCellValue("客户");
            headerCell.setCellStyle(centerStyle);
            headerCell = headerRow4.createCell(1);
            headerCell.setCellValue("出库日期");
            headerCell.setCellStyle(centerStyle);
            headerCell = headerRow4.createCell(2);
            headerCell.setCellValue("准备日期");
            headerCell.setCellStyle(centerStyle);
            headerCell = headerRow4.createCell(3);
            headerCell.setCellValue("到货日期");
            headerCell.setCellStyle(centerStyle);
            headerCell = headerRow4.createCell(4);
            headerCell.setCellValue("型号");
            headerCell.setCellStyle(centerStyle);
            headerCell = headerRow4.createCell(5);
            headerCell.setCellValue("尺寸");
            headerCell.setCellStyle(centerStyle);
            headerCell = headerRow4.createCell(6);
            headerCell.setCellValue("线盘");
            headerCell.setCellStyle(centerStyle);
            headerCell = headerRow4.createCell(7);
            headerCell.setCellValue("部品编号");
            headerCell.setCellStyle(centerStyle);
            headerCell = headerRow4.createCell(8);
            headerCell.setCellValue("订单号");
            headerCell.setCellStyle(centerStyle);
            headerCell = headerRow4.createCell(9);
            headerCell.setCellValue("订单数量");
            headerCell.setCellStyle(centerStyle);
            headerCell = headerRow4.createCell(10);
            headerCell.setCellValue("已送数量");
            headerCell.setCellStyle(centerStyle);
            headerCell = headerRow4.createCell(11);
            headerCell.setCellValue("剩余数量");
            headerCell.setCellStyle(centerStyle);
            headerCell = headerRow4.createCell(12);
            headerCell.setCellValue("备注");
            headerCell.setCellStyle(centerStyle);

            // 设置列宽
            for(int i = 0; i <= 12; i++) {
               sheet.setColumnWidth(i, 3500);
               if (i == 3) {
                   sheet.setColumnWidth(i, 5000);
               } else if (i == 12) {
                   sheet.setColumnWidth(i, 10000);
               }
            }

            // 创建数据行
            for(int i = 0; i <= 10; i++) {
                Row dataRow = sheet.createRow(i + 5);
                dataRow.setHeight((short) 600);
                Cell dataCell = dataRow.createCell(0);
                dataCell.setCellValue("客户" + i);
                dataCell.setCellStyle(centerStyle);
                dataCell = dataRow.createCell(1);
                dataCell.setCellValue("出库日期" + i);
                dataCell.setCellStyle(centerStyle);
                dataCell = dataRow.createCell(2);
                dataCell.setCellValue("准备日期" + i);
                dataCell.setCellStyle(centerStyle);
                dataCell = dataRow.createCell(3);
                dataCell.setCellValue(DateUtils.convertIntToDateString(DateUtils.getCurrentTime(),"yyyy-MM-dd HH:mm:ss"));
                dataCell.setCellStyle(centerStyle);
                dataCell = dataRow.createCell(4);
                dataCell.setCellValue("型号" + i);
                dataCell.setCellStyle(centerStyle);
                dataCell = dataRow.createCell(5);
                dataCell.setCellValue(String.valueOf(i));
                dataCell.setCellStyle(centerStyle);
                dataCell = dataRow.createCell(6);
                dataCell.setCellValue(String.valueOf(10 + i));
                dataCell.setCellStyle(centerStyle);
                dataCell = dataRow.createCell(7);
                dataCell.setCellValue("2025110" + i);
                dataCell.setCellStyle(centerStyle);
                dataCell = dataRow.createCell(8);
                dataCell.setCellValue("2025120" + i);
                dataCell.setCellStyle(centerStyle);
                dataCell = dataRow.createCell(9);
                dataCell.setCellValue(String.valueOf(1000 + i));
                dataCell.setCellStyle(centerStyle);
                dataCell = dataRow.createCell(10);
                dataCell.setCellValue(String.valueOf(100 + i));
                dataCell.setCellStyle(centerStyle);
                dataCell = dataRow.createCell(11);
                dataCell.setCellValue(String.valueOf(1000 - 100 - i));
                dataCell.setCellStyle(centerStyle);
                dataCell = dataRow.createCell(12);
                dataCell.setCellValue("备注" + i);
                dataCell.setCellStyle(centerStyle);

                // 每两行加一个分割线
                if(i % 2 == 0) {
                    // 下边框加粗，下边框颜色为黑色,单元格宽度为50
                    CellStyle cellStyle = workbook.createCellStyle();
                    cellStyle.setBorderBottom(BorderStyle.THICK);
                    cellStyle.setBottomBorderColor(IndexedColors.BLACK.getIndex());
                    cellStyle.setAlignment(HorizontalAlignment.CENTER);  // 水平居中
                    cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);  // 垂直居中

                    dataRow.getCell(0).setCellStyle(cellStyle);
                    dataRow.getCell(1).setCellStyle(cellStyle);
                    dataRow.getCell(2).setCellStyle(cellStyle);
                    dataRow.getCell(3).setCellStyle(cellStyle);
                    dataRow.getCell(4).setCellStyle(cellStyle);
                    dataRow.getCell(5).setCellStyle(cellStyle);
                    dataRow.getCell(6).setCellStyle(cellStyle);
                    dataRow.getCell(7).setCellStyle(cellStyle);
                    dataRow.getCell(8).setCellStyle(cellStyle);
                    dataRow.getCell(9).setCellStyle(cellStyle);
                    dataRow.getCell(10).setCellStyle(cellStyle);
                    dataRow.getCell(11).setCellStyle(cellStyle);
                    dataRow.getCell(12).setCellStyle(cellStyle);
                }
            }

            // 保存工作簿到文件
            // 将工作簿写入到响应输出流
            response.setContentType("application/vnd.ms-excel");
            response.setHeader("Content-Disposition", "attachment;filename=订单记录_110.xlsx");
            ServletOutputStream outputStream = response.getOutputStream();
            workbook.write(outputStream);
            outputStream.flush();
            outputStream.close();
            workbook.close();

            // 导出订单详情
            return new HrResult(CommonReturnCode.SUCCESS);
        }catch (Exception e){
            e.printStackTrace();
            return new HrResult(0,e.getMessage());
        }
    }

    /**
     * 在网页中预览订单Excel
     *
     * @param response
     * @throws Exception
     * @return
     */
    @PostMapping("/orderEntry/preview")
    @ResponseBody
    public Object orderEntryPreview(HttpServletResponse response) throws Exception {
        try {
            // 创建工作簿
            Workbook workbook = new XSSFWorkbook();
            Sheet sheet = workbook.createSheet("订单记录");

            // 设置默认行数
            sheet.setDefaultRowHeight((short) 20);

            // 前三行的右上角需要显示「记录号、修改号、文件号」
            Row headerRow = sheet.createRow(0);
            headerRow.setHeight((short) 600);
            // 在右上角显示记录号、修改号、文件号
            int lastColumnIndex = 13;
            headerRow.createCell(lastColumnIndex).setCellValue("记录号：20251101");

            Row headerRow1 = sheet.createRow(1);
            headerRow1.setHeight((short) 600);
            // 修改号
            headerRow1.createCell(lastColumnIndex).setCellValue("修改号：1");

            Row headerRow2 = sheet.createRow(2);
            headerRow2.setHeight((short) 600);
            // 文件号
            headerRow2.createCell(lastColumnIndex).setCellValue("文件号：1");

            // 第四行需要居中显示一个加粗字体的标题
            Row titleRow = sheet.createRow(3);
            titleRow.setHeight((short) 600);
            titleRow.setHeightInPoints(20);

            // 创建单元格并设置值
            Cell titleCell = titleRow.createCell(0);
            titleCell.setCellValue("订单记录");

            // 创建居中加粗样式
            CellStyle centerStyle = workbook.createCellStyle();
            centerStyle.setAlignment(HorizontalAlignment.CENTER); // 水平居中
            centerStyle.setVerticalAlignment(VerticalAlignment.CENTER); // 垂直居中

            // 创建居中加粗样式
            CellStyle centerBoldStyle = workbook.createCellStyle();
            centerBoldStyle.setAlignment(HorizontalAlignment.CENTER); // 水平居中
            centerBoldStyle.setVerticalAlignment(VerticalAlignment.CENTER); // 垂直居中

            // 创建加粗字体
            Font boldFont = workbook.createFont();
            boldFont.setBold(true);
            boldFont.setFontHeightInPoints((short) 22);
            centerBoldStyle.setFont(boldFont);

            // 合并单元格使标题跨整个表格
            sheet.addMergedRegion(new CellRangeAddress(3, 3, 0, 13));

            // 应用样式
            titleCell.setCellStyle(centerBoldStyle);

            // 创建表头（文字居中）
            Row headerRow4 = sheet.createRow(4);
            headerRow4.setHeight((short) 600);
            // 创建单元格并设置值
            Cell headerCell = headerRow4.createCell(0);
            headerCell.setCellValue("客户");
            headerCell.setCellStyle(centerStyle);
            headerCell = headerRow4.createCell(1);
            headerCell.setCellValue("出库日期");
            headerCell.setCellStyle(centerStyle);
            headerCell = headerRow4.createCell(2);
            headerCell.setCellValue("准备日期");
            headerCell.setCellStyle(centerStyle);
            headerCell = headerRow4.createCell(3);
            headerCell.setCellValue("到货日期");
            headerCell.setCellStyle(centerStyle);
            headerCell = headerRow4.createCell(5);
            headerCell.setCellValue("型号");
            headerCell.setCellStyle(centerStyle);
            headerCell = headerRow4.createCell(6);
            headerCell.setCellValue("尺寸");
            headerCell.setCellStyle(centerStyle);
            headerCell = headerRow4.createCell(7);
            headerCell.setCellValue("线盘");
            headerCell.setCellStyle(centerStyle);
            headerCell = headerRow4.createCell(8);
            headerCell.setCellValue("部品编号");
            headerCell.setCellStyle(centerStyle);
            headerCell = headerRow4.createCell(9);
            headerCell.setCellValue("订单号");
            headerCell.setCellStyle(centerStyle);
            headerCell = headerRow4.createCell(10);
            headerCell.setCellValue("订单数量");
            headerCell.setCellStyle(centerStyle);
            headerCell = headerRow4.createCell(11);
            headerCell.setCellValue("已送数量");
            headerCell.setCellStyle(centerStyle);
            headerCell = headerRow4.createCell(12);
            headerCell.setCellValue("剩余数量");
            headerCell.setCellStyle(centerStyle);
            headerCell = headerRow4.createCell(13);
            headerCell.setCellValue("备注");
            headerCell.setCellStyle(centerStyle);

            // 设置列宽
            for (int i = 0; i <= 13; i++) {
                sheet.setColumnWidth(i, 3500);
                if (i == 3) {
                    sheet.setColumnWidth(i, 5000);
                } else if (i == 12) {
                    sheet.setColumnWidth(i, 10000);
                }
            }

            // 创建数据行
            for (int i = 0; i <= 10; i++) {
                Row dataRow = sheet.createRow(i + 5);
                dataRow.setHeight((short) 600);
                Cell dataCell = dataRow.createCell(0);
                dataCell.setCellValue("客户" + i);
                dataCell.setCellStyle(centerStyle);
                dataCell = dataRow.createCell(1);
                dataCell.setCellValue("出库日期" + i);
                dataCell.setCellStyle(centerStyle);
                dataCell = dataRow.createCell(2);
                dataCell.setCellValue("准备日期" + i);
                dataCell.setCellStyle(centerStyle);
                dataCell = dataRow.createCell(3);
                dataCell.setCellValue(
                        DateUtils.convertIntToDateString(DateUtils.getCurrentTime(), "yyyy-MM-dd HH:mm:ss"));
                dataCell.setCellStyle(centerStyle);
                dataCell = dataRow.createCell(4);
                dataCell.setCellValue("型号" + i);
                dataCell.setCellStyle(centerStyle);
                dataCell = dataRow.createCell(5);
                dataCell.setCellValue(String.valueOf(i));
                dataCell.setCellStyle(centerStyle);
                dataCell = dataRow.createCell(6);
                dataCell.setCellValue(String.valueOf(10 + i));
                dataCell.setCellStyle(centerStyle);
                dataCell = dataRow.createCell(7);
                dataCell.setCellValue("2025110" + i);
                dataCell.setCellStyle(centerStyle);
                dataCell = dataRow.createCell(8);
                dataCell.setCellValue("2025120" + i);
                dataCell.setCellStyle(centerStyle);
                dataCell = dataRow.createCell(9);
                dataCell.setCellValue(String.valueOf(1000 + i));
                dataCell.setCellStyle(centerStyle);
                dataCell = dataRow.createCell(10);
                dataCell.setCellValue(String.valueOf(100 + i));
                dataCell.setCellStyle(centerStyle);
                dataCell = dataRow.createCell(11);
                dataCell.setCellValue(String.valueOf(1000 - 100 - i));
                dataCell.setCellStyle(centerStyle);
                dataCell = dataRow.createCell(12);
                dataCell.setCellValue("备注" + i);
                dataCell.setCellStyle(centerStyle);

                // 每两行加一个分割线
                if (i % 2 == 0) {
                    // 下边框加粗，下边框颜色为黑色,单元格宽度为50
                    CellStyle cellStyle = workbook.createCellStyle();
                    cellStyle.setBorderBottom(BorderStyle.THICK);
                    cellStyle.setBottomBorderColor(IndexedColors.BLACK.getIndex());
                    cellStyle.setAlignment(HorizontalAlignment.CENTER); // 水平居中
                    cellStyle.setVerticalAlignment(VerticalAlignment.CENTER); // 垂直居中

                    dataRow.getCell(0).setCellStyle(cellStyle);
                    dataRow.getCell(1).setCellStyle(cellStyle);
                    dataRow.getCell(2).setCellStyle(cellStyle);
                    dataRow.getCell(3).setCellStyle(cellStyle);
                    dataRow.getCell(4).setCellStyle(cellStyle);
                    dataRow.getCell(5).setCellStyle(cellStyle);
                    dataRow.getCell(6).setCellStyle(cellStyle);
                    dataRow.getCell(7).setCellStyle(cellStyle);
                    dataRow.getCell(8).setCellStyle(cellStyle);
                    dataRow.getCell(9).setCellStyle(cellStyle);
                    dataRow.getCell(10).setCellStyle(cellStyle);
                    dataRow.getCell(11).setCellStyle(cellStyle);
                    dataRow.getCell(12).setCellStyle(cellStyle);
                }
            }

            // 保存工作簿到临时文件
            String fileName = "订单记录_110.xlsx";
            String uploadPath = getRequest().getSession().getServletContext().getRealPath("/uploads/excels/");
            File dir = new File(uploadPath);
            if (!dir.exists()) {
                dir.mkdirs();
            }
            String filePath = uploadPath + fileName;
            FileOutputStream fileOut = new FileOutputStream(filePath);
            workbook.write(fileOut);
            fileOut.close();
            workbook.close();

            // 计算文件URL
            String serverUrl = getRequest().getScheme() + "://" + getRequest().getServerName() + ":"
                    + getRequest().getServerPort();
            String fileUrl = serverUrl + getRequest().getContextPath() + "/uploads/excels/" + fileName;

            // 返回预览URL和嵌入代码
            Map<String, Object> result = new HashMap<>();
            result.put("fileUrl", fileUrl);
            result.put("embedHtml", "<iframe src=\"https://view.officeapps.live.com/op/embed.aspx?src=" + fileUrl
                    + "\" width=\"100%\" height=\"600px\" frameborder=\"0\"></iframe>");

            return new HrResult(CommonReturnCode.SUCCESS, result);
        } catch (Exception e) {
            e.printStackTrace();
            return new HrResult(0, e.getMessage());
        }
    }

    /**
     * 预览Excel文件的View
     *
     * @param model
     * @throws Exception
     * @return
     */
    @GetMapping("/orderEntry/preview/view")
    public String orderEntryPreviewView(Model model) throws Exception {
        return "/modules/order/orderEntry_preview";
    }

    /**
     * 根据客户代码获取产品列表
     * @param customerCode 客户代码
     * @throws Exception
     * @return
     */
    @PostMapping("/orderEntry/getProductListByCustomerCode")
    @ResponseBody
    public Object getProductListByCustomerCode(String customerCode) throws Exception {
        try {
            // 获取产品列表 - 使用新的service方法获取条码信息
            List<String> productList = orderService.getProductListByCustomerCode(customerCode);
            return new HrResult(CommonReturnCode.SUCCESS, productList);
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("获取产品信息异常：", e);
            return new HrResult(CommonReturnCode.FAILED, "获取产品信息失败：" + e.getMessage());
        }
    }

    /**
     * 获取产品尺寸列表
     * @param customerCode 客户代码
     * @param productCode 产品代码
     * @throws Exception
     * @return
     */
    @PostMapping("/orderEntry/getProductSizeList")
    @ResponseBody
    public Object getProductSizeList(String customerCode, String productCode) throws Exception {
        // 获取产品尺寸列表
        List<String> productSizeList = sumitomoService.getProductSizeList(customerCode, productCode, "0");
        return new HrResult(CommonReturnCode.SUCCESS, productSizeList);
    }

    /**
     * 根据产品代码获取产品信息
     * @param productCode 产品代码
     * @throws Exception
     * @return
     */
    @PostMapping("/orderEntry/getProductInfo")
    @ResponseBody
    public Object getProductInfo(String productCode) throws Exception {
        // 获取产品信息
        Product productInfo = sumitomoService.getProductInfo(productCode, "0");
        return new HrResult(CommonReturnCode.SUCCESS, productInfo);
    }

    /**
     * 获取产品线盘名称列表
     * @param customerCode 客户代码
     * @param productCode 产品代码
     * @param size 尺寸
     * @throws Exception
     * @return
     */
    @PostMapping("/orderEntry/getProductWireReelNameList")
    @ResponseBody
    public Object getProductWireReelNameList(String customerCode, String productCode, String size) throws Exception {
        // 获取产品线盘名称列表
        List<String> productWireReelNameList = sumitomoService.getProductWireReelNameList(customerCode, productCode, size, "0");
        return new HrResult(CommonReturnCode.SUCCESS, productWireReelNameList);
    }

    /**
     * 在网页中预览订单Excel的数据（返回JSON格式）
     *
     * @return 返回JSON格式的Excel数据
     * @throws Exception
     */
    @PostMapping("/orderEntry/previewData")
    @ResponseBody
    public Object orderEntryPreviewData(Integer page, Integer limit, String orderStartDate, String orderEndDate, String prepareStartDate, String prepareEndDate, String outboundStartDate, String outboundEndDate, String demandCustomerCode, String contractPartyCustomerCode, String orderType, String status, String planStatus) throws Exception {
        try {
            String userName = null;
            if (StringUtil.isStringEmpty(planStatus)) {
                // 只能查询本人录入的订单
                AuthorizingUser authorizingUser = SingletonLoginUtils.getUser();
                if (authorizingUser == null) {
                    return new HrResult(CommonReturnCode.FAILED, "登录超时，请重新登录");
                }

                User user = userService.getById(authorizingUser.getUserId());
            }
            
            // 参数验证和默认值设置
            if (page == null)
                page = 1;
            if (limit == null)
                limit = 100000;
            if (orderType == null)
                orderType = "0";

            logger.info("预览数据请求参数: page={}, limit={}, orderType={}", page, limit, orderType);

            PageInfo pageInfo = new PageInfo(limit, page);

            OrderEntryDetailsPageDTO orderEntryDetailsPageDTO = orderService.orderEntryDetailsListByPageForExport(pageInfo, orderStartDate, orderEndDate, prepareStartDate, prepareEndDate, outboundStartDate, outboundEndDate, demandCustomerCode, contractPartyCustomerCode, orderType, status, planStatus, userName);

            // 空值检查
            if (orderEntryDetailsPageDTO == null) {
                logger.warn("预览数据返回为空");
                return new HrResult(0, "查询结果为空");
            }

            List<OrderEntryDetails> orderEntryDetails = Optional.ofNullable(orderEntryDetailsPageDTO.getOrderEntryDetailsList()).orElse(new ArrayList<>());

            if (orderEntryDetails.isEmpty()) {
                return new HrResult(0, "没有需要预览的信息");
            }

            // 创建返回的数据结构
            Map<String, Object> result = new HashMap<>();
            result.put("recordNo", "20251101");
            result.put("modifyNo", "1");
            result.put("fileNo", "1");

            // 表头
            List<String> headers = new ArrayList<>();
            headers.add("客户");
            headers.add("出库日期");
            headers.add("准备日期");
            headers.add("到货日期");
            headers.add("型号");
            headers.add("尺寸");
            headers.add("线盘");
            headers.add("部品编号");
            headers.add("订单号");
            headers.add("订单数量");
            headers.add("已送数量");
            headers.add("剩余数量");
            headers.add("备注");
            result.put("headers", headers);

            // 数据行
            List<List<String>> rows = new ArrayList<>();
            for (OrderEntryDetails orderEntryDetail : orderEntryDetails) {
                List<String> row = new ArrayList<>();
                // 安全地获取各个字段值，防止NPE，并确保不返回"null"字符串
                String demandCustomerAlias = Optional.ofNullable(orderEntryDetail.getDemandCustomerAlias()).orElse("");
                String outboundDate = Optional.ofNullable(orderEntryDetail.getOutboundDate()).orElse("");
                String prepareDate = Optional.ofNullable(orderEntryDetail.getPrepareDate()).orElse("");
                String arrivalDate = Optional.ofNullable(orderEntryDetail.getArrivalDate()).orElse("");
                String modelNumber = Optional.ofNullable(orderEntryDetail.getModelNumber()).orElse("");
                String size = Optional.ofNullable(orderEntryDetail.getSize()).orElse("");
                String wireSpool = Optional.ofNullable(orderEntryDetail.getWireSpool()).orElse("");
                String partNumber = Optional.ofNullable(orderEntryDetail.getPartNumber()).orElse("");
                String orderNo = Optional.ofNullable(orderEntryDetail.getOrderNo()).orElse("");
                String orderQuantity = Optional.ofNullable(orderEntryDetail.getOrderQuantity()).orElse("0");
                String deliveredQuantity = Optional.ofNullable(orderEntryDetail.getDeliveredQuantity()).orElse("0");

                // 处理"null"字符串
                demandCustomerAlias = "null".equals(demandCustomerAlias) ? "" : demandCustomerAlias;
                outboundDate = "null".equals(outboundDate) ? "" : outboundDate;
                prepareDate = "null".equals(prepareDate) ? "" : prepareDate;
                arrivalDate = "null".equals(arrivalDate) ? "" : arrivalDate;
                modelNumber = "null".equals(modelNumber) ? "" : modelNumber;
                size = "null".equals(size) ? "" : size;
                wireSpool = "null".equals(wireSpool) ? "" : wireSpool;
                partNumber = "null".equals(partNumber) ? "" : partNumber;
                orderNo = "null".equals(orderNo) ? "" : orderNo;
                orderQuantity = "null".equals(orderQuantity) ? "0" : orderQuantity;
                deliveredQuantity = "null".equals(deliveredQuantity) ? "0" : deliveredQuantity;

                row.add(demandCustomerAlias); // 客户
                row.add(outboundDate); // 出库日期
                row.add(prepareDate); // 准备日期
                row.add(arrivalDate); // 到货日期
                row.add(modelNumber); // 型号
                row.add(size); // 尺寸
                row.add(wireSpool); // 线盘
                row.add(partNumber); // 部品编号
                row.add(orderNo); // 订单号
                row.add(orderQuantity); // 订单数量
                row.add(deliveredQuantity); // 已送数量

                // 安全计算剩余数量
                try {
                    // 使用前面处理过的值
                    BigDecimal remaining = new BigDecimal(orderQuantity).subtract(new BigDecimal(deliveredQuantity));
                    row.add(remaining.toString()); // 剩余数量
                } catch (Exception e) {
                    logger.warn("计算剩余数量出错: {}", e.getMessage());
                    row.add("0"); // 出错时默认为0
                }

                // 处理备注字段
                String detailRemark = Optional.ofNullable(orderEntryDetail.getDetailRemark()).orElse("");
                detailRemark = "null".equals(detailRemark) ? "" : detailRemark;
                row.add(detailRemark); // 备注

                rows.add(row);
            }
            result.put("rows", rows);

            return new HrResult(CommonReturnCode.SUCCESS, result);
        } catch (Exception e) {
            logger.error("预览数据异常: {}", e.getMessage(), e);
            return new HrResult(0, "预览数据异常: " + e.getMessage());
        }
    }
    
    /*=================================订单确认页面======================================*/
    /**
     * 跳转订单确认列表页面
     * @param model
     * @throws Exception
     * @return
     */
    @GetMapping("/orderConfirm/list/view")
    public String orderConfirmListView(Model model) throws Exception{
        //获取客户列表（需求方）
        List<Customers> customersList = sumitomoService.listCustomersList();
        //获取合同方信息列表
        List<Customers> contractPartyCustomerList = orderService.getContractPartyCustomerList();

        model.addAttribute("customersList",customersList);
        model.addAttribute("contractPartyCustomerList",contractPartyCustomerList);
        return "/modules/order/orderConfirm_list";
    }

    /**
     * 查询订单确认列表
     * @param page 当前页码
     * @param limit 每页条数
     * @param outboundStartDate 出库日期开始
     * @param outboundEndDate 出库日期结束
     * @param demandCustomerCode 需求方
     * @param contractPartyCustomerCode 合同方
     * @return
     */
    @PostMapping("/orderConfirm/list")
    @ResponseBody
    public Object orderConfirmList(Integer page, Integer limit, String outboundStartDate, String outboundEndDate, String demandCustomerCode, String contractPartyCustomerCode) throws Exception {
        // 移除对需求方的强制检查，允许在不选择需求方的情况下也能查询
        // if (StringUtil.isStringEmpty(demandCustomerCode)) {
        // return new HrResult(0, "请选择需求方后，再进行检索");
        // }

        // 只能查询本人录入的订单
        AuthorizingUser authorizingUser = SingletonLoginUtils.getUser();
        if (authorizingUser == null) {
            return new HrResult(CommonReturnCode.FAILED, "登录超时，请重新登录");
        }

        User user = userService.getById(authorizingUser.getUserId());

        PageInfo pageInfo = new PageInfo(limit, page);

        OrderEntryDetailsPageDTO orderEntryDetailsPageDTO = orderService.queryOrderConfirmListByPage(pageInfo,
                outboundStartDate, outboundEndDate, demandCustomerCode, contractPartyCustomerCode, null,
                user.getUserName());

    	return new HrPageResult(orderEntryDetailsPageDTO.getOrderEntryDetailsList(), orderEntryDetailsPageDTO.getPageInfo().getTotal());
    }   
            

    /**
     * 确认订单
     * @param orderNoAndSerialNumsStr 订单号及订单序号集合拼接字符串（例：D25042402-1,D25042402-2）
     * @return
     */
    @PostMapping("/orderConfirm/confirm")
    @ResponseBody
    public Object orderConfirm(String orderNoAndSerialNumsStr) throws Exception {
        // 参数验证
        if (StringUtil.isStringEmpty(orderNoAndSerialNumsStr)) {
            return new HrResult(0, "没有需要确认的信息");
        }

        // 将订单号及订单序号集合拼接字符串转换为列表
        List<String> orderNoAndSerialNumList = Arrays.asList(orderNoAndSerialNumsStr.split(","));

        // 更新订单确认状态
        orderService.updateOrderConfirm(orderNoAndSerialNumList);

        return new HrResult(CommonReturnCode.SUCCESS, "确认成功");
    }

    /**
     * 预览Excel文件的View (制作联络单)
     *
     * @param model
     * @throws Exception
     * @return
     */
    @GetMapping("/manufacturingContactForm/preview/view")
    public String manufacturingContactFormPreviewView(Model model) throws Exception {
        return "/modules/order/manufacturingContactForm_preview";
    }

    /**
     * 在网页中预览Excel制作联络单的数据（返回JSON格式）
     *
     * @return 返回JSON格式的Excel数据
     * @throws Exception
     */
    @PostMapping("/manufacturingContactForm/previewData")
    @ResponseBody
    public Object manufacturingContactFormPreviewData(Integer page, Integer limit, String outboundStartDate, String outboundEndDate, String demandCustomerCode, String contractPartyCustomerCode, String orderNoAndSerialNumsStr) throws Exception {
        try {
            if (StringUtil.isStringEmpty(orderNoAndSerialNumsStr)) {
                return new HrResult(0, "没有需要预览的信息");
            }

            AuthorizingUser authorizingUser = SingletonLoginUtils.getUser();
	         if(authorizingUser == null ) {
	             return new HrResult(CommonReturnCode.FAILED,"登录超时，请重新登录");
	         }

             User user = userService.getById(authorizingUser.getUserId());

            // 参数验证和默认值设置
            if (page == null) {
                page = 1;
            }
            if (limit == null) {
                limit = 100000;
            }

            logger.info("预览数据请求参数: page={}, limit={}, outboundStartDate={}, outboundEndDate={}, demandCustomerCode={}, contractPartyCustomerCode={}", page, limit, outboundStartDate, outboundEndDate, demandCustomerCode, contractPartyCustomerCode);

            PageInfo pageInfo = new PageInfo(limit, page);

            // 将订单号及订单序号集合拼接字符串转换为列表
            List<String> orderNoAndSerialNumList = Arrays.asList(orderNoAndSerialNumsStr.split(","));

            // 将订单号及订单序号列表转换为订单号集合
            Set<String> orderNoSet = new HashSet<>();
            for (String orderNoAndSerialNum : orderNoAndSerialNumList) {
                orderNoSet.add(orderNoAndSerialNum.split("-")[0]);
            }

            // 将订单号集合转换为列表
            List<String> orderNoList = new ArrayList<>(orderNoSet);

            OrderEntryDetailsPageDTO orderEntryDetailsPageDTO = orderService.queryOrderConfirmListByPage(pageInfo, outboundStartDate, outboundEndDate, demandCustomerCode, contractPartyCustomerCode, orderNoList, null);

            // 空值检查
            if (orderEntryDetailsPageDTO == null) {
                logger.warn("预览数据返回为空");
                return new HrResult(0, "查询结果为空");
            }

            List<OrderEntryDetails> orderEntryDetails = Optional.ofNullable(orderEntryDetailsPageDTO.getOrderEntryDetailsList()).orElse(new ArrayList<>());

            if (orderEntryDetails.isEmpty()) {
                return new HrResult(0, "没有需要预览的信息");
            }

            // 过滤需要预览的订单
            orderEntryDetails = orderEntryDetails.stream().filter(orderEntryDetail -> orderNoAndSerialNumList.contains(orderEntryDetail.getOrderNo() + "-" + orderEntryDetail.getOrderSerialNum()) || "9".equals(orderEntryDetail.getStatus())).collect(Collectors.toList());

            if (orderEntryDetails.isEmpty()) {
                return new HrResult(0, "没有需要预览的信息");
            }

            // 创建返回的数据结构
            Map<String, Object> result = new HashMap<>();
            result.put("recordNo", "20251101");
            result.put("modifyNo", "1");
            result.put("customerName", orderEntryDetails.get(0).getDemandCustomerName());
            result.put("dateTime", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy年MM月dd日 HH:mm:ss")));
            result.put("currentUserName", user.getRealName());

            // 表头
            List<String> headers = new ArrayList<>();
            headers.add("序号");
            headers.add("接单号");
            headers.add("客户订单号");
            headers.add("型号/尺寸");
            headers.add("数量");
            headers.add("线盘型号");
            headers.add("要求出库日");
            headers.add("状态");
            result.put("headers", headers);

            // 数据行
            List<List<String>> rows = new ArrayList<>();
            int index = 0;
            for (OrderEntryDetails orderEntryDetail : orderEntryDetails) {
                List<String> row = new ArrayList<>();
                // 安全地获取各个字段值，防止NPE，并确保不返回"null"字符串
                String orderNo = Optional.ofNullable(orderEntryDetail.getOrderNo()).orElse("");
                String customerOrderNo = Optional.ofNullable(orderEntryDetail.getCustomerOrderNo()).orElse("");
                String modelNumber = Optional.ofNullable(orderEntryDetail.getModelNumber()).orElse("");
                String orderQuantity = Optional.ofNullable(orderEntryDetail.getOrderQuantity()).orElse("0");
                String wireSpool = Optional.ofNullable(orderEntryDetail.getWireSpool()).orElse("");
                String outboundDate = Optional.ofNullable(orderEntryDetail.getOutboundDate()).orElse("");
                String status = Optional.ofNullable(orderEntryDetail.getStatus()).orElse("");

                // 处理"null"字符串
                orderNo = "null".equals(orderNo) ? "" : orderNo;
                customerOrderNo = "null".equals(customerOrderNo) ? "" : customerOrderNo;
                modelNumber = "null".equals(modelNumber) ? "" : modelNumber;
                orderQuantity = "null".equals(orderQuantity) ? "0" : orderQuantity;
                wireSpool = "null".equals(wireSpool) ? "" : wireSpool;
                outboundDate = "null".equals(outboundDate) ? "" : outboundDate;
                status = "null".equals(status) ? "0" : status;

                row.add(String.valueOf(++index)); // 序号
                row.add(orderNo); // 接单号
                row.add(customerOrderNo); // 客户订单号
                row.add(modelNumber); // 型号/尺寸
                row.add(orderQuantity); // 数量
                row.add(wireSpool); // 线盘型号
                row.add(outboundDate); // 要求出库日
                row.add("0".equals(status) ? "登录" : "删除"); // 状态

                rows.add(row);
            }
            result.put("rows", rows);

            return new HrResult(CommonReturnCode.SUCCESS, result);
        } catch (Exception e) {
            logger.error("预览数据异常: {}", e.getMessage(), e);
            return new HrResult(0, "预览数据异常: " + e.getMessage());
        }
    }

    /*=================================出库计划录入页面======================================*/
    /**
     * 跳转出库计划录入列表页面
     * @param model
     * @throws Exception
     * @return
     */
    @GetMapping("/shipmentPlanEntry/list/view")
    public String shipmentPlanEntryListView(Model model) throws Exception{
        //获取客户列表（需求方）
        List<Customers> customersList = sumitomoService.listCustomersList();
        //获取合同方信息列表
        List<Customers> contractPartyCustomerList = orderService.getContractPartyCustomerList();

        model.addAttribute("customersList",customersList);
        model.addAttribute("contractPartyCustomerList",contractPartyCustomerList);
        
        return "/modules/order/shipmentPlanEntry_list";
    }

    /**
     * 查询出库计划录入列表
     * @param page 当前页码
     * @param limit 每页条数
     * @param outboundStartDate 出库开始日期
     * @param outboundEndDate 出库结束日期
     * @param demandCustomerCode 需求方
     * @param contractPartyCustomerCode 合同方
     * @return
     */
    @PostMapping("/shipmentPlanEntry/list")
    @ResponseBody
    public Object shipmentPlanEntryList(Integer page, Integer limit, String outboundStartDate, String outboundEndDate, String demandCustomerCode, String contractPartyCustomerCode) throws Exception {
        // 只能查询本人录入的出货计划
        AuthorizingUser authorizingUser = SingletonLoginUtils.getUser();
        if (authorizingUser == null) {
            return new HrResult(CommonReturnCode.FAILED, "登录超时，请重新登录");
        }

        User user = userService.getById(authorizingUser.getUserId());

        PageInfo pageInfo = new PageInfo(limit, page);

        OrderEntryDetailsPageDTO orderEntryDetailsPageDTO = orderService.queryShipmentPlanEntryListByPage(pageInfo,
                outboundStartDate, outboundEndDate, demandCustomerCode, contractPartyCustomerCode, null,
                user.getUserName());

        return new HrPageResult(orderEntryDetailsPageDTO.getOrderEntryDetailsList(), orderEntryDetailsPageDTO.getPageInfo().getTotal());
    }

    /**
     * 查询出库计划详情列表
     * @param outboundDate 出库日期
     * @param demandCustomerCode 需求方
     * @param contractPartyCustomerCode 合同方
     * @param planStatus 出货计划状态(null: 未登录计划, 0: 待确认, 1: 已确认)
     * @return
     */
    @GetMapping("/shipmentPlanEntry/detail/view")
    public String shipmentPlanEntryDetailView(Model model, String outboundDate, String demandCustomerCode, String contractPartyCustomerCode, String planStatus) throws Exception {
        if ("null".equals(outboundDate)) {
            outboundDate = null;
        }

        // 查询出库计划详情列表
        List<OrderEntryDetails> orderDetailsList = orderService.queryShipmentPlanEntryDetailList(outboundDate, demandCustomerCode, contractPartyCustomerCode, null, planStatus);

        if (StringUtil.isStringEmpty(outboundDate)) {
            orderDetailsList = orderDetailsList.stream().filter(item -> StringUtil.isStringEmpty(item.getOutboundDate())).collect(Collectors.toList());
        }

        model.addAttribute("orderDetailsList", orderDetailsList);

        return "/modules/order/shipmentPlanEntry_detail";
    }

    /**
     * 确认登录出库计划
     * @param confirmDataParamStr 出库日期-需求方-合同方集合拼接字符串（例：2025-05-14-001-002,2025-05-15-001-003）
     * @return
     */
    @PostMapping("/shipmentPlanEntry/confirm")
    @ResponseBody
    public Object shipmentPlanEntryConfirm(String confirmDataParamStr) throws Exception {
        if (StringUtil.isStringEmpty(confirmDataParamStr)) {
            return new HrResult(0, "没有需要确认的信息");
        }

        // 将出库日期-需求方-合同方拼接字符串转换为列表
        List<String> confirmDataParamList = Arrays.asList(confirmDataParamStr.split(","));

        // 查询出库计划录入详情列表
        List<OrderEntryDetails> orderEntryDetailsList = orderService.queryShipmentPlanEntryDetailList(null, null, null, confirmDataParamList, null);

        if (orderEntryDetailsList.isEmpty()) {
            return new HrResult(0, "没有需要确认的信息");
        }

        // 根据出库日期、需求方、合同方排序
        List<OrderEntryDetails> sortedOrderEntryDetailsList = orderEntryDetailsList.stream()
            .sorted(Comparator.comparing(OrderEntryDetails::getOutboundDate)
            .thenComparing(OrderEntryDetails::getDemandCustomerCode)
            .thenComparing(OrderEntryDetails::getContractPartyCustomerCode))
            .collect(Collectors.toList());

        for (OrderEntryDetails orderEntryDetails : sortedOrderEntryDetailsList) {
            // 整合订单号及订单序号拼接字符串，用于更新出货计划
            String orderNoAndSerialNum = orderEntryDetails.getOrderNo() + "-" + orderEntryDetails.getOrderSerialNum();

            // 获取当前日期年月日字符串
            String nowDate = DateUtils.convertIntToDateString(DateUtils.getCurrentTime(),"yyyyMMdd");

            // 获取当前日期前缀的最大出货计划番号
            String maxPlanNumber = orderService.getMaxPlanNumberByplanNumberPrefix(nowDate.substring(2));

            // 生成当前出货计划番号
            String currentPlanNumber = "";
            if (!StringUtil.isStringEmpty(maxPlanNumber)) {
                // 如果maxPlanNumber不为空，则当前订单号为maxPlanNumber加1 (例：250423001 -> 250423002)
                currentPlanNumber = nowDate.substring(2) + String.format("%03d",(Integer.parseInt(maxPlanNumber.substring(6)) + 1));
            } else {
                // 如果maxPlanNumber为空，则当前订单号为D加上当前日期和001 (例：250423001)
                currentPlanNumber = nowDate.substring(2) + "001";
            }

            // 更新出货计划
            orderService.updateShipmentPlanEntryDetails(Arrays.asList(orderNoAndSerialNum), "0", currentPlanNumber);
        }

        return new HrResult(CommonReturnCode.SUCCESS, "确认成功");
    }
    /*=================================出库计划确认列表页面======================================*/
    /**
     * 跳转出库计划确认列表页面
     * @param model
     * @throws Exception
     * @return
     */ 
    @GetMapping("/shipmentPlanConfirm/list/view")
    public String shipmentPlanConfirmListView(Model model) throws Exception{
        //获取客户列表（需求方）
        List<Customers> customersList = sumitomoService.listCustomersList();
        //获取合同方信息列表
        List<Customers> contractPartyCustomerList = orderService.getContractPartyCustomerList();

        model.addAttribute("customersList",customersList);
        model.addAttribute("contractPartyCustomerList",contractPartyCustomerList);

        return "/modules/order/shipmentPlanConfirm_list";
    }

    /**
     * 查询出库计划确认列表
     * @param page 当前页码
     * @param limit 每页条数
     * @param outboundStartDate 出库开始日期
     * @param outboundEndDate 出库结束日期
     * @param demandCustomerCode 需求方
     * @param contractPartyCustomerCode 合同方
     * @param planStatus 出货计划状态(null: 未登录计划, 0: 待确认, 1: 已确认)
     * @return
     */
    @PostMapping("/shipmentPlanConfirm/list")
    @ResponseBody
    public Object shipmentPlanConfirmList(Integer page, Integer limit, String outboundStartDate, String outboundEndDate, String demandCustomerCode, String contractPartyCustomerCode, String planStatus) throws Exception {
        PageInfo pageInfo = new PageInfo(limit, page);

        OrderEntryDetailsPageDTO orderEntryDetailsPageDTO = orderService.queryShipmentPlanEntryListByPage(pageInfo, outboundStartDate, outboundEndDate, demandCustomerCode, contractPartyCustomerCode, planStatus, null);

        return new HrPageResult(orderEntryDetailsPageDTO.getOrderEntryDetailsList(), orderEntryDetailsPageDTO.getPageInfo().getTotal());
    }

    /**
     * 确认出库计划
     * @param confirmDataParamStr 出库日期-需求方-合同方集合拼接字符串（例：2025-05-14-001-002,2025-05-15-001-003）
     * @return
     */
    @PostMapping("/shipmentPlanConfirm/confirm")
    @ResponseBody
    public Object shipmentPlanConfirm(String confirmDataParamStr) throws Exception {
        if (StringUtil.isStringEmpty(confirmDataParamStr)) {
            return new HrResult(0, "没有需要确认的信息");
        }

        // 将出库日期-需求方-合同方拼接字符串转换为列表
        List<String> confirmDataParamList = Arrays.asList(confirmDataParamStr.split(","));

        // 查询出库计划录入详情列表
        List<OrderEntryDetails> orderEntryDetailsList = orderService.queryShipmentPlanEntryDetailList(null, null, null, confirmDataParamList, "0");

        if (orderEntryDetailsList.isEmpty()) {
            return new HrResult(0, "没有需要确认的信息");
        }

        // 整合订单号及订单序号字符串拼接列表，用于批量更新出货计划
        List<String> orderNoAndSerialNumsList = orderEntryDetailsList.stream().map(orderEntryDetails -> orderEntryDetails.getOrderNo() + "-" + orderEntryDetails.getOrderSerialNum()).collect(Collectors.toList());

        // 确认出货计划
        orderService.updateShipmentPlanEntryDetails(orderNoAndSerialNumsList, "1", null);

        return new HrResult(CommonReturnCode.SUCCESS, "确认成功");
        
    }

    /**
     * 取消确认出库计划
     * @param confirmDataParamStr 出库日期-需求方-合同方集合拼接字符串（例：2025-05-14-001-002,2025-05-15-001-003）
     * @return
     */
    @PostMapping("/shipmentPlanConfirm/cancelConfirm")
    @ResponseBody
    public Object shipmentPlanCancelConfirm(String confirmDataParamStr) throws Exception {
        if (StringUtil.isStringEmpty(confirmDataParamStr)) {
            return new HrResult(0, "没有需要取消确认的信息");
        }

        // 将出库日期-需求方-合同方拼接字符串转换为列表
        List<String> confirmDataParamList = Arrays.asList(confirmDataParamStr.split(","));

        // 查询出库计划录入详情列表
        List<OrderEntryDetails> orderEntryDetailsList = orderService.queryShipmentPlanEntryDetailList(null, null, null, confirmDataParamList, "1");

        if (orderEntryDetailsList.isEmpty()) {
            return new HrResult(0, "没有需要取消确认的信息");
        }

        // 整合订单号及订单序号字符串拼接列表，用于批量更新出货计划
        List<String> orderNoAndSerialNumsList = orderEntryDetailsList.stream().map(orderEntryDetails -> orderEntryDetails.getOrderNo() + "-" + orderEntryDetails.getOrderSerialNum()).collect(Collectors.toList());

        // 取消确认出货计划
        orderService.updateShipmentPlanEntryDetails(orderNoAndSerialNumsList, "0", null);

        return new HrResult(CommonReturnCode.SUCCESS, "取消确认成功");
    }

    /**
     * 跳转周出货计划预览页面
     * @param model
     * @throws Exception
     * @return
     */
    @GetMapping("/shipmentPlanConfirm/preview/view")
    public String shipmentPlanConfirmPreviewView(Model model) throws Exception {
        return "/modules/order/shipmentPlanConfirm_preview";
    }

    /**
     * 导出出货计划确认Excel
     * 
     * @param outboundStartDate  出库开始日期
     * @param outboundEndDate    出库结束日期
     * @param demandCustomerCode 需求方代码
     * @param exportType         导出类型 (emergency: 紧急出库, weekly: 周出库计划)
     * @param response           HTTP响应
     * @throws Exception
     */
    @PostMapping("/shipmentPlanConfirm/export")
    public void exportShipmentPlanConfirm(String outboundStartDate, String outboundEndDate,
            String demandCustomerCode, String exportType,
            HttpServletResponse response) throws Exception {
        try {
            // 参数验证
            if (StringUtil.isStringEmpty(outboundStartDate)) {
                response.setContentType("text/html;charset=UTF-8");
                response.getWriter().write("<script>alert('请选择出库开始日期');window.close();</script>");
                return;
            }

            // 查询数据
            PageInfo pageInfo = new PageInfo(99999, 1);
            String planStatus = "1"; // 只查询已确认的计划

            OrderEntryDetailsPageDTO orderEntryDetailsPageDTO = orderService.orderEntryDetailsListByPageForExport(
                    pageInfo, null, null, null, null, outboundStartDate, outboundEndDate,
                    demandCustomerCode, null, "0", null, planStatus, null);

            if (orderEntryDetailsPageDTO == null || orderEntryDetailsPageDTO.getOrderEntryDetailsList().isEmpty()) {
                response.setContentType("text/html;charset=UTF-8");
                response.getWriter().write("<script>alert('没有找到符合条件的数据');window.close();</script>");
                return;
            }

            List<OrderEntryDetails> orderEntryDetailsList = orderEntryDetailsPageDTO.getOrderEntryDetailsList();

            // 根据导出类型生成不同的Excel
            if ("emergency".equals(exportType)) {
                exportEmergencyShipmentExcel(orderEntryDetailsList, outboundStartDate, response);
            } else {
                exportWeeklyShipmentExcel(orderEntryDetailsList, outboundStartDate, outboundEndDate, response);
            }

        } catch (Exception e) {
            logger.error("导出出货计划确认Excel异常: {}", e.getMessage(), e);
            response.setContentType("text/html;charset=UTF-8");
            response.getWriter().write("<script>alert('导出失败: " + e.getMessage() + "');window.close();</script>");
        }
    }

    /**
     * 导出紧急出库Excel
     * 
     * @param orderEntryDetailsList 订单详情列表
     * @param outboundStartDate     出库开始日期
     * @param response              HTTP响应
     * @throws Exception
     */
    private void exportEmergencyShipmentExcel(List<OrderEntryDetails> orderEntryDetailsList,
            String outboundStartDate, HttpServletResponse response) throws Exception {
        // 创建工作簿
        Workbook workbook = new XSSFWorkbook();
        Sheet sheet = workbook.createSheet("紧急出库变更通知");

        // 设置默认行高
        sheet.setDefaultRowHeight((short) 400);

        // 创建样式
        CellStyle centerStyle = workbook.createCellStyle();
        centerStyle.setAlignment(HorizontalAlignment.CENTER);
        centerStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        centerStyle.setBorderTop(BorderStyle.THIN);
        centerStyle.setBorderBottom(BorderStyle.THIN);
        centerStyle.setBorderLeft(BorderStyle.THIN);
        centerStyle.setBorderRight(BorderStyle.THIN);

        CellStyle titleStyle = workbook.createCellStyle();
        titleStyle.setAlignment(HorizontalAlignment.CENTER);
        titleStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        Font titleFont = workbook.createFont();
        titleFont.setBold(true);
        titleFont.setFontHeightInPoints((short) 16);
        titleStyle.setFont(titleFont);

        // 右上角信息
        Row row0 = sheet.createRow(0);
        row0.createCell(12).setCellValue("记录号：RD-SD-004");
        Row row1 = sheet.createRow(1);
        row1.createCell(12).setCellValue("修改号：01");
        Row row2 = sheet.createRow(2);
        row2.createCell(12).setCellValue("文件号：QP-SD-001");

        // 标题
        Row titleRow = sheet.createRow(4);
        Cell titleCell = titleRow.createCell(0);
        titleCell.setCellValue("紧急出库变更通知");
        titleCell.setCellStyle(titleStyle);
        sheet.addMergedRegion(new CellRangeAddress(4, 4, 0, 12));

        // 表头
        Row headerRow = sheet.createRow(5);
        String[] headers = { "客户", "出库日期", "AM", "PM", "到货日期", "型号", "尺寸", "线盘", "部品编号", "订单号", "订单数量", "已送数量", "本次数量",
                "备注" };
        for (int i = 0; i < headers.length; i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(headers[i]);
            cell.setCellStyle(centerStyle);
            sheet.setColumnWidth(i, 3000);
        }

        // 数据行
        int rowIndex = 6;
        for (OrderEntryDetails detail : orderEntryDetailsList) {
            Row dataRow = sheet.createRow(rowIndex++);

            dataRow.createCell(0)
                    .setCellValue(detail.getDemandCustomerAlias() != null ? detail.getDemandCustomerAlias() : "");
            dataRow.createCell(1).setCellValue(detail.getOutboundDate() != null ? detail.getOutboundDate() : "");
            dataRow.createCell(2).setCellValue(""); // AM
            dataRow.createCell(3).setCellValue(""); // PM
            dataRow.createCell(4).setCellValue(detail.getArrivalDate() != null ? detail.getArrivalDate() : "");
            dataRow.createCell(5).setCellValue(detail.getModelNumber() != null ? detail.getModelNumber() : "");
            dataRow.createCell(6).setCellValue(detail.getSize() != null ? detail.getSize() : "");
            dataRow.createCell(7).setCellValue(detail.getWireSpool() != null ? detail.getWireSpool() : "");
            dataRow.createCell(8).setCellValue(detail.getPartNumber() != null ? detail.getPartNumber() : "");
            dataRow.createCell(9).setCellValue(detail.getOrderNo() != null ? detail.getOrderNo() : "");
            dataRow.createCell(10).setCellValue(detail.getOrderQuantity() != null ? detail.getOrderQuantity() : "");
            dataRow.createCell(11)
                    .setCellValue(detail.getDeliveredQuantity() != null ? detail.getDeliveredQuantity() : "0");

            // 计算本次数量 = 订单数量 - 已送数量
            String currentQuantity = "";
            try {
                String orderQty = detail.getOrderQuantity() != null ? detail.getOrderQuantity() : "0";
                String deliveredQty = detail.getDeliveredQuantity() != null ? detail.getDeliveredQuantity() : "0";

                double orderQuantityNum = Double.parseDouble(orderQty);
                double deliveredQuantityNum = Double.parseDouble(deliveredQty);
                double currentQuantityNum = orderQuantityNum - deliveredQuantityNum;

                // 如果结果为整数，显示为整数；否则显示小数
                if (currentQuantityNum == (int) currentQuantityNum) {
                    currentQuantity = String.valueOf((int) currentQuantityNum);
                } else {
                    currentQuantity = String.valueOf(currentQuantityNum);
                }
            } catch (NumberFormatException e) {
                currentQuantity = "0";
            }
            dataRow.createCell(12).setCellValue(currentQuantity);
            dataRow.createCell(13).setCellValue(""); // 备注

            // 应用样式
            for (int i = 0; i < headers.length; i++) {
                dataRow.getCell(i).setCellStyle(centerStyle);
            }
        }

        // 设置响应头
        String fileName = "紧急出库变更通知_" + outboundStartDate.replace("-", "") + ".xlsx";
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setHeader("Content-Disposition",
                "attachment;filename=" + new String(fileName.getBytes("UTF-8"), "ISO-8859-1"));

        // 输出Excel
        ServletOutputStream outputStream = response.getOutputStream();
        workbook.write(outputStream);
        outputStream.flush();
        outputStream.close();
        workbook.close();
    }

    /**
     * 导出周出库计划Excel
     * 
     * @param orderEntryDetailsList 订单详情列表
     * @param outboundStartDate     出库开始日期
     * @param outboundEndDate       出库结束日期
     * @param response              HTTP响应
     * @throws Exception
     */
    private void exportWeeklyShipmentExcel(List<OrderEntryDetails> orderEntryDetailsList,
            String outboundStartDate, String outboundEndDate,
            HttpServletResponse response) throws Exception {
        // 创建工作簿
        Workbook workbook = new XSSFWorkbook();
        Sheet sheet = workbook.createSheet("周出库计划");

        // 设置默认行高
        sheet.setDefaultRowHeight((short) 400);

        // 创建样式
        CellStyle centerStyle = workbook.createCellStyle();
        centerStyle.setAlignment(HorizontalAlignment.CENTER);
        centerStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        centerStyle.setBorderTop(BorderStyle.THIN);
        centerStyle.setBorderBottom(BorderStyle.THIN);
        centerStyle.setBorderLeft(BorderStyle.THIN);
        centerStyle.setBorderRight(BorderStyle.THIN);

        CellStyle titleStyle = workbook.createCellStyle();
        titleStyle.setAlignment(HorizontalAlignment.CENTER);
        titleStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        Font titleFont = workbook.createFont();
        titleFont.setBold(true);
        titleFont.setFontHeightInPoints((short) 14);
        titleStyle.setFont(titleFont);

        // 右上角信息
        Row row0 = sheet.createRow(0);
        row0.createCell(13).setCellValue("记录号：RD-SD-021");
        Row row1 = sheet.createRow(1);
        row1.createCell(13).setCellValue("修改号：01");
        Row row2 = sheet.createRow(2);
        row2.createCell(13).setCellValue("文件号：QP-SD-003");

        // 标题
        Row titleRow = sheet.createRow(4);
        Cell titleCell = titleRow.createCell(0);
        String dateRange = outboundStartDate.replace("-", "/") + "-" + outboundEndDate.replace("-", "/");
        titleCell.setCellValue("周出库计划（WIN-W）(" + dateRange + ")");
        titleCell.setCellStyle(titleStyle);
        sheet.addMergedRegion(new CellRangeAddress(4, 4, 0, 13));

        // 表头
        Row headerRow = sheet.createRow(6);
        String[] headers = { "客户", "出库日期", "AM", "PM", "到货日期", "型号", "尺寸", "线盘", "部品编号", "订单号", "订单数量", "已送数量", "本次数量",
                "备注" };
        for (int i = 0; i < headers.length; i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(headers[i]);
            cell.setCellStyle(centerStyle);
            sheet.setColumnWidth(i, 3000);
        }

        // 数据行 - 按客户分组
        int rowIndex = 7;

        // 按客户分组排序
        orderEntryDetailsList.sort((a, b) -> {
            String customerA = a.getDemandCustomerAlias() != null ? a.getDemandCustomerAlias() : "";
            String customerB = b.getDemandCustomerAlias() != null ? b.getDemandCustomerAlias() : "";
            return customerA.compareTo(customerB);
        });

        // 按客户分组数据
        Map<String, List<OrderEntryDetails>> customerGroups = new LinkedHashMap<>();
        for (OrderEntryDetails detail : orderEntryDetailsList) {
            String customerName = detail.getDemandCustomerAlias() != null ? detail.getDemandCustomerAlias() : "";
            customerGroups.computeIfAbsent(customerName, k -> new ArrayList<>()).add(detail);
        }

        // 创建合计行样式
        CellStyle subtotalStyle = workbook.createCellStyle();
        subtotalStyle.cloneStyleFrom(centerStyle);
        Font subtotalFont = workbook.createFont();
        subtotalFont.setBold(true);
        subtotalStyle.setFont(subtotalFont);

        // 遍历每个客户组
        for (Map.Entry<String, List<OrderEntryDetails>> entry : customerGroups.entrySet()) {
            String customerName = entry.getKey();
            List<OrderEntryDetails> customerDetails = entry.getValue();

            int customerStartRow = rowIndex;
            double customerSubtotal = 0.0; // 客户本次数量合计

            // 添加客户的所有订单行
            for (OrderEntryDetails detail : customerDetails) {
                Row dataRow = sheet.createRow(rowIndex++);

                dataRow.createCell(0).setCellValue(customerName);
                dataRow.createCell(1).setCellValue(detail.getOutboundDate() != null ? detail.getOutboundDate() : "");
                dataRow.createCell(2).setCellValue(""); // AM
                dataRow.createCell(3).setCellValue(""); // PM
                dataRow.createCell(4).setCellValue(detail.getArrivalDate() != null ? detail.getArrivalDate() : "");
                dataRow.createCell(5).setCellValue(detail.getModelNumber() != null ? detail.getModelNumber() : "");
                dataRow.createCell(6).setCellValue(detail.getSize() != null ? detail.getSize() : "");
                dataRow.createCell(7).setCellValue(detail.getWireSpool() != null ? detail.getWireSpool() : "");
                dataRow.createCell(8).setCellValue(detail.getPartNumber() != null ? detail.getPartNumber() : "");
                dataRow.createCell(9).setCellValue(detail.getOrderNo() != null ? detail.getOrderNo() : "");
                dataRow.createCell(10).setCellValue(detail.getOrderQuantity() != null ? detail.getOrderQuantity() : "");
                dataRow.createCell(11)
                        .setCellValue(detail.getDeliveredQuantity() != null ? detail.getDeliveredQuantity() : "0");

                // 计算本次数量 = 订单数量 - 已送数量
                String currentQuantity = "";
                double currentQuantityNum = 0.0;
                try {
                    String orderQty = detail.getOrderQuantity() != null ? detail.getOrderQuantity() : "0";
                    String deliveredQty = detail.getDeliveredQuantity() != null ? detail.getDeliveredQuantity() : "0";

                    double orderQuantityNum = Double.parseDouble(orderQty);
                    double deliveredQuantityNum = Double.parseDouble(deliveredQty);
                    currentQuantityNum = orderQuantityNum - deliveredQuantityNum;

                    // 如果结果为整数，显示为整数；否则显示小数
                    if (currentQuantityNum == (int) currentQuantityNum) {
                        currentQuantity = String.valueOf((int) currentQuantityNum);
                    } else {
                        currentQuantity = String.valueOf(currentQuantityNum);
                    }
                } catch (NumberFormatException e) {
                    currentQuantity = "0";
                    currentQuantityNum = 0.0;
                }
                dataRow.createCell(12).setCellValue(currentQuantity);
                dataRow.createCell(13).setCellValue(""); // 备注

                // 累加客户合计
                customerSubtotal += currentQuantityNum;

                // 应用样式
                for (int i = 0; i < headers.length; i++) {
                    dataRow.getCell(i).setCellStyle(centerStyle);
                }
            }

            // 合并客户名称单元格
            if (customerDetails.size() > 1) {
                sheet.addMergedRegion(new CellRangeAddress(customerStartRow, rowIndex - 1, 0, 0));
            }

            // 添加SUB-TOTAL行
            Row subtotalRow = sheet.createRow(rowIndex++);
            subtotalRow.createCell(0).setCellValue("");
            subtotalRow.createCell(1).setCellValue("");
            subtotalRow.createCell(2).setCellValue("");
            subtotalRow.createCell(3).setCellValue("");
            subtotalRow.createCell(4).setCellValue("");
            subtotalRow.createCell(5).setCellValue("");
            subtotalRow.createCell(6).setCellValue("");
            subtotalRow.createCell(7).setCellValue("");
            subtotalRow.createCell(8).setCellValue("");
            subtotalRow.createCell(9).setCellValue("");
            subtotalRow.createCell(10).setCellValue("");
            subtotalRow.createCell(11).setCellValue("** SUB-TOTAL **");

            // 格式化合计数量
            String subtotalQuantity;
            if (customerSubtotal == (int) customerSubtotal) {
                subtotalQuantity = String.valueOf((int) customerSubtotal);
            } else {
                subtotalQuantity = String.valueOf(customerSubtotal);
            }
            subtotalRow.createCell(12).setCellValue(subtotalQuantity);
            subtotalRow.createCell(13).setCellValue("");

            // 应用合计行样式
            for (int i = 0; i < headers.length; i++) {
                if (i == 11 || i == 12) { // SUB-TOTAL和数量列使用粗体
                    subtotalRow.getCell(i).setCellStyle(subtotalStyle);
                } else {
                    subtotalRow.getCell(i).setCellStyle(centerStyle);
                }
            }
        }

        // 设置响应头
        String fileName = "周出库计划_" + outboundStartDate.replace("-", "") + "_" + outboundEndDate.replace("-", "")
                + ".xlsx";
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setHeader("Content-Disposition",
                "attachment;filename=" + new String(fileName.getBytes("UTF-8"), "ISO-8859-1"));

        // 输出Excel
        ServletOutputStream outputStream = response.getOutputStream();
        workbook.write(outputStream);
        outputStream.flush();
        outputStream.close();
        workbook.close();
    }

    /*=================================出库数量录入列表页面======================================*/
    /**
     * 跳转出库数量录入列表页面
     * @param model
     * @throws Exception
     * @return
     */
    @GetMapping("/shipmentQuantityEntry/list/view")
    public String shipmentQuantityEntryListView(Model model) throws Exception{
        //获取客户列表
        List<Customers> customersList = sumitomoService.listCustomersList();

        model.addAttribute("customersList",customersList);

        return "/modules/order/shipmentQuantityEntry_list";
    }

    /**
     * 查询出库数量录入列表
     * @param page 当前页码
     * @param limit 每页条数
     * @param outboundStartDate 出库开始日期
     * @param outboundEndDate 出库结束日期
     * @param customerCode 客户代码
     * @return
     */
    @PostMapping("/shipmentQuantityEntry/list")
    @ResponseBody
    public Object shipmentQuantityEntryList(Integer page, Integer limit, String outboundStartDate, String outboundEndDate, String customerCode) throws Exception {
        // 只能查询本人录入的出货数量
        AuthorizingUser authorizingUser = SingletonLoginUtils.getUser();
        if (authorizingUser == null) {
            return new HrResult(CommonReturnCode.FAILED, "登录超时，请重新登录");
        }

        User user = userService.getById(authorizingUser.getUserId());

        PageInfo pageInfo = new PageInfo(limit, page);

        ShipmentQuantityInfoPageDTO shipmentQuantityInfoPageDTO = orderService.queryShipmentQuantityEntryListByPage(pageInfo, outboundStartDate, outboundEndDate, customerCode, user.getUserName());

        return new HrPageResult(shipmentQuantityInfoPageDTO.getShipmentQuantityInfoList(), shipmentQuantityInfoPageDTO.getPageInfo().getTotal());
    }

    /**
     * 跳转到出库数量录入新增页面
     * @param model
     * @throws Exception
     * @return
     */
    @GetMapping("/shipmentQuantityEntry/add/view")
    public String shipmentQuantityEntryAddView(Model model) throws Exception {
        //获取客户列表
        List<Customers> customersList = sumitomoService.listCustomersList();

        model.addAttribute("customersList",customersList);

        return "/modules/order/shipmentQuantityEntry_add";
    }

    /**
     * 查询出库数量信息列表（用于新增出库数量）
     * @param page 当前页码
     * @param limit 每页条数
     * @param outboundStartDate 出库开始日期
     * @param outboundEndDate 出库结束日期
     * @param customerCode 客户代码
     * @return
     */
    @PostMapping("/shipmentQuantityEntry/listForAdd")
    @ResponseBody
    public Object shipmentQuantityEntryListForAdd(Integer page, Integer limit, String outboundStartDate, String outboundEndDate, String customerCode) throws Exception {
        // 只能查询本人录入的数据
        AuthorizingUser authorizingUser = SingletonLoginUtils.getUser();
        if (authorizingUser == null) {
            return new HrResult(CommonReturnCode.FAILED, "登录超时，请重新登录");
        }

        User user = userService.getById(authorizingUser.getUserId());

        PageInfo pageInfo = new PageInfo(limit, page);

        ShipmentQuantityInfoPageDTO shipmentQuantityInfoPageDTO = orderService.queryShipmentQuantityEntryListForAdd(
                pageInfo, outboundStartDate, outboundEndDate, customerCode, user.getUserName(), null);

        return new HrPageResult(shipmentQuantityInfoPageDTO.getShipmentQuantityInfoList(), shipmentQuantityInfoPageDTO.getPageInfo().getTotal());
    }

    /**
     * 新增出库数量信息
     * @param outboundStartDate 出库开始日期
     * @param outboundEndDate 出库结束日期
     * @param customerCode 客户代码
     * @param outboundDateAndCustomerCodeAndProductCodeListStr 到货日期-客户编号-产品编号集合拼接字符串（例：2025-04-23-001-002,2025-04-23-003-006）
     * @return
     */
    @PostMapping("/shipmentQuantityEntry/add")
    @ResponseBody
    public Object shipmentQuantityEntryAdd(String outboundStartDate, String outboundEndDate, String customerCode, String outboundDateAndCustomerCodeAndProductCodeListStr) throws Exception {
        if (StringUtil.isStringEmpty(outboundDateAndCustomerCodeAndProductCodeListStr)) {
            return new HrResult(0, "没有需要新增的信息");
        }

        AuthorizingUser authorizingUser = SingletonLoginUtils.getUser();
        if(authorizingUser == null ) {
            return new HrResult(CommonReturnCode.FAILED,"登录超时，请重新登录");
        }

        User user = userService.getById(authorizingUser.getUserId());

        // 将到货日期-客户编号-产品编号集合拼接字符串转换为列表
        List<String> outboundDateAndCustomerCodeAndProductCodeList = Arrays.asList(outboundDateAndCustomerCodeAndProductCodeListStr.split(","));

        PageInfo pageInfo = new PageInfo(99999, 1);

        // 查询出库数量信息列表（用于新增出库数量）
        ShipmentQuantityInfoPageDTO shipmentQuantityInfoPageDTO = orderService.queryShipmentQuantityEntryListForAdd(
                pageInfo, outboundStartDate, outboundEndDate, customerCode, user.getUserName(),
                outboundDateAndCustomerCodeAndProductCodeList);

        if (shipmentQuantityInfoPageDTO == null || shipmentQuantityInfoPageDTO.getShipmentQuantityInfoList().isEmpty()) {
            return new HrResult(0, "没有需要新增的信息");
        }

        List<ShipmentQuantityInfo> shipmentQuantityInfoList = shipmentQuantityInfoPageDTO.getShipmentQuantityInfoList();

        // 整合出库日期-产品代码-客户订单号拼接字符串列表，用于查询已存在的订单数量信息
        List<String> queryParamList = shipmentQuantityInfoList.stream()
                .map(shipmentQuantityInfo -> shipmentQuantityInfo.getOutboundDate() + "-" + shipmentQuantityInfo.getProductCode() + "-" + shipmentQuantityInfo.getCustomerOrderNo())
                .collect(Collectors.toList());

        // 查询出库数量详情列表，用于校验是否已存在该订单数量信息
        List<ShipmentQuantityInfo> shipmentQuantityEntryDetailList = orderService.queryShipmentQuantityEntryDetailList(queryParamList);

        if (!shipmentQuantityEntryDetailList.isEmpty()) {
            // 移除已存在的订单数量信息
            shipmentQuantityInfoList.removeIf(shipmentQuantityInfo -> 
                shipmentQuantityEntryDetailList.stream().anyMatch(detail -> 
                    detail.getOutboundDate().equals(shipmentQuantityInfo.getOutboundDate()) 
                    && detail.getProductCode().equals(shipmentQuantityInfo.getProductCode()) 
                    && detail.getCustomerOrderNo().equals(shipmentQuantityInfo.getCustomerOrderNo()) 
                    && detail.getShipmentOrderNo().equals(shipmentQuantityInfo.getShipmentOrderNo())
                    && detail.getShipmentOrderSerial().equals(shipmentQuantityInfo.getShipmentOrderSerial())
                )
            );
        }

        if (shipmentQuantityInfoList.isEmpty()) {
            return new HrResult(0, "没有需要新增的信息");
        }

        // 整合出库日期-产品代码-客户订单号拼接字符串列表，用于查询出货计划番号
       queryParamList = shipmentQuantityInfoList.stream()
                .map(shipmentQuantityInfo -> shipmentQuantityInfo.getOutboundDate() + "-" + shipmentQuantityInfo.getProductCode() + "-" + shipmentQuantityInfo.getCustomerOrderNo())
                .collect(Collectors.toList());

        // 获取出货计划番号列表
        List<ShipmentQuantityInfo> shipmentPlanNoList = Optional.ofNullable(orderService.queryShipmentPlanNoList(queryParamList)).orElse(new ArrayList<>());

        // 将出货计划番号列表转换为Map
        Map<String, String> shipmentPlanNoMap = shipmentPlanNoList.stream()
                .collect(Collectors.toMap(shipmentQuantityInfo -> shipmentQuantityInfo.getOutboundDate() + "-" + shipmentQuantityInfo.getProductCode() + "-" + shipmentQuantityInfo.getCustomerOrderNo(), ShipmentQuantityInfo::getShipmentPlanNo));

        // 整合出货计划番号
        for (ShipmentQuantityInfo shipmentQuantityInfo : shipmentQuantityInfoList) {
            shipmentQuantityInfo.setShipmentPlanNo(shipmentPlanNoMap.get(shipmentQuantityInfo.getOutboundDate() + "-" + shipmentQuantityInfo.getProductCode() + "-" + shipmentQuantityInfo.getCustomerOrderNo()));
            shipmentQuantityInfo.setCreator(user.getUserName());
        }

        // 批量新增出库数量信息
        orderService.batchAddShipmentQuantityEntryList(shipmentQuantityInfoList);

        // 将出货计划数量列表转换为Map
        Map<String, BigDecimal> shipmentQuantityInfoMap = shipmentQuantityInfoList.stream()
                .collect(Collectors.toMap(shipmentQuantityInfo -> shipmentQuantityInfo.getOutboundDate() + "-" + shipmentQuantityInfo.getProductCode() + "-" + shipmentQuantityInfo.getCustomerOrderNo(), ShipmentQuantityInfo::getShipmentQuantity));

        // 同步出货计划数量到订单明细表的已送数量字段
        if (CollectionUtils.isNotEmpty(shipmentPlanNoList)) {
            for (ShipmentQuantityInfo shipmentPlanNoInfo : shipmentPlanNoList) {
                OrderEntryDetails orderEntryDetailInfo = new OrderEntryDetails();
                orderEntryDetailInfo.setOrderNo(shipmentPlanNoInfo.getShipmentOrderNo());
//                orderEntryDetailInfo.setOrderSerialNum(String.valueOf(shipmentPlanNoInfo.getShipmentOrderSerial()));
                orderEntryDetailInfo.setDeliveredQuantity(Optional.ofNullable(shipmentQuantityInfoMap.get(shipmentPlanNoInfo.getOutboundDate() + "-" + shipmentPlanNoInfo.getProductCode() + "-" + shipmentPlanNoInfo.getCustomerOrderNo())).orElse(BigDecimal.ZERO).toString());
                orderEntryDetailInfo.setUpdaterName(user.getUserName());

                orderService.updateOrderEntryDetailDeliveredQuantity(orderEntryDetailInfo);
            }
        }

        return new HrResult(CommonReturnCode.SUCCESS, "新增成功");
    }

    /**
     * 跳转到出库数量录入编辑页面
     * @param model
     * @param id 流水号
     * @throws Exception
     * @return
     */ 
    @GetMapping("/shipmentQuantityEntry/edit/view")
    public String shipmentQuantityEntryEditView(Model model, String id) throws Exception {
        ShipmentQuantityInfo shipmentQuantityInfo = orderService.queryShipmentQuantityEntryDetail(id);

        model.addAttribute("shipmentQuantityInfo", shipmentQuantityInfo);

        return "/modules/order/shipmentQuantityEntry_edit";
    }
    
    /**
     * 更新出库数量信息
     * @param id 流水号
     * @param outboundDateAndProductCodeAndCustomerOrderNoStr 到货日期-产品编号-客户订单号拼接字符串（例：2025-04-23-001-002）
     * @param shipmentQuantity 出库数量
     * @return
     */
    @PostMapping("/shipmentQuantityEntry/update")
    @ResponseBody
    public Object shipmentQuantityEntryUpdate(Integer id, String outboundDateAndProductCodeAndCustomerOrderNoStr, String shipmentQuantity) throws Exception {
        AuthorizingUser authorizingUser = SingletonLoginUtils.getUser();
        if(authorizingUser == null ) {
            return new HrResult(CommonReturnCode.FAILED,"登录超时，请重新登录");
        }

        User user = userService.getById(authorizingUser.getUserId());

        List<String> outboundDateAndProductCodeAndCustomerOrderNoStrList = new ArrayList<>();
        if (!StringUtil.isStringEmpty(outboundDateAndProductCodeAndCustomerOrderNoStr)) {
            outboundDateAndProductCodeAndCustomerOrderNoStrList.add(outboundDateAndProductCodeAndCustomerOrderNoStr);
        }

        // 获取出货计划番号列表
        List<ShipmentQuantityInfo> shipmentPlanNoList = Optional.ofNullable(orderService.queryShipmentPlanNoList(outboundDateAndProductCodeAndCustomerOrderNoStrList)).orElse(new ArrayList<>());

        String shipmentPlanNo = null;
        if (CollectionUtils.isNotEmpty(shipmentPlanNoList)) {
            shipmentPlanNo = shipmentPlanNoList.get(0).getShipmentPlanNo();
        }

        orderService.updateShipmentQuantityEntry(id, shipmentQuantity, shipmentPlanNo);

        // 同步出货计划数量到订单明细表的已送数量字段
        if (CollectionUtils.isNotEmpty(shipmentPlanNoList)) {
            for (ShipmentQuantityInfo shipmentPlanNoInfo : shipmentPlanNoList) {
                OrderEntryDetails orderEntryDetailInfo = new OrderEntryDetails();
                orderEntryDetailInfo.setOrderNo(shipmentPlanNoInfo.getShipmentOrderNo());
//                orderEntryDetailInfo.setOrderSerialNum(String.valueOf(shipmentPlanNoInfo.getShipmentOrderSerial()));
                orderEntryDetailInfo.setDeliveredQuantity(shipmentQuantity);
                orderEntryDetailInfo.setUpdaterName(user.getUserName());

                orderService.updateOrderEntryDetailDeliveredQuantity(orderEntryDetailInfo);
            }
        }

        return new HrResult(CommonReturnCode.SUCCESS, "更新成功");
    }

    /**
     * 删除出库数量信息
     * @param id 流水号
     * @return
     */
    @PostMapping("/shipmentQuantityEntry/delete")
    @ResponseBody
    public Object shipmentQuantityEntryDelete(Integer id) throws Exception {
        orderService.deleteShipmentQuantityEntry(id);

        return new HrResult(CommonReturnCode.SUCCESS, "删除成功");
    }
}
