<%@ page language="java" import="java.util.*"
         contentType="text/html; charset=utf-8"%>
<%@ include file="/WEB-INF/layouts/base.jsp"%>
<html>
<head>
    <title>铜条件添加</title>
    <style>
        /*设置数据表表头字体*/
        .layui-table th .layui-table-cell{
            text-align: center;
            font-size: 16px;
        }
    </style>
</head>
<body>
<form class="layui-form hr-form-add" action="${ctx}/salesCommon/copperConditions/add" method="post" id="submitForm">
    <div class="layui-fluid hr-layui-fluid">
        <div class="layui-card">
            <div class="layui-card-body">
                <div class="layui-form-item layui-row">
                    <div class="layui-inline layui-col-md5">
                        <label class="layui-form-label"><span class="star">*</span>铜条件:</label>
                        <div class="layui-input-block">
                            <input type="text" class="layui-input" id="copperCondition" name="copperCondition" required lay-verify="required" />
                        </div>
                    </div>
                    <div class="layui-inline layui-col-md5">
                        <label class="layui-form-label"><span class="star">*</span>铜条件名:</label>
                        <div class="layui-input-block">
                             <input type="text" class="layui-input" id="copperConditionName" name="copperConditionName" required lay-verify="required" />
                        </div>
                    </div>
                    <div class="layui-inline layui-col-md5">
                        <label class="layui-form-label"><span class="star">*</span>货币:</label>
		           		<div class="layui-input-block">
		          			<select class="layui-select" id="currency" name="currency" lay-search="true" lay-verify="required" required>
		          				<option value="">请选择</option>
		                     	<c:forEach items="${currencyList}" var="currency">
		                        	<option value="${currency}">${currency}</option>
		                       	</c:forEach>
		                    </select>
		          		</div>
                    </div>
                </div>
                <div class="layui-form-item layui-row">
                    <div class="layui-inline layui-col-md12">
                        <label class="layui-form-label"></label>
                        <div class="layui-input-block">
                            <button type="button" class="layui-btn layui-bg-blue" lay-submit lay-filter="formDemo">保存</button>
                            <button type="button" class="layui-btn layui-btn-primary" onclick="closeAll();">取消</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</form>
<myfooter>
    <script src="${ctxsta}/hongru/js/salesCommon/copperConditions_add.js?time=1"></script>
</myfooter>
</body>
</html>
