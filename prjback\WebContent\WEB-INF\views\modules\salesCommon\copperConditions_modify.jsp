<%@ page language="java" import="java.util.*"
         contentType="text/html; charset=utf-8"%>
<%@ include file="/WEB-INF/layouts/base.jsp"%>
<html>
<head>
    <title>铜条件修改</title>
    <style>
        /*设置数据表表头字体*/
        .layui-table th .layui-table-cell{
            text-align: center;
        }
    </style>
</head>
<body>
<form class="layui-form hr-form-add" action="${ctx}/salesCommon/copperConditions/edit" method="post" id="submitForm">
    <div class="layui-fluid hr-layui-fluid">
        <div class="layui-card">
            <div class="layui-card-body">
                <div class="layui-form-item layui-row">

                    <div class="layui-inline layui-col-md5">
                        <label class="layui-form-label"><span class="star">*</span>铜条件:</label>
                        <div class="layui-input-block">
                            <input type="text" class="layui-input" id="copperCondition" name="copperCondition" value="${copperConditions.copperCondition}" required lay-verify="required" />
                        </div>
                    </div>
                    <div class="layui-inline layui-col-md5">
                        <label class="layui-form-label"><span class="star">*</span>铜条件名:</label>
                        <div class="layui-input-block">
                            <input type="text" class="layui-input" id="copperConditionName" name="copperConditionName" value="${copperConditions.copperConditionName}" required lay-verify="required" />
                        </div>
                    </div>
                    <div class="layui-inline layui-col-md5">
                        <label class="layui-form-label"><span class="star">*</span>货币:</label>
                        <div class="layui-input-block">
                            <select class="layui-select" id="currency" name="currency" required>
		                     	<c:forEach items="${currencyList}" var="currency">
		                        	<option value="${currency}"${copperConditions.currency eq currency ?'selected="selected"':''}>${currency}</option>
		                       	</c:forEach>
                            </select>
                        </div>
                    </div>
                </div>
                </div>
                <div class="layui-form-item layui-row">
                    <div class="layui-inline layui-col-md12">
                        <label class="layui-form-label"></label>
                        <div class="layui-input-block">
                            <input type="hidden" id="id" name="id" value="${copperConditions.id}"/>
                            <button type="button" class="layui-btn layui-bg-blue" lay-submit lay-filter="formDemo">保存</button>
                            <button type="button" class="layui-btn layui-btn-primary" onclick="closeAll();">取消</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</form>
<myfooter>
    <script src="${ctxsta}/hongru/js/salesCommon/copperConditions_modify.js?time=1"></script>
</myfooter>
</body>
</html>
