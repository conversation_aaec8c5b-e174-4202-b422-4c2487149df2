package com.hongru.entity.businessOps;

import java.math.BigDecimal;

import com.baomidou.mybatisplus.annotations.TableField;
import com.baomidou.mybatisplus.annotations.TableId;
import com.baomidou.mybatisplus.annotations.TableName;
import com.baomidou.mybatisplus.enums.IdType;


@TableName("铜价表")//CostPrice
public class CopperPrices {

	/* 流水号 */
	@TableId(value="流水号", type= IdType.AUTO)
	protected int id;
	/* 铜条件 */
	protected String copperCondition;
	/* 适用开始日期 */
	protected String applyDateStart;
	/* 适用终了日期 */
	protected String applyDateEnd;
	/* 铜基本 */
	protected BigDecimal copperPrice;
	/* 创建人姓名 */
	protected String creatorName;
	/* 创建时间 */
	protected String createdTime;
	/* 更新人姓名 */
	protected String updaterName;
	/* 更新时间 */
	protected String updatedTime;
	
	/* 铜条件名 */
	@TableField(exist = false)
	protected String copperConditionName;
	/* 货币 */
	@TableField(exist = false)
	protected String currency;
	
	public int getId() {
		return id;
	}
	public void setId(int id) {
		this.id = id;
	}
	public String getCopperCondition() {
		return copperCondition;
	}
	public void setCopperCondition(String copperCondition) {
		this.copperCondition = copperCondition;
	}
	public String getApplyDateStart() {
		return applyDateStart;
	}
	public void setApplyDateStart(String applyDateStart) {
		this.applyDateStart = applyDateStart;
	}
	public String getApplyDateEnd() {
		return applyDateEnd;
	}
	public void setApplyDateEnd(String applyDateEnd) {
		this.applyDateEnd = applyDateEnd;
	}
	public BigDecimal getCopperPrice() {
		return copperPrice;
	}
	public void setCopperPrice(BigDecimal copperPrice) {
		this.copperPrice = copperPrice;
	}
	public String getCreatorName() {
		return creatorName;
	}
	public void setCreatorName(String creatorName) {
		this.creatorName = creatorName;
	}
	public String getCreatedTime() {
		return createdTime;
	}
	public void setCreatedTime(String createdTime) {
		this.createdTime = createdTime;
	}
	public String getUpdaterName() {
		return updaterName;
	}
	public void setUpdaterName(String updaterName) {
		this.updaterName = updaterName;
	}
	public String getUpdatedTime() {
		return updatedTime;
	}
	public void setUpdatedTime(String updatedTime) {
		this.updatedTime = updatedTime;
	}
	public String getCopperConditionName() {
		return copperConditionName;
	}
	public void setCopperConditionName(String copperConditionName) {
		this.copperConditionName = copperConditionName;
	}
	public String getCurrency() {
		return currency;
	}
	public void setCurrency(String currency) {
		this.currency = currency;
	}
}