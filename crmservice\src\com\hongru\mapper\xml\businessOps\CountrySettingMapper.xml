<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hongru.mapper.businessOps.CountrySettingMapper">
    <sql id="countrySetting_sql">
		pc.[流水号] AS id,pc.[国家] AS country,pc.[国家名] AS countryName,
		pc.[创建人姓名] AS creatorName,pc.[创建时间] AS createdTime,
		pc.[更新人姓名] AS updaterName,pc.[更新时间] AS updatedTime
	</sql>

	<insert id="insertCountrySetting" parameterType="com.hongru.entity.businessOps.CountrySetting">
		INSERT INTO [businessOps].[dbo].[国家设定表]
		(
		[国家],
		[国家名],
		[创建人姓名],
		[创建时间]
		)VALUES(
		#{countrySetting.country},
		#{countrySetting.countryName},
		#{countrySetting.creatorName},
		#{countrySetting.createdTime}
		)
	</insert>

	<select id="selectCountrySettingById" resultType="com.hongru.entity.businessOps.CountrySetting">
		SELECT
		<include refid="countrySetting_sql"/>
		FROM [businessOps].[dbo].[国家设定表] pc
		<where>
			<if test="id != null">
				AND pc.[流水号] = #{id}
			</if>
		</where>
	</select>

	<select id="listCountrySetting" resultType="com.hongru.entity.businessOps.CountrySetting">
		SELECT
		<include refid="countrySetting_sql"/>
		FROM [businessOps].[dbo].[国家设定表] pc
		<where>
			<if test="country != null and country != ''">
				AND pc.[国家] = #{country}
			</if>
			<if test="countryName != null and countryName != ''">
				AND pc.[国家名] = #{countryName}
			</if>
		</where>
		ORDER BY pc.[流水号]
	</select>

	<update id="updateCountrySetting">
		UPDATE [businessOps].[dbo].[国家设定表]
		<set>
			<if test="countrySetting.country != null and countrySetting.country != ''">
				[国家] = #{countrySetting.country},
			</if>
			<if test="countrySetting.countryName != null and countrySetting.countryName != ''">
				[国家名] = #{countrySetting.countryName},
			</if>
			<if test="countrySetting.updaterName != null and countrySetting.updaterName != ''">
				[更新人姓名] = #{countrySetting.updaterName},
			</if>
			<if test="countrySetting.updatedTime != null">
				[更新时间] = #{countrySetting.updatedTime}
			</if>
		</set>
		WHERE [流水号] = #{countrySetting.id}
	</update>
	
	<delete id="deleteCountrySetting">
		DELETE [businessOps].[dbo].[国家设定表] WHERE [流水号] = #{id}
	</delete>
</mapper>