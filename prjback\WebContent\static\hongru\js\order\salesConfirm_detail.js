layui.use(['form', 'laydate', 'table', 'layer'], function(){
    var form = layui.form
        ,laydate = layui.laydate
        ,table = layui.table
        ,layer = layui.layer;

    // 初始化两行表头表格
    initializeSalesConfirmTwoRowTable();

    // 获取支付请求NO
    var paymentRequestNo = $('#paymentRequestNo').val() || getUrlParam('paymentRequestNo');

    // 获取URL参数的函数
    function getUrlParam(name) {
        var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
        var r = window.location.search.substr(1).match(reg);
        if (r != null) return unescape(r[2]);
        return null;
    }

    // 页面加载时自动加载数据
    if (paymentRequestNo) {
        loadDataByNo(paymentRequestNo);
    } else {
        layer.msg('支付请求NO参数缺失', {icon: 2});
    }

    // 根据支付请求NO加载数据
    function loadDataByNo(paymentRequestNo) {
        // 发送AJAX请求获取支付请求数据
        $.ajax({
            url: baselocation + '/order/salesConfirm/detail',
            type: 'POST',
            data: {paymentRequestNo: paymentRequestNo},
            success: function(result) {
                console.log('获取详情数据结果:', result);
                if (result.code === 1) {
                    var paymentAmount = result.data.paymentAmount;
                    var paymentAmountDetails = result.data.paymentAmountDetails;

                    console.log('支付请求数据:', paymentAmount);
                    console.log('支付请求明细数据:', paymentAmountDetails);

                    // 填充表单数据
                    $('#paymentRequestNoDisplay').val(paymentAmount.paymentRequestNo);
                    $('#paymentRequestDate').val(paymentAmount.paymentRequestDate);
                    $('#paymentDate').val(paymentAmount.paymentDate);
                    $('#customerAlias').val(paymentAmount.customerAlias);
                    $('#currency').val(paymentAmount.settlementCurrency);

                    // 设置税率字段
                    var taxRateValue = paymentAmount.taxCode;
                    $('#taxCode').val(taxRateValue);
                    console.log('设置税率值:', taxRateValue);

                    // 填充确认信息
                    $('#confirmer').val(paymentAmount.confirmer || '');
                    $('#confirmTime').val(paymentAmount.confirmTime || '');

                    // 设置确认状态
                    var confirmStatus = paymentAmount.paymentRequestConfirm === '1' ? '已确认' : '未确认';
                    $('#confirmStatus').val(confirmStatus);

                    // 显示相应的操作按钮
                    if (paymentAmount.paymentRequestConfirm === '1') {
                        $('#cancelConfirmBtn').show();
                        $('#confirmBtn').hide();
                    } else {
                        $('#confirmBtn').show();
                        $('#cancelConfirmBtn').hide();
                    }

                    // 渲染明细表格
                    if (paymentAmountDetails && paymentAmountDetails.length > 0) {
                        renderDetailTable(paymentAmountDetails);
                        calculateTotalAmount();
                    } else {
                        // 如果没有明细数据，直接显示支付金额
                        if (paymentAmount.paymentAmount) {
                            $('#totalAmount').val('¥' + parseFloat(paymentAmount.paymentAmount).toFixed(2));
                        }
                    }
                } else {
                    layer.msg('获取数据失败: ' + (result.message || '未知错误'), {icon: 2});
                }
            },
            error: function(xhr, status, error) {
                console.error('获取数据失败:', error);
                layer.msg('获取数据失败: ' + error, {icon: 2});
            }
        });
    }

    // 渲染明细表格（只读模式）
    function renderDetailTable(detailsData) {
        // 清空现有数据
        $('#detailTable tbody.record').remove();

        detailsData.forEach(function(item, index) {
            // 使用新的添加行函数
            addSalesConfirmDetailRow(item);
        });
    }

    // 计算总金额
    function calculateTotalAmount() {
        var total = 0;
        // 修正选择器：选择每个记录的第二行（计算信息行）的第9列（请求金额）
        $('#detailTable tbody.record tr:nth-child(2) td:nth-child(9)').each(function() {
            var amount = parseFloat($(this).text()) || 0;
            total += amount;
        });
        updateTotalAmount(total);
    }

    // 更新总金额显示
    function updateTotalAmount(total) {
        $('#totalAmount').val('¥' + total.toFixed(2));
    }

    // 货币换算显示函数（销售额确认专用）
    function displayCurrencyConversionInfo(originalPrice, currency, arrivalDate, callback) {
        console.log('=== 销售额确认货币换算显示 ===');
        console.log('原价格:', originalPrice, '货币:', currency, '到货日期:', arrivalDate);

        // 如果是人民币，直接返回原价格
        if (!currency || currency === 'RMB') {
            console.log('货币为RMB，无需换算');
            callback(originalPrice, 1.0, '');
            return;
        }

        // 查询汇率用于显示换算信息
        $.ajax({
            url: baselocation + '/salesCommon/getCurrencyExchangeRate',
            type: 'POST',
            data: {
                originalCurrency: currency,
                arrivalDate: arrivalDate
            },
            success: function(result) {
                console.log('销售额确认汇率查询返回:', result);

                if (result.code === 1 && result.data && result.data.exchangeRate) {
                    var exchangeRate = parseFloat(result.data.exchangeRate);
                    var originalPriceFromRMB = (parseFloat(originalPrice) / exchangeRate).toFixed(4);
                    var conversionInfo = ' (原价: ' + originalPriceFromRMB + ' ' + currency + ', 汇率: ' + exchangeRate + ')';

                    console.log('汇率:', exchangeRate, '换算信息:', conversionInfo);
                    callback(originalPrice, exchangeRate, conversionInfo);
                } else {
                    console.log('未找到汇率数据，显示原价格');
                    callback(originalPrice, 1.0, '');
                }
            },
            error: function(xhr, status, error) {
                console.log('销售额确认查询汇率异常:', error);
                callback(originalPrice, 1.0, '');
            }
        });
    }

    // 确认按钮事件
    $('#confirmBtn').on('click', function () {
        layer.confirm('确认要确认该销售额吗？', {
            btn: ['确定', '取消']
        }, function (index) {
            $.ajax({
                url: baselocation + '/order/salesConfirm/confirm',
                type: 'POST',
                data: {
                    paymentRequestNo: paymentRequestNo
                },
                dataType: 'json',
                success: function (result) {
                    if (result.code === 1) {
                        layer.msg('确认成功!', {
                            icon: 6,
                            time: 1000
                        }, function () {
                            loadDataByNo(paymentRequestNo); // 重新加载数据
                        });
                    } else {
                        layer.alert(result.message, {
                            icon: 2
                        });
                    }
                },
                error: function () {
                    layer.alert('网络异常，请稍后重试', {
                        icon: 2
                    });
                }
            });
            layer.close(index);
        });
    });

    // 取消确认按钮事件
    $('#cancelConfirmBtn').on('click', function () {
        layer.confirm('确认要取消确认该销售额吗？', {
            btn: ['确定', '取消']
        }, function (index) {
            $.ajax({
                url: baselocation + '/order/salesConfirm/cancelConfirm',
                type: 'POST',
                data: {
                    paymentRequestNo: paymentRequestNo
                },
                dataType: 'json',
                success: function (result) {
                    if (result.code === 1) {
                        layer.msg('取消确认成功!', {
                            icon: 6,
                            time: 1000
                        }, function () {
                            loadDataByNo(paymentRequestNo); // 重新加载数据
                        });
                    } else {
                        layer.alert(result.message, {
                            icon: 2
                        });
                    }
                },
                error: function () {
                    layer.alert('网络异常，请稍后重试', {
                        icon: 2
                    });
                }
            });
            layer.close(index);
        });
    });
});

// 关闭当前窗口函数
function closeLayer() {
    // 由于详情页面是通过window.open在新窗口中打开的，所以直接关闭当前窗口
    window.close();
}

// 初始化销售额确认的两行表头表格（详情页面只读版本）
function initializeSalesConfirmTwoRowTable() {
    // 添加样式
    if (!$('#salesConfirmTwoRowStyle').length) {
        var style = $('<style id="salesConfirmTwoRowStyle">');
        style.text(
            '#detailTable {' +
                'width: 100%;' +
                'border-collapse: collapse;' +
                'margin-top: 10px;' +
            '}' +
            '#detailTable th, #detailTable td {' +
                'border: 1px solid #e6e6e6;' +
                'padding: 8px 6px;' +
                'text-align: center;' +
                'word-break: break-all;' +
                'font-size: 12px;' +
                'line-height: 1.3;' +
                'vertical-align: middle;' +
            '}' +
            '#detailTable thead th {' +
                'font-weight: bold;' +
                'white-space: nowrap;' +
            '}' +
            '/* 第一行表头 - 基础信息 */' +
            '#detailTable thead tr:first-child th {' +
                'background-color: #e6f7ff;' +
                'color: #1890ff;' +
                'border-bottom: 1px solid #1890ff;' +
            '}' +
            '/* 第二行表头 - 计算信息 */' +
            '#detailTable thead tr:last-child th {' +
                'background-color: #fff2e8;' +
                'color: #fa8c16;' +
                'border-bottom: 1px solid #fa8c16;' +
            '}' +
            '/* 每条记录用两个 <tr>，不同记录间用背景色区分 */' +
            '#detailTable tbody.record:nth-of-type(odd) td {' +
                'background: #f8f8f8;' +
            '}' +
            '#detailTable tbody.record:nth-of-type(even) td {' +
                'background: #fff;' +
            '}' +
            '/* 让第二行数据和第一行数据视觉上依然"连成一体" */' +
            '#detailTable tbody.record tr:first-child td {' +
                'border-bottom: none;' +
                'border-left: 3px solid #1890ff;' +
            '}' +
            '#detailTable tbody.record tr:last-child td {' +
                'border-top: none;' +
                'border-left: 3px solid #fa8c16;' +
                'border-bottom: 2px solid #d2d2d2;' +
            '}' +
            '.table-container {' +
                'overflow-x: auto;' +
                'max-height: 400px;' +
                'overflow-y: auto;' +
            '}'
        );
        $('head').append(style);
    }

    // 生成初始的空表格HTML
    var html = '<div class="table-container"><table id="detailTable">';

    // 生成表头 - 两行，每行13列，与支付请求登录完全一致
    html += '<thead>';
    // 第一行表头 - 基础信息标题
    html += '<tr>';
    html += '<th>出货单No.</th><th>送货单序号</th><th>条码</th><th>产品代码</th>';
    html += '<th>线盘</th><th>客户代码</th><th>出货日</th><th>到货日期</th><th>客户订单No.</th>';
    html += '<th>明细内部备注</th><th>铜合同类别</th><th>铜合同NO</th><th>铜条件</th>';
    html += '</tr>';
    // 第二行表头 - 计算信息标题
    html += '<tr>';
    html += '<th>铜货币</th><th>换算率</th><th>数量(KG)</th><th>铜base</th>';
    html += '<th>升水</th><th>换算后铜单价</th><th>0base</th><th>支付单价</th>';
    html += '<th>请求金额</th><th>产品中分类</th><th>出货计划番号</th><th></th><th></th>';
    html += '</tr>';
    html += '</thead>';

    html += '</table></div>';

    // 替换原来的表格
    var $container = $('#detailTable').parent();
    console.log('表格容器:', $container.length, $container.html());

    $container.html(html);

    console.log('已初始化销售额确认的两行表格（详情页面），容器内容:', $container.html());
}

// 添加销售额确认明细行到两行表格（详情页面只读版本）
function addSalesConfirmDetailRow(item) {
    var tableBody = $('#detailTable');

    // 处理数据
    var processedItem = {
        deliveryNoteNo: item.deliveryNoteNo || '',
        deliveryNoteSeq: item.deliveryNoteSeq || item.salesAmountSeq || '',
        barcode: item.barcode || '',
        productCode: item.productCode || '',
        wireDrum: item.wireDrum || '',
        customerCode: item.customerCode || '',
        shipmentDate: item.shipmentDate || '',
        arrivalDate: item.arrivalDate || '',
        customerOrderNo: item.customerOrderNo || '',
        internalRemark: item.internalRemark || item.detailRemark || '',
        copperContractType: item.copperContractType || '',
        copperContractNo: item.copperContractNo || '',
        copperCondition: item.copperCondition || '',
        copperCurrency: item.copperCurrency || item.currency || '',
        conversionRate: item.conversionRate || '',
        quantity: item.quantity || '',
        copperBase: item.copperBase || '',
        premium: item.premium || '',
        convertedCopperPrice: item.convertedCopperPrice || '',
        zeroBase: item.zeroBase || '',
        paymentUnitPrice: item.paymentUnitPrice || '',
        requestAmount: item.requestAmount || '',
        productMiddleCategory: item.productMiddleCategory || '',
        shipmentPlanNo: item.shipmentPlanNo || ''
    };

    // 生成唯一ID
    var recordId = 'record_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);

    // 第一行：基础信息（13列）
    var firstRowHtml = '<tbody class="record" data-record-id="' + recordId + '">';
    firstRowHtml += '<tr>';
    firstRowHtml += '<td>' + processedItem.deliveryNoteNo + '</td>';
    firstRowHtml += '<td>' + processedItem.deliveryNoteSeq + '</td>';
    firstRowHtml += '<td>' + processedItem.barcode + '</td>';
    firstRowHtml += '<td>' + processedItem.productCode + '</td>';
    firstRowHtml += '<td>' + processedItem.wireDrum + '</td>';
    firstRowHtml += '<td>' + processedItem.customerCode + '</td>';
    firstRowHtml += '<td>' + processedItem.shipmentDate + '</td>';
    firstRowHtml += '<td>' + processedItem.arrivalDate + '</td>';
    firstRowHtml += '<td>' + processedItem.customerOrderNo + '</td>';
    firstRowHtml += '<td>' + processedItem.internalRemark + '</td>';
    // 转换铜合同类别数字为中文
    var copperContractTypeText = '';
    switch(processedItem.copperContractType) {
        case '1': copperContractTypeText = '预约铜'; break;
        case '2': copperContractTypeText = '支给铜'; break;
        case '3': copperContractTypeText = '一般铜'; break;
        case '4': copperContractTypeText = '无偿'; break;
        default: copperContractTypeText = processedItem.copperContractType || '';
    }
    firstRowHtml += '<td>' + copperContractTypeText + '</td>';
    firstRowHtml += '<td>' + processedItem.copperContractNo + '</td>';
    firstRowHtml += '<td>' + processedItem.copperCondition + '</td>';
    firstRowHtml += '</tr>';

    // 第二行：计算信息（13列）
    firstRowHtml += '<tr>';
    firstRowHtml += '<td>' + processedItem.copperCurrency + '</td>';
    firstRowHtml += '<td>' + processedItem.conversionRate + '</td>';
    firstRowHtml += '<td>' + processedItem.quantity + '</td>';
    firstRowHtml += '<td>' + processedItem.copperBase + '</td>';
    firstRowHtml += '<td>' + processedItem.premium + '</td>';
    firstRowHtml += '<td>' + processedItem.convertedCopperPrice + '</td>';
    firstRowHtml += '<td>' + processedItem.zeroBase + '</td>';
    firstRowHtml += '<td>' + processedItem.paymentUnitPrice + '</td>';
    firstRowHtml += '<td>' + processedItem.requestAmount + '</td>';
    firstRowHtml += '<td>' + (processedItem.productMiddleCategory || '') + '</td>';
    firstRowHtml += '<td>' + (processedItem.shipmentPlanNo || '') + '</td>';
    firstRowHtml += '<td></td>';
    firstRowHtml += '<td></td>';
    firstRowHtml += '</tr>';
    firstRowHtml += '</tbody>';

    // 添加到表格
    tableBody.append(firstRowHtml);

    console.log('已添加销售额确认明细行（详情页面），记录ID:', recordId);
}
