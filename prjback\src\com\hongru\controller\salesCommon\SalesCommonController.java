package com.hongru.controller.salesCommon;

import com.hongru.base.BaseController;
import com.hongru.common.result.HrPageResult;
import com.hongru.common.result.HrResult;
import com.hongru.common.security.AuthorizingUser;
import com.hongru.common.util.DateUtils;
import com.hongru.common.util.SingletonLoginUtils;
import com.hongru.common.util.StringUtil;
import com.hongru.constant.CommonReturnCode;
import com.hongru.entity.admin.User;
import com.hongru.entity.businessOps.*;
import com.hongru.entity.sumitomo.Customers;
import com.hongru.entity.sumitomo.Product;
import com.hongru.pojo.dto.AscendingWaterPageDTO;
import com.hongru.pojo.dto.ContractDetailsPageDTO;
import com.hongru.pojo.dto.CopperContractPageDTO;
import com.hongru.pojo.dto.CopperPricesPageDTO;
import com.hongru.pojo.dto.CurrencyExchangeRatePageDTO;
import com.hongru.pojo.dto.CustomerDetailsPageDTO;
import com.hongru.pojo.dto.ProductsPricePageDTO;
import com.hongru.service.admin.IUserService;
import com.hongru.service.businessOps.ISalesCommonService;
import com.hongru.service.sumitomo.ISumitomoService;
import com.hongru.support.page.PageInfo;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.math.BigDecimal;
import java.util.*;

@Controller
@RequestMapping(value = "/salesCommon")
public class SalesCommonController extends BaseController {
    @Autowired
    private ISalesCommonService salesCommonService;
    @Autowired
    private IUserService userService;
    @Autowired
    private ISumitomoService sumitomoService;

    /*
     * =================================客户详情======================================
     */
    /**
     * 检索客户详情列表页面
     * 
     * @param model
     * @throws Exception
     * @return
     */
    @GetMapping("/customerDetails/list/view")
    public String customerDetailsListView(Model model) throws Exception {
        // 获取客户列表
        List<Customers> customersList = sumitomoService.listCustomersList();
        model.addAttribute("customersList", customersList);
        return "/modules/salesCommon/customerDetails_list";
    }

    /**
     * 检索客户详情列表
     * 
     * @param
     * @throws Exception
     * @return
     */
    @PostMapping("/customerDetails/list")
    @ResponseBody
    public Object customerDetailsList(Integer page, Integer limit, String customerCode, String status)
            throws Exception {
        PageInfo pageInfo = new PageInfo(limit, page);

        // 获取客户详情
        CustomerDetailsPageDTO customerDetailsPageDTO = salesCommonService.customerDetailsListByPage(pageInfo,
                customerCode, status);
        return new HrPageResult(customerDetailsPageDTO.getCustomerDetailsList(),
                customerDetailsPageDTO.getPageInfo().getTotal());
    }

    /**
     * 添加客户详情页面
     * 
     * @param model
     * @throws Exception
     * @return
     */
    @GetMapping("/customerDetails/add/view")
    public String customerDetailsAddView(Model model) throws Exception {
        // 获取客户列表
        List<Customers> customersList = sumitomoService.listCustomersList();
        model.addAttribute("customersList", customersList);

        // 获取客户分类列表
        List<CustomerClassify> customerClassifyList = salesCommonService.listCustomerClassify(null, null);
        model.addAttribute("customerClassifyList", customerClassifyList);

        // 获取货币列表
        List<CurrencySetting> currencySettingList = salesCommonService.listCurrencySetting(null, null);
        model.addAttribute("currencySettingList", currencySettingList);

        // 获取税率计算列表
        List<TaxRateCalculation> taxRateCalculationList = salesCommonService.listTaxRateCalculation(null, null);
        model.addAttribute("taxRateCalculationList", taxRateCalculationList);

        // 获取交易条件列表
        List<TradeConditions> tradeConditionsList = salesCommonService.listTradeConditions(null, null);
        model.addAttribute("tradeConditionsList", tradeConditionsList);

        // 获取付款条件列表
        List<PayConditions> payConditionsList = salesCommonService.listPayConditions(null, null);
        model.addAttribute("payConditionsList", payConditionsList);

        // 获取运输条件列表
        List<TransportConditions> transportConditionsList = salesCommonService.listTransportConditions(null, null);
        model.addAttribute("transportConditionsList", transportConditionsList);

        return "/modules/salesCommon/customerDetails_add";
    }

    /**
     * 添加客户详情
     * 
     * @param ascendingWater
     * @throws Exception
     * @return
     */
    @PostMapping("/customerDetails/add")
    @ResponseBody
    public Object customerDetailsAdd(CustomerDetails customerDetails) throws Exception {
        try {
            AuthorizingUser authorizingUser = SingletonLoginUtils.getUser();
            if (authorizingUser == null) {
                return new HrResult(CommonReturnCode.FAILED, "登录超时，请重新登录");
            }
            User user = userService.getById(authorizingUser.getUserId());
            customerDetails.setStatus("0"); // 有效
            customerDetails.setCreatorName(user.getUserName());
            customerDetails.setCreatedTime(
                    DateUtils.convertIntToDateString(DateUtils.getCurrentTime(), "yyyy-MM-dd HH:mm:ss"));
            salesCommonService.insertCustomerDetails(customerDetails);
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("客户详情新增异常信息：", e);
            return new HrResult(0, e.getMessage());
        }
        return new HrResult(CommonReturnCode.SUCCESS);
    }

    /**
     * 编辑客户详情页面
     * 
     * @param model
     * @param id
     * @throws Exception
     * @return
     */
    @GetMapping("/customerDetails/edit/view")
    public String customerDetailsEditView(Model model, Integer id) throws Exception {
        // 获取客户列表
        List<Customers> customersList = sumitomoService.listCustomersList();
        model.addAttribute("customersList", customersList);

        // 获取客户分类列表
        List<CustomerClassify> customerClassifyList = salesCommonService.listCustomerClassify(null, null);
        model.addAttribute("customerClassifyList", customerClassifyList);

        // 获取货币列表
        List<CurrencySetting> currencySettingList = salesCommonService.listCurrencySetting(null, null);
        model.addAttribute("currencySettingList", currencySettingList);

        // 获取税率计算列表
        List<TaxRateCalculation> taxRateCalculationList = salesCommonService.listTaxRateCalculation(null, null);
        model.addAttribute("taxRateCalculationList", taxRateCalculationList);

        // 获取交易条件列表
        List<TradeConditions> tradeConditionsList = salesCommonService.listTradeConditions(null, null);
        model.addAttribute("tradeConditionsList", tradeConditionsList);

        // 获取付款条件列表
        List<PayConditions> payConditionsList = salesCommonService.listPayConditions(null, null);
        model.addAttribute("payConditionsList", payConditionsList);

        // 获取运输条件列表
        List<TransportConditions> transportConditionsList = salesCommonService.listTransportConditions(null, null);
        model.addAttribute("transportConditionsList", transportConditionsList);

        CustomerDetails customerDetails = salesCommonService.getCustomerDetailsById(id);
        model.addAttribute("customerDetails", customerDetails);

        TaxRateCalculation taxRateCalculation = salesCommonService
                .getTaxRateByTaxCode(customerDetails.getTaxRateCode());
        model.addAttribute("taxRateCalculation", taxRateCalculation);
        return "/modules/salesCommon/customerDetails_modify";
    }

    /**
     * 编辑客户详情
     * 
     * @param model
     * @param customerDetails
     * @throws Exception
     * @return
     */
    @PostMapping("/customerDetails/edit")
    @ResponseBody
    public Object customerDetailsEdit(CustomerDetails customerDetails) throws Exception {
        try {
            AuthorizingUser authorizingUser = SingletonLoginUtils.getUser();
            if (authorizingUser == null) {
                return new HrResult(CommonReturnCode.FAILED, "登录超时，请重新登录");
            }
            User user = userService.getById(authorizingUser.getUserId());
            customerDetails.setUpdaterName(user.getUserName());
            customerDetails.setUpdatedTime(
                    DateUtils.convertIntToDateString(DateUtils.getCurrentTime(), "yyyy-MM-dd HH:mm:ss"));
            salesCommonService.updateCustomerDetails(customerDetails);
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("客户明细修改异常信息：", e);
            return new HrResult(0, e.getMessage());
        }
        return new HrResult(CommonReturnCode.SUCCESS);
    }

    /**
     * 删除客户详情
     * 
     * @param id
     * @throws Exception
     * @return
     */
    @PostMapping("/customerDetails/remove")
    @ResponseBody
    public Object customerDetailsRemove(Integer id) throws Exception {
        try {
            salesCommonService.updateCustomerDetailsStatus(id);
        } catch (Exception e) {
            e.printStackTrace();
            return new HrResult(0, e.getMessage());
        }
        return new HrResult(CommonReturnCode.SUCCESS);
    }

    /*
     * =================================合同方详情======================================
     */
    /**
     * 检索客户详情列表页面
     * 
     * @param model
     * @throws Exception
     * @return
     */
    @GetMapping("/contractDetails/list/view")
    public String contractDetailsListView(Model model) throws Exception {
        // 获取合同方列表
        List<ContractDetails> contractDetailsList = salesCommonService.listContractDetailsList();
        model.addAttribute("contractDetailsList", contractDetailsList);
        return "/modules/salesCommon/contractDetails_list";
    }

    /**
     * 检索合同方详情列表
     * 
     * @param
     * @throws Exception
     * @return
     */
    @PostMapping("/contractDetails/list")
    @ResponseBody
    public Object contractDetailsList(Integer page, Integer limit, String contractCode) throws Exception {
        PageInfo pageInfo = new PageInfo(limit, page);

        // 获取合同方详情
        ContractDetailsPageDTO contractDetailsPageDTO = salesCommonService.contractDetailsListByPage(pageInfo,
                contractCode);
        return new HrPageResult(contractDetailsPageDTO.getContractDetailsList(),
                contractDetailsPageDTO.getPageInfo().getTotal());
    }

    /**
     * 添加合同方详情页面
     * 
     * @param model
     * @throws Exception
     * @return
     */
    @GetMapping("/contractDetails/add/view")
    public String contractDetailsAddView(Model model) throws Exception {
        // 获取合同方列表
        String contractCode = "0";
        // 取得当前最大合同方代码
        contractCode = salesCommonService.getMaxContractCode();
        int contractCodeNum = Integer.parseInt(contractCode);
        contractCodeNum += 1;
        contractCode = String.valueOf(contractCodeNum);

        if (contractCode.length() == 1) {
            contractCode = "00" + contractCode;
        } else if (contractCode.length() == 1) {
            contractCode = "0" + contractCode;
        } else {
            ;
        }

        model.addAttribute("contractCode", contractCode);

        return "/modules/salesCommon/contractDetails_add";
    }

    /**
     * 添加合同方
     * 
     * @param contractDetails
     * @throws Exception
     * @return
     */
    @PostMapping("/contractDetails/add")
    @ResponseBody
    public Object contractDetailsAdd(ContractDetails contractDetails) throws Exception {
        try {
            AuthorizingUser authorizingUser = SingletonLoginUtils.getUser();
            if (authorizingUser == null) {
                return new HrResult(CommonReturnCode.FAILED, "登录超时，请重新登录");
            }
            User user = userService.getById(authorizingUser.getUserId());
            contractDetails.setCreatorName(user.getUserName());
            contractDetails.setCreatedTime(
                    DateUtils.convertIntToDateString(DateUtils.getCurrentTime(), "yyyy-MM-dd HH:mm:ss"));
            salesCommonService.insertContractDetails(contractDetails);
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("合同方新增异常信息：", e);
            return new HrResult(0, e.getMessage());
        }
        return new HrResult(CommonReturnCode.SUCCESS);
    }

    /**
     * 编辑合同方详情页面
     * 
     * @param model
     * @param id
     * @throws Exception
     * @return
     */
    @GetMapping("/contractDetails/edit/view")
    public String contractDetailsEditView(Model model, Integer id) throws Exception {
        // 获取合同方列表
        List<ContractDetails> contractDetailsList = salesCommonService.listContractDetailsList();
        model.addAttribute("contractDetailsList", contractDetailsList);

        ContractDetails contractDetails = salesCommonService.getContractDetailsById(id);
        model.addAttribute("contractDetails", contractDetails);
        return "/modules/salesCommon/contractDetails_modify";
    }

    /**
     * 编辑客户详情
     * 
     * @param model
     * @param ascendingWater
     * @throws Exception
     * @return
     */
    @PostMapping("/contractDetails/edit")
    @ResponseBody
    public Object contractDetailsEdit(ContractDetails contractDetails) throws Exception {
        try {
            AuthorizingUser authorizingUser = SingletonLoginUtils.getUser();
            if (authorizingUser == null) {
                return new HrResult(CommonReturnCode.FAILED, "登录超时，请重新登录");
            }
            User user = userService.getById(authorizingUser.getUserId());
            contractDetails.setUpdaterName(user.getUserName());
            contractDetails.setUpdatedTime(
                    DateUtils.convertIntToDateString(DateUtils.getCurrentTime(), "yyyy-MM-dd HH:mm:ss"));
            salesCommonService.updateContractDetails(contractDetails);
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("合同方明细修改异常信息：", e);
            return new HrResult(0, e.getMessage());
        }
        return new HrResult(CommonReturnCode.SUCCESS);
    }

    /**
     * 删除升水价格
     * 
     * @param id
     * @throws Exception
     * @return
     */
    @PostMapping("/contractDetails/remove")
    @ResponseBody
    public Object contractDetailsRemove(Integer id) throws Exception {
        try {
            salesCommonService.deleteContractDetailsById(id);
        } catch (Exception e) {
            e.printStackTrace();
            return new HrResult(0, e.getMessage());
        }
        return new HrResult(CommonReturnCode.SUCCESS);
    }

    /*
     * =================================客户分类======================================
     */
    /**
     * 客户分类列表页面
     * 
     * @param model
     * @throws Exception
     * @return
     */
    @GetMapping("/customerClassify/list/view")
    public String customerClassifyListView(Model model) throws Exception {
        // 获取客户分类列表
        List<CustomerClassify> customerClassifyList = salesCommonService.listCustomerClassify(null, null);
        model.addAttribute("customerClassifyList", customerClassifyList);
        return "/modules/salesCommon/customerClassify_list";
    }

    /**
     * 检索客户分类列表
     * 
     * @param
     * @throws Exception
     * @return
     */
    @PostMapping("/customerClassify/list")
    @ResponseBody
    public Object customerClassifyList(String customerCategoryCode) throws Exception {
        // 获取客户分类列表
        List<CustomerClassify> customerClassifyList = salesCommonService.listCustomerClassify(customerCategoryCode,
                null);
        return new HrPageResult(customerClassifyList, customerClassifyList.size());
    }

    /**
     * 添加客户分类页面
     * 
     * @param model
     * @throws Exception
     * @return
     */
    @GetMapping("/customerClassify/add/view")
    public String customerClassifyAddView(Model model) throws Exception {
        return "/modules/salesCommon/customerClassify_add";
    }

    /**
     * 添加客户分类
     * 
     * @param proportion
     * @throws Exception
     * @return
     */
    @PostMapping("/customerClassify/add")
    @ResponseBody
    public Object customerClassifyAdd(CustomerClassify customerClassify) throws Exception {
        try {
            AuthorizingUser authorizingUser = SingletonLoginUtils.getUser();
            if (authorizingUser == null) {
                return new HrResult(CommonReturnCode.FAILED, "登录超时，请重新登录");
            }
            User user = userService.getById(authorizingUser.getUserId());
            customerClassify.setCreatorName(user.getUserName());
            customerClassify.setCreatedTime(
                    DateUtils.convertIntToDateString(DateUtils.getCurrentTime(), "yyyy-MM-dd HH:mm:ss"));
            salesCommonService.insertCustomerClassify(customerClassify);
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("客户分类明细新增异常信息：", e);
            return new HrResult(0, e.getMessage());
        }
        return new HrResult(CommonReturnCode.SUCCESS);
    }

    /**
     * 编辑客户分类页面
     * 
     * @param model
     * @param id
     * @throws Exception
     * @return
     */
    @GetMapping("/customerClassify/edit/view")
    public String customerClassifyEditView(Model model, Integer id) throws Exception {
        CustomerClassify customerClassify = salesCommonService.getCustomerClassifyById(id);
        model.addAttribute("customerClassify", customerClassify);
        return "/modules/salesCommon/customerClassify_modify";
    }

    /**
     * 编辑客户分类
     * 
     * @param model
     * @param customerClassify
     * @throws Exception
     * @return
     */
    @PostMapping("/customerClassify/edit")
    @ResponseBody
    public Object customerClassifyEdit(CustomerClassify customerClassify) throws Exception {
        try {
            AuthorizingUser authorizingUser = SingletonLoginUtils.getUser();
            if (authorizingUser == null) {
                return new HrResult(CommonReturnCode.FAILED, "登录超时，请重新登录");
            }
            User user = userService.getById(authorizingUser.getUserId());
            customerClassify.setUpdaterName(user.getUserName());
            customerClassify.setUpdatedTime(
                    DateUtils.convertIntToDateString(DateUtils.getCurrentTime(), "yyyy-MM-dd HH:mm:ss"));
            salesCommonService.updateCustomerClassify(customerClassify);
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("客户分类明细修改异常信息：", e);
            return new HrResult(0, e.getMessage());
        }
        return new HrResult(CommonReturnCode.SUCCESS);
    }

    /**
     * 删除客户分类列表
     * 
     * @param id
     * @throws Exception
     * @return
     */
    @PostMapping("/customerClassify/remove")
    @ResponseBody
    public Object customerClassifyRemove(Integer id) throws Exception {
        try {
            salesCommonService.deleteCustomerClassify(id);
        } catch (Exception e) {
            e.printStackTrace();
            return new HrResult(0, e.getMessage());
        }
        return new HrResult(CommonReturnCode.SUCCESS);
    }

    /*
     * =================================产品价格表======================================
     */
    /**
     * 产品价格列表页面
     * 
     * @param model
     * @throws Exception
     * @return
     */
    @GetMapping("/productsPrice/list/view")
    public String productsPriceListView(Model model) throws Exception {
        // 获取客户列表
        List<Customers> customersList = sumitomoService.listCustomersList();
        model.addAttribute("customersList", customersList);

        // 获取货币列表
        List<String> currencyList = salesCommonService.selectDistinctCurrency();
        model.addAttribute("currencyList", currencyList);
        return "/modules/salesCommon/productsPrice_list";
    }

    /**
     * 检索产品价格列表
     * 
     * @param
     * @throws Exception
     * @return
     */
    @PostMapping("/productsPrice/list")
    @ResponseBody
    public Object productsPriceList(Integer page, Integer limit, String customerCode, String productCode,
            String applyDateStart, String applyDateEnd) throws Exception {
        PageInfo pageInfo = new PageInfo(limit, page);

        if (StringUtil.isStringEmpty(applyDateStart)) {
            applyDateStart = null;
        }
        if (StringUtil.isStringEmpty(applyDateEnd)) {
            applyDateEnd = null;
        }

        // 获取升水列表
        ProductsPricePageDTO productsPricePageDTO = salesCommonService.productsPriceListByPage(pageInfo, customerCode,
                productCode, applyDateStart, applyDateEnd);

        return new HrPageResult(productsPricePageDTO.getProductsPriceList(),
                productsPricePageDTO.getPageInfo().getTotal());
    }

    /**
     * 添加产品价格页面
     * 
     * @param model
     * @throws Exception
     * @return
     */
    @GetMapping("/productsPrice/add/view")
    public String productsPriceAddView(Model model) throws Exception {
        // 获取客户列表
        List<Customers> customersList = sumitomoService.listCustomersList();
        model.addAttribute("customersList", customersList);

        // 获取货币列表
        List<String> currencyList = salesCommonService.selectDistinctCurrency();
        model.addAttribute("currencyList", currencyList);
        return "/modules/salesCommon/productsPrice_add";
    }

    /**
     * 添加产品价格
     * 
     * @param ascendingWater
     * @throws Exception
     * @return
     */
    @PostMapping("/productsPrice/add")
    @ResponseBody
    public Object productsPriceAdd(ProductsPrice productsPrice) throws Exception {
        try {
            AuthorizingUser authorizingUser = SingletonLoginUtils.getUser();
            if (authorizingUser == null) {
                return new HrResult(CommonReturnCode.FAILED, "登录超时，请重新登录");
            }
            User user = userService.getById(authorizingUser.getUserId());
            productsPrice.setCreatorName(user.getUserName());
            productsPrice.setCreatedTime(
                    DateUtils.convertIntToDateString(DateUtils.getCurrentTime(), "yyyy-MM-dd HH:mm:ss"));
            salesCommonService.insertProductsPrice(productsPrice);
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("产品价格新增异常信息：", e);
            return new HrResult(0, e.getMessage());
        }
        return new HrResult(CommonReturnCode.SUCCESS);
    }

    /**
     * 编辑产品价格页面
     * 
     * @param model
     * @param id
     * @throws Exception
     * @return
     */
    @GetMapping("/productsPrice/edit/view")
    public String productsPriceEditView(Model model, Integer id) throws Exception {
        // 获取客户列表
        List<Customers> customersList = sumitomoService.listCustomersList();
        model.addAttribute("customersList", customersList);

        // 获取货币列表
        List<String> currencyList = salesCommonService.selectDistinctCurrency();
        model.addAttribute("currencyList", currencyList);

        // 根据流水号获取产品价格
        ProductsPrice productsPrice = salesCommonService.getProductsPriceById(id);
        model.addAttribute("productsPrice", productsPrice);

        // 获取产品列表
        List<String> productCodeList = sumitomoService.getProductListByCustomerCode(productsPrice.getCustomerCode(),
                "0");
        model.addAttribute("productCodeList", productCodeList);

        return "/modules/salesCommon/productsPrice_modify";
    }

    /**
     * 编辑产品价格
     * 
     * @param model
     * @param ascendingWater
     * @throws Exception
     * @return
     */
    @PostMapping("/productsPrice/edit")
    @ResponseBody
    public Object productsPriceEdit(ProductsPrice productsPrice) throws Exception {
        try {
            AuthorizingUser authorizingUser = SingletonLoginUtils.getUser();
            if (authorizingUser == null) {
                return new HrResult(CommonReturnCode.FAILED, "登录超时，请重新登录");
            }
            User user = userService.getById(authorizingUser.getUserId());
            productsPrice.setUpdaterName(user.getUserName());
            productsPrice.setUpdatedTime(
                    DateUtils.convertIntToDateString(DateUtils.getCurrentTime(), "yyyy-MM-dd HH:mm:ss"));
            salesCommonService.updateProductsPrice(productsPrice);
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("产品价格明细修改异常信息：", e);
            return new HrResult(0, e.getMessage());
        }
        return new HrResult(CommonReturnCode.SUCCESS);
    }

    /**
     * 删除产品价格
     * 
     * @param id
     * @throws Exception
     * @return
     */
    @PostMapping("/productsPrice/remove")
    @ResponseBody
    public Object productsPriceRemove(Integer id) throws Exception {
        try {
            salesCommonService.deleteProductsPrice(id);
        } catch (Exception e) {
            e.printStackTrace();
            return new HrResult(0, e.getMessage());
        }
        return new HrResult(CommonReturnCode.SUCCESS);
    }

    /**
     * 根据客户代码获取产品代码List
     * 
     * @param model
     * @param customerCode
     * @throws Exception
     * @return
     */
    @PostMapping(value = "/productListByCustomerCode/json")
    @ResponseBody
    public Object getProductListByCustomerCode(Model model, String customerCode) throws Exception {
        try {
            List<String> productCodes = sumitomoService.getProductListByCustomerCode(customerCode, "0");
            return new HrResult(CommonReturnCode.SUCCESS, productCodes);
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("异常信息：", e);
            return new HrResult(CommonReturnCode.FAILED.getCode(), "系统异常");
        }
    }

    /**
     * 根据客户代码、产品代码、到货日期获取产品单价
     * 
     * @param customerCode 客户代码
     * @param productCode  产品代码
     * @param arrivalDate  到货日期
     * @throws Exception
     * @return
     */
    @PostMapping(value = "/getProductPrice")
    @ResponseBody
    public Object getProductPrice(String customerCode, String productCode, String arrivalDate) throws Exception {
        try {
            // 验证用户登录状态
            AuthorizingUser authorizingUser = SingletonLoginUtils.getUser();
            if (authorizingUser == null) {
                return new HrResult(CommonReturnCode.FAILED, "登录超时，请重新登录");
            }

            // 参数验证
            if (StringUtil.isStringEmpty(customerCode)) {
                return new HrResult(CommonReturnCode.FAILED, "客户代码不能为空");
            }
            if (StringUtil.isStringEmpty(productCode)) {
                return new HrResult(CommonReturnCode.FAILED, "产品代码不能为空");
            }
            if (StringUtil.isStringEmpty(arrivalDate)) {
                return new HrResult(CommonReturnCode.FAILED, "到货日期不能为空");
            }

            // 查询产品价格
            ProductsPrice productsPrice = salesCommonService.getProductPriceByConditions(customerCode, productCode,
                    arrivalDate);
            if (productsPrice != null) {
                return new HrResult(CommonReturnCode.SUCCESS, productsPrice);
            } else {
                return new HrResult(CommonReturnCode.FAILED, "未找到匹配的产品价格数据");
            }
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("获取产品单价异常信息：", e);
            return new HrResult(CommonReturnCode.FAILED.getCode(), "系统异常：" + e.getMessage());
        }
    }

    /*
     * =================================交易条件======================================
     */
    /**
     * 交易条件列表页面
     * 
     * @param model
     * @throws Exception
     * @return
     */
    @GetMapping("/tradeConditions/list/view")
    public String tradeConditionsListView(Model model) throws Exception {
        // 获取交易条件列表
        List<TradeConditions> tradeConditionsList = salesCommonService.listTradeConditions(null, null);
        model.addAttribute("tradeConditionsList", tradeConditionsList);
        return "/modules/salesCommon/tradeConditions_list";
    }

    /**
     * 检索交易条件列表
     * 
     * @param
     * @throws Exception
     * @return
     */
    @PostMapping("/tradeConditions/list")
    @ResponseBody
    public Object tradeConditionsList(String tradeCondition) throws Exception {
        // 获取交易条件列表
        List<TradeConditions> tradeConditionsList = salesCommonService.listTradeConditions(tradeCondition, null);
        return new HrPageResult(tradeConditionsList, tradeConditionsList.size());
    }

    /**
     * 添加交易条件页面
     * 
     * @param model
     * @throws Exception
     * @return
     */
    @GetMapping("/tradeConditions/add/view")
    public String tradeConditionsAddView(Model model) throws Exception {
        return "/modules/salesCommon/tradeConditions_add";
    }

    /**
     * 添加交易条件
     * 
     * @param proportion
     * @throws Exception
     * @return
     */
    @PostMapping("/tradeConditions/add")
    @ResponseBody
    public Object tradeConditionsAdd(TradeConditions tradeConditions) throws Exception {
        try {
            AuthorizingUser authorizingUser = SingletonLoginUtils.getUser();
            if (authorizingUser == null) {
                return new HrResult(CommonReturnCode.FAILED, "登录超时，请重新登录");
            }
            User user = userService.getById(authorizingUser.getUserId());
            tradeConditions.setCreatorName(user.getUserName());
            tradeConditions.setCreatedTime(
                    DateUtils.convertIntToDateString(DateUtils.getCurrentTime(), "yyyy-MM-dd HH:mm:ss"));
            salesCommonService.insertTradeConditions(tradeConditions);
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("交易条件明细新增异常信息：", e);
            return new HrResult(0, e.getMessage());
        }
        return new HrResult(CommonReturnCode.SUCCESS);
    }

    /**
     * 编辑交易条件页面
     * 
     * @param model
     * @param id
     * @throws Exception
     * @return
     */
    @GetMapping("/tradeConditions/edit/view")
    public String tradeConditionsEditView(Model model, Integer id) throws Exception {
        TradeConditions tradeConditions = salesCommonService.getTradeConditionsById(id);
        model.addAttribute("tradeConditions", tradeConditions);
        return "/modules/salesCommon/tradeConditions_modify";
    }

    /**
     * 编辑交易条件
     * 
     * @param model
     * @param tradeConditions
     * @throws Exception
     * @return
     */
    @PostMapping("/tradeConditions/edit")
    @ResponseBody
    public Object tradeConditionsEdit(TradeConditions tradeConditions) throws Exception {
        try {
            AuthorizingUser authorizingUser = SingletonLoginUtils.getUser();
            if (authorizingUser == null) {
                return new HrResult(CommonReturnCode.FAILED, "登录超时，请重新登录");
            }
            User user = userService.getById(authorizingUser.getUserId());
            tradeConditions.setUpdaterName(user.getUserName());
            tradeConditions.setUpdatedTime(
                    DateUtils.convertIntToDateString(DateUtils.getCurrentTime(), "yyyy-MM-dd HH:mm:ss"));
            salesCommonService.updateTradeConditions(tradeConditions);
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("交易条件明细修改异常信息：", e);
            return new HrResult(0, e.getMessage());
        }
        return new HrResult(CommonReturnCode.SUCCESS);
    }

    /**
     * 删除交易条件
     * 
     * @param id
     * @throws Exception
     * @return
     */
    @PostMapping("/tradeConditions/remove")
    @ResponseBody
    public Object tradeConditionsRemove(Integer id) throws Exception {
        try {
            salesCommonService.deleteTradeConditions(id);
        } catch (Exception e) {
            e.printStackTrace();
            return new HrResult(0, e.getMessage());
        }
        return new HrResult(CommonReturnCode.SUCCESS);
    }

    /*
     * =================================付款条件======================================
     */
    /**
     * 付款条件列表页面
     * 
     * @param model
     * @throws Exception
     * @return
     */
    @GetMapping("/payConditions/list/view")
    public String payConditionsListView(Model model) throws Exception {
        // 获取付款条件列表
        List<PayConditions> payConditionsList = salesCommonService.listPayConditions(null, null);
        model.addAttribute("payConditionsList", payConditionsList);
        return "/modules/salesCommon/payConditions_list";
    }

    /**
     * 检索付款条件列表
     * 
     * @param
     * @throws Exception
     * @return
     */
    @PostMapping("/payConditions/list")
    @ResponseBody
    public Object payConditionsList(String payCondition) throws Exception {
        // 获取付款条件列表
        List<PayConditions> payConditionsList = salesCommonService.listPayConditions(payCondition, null);
        return new HrPageResult(payConditionsList, payConditionsList.size());
    }

    /**
     * 添加付款条件页面
     * 
     * @param model
     * @throws Exception
     * @return
     */
    @GetMapping("/payConditions/add/view")
    public String payConditionsAddView(Model model) throws Exception {
        return "/modules/salesCommon/payConditions_add";
    }

    /**
     * 添加付款条件
     * 
     * @param proportion
     * @throws Exception
     * @return
     */
    @PostMapping("/payConditions/add")
    @ResponseBody
    public Object payConditionsAdd(PayConditions payConditions) throws Exception {
        try {
            AuthorizingUser authorizingUser = SingletonLoginUtils.getUser();
            if (authorizingUser == null) {
                return new HrResult(CommonReturnCode.FAILED, "登录超时，请重新登录");
            }
            User user = userService.getById(authorizingUser.getUserId());
            payConditions.setCreatorName(user.getUserName());
            payConditions.setCreatedTime(
                    DateUtils.convertIntToDateString(DateUtils.getCurrentTime(), "yyyy-MM-dd HH:mm:ss"));
            salesCommonService.insertPayConditions(payConditions);
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("付款条件明细新增异常信息：", e);
            return new HrResult(0, e.getMessage());
        }
        return new HrResult(CommonReturnCode.SUCCESS);
    }

    /**
     * 编辑付款条件页面
     * 
     * @param model
     * @param id
     * @throws Exception
     * @return
     */
    @GetMapping("/payConditions/edit/view")
    public String payConditionsEditView(Model model, Integer id) throws Exception {
        PayConditions payConditions = salesCommonService.getPayConditionsById(id);
        model.addAttribute("payConditions", payConditions);
        return "/modules/salesCommon/payConditions_modify";
    }

    /**
     * 编辑付款条件
     * 
     * @param model
     * @param payConditions
     * @throws Exception
     * @return
     */
    @PostMapping("/payConditions/edit")
    @ResponseBody
    public Object payConditionsEdit(PayConditions payConditions) throws Exception {
        try {
            AuthorizingUser authorizingUser = SingletonLoginUtils.getUser();
            if (authorizingUser == null) {
                return new HrResult(CommonReturnCode.FAILED, "登录超时，请重新登录");
            }
            User user = userService.getById(authorizingUser.getUserId());
            payConditions.setUpdaterName(user.getUserName());
            payConditions.setUpdatedTime(
                    DateUtils.convertIntToDateString(DateUtils.getCurrentTime(), "yyyy-MM-dd HH:mm:ss"));
            salesCommonService.updatePayConditions(payConditions);
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("付款条件明细修改异常信息：", e);
            return new HrResult(0, e.getMessage());
        }
        return new HrResult(CommonReturnCode.SUCCESS);
    }

    /**
     * 删除付款条件
     * 
     * @param id
     * @throws Exception
     * @return
     */
    @PostMapping("/payConditions/remove")
    @ResponseBody
    public Object payConditionsRemove(Integer id) throws Exception {
        try {
            salesCommonService.deletePayConditions(id);
        } catch (Exception e) {
            e.printStackTrace();
            return new HrResult(0, e.getMessage());
        }
        return new HrResult(CommonReturnCode.SUCCESS);
    }

    /*
     * =================================运输条件======================================
     */
    /**
     * 运输条件列表页面
     * 
     * @param model
     * @throws Exception
     * @return
     */
    @GetMapping("/transportConditions/list/view")
    public String transportConditionsListView(Model model) throws Exception {
        // 获取运输条件列表
        List<TransportConditions> transportConditionsList = salesCommonService.listTransportConditions(null, null);
        model.addAttribute("transportConditionsList", transportConditionsList);
        return "/modules/salesCommon/transportConditions_list";
    }

    /**
     * 检索运输条件列表
     * 
     * @param
     * @throws Exception
     * @return
     */
    @PostMapping("/transportConditions/list")
    @ResponseBody
    public Object transportConditionsList(String transportCondition) throws Exception {
        // 获取运输条件列表
        List<TransportConditions> transportConditionsList = salesCommonService
                .listTransportConditions(transportCondition, null);
        return new HrPageResult(transportConditionsList, transportConditionsList.size());
    }

    /**
     * 添加运输条件页面
     * 
     * @param model
     * @throws Exception
     * @return
     */
    @GetMapping("/transportConditions/add/view")
    public String transportConditionsAddView(Model model) throws Exception {
        return "/modules/salesCommon/transportConditions_add";
    }

    /**
     * 添加运输条件
     * 
     * @param proportion
     * @throws Exception
     * @return
     */
    @PostMapping("/transportConditions/add")
    @ResponseBody
    public Object transportConditionsAdd(TransportConditions transportConditions) throws Exception {
        try {
            AuthorizingUser authorizingUser = SingletonLoginUtils.getUser();
            if (authorizingUser == null) {
                return new HrResult(CommonReturnCode.FAILED, "登录超时，请重新登录");
            }
            User user = userService.getById(authorizingUser.getUserId());
            transportConditions.setCreatorName(user.getUserName());
            transportConditions.setCreatedTime(
                    DateUtils.convertIntToDateString(DateUtils.getCurrentTime(), "yyyy-MM-dd HH:mm:ss"));
            salesCommonService.insertTransportConditions(transportConditions);
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("运输条件明细新增异常信息：", e);
            return new HrResult(0, e.getMessage());
        }
        return new HrResult(CommonReturnCode.SUCCESS);
    }

    /**
     * 编辑运输条件页面
     * 
     * @param model
     * @param id
     * @throws Exception
     * @return
     */
    @GetMapping("/transportConditions/edit/view")
    public String transportConditionsEditView(Model model, Integer id) throws Exception {
        TransportConditions transportConditions = salesCommonService.getTransportConditionsById(id);
        model.addAttribute("transportConditions", transportConditions);
        return "/modules/salesCommon/transportConditions_modify";
    }

    /**
     * 编辑运输条件
     * 
     * @param model
     * @param transportConditions
     * @throws Exception
     * @return
     */
    @PostMapping("/transportConditions/edit")
    @ResponseBody
    public Object transportConditionsEdit(TransportConditions transportConditions) throws Exception {
        try {
            AuthorizingUser authorizingUser = SingletonLoginUtils.getUser();
            if (authorizingUser == null) {
                return new HrResult(CommonReturnCode.FAILED, "登录超时，请重新登录");
            }
            User user = userService.getById(authorizingUser.getUserId());
            transportConditions.setUpdaterName(user.getUserName());
            transportConditions.setUpdatedTime(
                    DateUtils.convertIntToDateString(DateUtils.getCurrentTime(), "yyyy-MM-dd HH:mm:ss"));
            salesCommonService.updateTransportConditions(transportConditions);
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("运输条件明细修改异常信息：", e);
            return new HrResult(0, e.getMessage());
        }
        return new HrResult(CommonReturnCode.SUCCESS);
    }

    /**
     * 删除运输条件
     * 
     * @param id
     * @throws Exception
     * @return
     */
    @PostMapping("/transportConditions/remove")
    @ResponseBody
    public Object transportConditionsRemove(Integer id) throws Exception {
        try {
            salesCommonService.deleteTransportConditions(id);
        } catch (Exception e) {
            e.printStackTrace();
            return new HrResult(0, e.getMessage());
        }
        return new HrResult(CommonReturnCode.SUCCESS);
    }

    /*
     * =================================国家设定======================================
     */
    /**
     * 国家设定列表页面
     * 
     * @param model
     * @throws Exception
     * @return
     */
    @GetMapping("/countrySetting/list/view")
    public String CountrySettingListView(Model model) throws Exception {
        // 获取国家列表
        List<CountrySetting> countrySettingList = salesCommonService.listCountrySetting(null, null);
        model.addAttribute("countrySettingList", countrySettingList);
        return "/modules/salesCommon/countrySetting_list";
    }

    /**
     * 检索国家设定列表
     * 
     * @param
     * @throws Exception
     * @return
     */
    @PostMapping("/countrySetting/list")
    @ResponseBody
    public Object countrySettingList(String country) throws Exception {
        // 获取国家列设定表
        List<CountrySetting> countrySettingList = salesCommonService.listCountrySetting(country, null);
        return new HrPageResult(countrySettingList, countrySettingList.size());
    }

    /**
     * 添加国家页面
     * 
     * @param model
     * @throws Exception
     * @return
     */
    @GetMapping("/countrySetting/add/view")
    public String countrySettingAddView(Model model) throws Exception {
        return "/modules/salesCommon/countrySetting_add";
    }

    /**
     * 添加国家
     * 
     * @param proportion
     * @throws Exception
     * @return
     */
    @PostMapping("/countrySetting/add")
    @ResponseBody
    public Object countrySettingAdd(CountrySetting countrySetting) throws Exception {
        try {
            AuthorizingUser authorizingUser = SingletonLoginUtils.getUser();
            if (authorizingUser == null) {
                return new HrResult(CommonReturnCode.FAILED, "登录超时，请重新登录");
            }
            User user = userService.getById(authorizingUser.getUserId());
            countrySetting.setCreatorName(user.getUserName());
            countrySetting.setCreatedTime(
                    DateUtils.convertIntToDateString(DateUtils.getCurrentTime(), "yyyy-MM-dd HH:mm:ss"));
            salesCommonService.insertCountrySetting(countrySetting);
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("国家明细新增异常信息：", e);
            return new HrResult(0, e.getMessage());
        }
        return new HrResult(CommonReturnCode.SUCCESS);
    }

    /**
     * 编辑国家设定页面
     * 
     * @param model
     * @param id
     * @throws Exception
     * @return
     */
    @GetMapping("/countrySetting/edit/view")
    public String countrySettingEditView(Model model, Integer id) throws Exception {
        CountrySetting countrySetting = salesCommonService.getCountrySettingById(id);
        model.addAttribute("countrySetting", countrySetting);
        return "/modules/salesCommon/countrySetting_modify";
    }

    /**
     * 编辑国家设定
     * 
     * @param model
     * @param countrySetting
     * @throws Exception
     * @return
     */
    @PostMapping("/countrySetting/edit")
    @ResponseBody
    public Object countrySettingEdit(CountrySetting countrySetting) throws Exception {
        try {
            AuthorizingUser authorizingUser = SingletonLoginUtils.getUser();
            if (authorizingUser == null) {
                return new HrResult(CommonReturnCode.FAILED, "登录超时，请重新登录");
            }
            User user = userService.getById(authorizingUser.getUserId());
            countrySetting.setUpdaterName(user.getUserName());
            countrySetting.setUpdatedTime(
                    DateUtils.convertIntToDateString(DateUtils.getCurrentTime(), "yyyy-MM-dd HH:mm:ss"));
            salesCommonService.updateCountrySetting(countrySetting);
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("国家明细修改异常信息：", e);
            return new HrResult(0, e.getMessage());
        }
        return new HrResult(CommonReturnCode.SUCCESS);
    }

    /**
     * 删除国家设定表
     * 
     * @param id
     * @throws Exception
     * @return
     */
    @PostMapping("/countrySetting/remove")
    @ResponseBody
    public Object countrySettingRemove(Integer id) throws Exception {
        try {
            salesCommonService.deleteCountrySetting(id);
        } catch (Exception e) {
            e.printStackTrace();
            return new HrResult(0, e.getMessage());
        }
        return new HrResult(CommonReturnCode.SUCCESS);
    }

    /*
     * =================================仓库设定======================================
     */
    /**
     * 仓库设定列表页面
     * 
     * @param model
     * @throws Exception
     * @return
     */
    @GetMapping("/wareHouse/list/view")
    public String wareHouseListView(Model model) throws Exception {
        // 获取仓库列表
        List<WareHouse> wareHouseList = salesCommonService.listWareHouse(null, null);
        model.addAttribute("wareHouseList", wareHouseList);
        return "/modules/salesCommon/wareHouse_list";
    }

    /**
     * 检索仓库设定列表
     * 
     * @param
     * @throws Exception
     * @return
     */
    @PostMapping("/wareHouse/list")
    @ResponseBody
    public Object wareHouseList(String wareHouseCode) throws Exception {
        // 获取仓库列表
        List<WareHouse> wareHouseList = salesCommonService.listWareHouse(wareHouseCode, null);
        return new HrPageResult(wareHouseList, wareHouseList.size());
    }

    /**
     * 添加仓库页面
     * 
     * @param model
     * @throws Exception
     * @return
     */
    @GetMapping("/wareHouse/add/view")
    public String wareHouseAddView(Model model) throws Exception {
        return "/modules/salesCommon/wareHouse_add";
    }

    /**
     * 添加仓库
     * 
     * @param proportion
     * @throws Exception
     * @return
     */
    @PostMapping("/wareHouse/add")
    @ResponseBody
    public Object wareHouseAdd(WareHouse wareHouse) throws Exception {
        try {
            AuthorizingUser authorizingUser = SingletonLoginUtils.getUser();
            if (authorizingUser == null) {
                return new HrResult(CommonReturnCode.FAILED, "登录超时，请重新登录");
            }
            User user = userService.getById(authorizingUser.getUserId());
            wareHouse.setCreatorName(user.getUserName());
            wareHouse.setCreatedTime(
                    DateUtils.convertIntToDateString(DateUtils.getCurrentTime(), "yyyy-MM-dd HH:mm:ss"));
            salesCommonService.insertWareHouse(wareHouse);
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("仓库明细新增异常信息：", e);
            return new HrResult(0, e.getMessage());
        }
        return new HrResult(CommonReturnCode.SUCCESS);
    }

    /**
     * 编辑仓库页面
     * 
     * @param model
     * @param id
     * @throws Exception
     * @return
     */
    @GetMapping("/wareHouse/edit/view")
    public String wareHouseEditView(Model model, Integer id) throws Exception {
        WareHouse wareHouse = salesCommonService.getWareHouseById(id);
        model.addAttribute("wareHouse", wareHouse);
        return "/modules/salesCommon/wareHouse_modify";
    }

    /**
     * 编辑仓库
     * 
     * @param model
     * @param wareHouse
     * @throws Exception
     * @return
     */
    @PostMapping("/wareHouse/edit")
    @ResponseBody
    public Object wareHouseEdit(WareHouse wareHouse) throws Exception {
        try {
            AuthorizingUser authorizingUser = SingletonLoginUtils.getUser();
            if (authorizingUser == null) {
                return new HrResult(CommonReturnCode.FAILED, "登录超时，请重新登录");
            }
            User user = userService.getById(authorizingUser.getUserId());
            wareHouse.setUpdaterName(user.getUserName());
            wareHouse.setUpdatedTime(
                    DateUtils.convertIntToDateString(DateUtils.getCurrentTime(), "yyyy-MM-dd HH:mm:ss"));
            salesCommonService.updateWareHouse(wareHouse);
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("仓库明细修改异常信息：", e);
            return new HrResult(0, e.getMessage());
        }
        return new HrResult(CommonReturnCode.SUCCESS);
    }

    /**
     * 删除仓库
     * 
     * @param id
     * @throws Exception
     * @return
     */
    @PostMapping("/wareHouse/remove")
    @ResponseBody
    public Object wareHouseRemove(Integer id) throws Exception {
        try {
            salesCommonService.deleteWareHouse(id);
        } catch (Exception e) {
            e.printStackTrace();
            return new HrResult(0, e.getMessage());
        }
        return new HrResult(CommonReturnCode.SUCCESS);
    }

    /*
     * =================================货币设定======================================
     */
    /**
     * 货币设定列表页面
     * 
     * @param model
     * @throws Exception
     * @return
     */
    @GetMapping("/currencySetting/list/view")
    public String currencySettingListView(Model model) throws Exception {
        // 获取货币列表
        List<CurrencySetting> currencySettingList = salesCommonService.listCurrencySetting(null, null);
        model.addAttribute("currencySettingList", currencySettingList);
        return "/modules/salesCommon/currencySetting_list";
    }

    /**
     * 检索货币设定列表
     * 
     * @param
     * @throws Exception
     * @return
     */
    @PostMapping("/currencySetting/list")
    @ResponseBody
    public Object currencySettingList(String currency) throws Exception {
        // 获取货币列表
        List<CurrencySetting> currencySettingList = salesCommonService.listCurrencySetting(currency, null);
        return new HrPageResult(currencySettingList, currencySettingList.size());
    }

    /**
     * 添加货币种类页面
     * 
     * @param model
     * @throws Exception
     * @return
     */
    @GetMapping("/currencySetting/add/view")
    public String currencySettingAddView(Model model) throws Exception {
        return "/modules/salesCommon/currencySetting_add";
    }

    /**
     * 添加货币种类
     * 
     * @param proportion
     * @throws Exception
     * @return
     */
    @PostMapping("/currencySetting/add")
    @ResponseBody
    public Object currencySettingAdd(CurrencySetting currencySetting) throws Exception {
        try {
            AuthorizingUser authorizingUser = SingletonLoginUtils.getUser();
            if (authorizingUser == null) {
                return new HrResult(CommonReturnCode.FAILED, "登录超时，请重新登录");
            }
            User user = userService.getById(authorizingUser.getUserId());
            currencySetting.setCreatorName(user.getUserName());
            currencySetting.setCreatedTime(
                    DateUtils.convertIntToDateString(DateUtils.getCurrentTime(), "yyyy-MM-dd HH:mm:ss"));
            salesCommonService.insertCurrencySetting(currencySetting);
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("货币明细新增异常信息：", e);
            return new HrResult(0, e.getMessage());
        }
        return new HrResult(CommonReturnCode.SUCCESS);
    }

    /**
     * 编辑仓库页面
     * 
     * @param model
     * @param id
     * @throws Exception
     * @return
     */
    @GetMapping("/currencySetting/edit/view")
    public String currencySettingEditView(Model model, Integer id) throws Exception {
        CurrencySetting currencySetting = salesCommonService.getCurrencySettingById(id);
        model.addAttribute("currencySetting", currencySetting);
        return "/modules/salesCommon/currencySetting_modify";
    }

    /**
     * 编辑货币种类
     * 
     * @param model
     * @param id
     * @throws Exception
     * @return
     */
    @PostMapping("/currencySetting/edit")
    @ResponseBody
    public Object currencySettingEdit(CurrencySetting currencySetting) throws Exception {
        try {
            AuthorizingUser authorizingUser = SingletonLoginUtils.getUser();
            if (authorizingUser == null) {
                return new HrResult(CommonReturnCode.FAILED, "登录超时，请重新登录");
            }
            User user = userService.getById(authorizingUser.getUserId());
            currencySetting.setUpdaterName(user.getUserName());
            currencySetting.setUpdatedTime(
                    DateUtils.convertIntToDateString(DateUtils.getCurrentTime(), "yyyy-MM-dd HH:mm:ss"));
            salesCommonService.updateCurrencySetting(currencySetting);
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("货币明细修改异常信息：", e);
            return new HrResult(0, e.getMessage());
        }
        return new HrResult(CommonReturnCode.SUCCESS);
    }

    /**
     * 删除货币
     * 
     * @param id
     * @throws Exception
     * @return
     */
    @PostMapping("/currencySetting/remove")
    @ResponseBody
    public Object currencySettingRemove(Integer id) throws Exception {
        try {
            salesCommonService.deleteCurrencySetting(id);
        } catch (Exception e) {
            e.printStackTrace();
            return new HrResult(0, e.getMessage());
        }
        return new HrResult(CommonReturnCode.SUCCESS);
    }

    /*
     * =================================货币换算汇率======================================
     */
    /**
     * 货币换算汇率列表页面
     * 
     * @param model
     * @throws Exception
     * @return
     */
    @GetMapping("/currencyExchangeRate/list/view")
    public String currencyExchangeRateListView(Model model) throws Exception {
        // 获取货币列表
        List<String> currencyList = salesCommonService.selectDistinctCurrency();
        model.addAttribute("currencyList", currencyList);
        return "/modules/salesCommon/currencyExchangeRate_list";
    }

    /**
     * 货币换算汇率列表
     * 
     * @param
     * @throws Exception
     * @return
     */
    @PostMapping("/currencyExchangeRate/list")
    @ResponseBody
    public Object currencyExchangeRateList(Integer page, Integer limit, String originalCurrency, String targetCurrency,
            String applyDateStart, String applyDateEnd) throws Exception {
        PageInfo pageInfo = new PageInfo(limit, page);

        if (StringUtil.isStringEmpty(applyDateStart)) {
            applyDateStart = null;
        }
        if (StringUtil.isStringEmpty(applyDateEnd)) {
            applyDateEnd = null;
        }

        // 货币换算汇率列表
        CurrencyExchangeRatePageDTO currencyExchangeRatePageDTO = salesCommonService.currencyExchangeRateListByPage(
                pageInfo, originalCurrency, targetCurrency, applyDateStart, applyDateEnd);

        return new HrPageResult(currencyExchangeRatePageDTO.getCurrencyExchangeRateList(),
                currencyExchangeRatePageDTO.getPageInfo().getTotal());
    }

    /**
     * 添加货币换算汇率页面
     * 
     * @param model
     * @throws Exception
     * @return
     */
    @GetMapping("/currencyExchangeRate/add/view")
    public String currencyExchangeRateAddView(Model model) throws Exception {
        // 获取货币列表
        List<String> currencyList = salesCommonService.selectDistinctCurrency();
        model.addAttribute("currencyList", currencyList);
        return "/modules/salesCommon/currencyExchangeRate_add";
    }

    /**
     * 添加货币换算汇率
     * 
     * @param currencyExchangeRate
     * @throws Exception
     * @return
     */
    @PostMapping("/currencyExchangeRate/add")
    @ResponseBody
    public Object currencyExchangeRateAdd(CurrencyExchangeRate currencyExchangeRate) throws Exception {
        try {
            AuthorizingUser authorizingUser = SingletonLoginUtils.getUser();
            if (authorizingUser == null) {
                return new HrResult(CommonReturnCode.FAILED, "登录超时，请重新登录");
            }
            User user = userService.getById(authorizingUser.getUserId());
            currencyExchangeRate.setCreatorName(user.getUserName());
            currencyExchangeRate.setCreatedTime(
                    DateUtils.convertIntToDateString(DateUtils.getCurrentTime(), "yyyy-MM-dd HH:mm:ss"));
            salesCommonService.insertCurrencyExchangeRate(currencyExchangeRate);
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("货币换算汇率新增异常信息：", e);
            return new HrResult(0, e.getMessage());
        }
        return new HrResult(CommonReturnCode.SUCCESS);
    }

    /**
     * 编辑货币换算汇率页面
     * 
     * @param model
     * @param id
     * @throws Exception
     * @return
     */
    @GetMapping("/currencyExchangeRate/edit/view")
    public String currencyExchangeRateEditView(Model model, Integer id) throws Exception {
        CurrencyExchangeRate currencyExchangeRate = salesCommonService.getCurrencyExchangeRateById(id);
        model.addAttribute("currencyExchangeRate", currencyExchangeRate);
        // 获取货币列表
        List<String> currencyList = salesCommonService.selectDistinctCurrency();
        model.addAttribute("currencyList", currencyList);
        return "/modules/salesCommon/currencyExchangeRate_modify";
    }

    /**
     * 编辑货币换算汇率页面
     * 
     * @param model
     * @param currencyExchangeRate
     * @throws Exception
     * @return
     */
    @PostMapping("/currencyExchangeRate/edit")
    @ResponseBody
    public Object currencyExchangeRateEdit(CurrencyExchangeRate currencyExchangeRate) throws Exception {
        try {
            AuthorizingUser authorizingUser = SingletonLoginUtils.getUser();
            if (authorizingUser == null) {
                return new HrResult(CommonReturnCode.FAILED, "登录超时，请重新登录");
            }
            User user = userService.getById(authorizingUser.getUserId());
            currencyExchangeRate.setUpdaterName(user.getUserName());
            currencyExchangeRate.setUpdatedTime(
                    DateUtils.convertIntToDateString(DateUtils.getCurrentTime(), "yyyy-MM-dd HH:mm:ss"));
            salesCommonService.updateCurrencyExchangeRate(currencyExchangeRate);
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("货币换算汇率明细修改异常信息：", e);
            return new HrResult(0, e.getMessage());
        }
        return new HrResult(CommonReturnCode.SUCCESS);
    }

    /**
     * 根据货币和日期查询汇率
     * 
     * @param originalCurrency 换算原货币
     * @param arrivalDate      到货日期
     * @return
     * @throws Exception
     */
    @PostMapping("/getCurrencyExchangeRate")
    @ResponseBody
    public Object getCurrencyExchangeRate(String originalCurrency, String arrivalDate) throws Exception {
        try {
            logger.info("查询货币换算汇率，原货币: " + originalCurrency + ", 到货日期: " + arrivalDate);

            // 如果是人民币，直接返回汇率为1
            if ("RMB".equals(originalCurrency)) {
                CurrencyExchangeRate result = new CurrencyExchangeRate();
                result.setOriginalCurrency("RMB");
                result.setTargetCurrency("RMB");
                result.setExchangeRate(new java.math.BigDecimal("1.0000"));
                return new HrResult(CommonReturnCode.SUCCESS, result);
            }

            // 查询货币换算汇率
            CurrencyExchangeRate currencyExchangeRate = salesCommonService
                    .getCurrencyExchangeRateByOriginalCurrencyAndDate(originalCurrency, arrivalDate);

            if (currencyExchangeRate != null) {
                logger.info("查询到汇率: " + currencyExchangeRate.getExchangeRate());
                return new HrResult(CommonReturnCode.SUCCESS, currencyExchangeRate);
            } else {
                logger.info("未找到匹配的汇率数据");
                return new HrResult(CommonReturnCode.FAILED, "货币换算汇率未登录！");
            }

        } catch (Exception e) {
            e.printStackTrace();
            logger.error("查询货币换算汇率异常：", e);
            return new HrResult(CommonReturnCode.FAILED.getCode(), "系统异常：" + e.getMessage());
        }
    }

    /**
     * 删除货币换算汇率
     * 
     * @param id
     * @throws Exception
     * @return
     */
    @PostMapping("/currencyExchangeRate/remove")
    @ResponseBody
    public Object currencyExchangeRateRemove(Integer id) throws Exception {
        try {
            salesCommonService.deleteCurrencyExchangeRate(id);
        } catch (Exception e) {
            e.printStackTrace();
            return new HrResult(0, e.getMessage());
        }
        return new HrResult(CommonReturnCode.SUCCESS);
    }

    /*
     * =================================税率计算======================================
     */
    /**
     * 税率计算列表页面
     * 
     * @param model
     * @throws Exception
     * @return
     */
    @GetMapping("/taxRateCalculation/list/view")
    public String taxRateCalculationView(Model model) throws Exception {
        // 获取税率计算列表
        List<TaxRateCalculation> taxRateCalculationList = salesCommonService.listTaxRateCalculation(null, null);
        model.addAttribute("taxRateCalculationList", taxRateCalculationList);
        return "/modules/salesCommon/taxRateCalculation_list";
    }

    /**
     * 检索税率计算列表
     * 
     * @param
     * @throws Exception
     * @return
     */
    @PostMapping("/taxRateCalculation/list")
    @ResponseBody
    public Object taxRateCalculationList(String taxCalcName, String taxRate) throws Exception {
        BigDecimal taxRateTemp = null;
        if (!StringUtil.isStringEmpty(taxRate)) {
            taxRateTemp = new BigDecimal(taxRate);
        }
        // 获取税率计算列表
        List<TaxRateCalculation> taxRateCalculationList = salesCommonService.listTaxRateCalculation(taxCalcName,
                taxRateTemp);
        return new HrPageResult(taxRateCalculationList, taxRateCalculationList.size());
    }

    /**
     * 添加税率计算页面
     * 
     * @param model
     * @throws Exception
     * @return
     */
    @GetMapping("/taxRateCalculation/add/view")
    public String taxRateCalculationAddView(Model model) throws Exception {
        // 获取当前最大税率代码
        int taxCode = salesCommonService.selectMaxTaxCode();
        taxCode = taxCode + 1;
        model.addAttribute("taxCode", taxCode);
        return "/modules/salesCommon/taxRateCalculation_add";
    }

    /**
     * 添加税率计算
     * 
     * @param proportion
     * @throws Exception
     * @return
     */
    @PostMapping("/taxRateCalculation/add")
    @ResponseBody
    public Object taxRateCalculationAdd(TaxRateCalculation taxRateCalculation) throws Exception {
        try {
            AuthorizingUser authorizingUser = SingletonLoginUtils.getUser();
            if (authorizingUser == null) {
                return new HrResult(CommonReturnCode.FAILED, "登录超时，请重新登录");
            }
            User user = userService.getById(authorizingUser.getUserId());
            taxRateCalculation.setCreatorName(user.getUserName());
            taxRateCalculation.setCreatedTime(
                    DateUtils.convertIntToDateString(DateUtils.getCurrentTime(), "yyyy-MM-dd HH:mm:ss"));
            salesCommonService.insertTaxRateCalculation(taxRateCalculation);
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("税率计算明细新增异常信息：", e);
            return new HrResult(0, e.getMessage());
        }
        return new HrResult(CommonReturnCode.SUCCESS);
    }

    /**
     * 编辑税率计算页面
     * 
     * @param model
     * @param id
     * @throws Exception
     * @return
     */
    @GetMapping("/taxRateCalculation/edit/view")
    public String taxRateCalculationEditView(Model model, Integer id) throws Exception {
        TaxRateCalculation taxRateCalculation = salesCommonService.getTaxRateCalculationById(id);
        model.addAttribute("taxRateCalculation", taxRateCalculation);
        return "/modules/salesCommon/taxRateCalculation_modify";
    }

    /**
     * 编辑税率计算
     * 
     * @param model
     * @param taxRateCalculation
     * @throws Exception
     * @return
     */
    @PostMapping("/taxRateCalculation/edit")
    @ResponseBody
    public Object taxRateCalculationEdit(TaxRateCalculation taxRateCalculation) throws Exception {
        try {
            AuthorizingUser authorizingUser = SingletonLoginUtils.getUser();
            if (authorizingUser == null) {
                return new HrResult(CommonReturnCode.FAILED, "登录超时，请重新登录");
            }
            User user = userService.getById(authorizingUser.getUserId());
            taxRateCalculation.setUpdaterName(user.getUserName());
            taxRateCalculation.setUpdatedTime(
                    DateUtils.convertIntToDateString(DateUtils.getCurrentTime(), "yyyy-MM-dd HH:mm:ss"));
            salesCommonService.updateTaxRateCalculation(taxRateCalculation);
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("税率计算明细修改异常信息：", e);
            return new HrResult(0, e.getMessage());
        }
        return new HrResult(CommonReturnCode.SUCCESS);
    }

    /**
     * 删除税率计算
     * 
     * @param id
     * @throws Exception
     * @return
     */
    @PostMapping("/taxRateCalculation/remove")
    @ResponseBody
    public Object taxRateCalculationRemove(Integer id) throws Exception {
        try {
            salesCommonService.deleteTaxRateCalculation(id);
        } catch (Exception e) {
            e.printStackTrace();
            return new HrResult(0, e.getMessage());
        }
        return new HrResult(CommonReturnCode.SUCCESS);
    }

    /**
     * 根据税率代码获取税率
     * 
     * @param model
     * @param id
     * @throws Exception
     * @return
     */
    @PostMapping(value = "/taxRateCodeFun/json")
    @ResponseBody
    public Object getTaxRateByTaxCode(Model model, String taxRateCode) throws Exception {
        try {
            TaxRateCalculation taxRateCalculation = salesCommonService.getTaxRateByTaxCode(taxRateCode);
            model.addAttribute("taxRateCalculation", taxRateCalculation);
            return new HrResult(CommonReturnCode.SUCCESS, taxRateCalculation);
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("异常信息：", e);
            return new HrResult(CommonReturnCode.FAILED.getCode(), "系统异常");
        }
    }

    /* =================================铜条件====================================== */
    /**
     * 铜条件列表页面
     * 
     * @param model
     * @throws Exception
     * @return
     */
    @GetMapping("/copperConditions/list/view")
    public String copperConditionsListView(Model model) throws Exception {
        // 获取铜条件列表
        List<CopperConditions> copperConditionsList = salesCommonService.listCopperConditions(null, null);
        model.addAttribute("copperConditionsList", copperConditionsList);
        return "/modules/salesCommon/copperConditions_list";
    }

    /**
     * 检索铜条件列表
     * 
     * @param
     * @throws Exception
     * @return
     */
    @PostMapping("/copperConditions/list")
    @ResponseBody
    public Object copperConditionsList(String copperCondition) throws Exception {
        // 获取付款条件列表
        List<CopperConditions> copperConditionsList = salesCommonService.listCopperConditions(copperCondition, null);
        return new HrPageResult(copperConditionsList, copperConditionsList.size());
    }

    /**
     * 添加铜条件页面
     * 
     * @param model
     * @throws Exception
     * @return
     */
    @GetMapping("/copperConditions/add/view")
    public String copperConditionsAddView(Model model) throws Exception {
        // 获取货币列表
        List<String> currencyList = salesCommonService.selectDistinctCurrency();
        model.addAttribute("currencyList", currencyList);
        return "/modules/salesCommon/copperConditions_add";
    }

    /**
     * 添加铜条件
     * 
     * @param proportion
     * @throws Exception
     * @return
     */
    @PostMapping("/copperConditions/add")
    @ResponseBody
    public Object copperConditionsAdd(CopperConditions copperConditions) throws Exception {
        try {
            AuthorizingUser authorizingUser = SingletonLoginUtils.getUser();
            if (authorizingUser == null) {
                return new HrResult(CommonReturnCode.FAILED, "登录超时，请重新登录");
            }
            User user = userService.getById(authorizingUser.getUserId());
            copperConditions.setCreatorName(user.getUserName());
            copperConditions.setCreatedTime(
                    DateUtils.convertIntToDateString(DateUtils.getCurrentTime(), "yyyy-MM-dd HH:mm:ss"));
            salesCommonService.insertCopperConditions(copperConditions);
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("铜条件明细新增异常信息：", e);
            return new HrResult(0, e.getMessage());
        }
        return new HrResult(CommonReturnCode.SUCCESS);
    }

    /**
     * 编辑铜条件页面
     * 
     * @param model
     * @param id
     * @throws Exception
     * @return
     */
    @GetMapping("/copperConditions/edit/view")
    public String copperConditionsEditView(Model model, Integer id) throws Exception {
        CopperConditions copperConditions = salesCommonService.getCopperConditionsById(id);
        model.addAttribute("copperConditions", copperConditions);
        // 获取货币列表
        List<String> currencyList = salesCommonService.selectDistinctCurrency();
        model.addAttribute("currencyList", currencyList);
        return "/modules/salesCommon/copperConditions_modify";
    }

    /**
     * 编辑铜条件
     * 
     * @param model
     * @param copperConditions
     * @throws Exception
     * @return
     */
    @PostMapping("/copperConditions/edit")
    @ResponseBody
    public Object copperConditionsEdit(CopperConditions copperConditions) throws Exception {
        try {
            AuthorizingUser authorizingUser = SingletonLoginUtils.getUser();
            if (authorizingUser == null) {
                return new HrResult(CommonReturnCode.FAILED, "登录超时，请重新登录");
            }
            User user = userService.getById(authorizingUser.getUserId());
            copperConditions.setUpdaterName(user.getUserName());
            copperConditions.setUpdatedTime(
                    DateUtils.convertIntToDateString(DateUtils.getCurrentTime(), "yyyy-MM-dd HH:mm:ss"));
            salesCommonService.updateCopperConditions(copperConditions);
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("铜条件明细修改异常信息：", e);
            return new HrResult(0, e.getMessage());
        }
        return new HrResult(CommonReturnCode.SUCCESS);
    }

    /**
     * 删除铜条件
     * 
     * @param id
     * @throws Exception
     * @return
     */
    @PostMapping("/copperConditions/remove")
    @ResponseBody
    public Object copperConditionsRemove(Integer id) throws Exception {
        try {
            salesCommonService.deleteCopperConditions(id);
        } catch (Exception e) {
            e.printStackTrace();
            return new HrResult(0, e.getMessage());
        }
        return new HrResult(CommonReturnCode.SUCCESS);
    }

    /**
     * 根据铜条件获取铜条件详情
     * 
     * @param model
     * @param id
     * @throws Exception
     * @return
     */
    @PostMapping(value = "/copperConditions/json")
    @ResponseBody
    public Object findCopperConditions(Model model, String copperCondition) throws Exception {
        try {
            CopperConditions copperConditions = salesCommonService.findCopperConditions(copperCondition);
            model.addAttribute("copperConditions", copperConditions);
            return new HrResult(CommonReturnCode.SUCCESS, copperConditions);
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("异常信息：", e);
            return new HrResult(CommonReturnCode.FAILED.getCode(), "系统异常");
        }
    }

    /* =================================铜价格====================================== */
    /**
     * 铜价格列表页面
     * 
     * @param model
     * @throws Exception
     * @return
     */
    @GetMapping("/copperPrices/list/view")
    public String copperPricesListView(Model model) throws Exception {
        // 获取铜条件列表
        List<CopperConditions> copperConditionsList = salesCommonService.listCopperConditions(null, null);
        model.addAttribute("copperConditionsList", copperConditionsList);
        return "/modules/salesCommon/copperPrices_list";
    }

    /**
     * 检索铜价格列表
     * 
     * @param
     * @throws Exception
     * @return
     */
    @PostMapping("/copperPrices/list")
    @ResponseBody
    public Object copperPricesList(Integer page, Integer limit, String copperCondition, String applyDateStart,
            String applyDateEnd) throws Exception {
        PageInfo pageInfo = new PageInfo(limit, page);
        if (StringUtil.isStringEmpty(applyDateStart)) {
            applyDateStart = null;
        }
        if (StringUtil.isStringEmpty(applyDateEnd)) {
            applyDateEnd = null;
        }

        // 获取铜价格列表
        CopperPricesPageDTO copperPricesPageDTO = salesCommonService.copperPricesListByPage(pageInfo, copperCondition,
                applyDateStart, applyDateEnd);

        return new HrPageResult(copperPricesPageDTO.getCopperPricesList(),
                copperPricesPageDTO.getPageInfo().getTotal());
    }

    /**
     * 添加铜价格页面
     * 
     * @param model
     * @throws Exception
     * @return
     */
    @GetMapping("/copperPrices/add/view")
    public String copperPricesAddView(Model model) throws Exception {
        // 获取铜条件列表
        List<CopperConditions> copperConditionsList = salesCommonService.listCopperConditions(null, null);
        model.addAttribute("copperConditionsList", copperConditionsList);
        return "/modules/salesCommon/copperPrices_add";
    }

    /**
     * 添加铜价格
     * 
     * @param proportion
     * @throws Exception
     * @return
     */
    @PostMapping("/copperPrices/add")
    @ResponseBody
    public Object copperPricesAdd(CopperPrices copperPrices) throws Exception {
        try {
            AuthorizingUser authorizingUser = SingletonLoginUtils.getUser();
            if (authorizingUser == null) {
                return new HrResult(CommonReturnCode.FAILED, "登录超时，请重新登录");
            }
            User user = userService.getById(authorizingUser.getUserId());
            copperPrices.setCreatorName(user.getUserName());
            copperPrices.setCreatedTime(
                    DateUtils.convertIntToDateString(DateUtils.getCurrentTime(), "yyyy-MM-dd HH:mm:ss"));
            salesCommonService.insertCopperPrices(copperPrices);
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("铜价格明细新增异常信息：", e);
            return new HrResult(0, e.getMessage());
        }
        return new HrResult(CommonReturnCode.SUCCESS);
    }

    /**
     * 编辑铜价格页面
     * 
     * @param model
     * @param id
     * @throws Exception
     * @return
     */
    @GetMapping("/copperPrices/edit/view")
    public String copperPricesEditView(Model model, Integer id) throws Exception {
        // 获取铜条件列表
        List<String> copperConditionList = salesCommonService.selectCopperCondition();
        model.addAttribute("copperConditionList", copperConditionList);

        CopperPrices copperPrices = salesCommonService.getCopperPricesById(id);
        model.addAttribute("copperPrices", copperPrices);
        return "/modules/salesCommon/copperPrices_modify";
    }

    /**
     * 编辑铜条件
     * 
     * @param model
     * @param copperPrices
     * @throws Exception
     * @return
     */
    @PostMapping("/copperPrices/edit")
    @ResponseBody
    public Object copperPricesEdit(CopperPrices copperPrices) throws Exception {
        try {
            AuthorizingUser authorizingUser = SingletonLoginUtils.getUser();
            if (authorizingUser == null) {
                return new HrResult(CommonReturnCode.FAILED, "登录超时，请重新登录");
            }
            User user = userService.getById(authorizingUser.getUserId());
            copperPrices.setUpdaterName(user.getUserName());
            copperPrices.setUpdatedTime(
                    DateUtils.convertIntToDateString(DateUtils.getCurrentTime(), "yyyy-MM-dd HH:mm:ss"));
            salesCommonService.updateCopperPrices(copperPrices);
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("铜价格明细修改异常信息：", e);
            return new HrResult(0, e.getMessage());
        }
        return new HrResult(CommonReturnCode.SUCCESS);
    }

    /**
     * 删除付款条件
     * 
     * @param id
     * @throws Exception
     * @return
     */
    @PostMapping("/copperPrices/remove")
    @ResponseBody
    public Object copperPricesRemove(Integer id) throws Exception {
        try {
            salesCommonService.deleteCopperPrices(id);
        } catch (Exception e) {
            e.printStackTrace();
            return new HrResult(0, e.getMessage());
        }
        return new HrResult(CommonReturnCode.SUCCESS);
    }

    /**
     * 根据铜条件和到货日期查询铜base
     * 
     * @param copperCondition 铜条件
     * @param arrivalDate     到货日期
     * @throws Exception
     * @return
     */
    @PostMapping("/getCopperBase")
    @ResponseBody
    public Object getCopperBase(String copperCondition, String arrivalDate) throws Exception {
        try {
            // 验证用户登录状态
            AuthorizingUser authorizingUser = SingletonLoginUtils.getUser();
            if (authorizingUser == null) {
                return new HrResult(CommonReturnCode.FAILED, "登录超时，请重新登录");
            }

            logger.info("查询铜base参数: copperCondition=" + copperCondition + ", arrivalDate=" + arrivalDate);

            if (StringUtil.isStringEmpty(copperCondition) || StringUtil.isStringEmpty(arrivalDate)) {
                return new HrResult(CommonReturnCode.FAILED, "铜条件和到货日期不能为空");
            }

            // 查询铜价表
            CopperPrices copperPrices = salesCommonService.getCopperBaseByConditionAndDate(copperCondition,
                    arrivalDate);

            if (copperPrices != null) {
                logger.info("查询到铜base: " + copperPrices.getCopperPrice());
                return new HrResult(CommonReturnCode.SUCCESS, copperPrices);
            } else {
                logger.info("未找到匹配的铜base数据");
                return new HrResult(CommonReturnCode.FAILED, "未找到匹配的铜base数据");
            }

        } catch (Exception e) {
            e.printStackTrace();
            logger.error("查询铜base异常：", e);
            return new HrResult(CommonReturnCode.FAILED.getCode(), "系统异常：" + e.getMessage());
        }
    }

    /**
     * 根据铜签约NO和使用月查询铜合同详情（用于预约铜/支给铜）
     *
     * @param copperSignNo 铜签约NO
     * @param useMonth     使用月（可以是完整日期YYYY-MM-DD或年月格式YYYY-MM）
     * @throws Exception
     * @return
     */
    @PostMapping("/getCopperContractDetailForBase")
    @ResponseBody
    public Object getCopperContractDetailForBase(String copperSignNo, String useMonth) throws Exception {
        try {
            // 验证用户登录状态
            AuthorizingUser authorizingUser = SingletonLoginUtils.getUser();
            if (authorizingUser == null) {
                return new HrResult(CommonReturnCode.FAILED, "登录超时，请重新登录");
            }

            logger.info("查询铜合同详情原始参数: copperSignNo=" + copperSignNo + ", useMonth=" + useMonth);

            if (StringUtil.isStringEmpty(copperSignNo) || StringUtil.isStringEmpty(useMonth)) {
                return new HrResult(CommonReturnCode.FAILED, "铜签约NO和使用月不能为空");
            }

            // 格式化useMonth参数：如果是完整日期格式（YYYY-MM-DD），则截取年月部分（YYYY-MM）
            String formattedUseMonth = useMonth;
            if (useMonth != null && useMonth.length() > 7 && useMonth.matches("\\d{4}-\\d{2}-\\d{2}")) {
                formattedUseMonth = useMonth.substring(0, 7);
                logger.info("日期格式化: " + useMonth + " -> " + formattedUseMonth);
            }

            logger.info("查询铜合同详情格式化后参数: copperSignNo=" + copperSignNo + ", useMonth=" + formattedUseMonth);

            // 查询铜合同详情
            CopperContract copperContract = salesCommonService.getCopperContractDetailWithCondition(copperSignNo,
                    formattedUseMonth);

            if (copperContract != null) {
                logger.info("查询到铜合同详情: copperCondition=" + copperContract.getCopperCondition() +
                        ", signedCopperPriceExTax=" + copperContract.getSignedCopperPriceExTax() +
                        ", currency=" + copperContract.getCurrency());
                return new HrResult(CommonReturnCode.SUCCESS, copperContract);
            } else {
                logger.info("未找到匹配的铜合同数据");
                return new HrResult(CommonReturnCode.FAILED, "未找到匹配的铜合同数据");
            }

        } catch (Exception e) {
            e.printStackTrace();
            logger.error("查询铜合同详情异常：", e);
            return new HrResult(CommonReturnCode.FAILED.getCode(), "系统异常：" + e.getMessage());
        }
    }

    /* =================================铜合同====================================== */
    /**
     * 铜合同列表页面
     * 
     * @param model
     * @throws Exception
     * @return
     */
    @GetMapping("/copperContract/list/view")
    public String copperContractListView(Model model) throws Exception {
        // 获取客户列表
        List<Customers> customersList = sumitomoService.listCustomersList();
        model.addAttribute("customersList", customersList);
        // 获取铜条件列表
        List<CopperConditions> copperConditionsList = salesCommonService.listCopperConditions(null, null);
        model.addAttribute("copperConditionsList", copperConditionsList);
        return "/modules/salesCommon/copperContract_list";
    }

    /**
     * 铜合同列表
     * 
     * @param
     * @throws Exception
     * @return
     */
    @PostMapping("/copperContract/list")
    @ResponseBody
    public Object copperContractList(Integer page, Integer limit, String customerCode, String copperSignNo,
            String copperSignType, String copperCondition, String appointmentDate, String useMonth, String status)
            throws Exception {
        PageInfo pageInfo = new PageInfo(limit, page);

        if (StringUtil.isStringEmpty(appointmentDate)) {
            appointmentDate = null;
        }
        if (StringUtil.isStringEmpty(useMonth)) {
            useMonth = null;
        }

        // 获取铜合同列表
        CopperContractPageDTO copperContractPageDTO = salesCommonService.copperContractListByPage(pageInfo,
                customerCode, copperSignNo, copperSignType, copperCondition, appointmentDate, useMonth, status);

        return new HrPageResult(copperContractPageDTO.getCopperContractList(),
                copperContractPageDTO.getPageInfo().getTotal());
    }

    /**
     * 添加铜合同页面
     * 
     * @param model
     * @throws Exception
     * @return
     */
    @GetMapping("/copperContract/add/view")
    public String copperContractAddView(Model model) throws Exception {
        // 获取客户列表
        List<Customers> customersList = sumitomoService.listCustomersList();
        model.addAttribute("customersList", customersList);

        // 获取铜条件列表
        List<CopperConditions> copperConditionsList = salesCommonService.listCopperConditions(null, null);
        model.addAttribute("copperConditionsList", copperConditionsList);
        return "/modules/salesCommon/copperContract_add";
    }

    /**
     * 添加铜合同
     * 
     * @param proportion
     * @throws Exception
     * @return
     */
    @PostMapping("/copperContract/add")
    @ResponseBody
    public Object copperContractAdd(CopperContract copperContract) throws Exception {
        try {
            AuthorizingUser authorizingUser = SingletonLoginUtils.getUser();
            if (authorizingUser == null) {
                return new HrResult(CommonReturnCode.FAILED, "登录超时，请重新登录");
            }
            User user = userService.getById(authorizingUser.getUserId());
            copperContract.setCreatorName(user.getUserName());
            copperContract.setCreatedTime(
                    DateUtils.convertIntToDateString(DateUtils.getCurrentTime(), "yyyy-MM-dd HH:mm:ss"));
            copperContract.setActualQuantity(BigDecimal.ZERO); // 实际数量
            copperContract.setRemainingQuantity(copperContract.getSignedQuantity()); // 剩余数量
            copperContract.setStatus(CopperContract.STATE_NORMAL);
            salesCommonService.insertCopperContract(copperContract);
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("铜合同明细新增异常信息：", e);
            return new HrResult(0, e.getMessage());
        }
        return new HrResult(CommonReturnCode.SUCCESS);
    }

    /**
     * 编辑铜合同页面
     * 
     * @param model
     * @param id
     * @throws Exception
     * @return
     */
    @GetMapping("/copperContract/edit/view")
    public String copperContractEditView(Model model, Integer id) throws Exception {
        // 获取客户列表
        List<Customers> customersList = sumitomoService.listCustomersList();
        model.addAttribute("customersList", customersList);

        // 获取铜条件列表
        List<String> copperConditionList = salesCommonService.selectCopperCondition();
        model.addAttribute("copperConditionList", copperConditionList);

        CopperContract copperContract = salesCommonService.getCopperContractById(id);
        model.addAttribute("copperContract", copperContract);
        return "/modules/salesCommon/copperContract_modify";
    }

    /**
     * 编辑铜合同
     * 
     * @param model
     * @param copperContract
     * @throws Exception
     * @return
     */
    @PostMapping("/copperContract/edit")
    @ResponseBody
    public Object copperContractEdit(CopperContract copperContract) throws Exception {
        try {
            AuthorizingUser authorizingUser = SingletonLoginUtils.getUser();
            if (authorizingUser == null) {
                return new HrResult(CommonReturnCode.FAILED, "登录超时，请重新登录");
            }
            User user = userService.getById(authorizingUser.getUserId());
            copperContract.setUpdaterName(user.getUserName());
            copperContract.setUpdatedTime(
                    DateUtils.convertIntToDateString(DateUtils.getCurrentTime(), "yyyy-MM-dd HH:mm:ss"));
            salesCommonService.updateCopperContract(copperContract);
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("铜合同明细修改异常信息：", e);
            return new HrResult(0, e.getMessage());
        }
        return new HrResult(CommonReturnCode.SUCCESS);
    }

    /**
     * 更新铜合同(9)
     * 
     * @param status
     * @param id
     * @throws Exception
     * @return
     */
    @PostMapping("/copperContract/remove")
    @ResponseBody
    public Object copperContractRemove(Integer id) throws Exception {
        try {
            salesCommonService.updateCopperContractStatus(CopperContract.STATE_DELETED, id);
        } catch (Exception e) {
            e.printStackTrace();
            return new HrResult(0, e.getMessage());
        }
        return new HrResult(CommonReturnCode.SUCCESS);
    }

    /* =================================升水====================================== */
    /**
     * 升水价格列表页面
     * 
     * @param model
     * @throws Exception
     * @return
     */
    @GetMapping("/ascendingWater/list/view")
    public String ascendingWaterListView(Model model) throws Exception {
        // 获取客户列表
        List<Customers> customersList = sumitomoService.listCustomersList();
        model.addAttribute("customersList", customersList);
        // 获取铜条件列表
        List<CopperConditions> copperConditionsList = salesCommonService.listCopperConditions(null, null);
        model.addAttribute("copperConditionsList", copperConditionsList);
        return "/modules/salesCommon/ascendingWater_list";
    }

    /**
     * 检索升水价格列表
     * 
     * @param
     * @throws Exception
     * @return
     */
    @PostMapping("/ascendingWater/list")
    @ResponseBody
    public Object ascendingWaterList(Integer page, Integer limit, String customerCode, String copperCondition,
            String applyDateStart, String applyDateEnd) throws Exception {
        PageInfo pageInfo = new PageInfo(limit, page);

        if (StringUtil.isStringEmpty(applyDateStart)) {
            applyDateStart = null;
        }
        if (StringUtil.isStringEmpty(applyDateEnd)) {
            applyDateEnd = null;
        }

        // 获取升水列表
        AscendingWaterPageDTO ascendingWaterPageDTO = salesCommonService.ascendingWaterListByPage(pageInfo,
                customerCode, copperCondition, applyDateStart, applyDateEnd);

        return new HrPageResult(ascendingWaterPageDTO.getAscendingWaterList(),
                ascendingWaterPageDTO.getPageInfo().getTotal());
    }

    /**
     * 添加升水页面
     * 
     * @param model
     * @throws Exception
     * @return
     */
    @GetMapping("/ascendingWater/add/view")
    public String ascendingWaterAddView(Model model) throws Exception {
        // 获取客户列表
        List<Customers> customersList = sumitomoService.listCustomersList();
        model.addAttribute("customersList", customersList);

        // 获取铜条件列表
        List<CopperConditions> copperConditionsList = salesCommonService.listCopperConditions(null, null);
        model.addAttribute("copperConditionsList", copperConditionsList);
        return "/modules/salesCommon/ascendingWater_add";
    }

    /**
     * 添加升水价格
     * 
     * @param ascendingWater
     * @throws Exception
     * @return
     */
    @PostMapping("/ascendingWater/add")
    @ResponseBody
    public Object ascendingWaterAdd(AscendingWater ascendingWater) throws Exception {
        try {
            AuthorizingUser authorizingUser = SingletonLoginUtils.getUser();
            if (authorizingUser == null) {
                return new HrResult(CommonReturnCode.FAILED, "登录超时，请重新登录");
            }
            User user = userService.getById(authorizingUser.getUserId());
            ascendingWater.setCreatorName(user.getUserName());
            ascendingWater.setCreatedTime(
                    DateUtils.convertIntToDateString(DateUtils.getCurrentTime(), "yyyy-MM-dd HH:mm:ss"));
            salesCommonService.insertAscendingWater(ascendingWater);
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("升水价格新增异常信息：", e);
            return new HrResult(0, e.getMessage());
        }
        return new HrResult(CommonReturnCode.SUCCESS);
    }

    /**
     * 编辑升水价格页面
     * 
     * @param model
     * @param id
     * @throws Exception
     * @return
     */
    @GetMapping("/ascendingWater/edit/view")
    public String ascendingWaterEditView(Model model, Integer id) throws Exception {
        // 获取客户列表
        List<Customers> customersList = sumitomoService.listCustomersList();
        model.addAttribute("customersList", customersList);

        // 获取铜条件列表
        List<String> copperConditionList = salesCommonService.selectCopperCondition();
        model.addAttribute("copperConditionList", copperConditionList);

        AscendingWater ascendingWater = salesCommonService.getAscendingWaterById(id);
        model.addAttribute("ascendingWater", ascendingWater);
        return "/modules/salesCommon/ascendingWater_modify";
    }

    /**
     * 编辑升水价格
     * 
     * @param model
     * @param ascendingWater
     * @throws Exception
     * @return
     */
    @PostMapping("/ascendingWater/edit")
    @ResponseBody
    public Object ascendingWaterEdit(AscendingWater ascendingWater) throws Exception {
        try {
            AuthorizingUser authorizingUser = SingletonLoginUtils.getUser();
            if (authorizingUser == null) {
                return new HrResult(CommonReturnCode.FAILED, "登录超时，请重新登录");
            }
            User user = userService.getById(authorizingUser.getUserId());
            ascendingWater.setUpdaterName(user.getUserName());
            ascendingWater.setUpdatedTime(
                    DateUtils.convertIntToDateString(DateUtils.getCurrentTime(), "yyyy-MM-dd HH:mm:ss"));
            salesCommonService.updateAscendingWater(ascendingWater);
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("升水价格明细修改异常信息：", e);
            return new HrResult(0, e.getMessage());
        }
        return new HrResult(CommonReturnCode.SUCCESS);
    }

    /**
     * 删除升水价格
     * 
     * @param id
     * @throws Exception
     * @return
     */
    @PostMapping("/ascendingWater/remove")
    @ResponseBody
    public Object ascendingWaterRemove(Integer id) throws Exception {
        try {
            salesCommonService.deleteAscendingWater(id);
        } catch (Exception e) {
            e.printStackTrace();
            return new HrResult(0, e.getMessage());
        }
        return new HrResult(CommonReturnCode.SUCCESS);
    }

    /**
     * 根据客户代码和到货日期查询升水表数据（用于一般铜的铜条件和升水单价获取）
     * 
     * @param customerCode 客户代码
     * @param arrivalDate  到货日期
     * @throws Exception
     * @return
     */
    @PostMapping("/getAscendingWaterByCustomerAndDate")
    @ResponseBody
    public Object getAscendingWaterByCustomerAndDate(String customerCode, String arrivalDate) throws Exception {
        try {
            // 验证用户登录状态
            AuthorizingUser authorizingUser = SingletonLoginUtils.getUser();
            if (authorizingUser == null) {
                return new HrResult(CommonReturnCode.FAILED, "登录超时，请重新登录");
            }

            if (StringUtil.isStringEmpty(customerCode)) {
                return new HrResult(CommonReturnCode.FAILED, "客户代码不能为空");
            }

            if (StringUtil.isStringEmpty(arrivalDate)) {
                return new HrResult(CommonReturnCode.FAILED, "到货日期不能为空");
            }

            logger.info("查询升水表数据 - 客户代码: " + customerCode + ", 到货日期: " + arrivalDate);

            // 调用服务获取升水表数据
            List<AscendingWater> ascendingWaterList = salesCommonService
                    .getAscendingWaterByCustomerAndDate(customerCode, arrivalDate);

            logger.info("获取到升水表数据数量: " + (ascendingWaterList != null ? ascendingWaterList.size() : 0));

            return new HrResult(CommonReturnCode.SUCCESS, ascendingWaterList);

        } catch (Exception e) {
            e.printStackTrace();
            logger.error("查询升水表数据异常：", e);
            return new HrResult(CommonReturnCode.FAILED.getCode(), "系统异常：" + e.getMessage());
        }
    }
}
