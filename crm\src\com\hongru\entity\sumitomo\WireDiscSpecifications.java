package com.hongru.entity.sumitomo;

import com.baomidou.mybatisplus.annotations.TableField;
import com.baomidou.mybatisplus.annotations.TableId;
import com.baomidou.mybatisplus.annotations.TableName;
import com.baomidou.mybatisplus.enums.IdType;

import java.math.BigDecimal;

@TableName("线盘规格")//CostPrice
public class WireDiscSpecifications {
	/* 流水号 */
	@TableId(value="流水号", type= IdType.AUTO)
	protected int id;
	/* 线盘型号 */
	protected String wireDiscType;
	/* 线盘代号 */
	protected String wireDiscCode;
	/* 线盘重量 */
	protected BigDecimal wireDiscWeight;
	/* 最小重量 */
	protected BigDecimal minWeigh;
	/* 最大重量 */
	protected BigDecimal maxWeigh;
	
	public int getId() {
		return id;
	}
	public void setId(int id) {
		this.id = id;
	}
	public String getWireDiscType() {
		return wireDiscType;
	}
	public void setWireDiscType(String wireDiscType) {
		this.wireDiscType = wireDiscType;
	}
	public String getWireDiscCode() {
		return wireDiscCode;
	}
	public void setWireDiscCode(String wireDiscCode) {
		this.wireDiscCode = wireDiscCode;
	}
	public BigDecimal getWireDiscWeight() {
		return wireDiscWeight;
	}
	public void setWireDiscWeight(BigDecimal wireDiscWeight) {
		this.wireDiscWeight = wireDiscWeight;
	}
	public BigDecimal getMinWeigh() {
		return minWeigh;
	}
	public void setMinWeigh(BigDecimal minWeigh) {
		this.minWeigh = minWeigh;
	}
	public BigDecimal getMaxWeigh() {
		return maxWeigh;
	}
	public void setMaxWeigh(BigDecimal maxWeigh) {
		this.maxWeigh = maxWeigh;
	}
}