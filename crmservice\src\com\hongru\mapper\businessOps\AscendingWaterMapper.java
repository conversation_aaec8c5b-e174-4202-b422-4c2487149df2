package com.hongru.mapper.businessOps;

import com.baomidou.mybatisplus.mapper.BaseMapper;
import com.hongru.entity.businessOps.AscendingWater;
import com.hongru.support.page.PageInfo;

import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface AscendingWaterMapper extends BaseMapper<AscendingWater> {
	int insertAscendingWater(@Param("ascendingWater") AscendingWater ascendingWater);
  
	AscendingWater selectAscendingWaterById(@Param("id") int id);

    List<AscendingWater> ascendingWaterListByPage(@Param("pageInfo") PageInfo pageInfo, @Param("customerCode") String customerCode, @Param("copperCondition") String copperCondition, @Param("applyDateStart") String applyDateStart, @Param("applyDateEnd") String applyDateEnd);
    Integer ascendingWaterListByPageCount(@Param("customerCode") String customerCode, @Param("copperCondition") String copperCondition, @Param("applyDateStart") String applyDateStart, @Param("applyDateEnd") String applyDateEnd);

    void updateAscendingWater(@Param("ascendingWater") AscendingWater ascendingWater);

    void deleteAscendingWater(@Param("id") Integer id);

    /**
     * 根据客户代码和到货日期查询升水表数据（用于一般铜的铜条件和升水单价获取）
     * 
     * @param customerCode 客户代码
     * @param arrivalDate  到货日期
     * @return 升水表数据列表
     */
    List<AscendingWater> getAscendingWaterByCustomerAndDate(@Param("customerCode") String customerCode,
            @Param("arrivalDate") String arrivalDate);
}
