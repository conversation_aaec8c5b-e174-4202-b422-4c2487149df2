<%@ page language="java" import="java.util.*"
         contentType="text/html; charset=utf-8"%>
<%@ include file="/WEB-INF/layouts/base.jsp"%>
<html>
<head>
    <title>货币种类修改</title>
    <style>
        /*设置数据表表头字体*/
        .layui-table th .layui-table-cell{
            text-align: center;
            font-size: 16px;
            font-weight: bold;
        }
    </style>
</head>
<body>
<form class="layui-form hr-form-add" action="${ctx}/salesCommon/currencySetting/edit" method="post" id="submitForm">
    <div class="layui-fluid hr-layui-fluid">
        <div class="layui-card">
            <div class="layui-card-body">
                <div class="layui-form-item layui-row">

                    <div class="layui-inline layui-col-md5">
                        <label class="layui-form-label"><span class="star">*</span>货币:</label>
                        <div class="layui-input-block">
                            <input type="text" class="layui-input" id="currency" name="currency" value="${currencySetting.currency}" required lay-verify="required" />
                        </div>
                    </div>
                    <div class="layui-inline layui-col-md5">
                        <label class="layui-form-label"><span class="star">*</span>货币名:</label>
                        <div class="layui-input-block">
                            <input type="text" class="layui-input" id="currencyName" name="currencyName" value="${currencySetting.currencyName}" required lay-verify="required" />
                        </div>
                    </div>
                </div>

                </div>
                <div class="layui-form-item layui-row">
                    <div class="layui-inline layui-col-md12">
                        <label class="layui-form-label"></label>
                        <div class="layui-input-block">
                            <input type="hidden" id="id" name="id" value="${currencySetting.id}"/>
                            <button type="button" class="layui-btn layui-bg-blue" lay-submit lay-filter="formDemo">保存</button>
                            <button type="button" class="layui-btn layui-btn-primary" onclick="closeAll();">取消</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</form>
<myfooter>
    <script src="${ctxsta}/hongru/js/salesCommon/currencySetting_modify.js?time=1"></script>
</myfooter>
</body>
</html>
