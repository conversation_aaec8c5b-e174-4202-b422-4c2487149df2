package com.hongru.mapper.businessOps;

import com.baomidou.mybatisplus.mapper.BaseMapper;
import com.hongru.entity.businessOps.PayConditions;

import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface PayConditionsMapper extends BaseMapper<PayConditions> {
	int insertPayConditions(@Param("payConditions") PayConditions payConditions);
  
	PayConditions selectPayConditionsById(@Param("id") int id);

    List<PayConditions> listPayConditions(@Param("payCondition") String payCondition, @Param("payConditionName") String payConditionName);

    void updatePayConditions(@Param("payConditions") PayConditions payConditions);

    void deletePayConditions(@Param("id") Integer id);
}
