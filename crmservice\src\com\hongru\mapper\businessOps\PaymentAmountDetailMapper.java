package com.hongru.mapper.businessOps;

import com.baomidou.mybatisplus.mapper.BaseMapper;
import com.hongru.entity.businessOps.PaymentAmountDetail;

import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface PaymentAmountDetailMapper extends BaseMapper<PaymentAmountDetail> {
    
    /**
     * 根据支付请求NO查询明细列表
     * 
     * @param paymentRequestNo 支付请求NO
     * @return 明细列表
     */
    List<PaymentAmountDetail> selectByPaymentRequestNo(@Param("paymentRequestNo") String paymentRequestNo);

    /**
     * 根据支付请求NO删除明细数据
     * 
     * @param paymentRequestNo 支付请求NO
     * @return 删除行数
     */
    int deleteByPaymentRequestNo(@Param("paymentRequestNo") String paymentRequestNo);

    /**
     * 批量插入明细数据
     * 
     * @param details 明细列表
     * @return 插入行数
     */
    int batchInsert(@Param("details") List<PaymentAmountDetail> details);

    /**
     * 批量更新明细数据
     * 
     * @param details 明细列表
     * @return 更新行数
     */
    int batchUpdate(@Param("details") List<PaymentAmountDetail> details);
}
