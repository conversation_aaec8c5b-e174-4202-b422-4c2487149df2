layui.use(['form', 'table', 'layer'], function(){
    var form = layui.form
        ,table = layui.table
        ,layer = layui.layer;

    // 获取URL参数
    function getUrlParam(name) {
        var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
        var r = window.location.search.substr(1).match(reg);
        if (r != null) return unescape(r[2]);
        return null;
    }

    // 获取销售额NO（优先使用销售额NO，不再使用ID）
    var salesAmountNo = $('#salesAmountNo').val() || getUrlParam('salesAmountNo');

    // 加载数据 - 只使用销售额NO
    if (salesAmountNo && salesAmountNo.trim() !== '') {
        console.log('使用销售额NO获取数据:', salesAmountNo);
        loadDataByNo(salesAmountNo);
    } else {
        console.log('没有有效的销售额NO，无法加载数据');
        layer.msg('缺少销售额NO参数，无法加载数据', {icon: 2});
    }
    
    // 注意：不再使用ID方式加载数据，统一使用销售额NO

    // 根据销售额NO加载数据
    function loadDataByNo(salesAmountNo) {
        // 从后端获取数据
        $.ajax({
            url: baselocation + '/order/salesLogin/detailByNo',
            type: 'POST',
            data: {salesAmountNo: salesAmountNo},
            success: function(result) {
                console.log('根据销售额NO获取详情数据结果:', result); // 添加日志，显示完整返回结果
                if (result.code === 1 && result.data) {
                    renderDetailData(result.data);
                } else {
                    layer.msg('获取数据失败: ' + (result.message || '未知错误'), {icon: 2});
                }
            },
            error: function(xhr, status, error) {
                layer.msg('获取数据失败: ' + error, {icon: 2});
            }
        });
    }

    // 渲染详情数据
    function renderDetailData(data) {
        var headerData = data.salesAmount;
        var detailData = data.salesAmountDetails || [];

        console.log('表头数据:', headerData); // 添加日志，显示表头数据
        console.log('明细数据:', detailData); // 添加日志，显示明细数据

        if (detailData && detailData.length > 0) {
            console.log('第一条明细数据的所有字段:', Object.keys(detailData[0])); // 添加日志，显示第一条明细数据的所有字段名
            console.log('第一条明细数据:', detailData[0]); // 添加日志，显示第一条明细数据的完整内容
            
            // 检查重要字段
            console.log('产品代码字段值检查:', detailData[0].productName || detailData[0].品名);
            console.log('线盘字段值检查:', detailData[0].reelName || detailData[0].线盘);
            console.log('客户代码字段值检查:', detailData[0].customerProductCode || detailData[0].客户品目C);
            console.log('出货日字段值检查:', detailData[0].outboundDate || detailData[0].shipmentDate || detailData[0].出货日);
            console.log('客户订单No字段值检查:', detailData[0].customerOrderNo || detailData[0].客户订单No);
            console.log('明细内部备注字段值检查:', detailData[0].remark || detailData[0].detailRemark || detailData[0].明细内部备注 || detailData[0].明细备注);
            console.log('条码字段值检查:', detailData[0].productCategoryCode || detailData[0].品目C);
            console.log('送货单号字段值检查:', detailData[0].deliveryNoteNo || detailData[0].deliveryOrderNo || detailData[0].出货单No);
            console.log('送货单序号字段值检查:', detailData[0].deliveryNoteSeq || detailData[0].deliveryOrderSeq || detailData[0].送货单序号);
        }

        // 填充表单数据
        $('#salesNo').text(headerData.salesAmountNo || '');
        $('#customerAlias').text(headerData.customerAlias || '');
        $('#salesDate').text(headerData.salesAmountDate || '');
        $('#currency').text(headerData.settlementCurrency || '');
        // 直接显示税率（后端已经返回了税率值）
        $('#taxRate').text(headerData.taxRate || '');

        // 格式化显示已保存的客户条件字段值（显示为"代码 名称"格式）
        formatAndDisplayConditionFields(headerData.tradeCondition, headerData.payCondition, headerData.transportCondition);

        console.log('销售额详情页面显示条件字段 - 交易条件:', headerData.tradeCondition, '付款条件:', headerData.payCondition, '运输条件:', headerData.transportCondition);

        // 计算总金额
        var totalAmount = 0;
        if (detailData && detailData.length > 0) {
            detailData.forEach(function(item) {
                if (item.salesAmount) {
                    totalAmount += parseFloat(item.salesAmount);
                }
            });
        }
        $('#totalAmount').text(totalAmount.toFixed(2));

        // 计算并显示税额
        calculateAndDisplayTaxAmount(totalAmount);

        $('#creatorName').text(headerData.creatorName || '');
        $('#createdTime').text(headerData.createdTime || '');
        $('#updaterName').text(headerData.updaterName || '');
        $('#updatedTime').text(headerData.updatedTime || '');

        // 获取客户详情信息来显示条件字段
        if (headerData.customerCode) {
            getCustomerInfoForDetail(headerData.customerCode);
        }

        // 生成两行表头的HTML表格（参考11.html的实现）
        generateTwoRowTable(detailData, headerData);

        // 刷新表单
        form.render();
    }

    // 计算并显示税额
    function calculateAndDisplayTaxAmount(totalAmount) {
        // 直接从页面获取税率值
        var taxRate = parseFloat($('#taxRate').text()) || 0;
        var taxAmount = totalAmount * (taxRate / 100); // 税率是百分比，需要除以100
        $('#taxAmount').text(taxAmount.toFixed(2));
        console.log('销售额详情页面计算税额 - 总金额:', totalAmount, '税率:', taxRate + '%', '税额:', taxAmount.toFixed(2));
        console.log('销售额详情页面税率字段值:', $('#taxRate').text(), '税率字段是否存在:', $('#taxRate').length);
    }
});

// 生成两行表头的HTML表格（参考11.html的实现）
function generateTwoRowTable(detailData, headerData) {
    // 定义表头字段 - 只有13列，但分两行显示
    var headers = [
        '出货单No.', '送货单序号', '条码', '产品代码', '线盘', '客户代码',
        '出货日', '到货日期', '客户订单No.', '明细内部备注', '铜合同类别', '铜合同NO', '铜条件'
    ];

    var secondRowHeaders = [
        '铜货币', '换算率', '数量(KG)', '铜base', '升水',
        '换算后铜单价', '0base', '销售单价', '销售金额', '产品中分类', '出货计划番号', '', ''
    ];

    // 添加样式
    if (!$('#twoRowTableStyle').length) {
        var style = $('<style id="twoRowTableStyle">');
        style.text(`
            #detailTable {
                width: 100%;
                border-collapse: collapse;
                margin-top: 10px;
            }
            #detailTable th, #detailTable td {
                border: 1px solid #e6e6e6;
                padding: 8px 6px;
                text-align: center;
                word-break: break-all;
                font-size: 12px;
                line-height: 1.3;
            }
            #detailTable thead th {
                font-weight: bold;
                white-space: nowrap;
            }
            /* 第一行表头 - 基础信息 */
            #detailTable thead tr:first-child th {
                background-color: #e6f7ff;
                color: #1890ff;
                border-bottom: 1px solid #1890ff;
            }
            /* 第二行表头 - 计算信息 */
            #detailTable thead tr:last-child th {
                background-color: #fff2e8;
                color: #fa8c16;
                border-bottom: 1px solid #fa8c16;
            }
            /* 每条记录用两个 <tr>，不同记录间用背景色区分 */
            #detailTable tbody.record:nth-of-type(odd) td {
                background: #f8f8f8;
            }
            #detailTable tbody.record:nth-of-type(even) td {
                background: #fff;
            }
            /* 让第二行数据和第一行数据视觉上依然"连成一体" */
            #detailTable tbody.record tr:first-child td {
                border-bottom: none;
                border-left: 3px solid #1890ff;
            }
            #detailTable tbody.record tr:last-child td {
                border-top: none;
                border-left: 3px solid #fa8c16;
                border-bottom: 2px solid #d2d2d2;
            }
            .table-container {
                overflow-x: auto;
                max-height: 400px;
                overflow-y: auto;
            }
        `);
        $('head').append(style);
    }

    // 生成HTML - 关键：只有13列
    var html = '<div class="table-container"><table id="detailTable">';

    // 生成表头 - 两行，但都是13列
    html += '<thead>';
    // 第一行表头 - 基础信息标题
    html += '<tr>';
    headers.forEach(function(header) {
        html += '<th>' + header + '</th>';
    });
    html += '</tr>';
    // 第二行表头 - 计算信息标题
    html += '<tr>';
    secondRowHeaders.forEach(function(header) {
        html += '<th>' + header + '</th>';
    });
    html += '</tr>';
    html += '</thead>';

    // 生成数据行
    detailData.forEach(function(item, index) {
        // 处理数据
        var processedItem = {
            deliveryNoteNo: item.deliveryNoteNo || item.deliveryOrderNo || item.出货单No || "",
            deliveryNoteSeq: item.deliveryNoteSeq || item.deliveryOrderSeq || item.送货单序号 || "",
            productCategoryCode: item.productCategoryCode || item.品目C || "", // 条码字段
            productName: item.productCode || item.品名 || "", // 产品代码字段
            reelName: item.reelName || item.线盘 || "",
            customerProductCode: item.customerProductCode || headerData.customerCode || item.客户品目C || "", // 客户代码字段
            outboundDate: item.outboundDate || item.shipmentDate || item.出货日 || "",
            arrivalDate: item.arrivalDate ? (new Date(item.arrivalDate)).toISOString().split('T')[0] : '',
            customerOrderNo: item.customerOrderNo || item.客户订单No || "",
            detailRemark: item.detailRemark || item.remark || item.明细备注 || item.明细内部备注 || "",
            copperContractType: (function(){
                var type = item.copperContractType || item.铜合同类别 || "";
                switch(type) {
                    case '1': return '预约铜';
                    case '2': return '支给铜';
                    case '3': return '一般铜';
                    case '4': return '无偿';
                    default: return type;
                }
            })(),
            copperContractNo: item.copperContractNo || item.铜合同No || item.铜签约No || "",
            copperCondition: item.copperCondition || item.铜条件 || "",
            currency: item.currency || item.铜货币 || "RMB",
            conversionRate: item.conversionRate || item.换算率 || "1.0",
            quantity: item.quantity || item.数量 || "0",
            copperBase: item.copperBase || item.铜base || "0",
            premium: item.premium || item.升水 || "0",
            convertedCopperPrice: item.convertedCopperPrice || "0",
            zeroBase: item.zeroBase || item.零基数 || item.零基础 || "0",
            salesUnitPrice: item.salesUnitPrice || "0",
            salesAmount: item.salesAmount || "0",
            productMiddleCategory: item.productMiddleCategory || item.产品中分类 || "",
            shipmentPlanNo: item.shipmentPlanNo || item.出库计划番号 || item.出货计划番号 || ""
        };

        // 第一行数据（基础信息，13列）
        var firstRowData = [
            processedItem.deliveryNoteNo,
            processedItem.deliveryNoteSeq,
            processedItem.productCategoryCode,
            processedItem.productName,
            processedItem.reelName,
            processedItem.customerProductCode,
            processedItem.outboundDate,
            processedItem.arrivalDate,
            processedItem.customerOrderNo,
            processedItem.detailRemark,
            processedItem.copperContractType,
            processedItem.copperContractNo,
            processedItem.copperCondition
        ];

        // 第二行数据（计算信息，13列）
        var secondRowData = [
            processedItem.currency,
            processedItem.conversionRate,
            processedItem.quantity,
            processedItem.copperBase,
            processedItem.premium,
            processedItem.convertedCopperPrice,
            processedItem.zeroBase,
            processedItem.salesUnitPrice,
            processedItem.salesAmount,
            processedItem.productMiddleCategory,
            processedItem.shipmentPlanNo,
            '', // 空值，对应第一行的铜条件位置
            ''  // 空值，保持13列一致
        ];

        // 生成每条记录的两行 - 关键：每行都是13列
        html += '<tbody class="record">';
        // 第一行：基础信息数据
        html += '<tr>';
        firstRowData.forEach(function(data) {
            html += '<td>' + (data || '') + '</td>';
        });
        html += '</tr>';
        // 第二行：计算信息数据
        html += '<tr>';
        secondRowData.forEach(function(data) {
            html += '<td>' + (data || '') + '</td>';
        });
        html += '</tr>';
        html += '</tbody>';
    });

    html += '</table></div>';

    // 替换原来的表格
    $('#detailTable').parent().html(html);

    console.log('已生成两行表头的HTML表格');
}

// 获取客户详情信息（用于详情页面显示）
function getCustomerInfoForDetail(customerCode) {
    if (!customerCode) {
        return;
    }

    console.log('详情页面获取客户详情信息，客户代码:', customerCode);

    $.ajax({
        url: baselocation + '/order/salesLogin/getCustomerInfo',
        type: 'POST',
        data: {customerCode: customerCode},
        success: function(result) {
            console.log('详情页面获取客户详情信息结果:', result);
            if (result.code === 1 && result.data) {
                var customerInfo = result.data;
                console.log('详情页面客户详情信息:', customerInfo);

                // 设置交易条件、付款条件、运输条件、税率等字段的显示值
                if (customerInfo) {
                    // 交易条件：显示格式为 "代码 名称"
                    if (customerInfo.交易条件) {
                        var tradeConditionText = customerInfo.交易条件;
                        if (customerInfo.交易条件名) {
                            tradeConditionText += ' ' + customerInfo.交易条件名;
                        }
                        $('#tradeCondition').text(tradeConditionText);
                        console.log('详情页面设置交易条件:', tradeConditionText);
                    }

                    // 付款条件：显示格式为 "代码 名称"
                    if (customerInfo.付款条件) {
                        var payConditionText = customerInfo.付款条件;
                        if (customerInfo.付款条件名) {
                            payConditionText += ' ' + customerInfo.付款条件名;
                        }
                        $('#payCondition').text(payConditionText);
                        console.log('详情页面设置付款条件:', payConditionText);
                    }

                    // 运输条件：显示格式为 "代码 名称"
                    if (customerInfo.运输条件) {
                        var transportConditionText = customerInfo.运输条件;
                        if (customerInfo.运输条件名) {
                            transportConditionText += ' ' + customerInfo.运输条件名;
                        }
                        $('#transportCondition').text(transportConditionText);
                        console.log('详情页面设置运输条件:', transportConditionText);
                    }

                    // 根据税代码获取税率并计算税额
                    if (customerInfo.税代码) {
                        getTaxRateByTaxCodeForDetail(customerInfo.税代码);
                        console.log('详情页面根据税代码获取税率:', customerInfo.税代码);
                    } else {
                        // 如果没有税代码，设置税率为0并计算税额
                        $('#taxRate').text('0');
                        var totalAmount = parseFloat($('#totalAmount').text()) || 0;
                        calculateAndDisplayTaxAmount(totalAmount);
                    }
                }
            } else {
                console.log('详情页面获取客户详情信息失败:', result.message);
            }
        },
        error: function(xhr, status, error) {
            console.error('详情页面获取客户详情信息异常:', error);
        }
    });
}

// 关闭弹窗
function closeLayer() {
    var index = parent.layer.getFrameIndex(window.name);
    parent.layer.close(index);
}

// 格式化显示条件字段（显示为"代码 名称"格式）
function formatAndDisplayConditionFields(tradeCondition, payCondition, transportCondition) {
    // 加载条件数据并格式化显示
    loadConditionDataAndFormat(tradeCondition, payCondition, transportCondition);
}

// 加载条件数据并格式化显示
function loadConditionDataAndFormat(tradeCondition, payCondition, transportCondition) {
    var conditionsLoaded = 0;
    var totalConditions = 3;
    var conditionData = {
        trade: {},
        pay: {},
        transport: {}
    };

    // 加载交易条件数据
    $.ajax({
        url: baselocation + '/order/salesLogin/getTradeConditions',
        type: 'POST',
        success: function(result) {
            if (result.code === 1 && result.data) {
                result.data.forEach(function(item) {
                    conditionData.trade[item.交易条件] = item.交易条件名;
                });
            }
            conditionsLoaded++;
            if (conditionsLoaded === totalConditions) {
                displayFormattedConditions(tradeCondition, payCondition, transportCondition, conditionData);
            }
        },
        error: function() {
            conditionsLoaded++;
            if (conditionsLoaded === totalConditions) {
                displayFormattedConditions(tradeCondition, payCondition, transportCondition, conditionData);
            }
        }
    });

    // 加载付款条件数据
    $.ajax({
        url: baselocation + '/order/salesLogin/getPayConditions',
        type: 'POST',
        success: function(result) {
            if (result.code === 1 && result.data) {
                result.data.forEach(function(item) {
                    conditionData.pay[item.付款条件] = item.付款条件名;
                });
            }
            conditionsLoaded++;
            if (conditionsLoaded === totalConditions) {
                displayFormattedConditions(tradeCondition, payCondition, transportCondition, conditionData);
            }
        },
        error: function() {
            conditionsLoaded++;
            if (conditionsLoaded === totalConditions) {
                displayFormattedConditions(tradeCondition, payCondition, transportCondition, conditionData);
            }
        }
    });

    // 加载运输条件数据
    $.ajax({
        url: baselocation + '/order/salesLogin/getTransportConditions',
        type: 'POST',
        success: function(result) {
            if (result.code === 1 && result.data) {
                result.data.forEach(function(item) {
                    conditionData.transport[item.运输条件] = item.运输条件名;
                });
            }
            conditionsLoaded++;
            if (conditionsLoaded === totalConditions) {
                displayFormattedConditions(tradeCondition, payCondition, transportCondition, conditionData);
            }
        },
        error: function() {
            conditionsLoaded++;
            if (conditionsLoaded === totalConditions) {
                displayFormattedConditions(tradeCondition, payCondition, transportCondition, conditionData);
            }
        }
    });
}

// 显示格式化的条件字段（销售额详情页面使用text方法）
function displayFormattedConditions(tradeCondition, payCondition, transportCondition, conditionData) {
    // 格式化交易条件
    var tradeConditionText = '';
    if (tradeCondition && conditionData.trade[tradeCondition]) {
        tradeConditionText = tradeCondition + ' ' + conditionData.trade[tradeCondition];
    } else if (tradeCondition) {
        tradeConditionText = tradeCondition;
    }
    $('#tradeCondition').text(tradeConditionText);

    // 格式化付款条件
    var payConditionText = '';
    if (payCondition && conditionData.pay[payCondition]) {
        payConditionText = payCondition + ' ' + conditionData.pay[payCondition];
    } else if (payCondition) {
        payConditionText = payCondition;
    }
    $('#payCondition').text(payConditionText);

    // 格式化运输条件
    var transportConditionText = '';
    if (transportCondition && conditionData.transport[transportCondition]) {
        transportConditionText = transportCondition + ' ' + conditionData.transport[transportCondition];
    } else if (transportCondition) {
        transportConditionText = transportCondition;
    }
    $('#transportCondition').text(transportConditionText);

    console.log('销售额详情页面格式化显示 - 交易条件:', tradeConditionText, '付款条件:', payConditionText, '运输条件:', transportConditionText);
}

// 根据税代码获取税率（详情页面专用）
function getTaxRateByTaxCodeForDetail(taxCode) {
    if (!taxCode) {
        return;
    }

    $.ajax({
        url: baselocation + '/salesCommon/taxRateCodeFun/json',
        type: 'POST',
        data: {taxRateCode: taxCode},
        success: function(result) {
            console.log('详情页面获取税率结果:', result);
            if (result.code === 1 && result.data && result.data.taxRate) {
                $('#taxRate').text(result.data.taxRate);
                console.log('详情页面设置税率:', result.data.taxRate);
            } else {
                console.log('详情页面未找到对应的税率数据');
                // 如果找不到税率，显示税代码作为兼容
                $('#taxRate').text(taxCode);
            }
        },
        error: function(xhr, status, error) {
            console.error('详情页面获取税率失败:', error);
            // 如果请求失败，显示税代码作为兼容
            $('#taxRate').text(taxCode);
        }
    });
}

function closeLayer() {
    var index = parent.layer.getFrameIndex(window.name);
    parent.layer.close(index);
    parent.location.reload();
}