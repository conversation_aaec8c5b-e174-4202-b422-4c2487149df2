package com.hongru.common.util;

import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.util.List;

import org.apache.poi.hssf.usermodel.*;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.util.IOUtils;

import com.hongru.pojo.dto.PaymentRequestExportDTO;
import com.hongru.pojo.dto.SalesAmountExportDTO;

/**
 * 业务Excel导出工具类
 * 专门用于支付请求和销售额的Excel导出，完全按照PDF模板格式
 */
public class BusinessExcelExportUtil {

    /**
     * 导出支付请求书Excel - 完全按照PDF模板格式
     * 
     * @param out         输出流
     * @param exportData  导出数据
     * @param companyInfo 公司信息
     * @throws IOException
     */
    public static void exportPaymentRequestExcel(OutputStream out, List<PaymentRequestExportDTO> exportData,
            String companyInfo) throws IOException {

        HSSFWorkbook workbook = new HSSFWorkbook();
        HSSFSheet sheet = workbook.createSheet("支付请求书");

        // 设置列宽 - 根据PDF模板精确调整
        sheet.setColumnWidth(0, 2800); // 出货日
        sheet.setColumnWidth(1, 2800); // 到货日
        sheet.setColumnWidth(2, 3500); // 型号、尺寸
        sheet.setColumnWidth(3, 4500); // 客户品目号
        sheet.setColumnWidth(4, 4500); // 客户单号
        sheet.setColumnWidth(5, 3000); // 数量(kg)
        sheet.setColumnWidth(6, 3500); // 单价(RMB/kg)
        sheet.setColumnWidth(7, 3500); // 金额(RMB)
        sheet.setColumnWidth(8, 3000); // 税(RMB)
        sheet.setColumnWidth(9, 3500); // 调单价

        // 创建样式
        HSSFCellStyle headerStyle = createHeaderStyle(workbook);
        HSSFCellStyle dataStyle = createDataStyle(workbook);
        HSSFCellStyle numberStyle = createNumberStyle(workbook);
        HSSFCellStyle titleStyle = createTitleStyle(workbook);
        HSSFCellStyle companyInfoStyle = createCompanyInfoStyle(workbook);
        HSSFCellStyle borderStyle = createBorderStyle(workbook);
        HSSFCellStyle noBorderStyle = createNoBorderStyle(workbook);

        int rowNum = 0;

        // 第一行：Logo和公司信息
        HSSFRow logoRow = sheet.createRow(rowNum++);
        logoRow.setHeight((short) 800); // 设置行高

        // 添加Logo（如果存在）
        try {
            addCompanyLogo(workbook, sheet, 0, 0);
        } catch (Exception e) {
            // Logo加载失败时继续执行
            System.out.println("Logo加载失败: " + e.getMessage());
        }

        // 公司名称 - 住友电工运营(无锡)有限公司
        HSSFCell companyNameCell = logoRow.createCell(1);
        companyNameCell.setCellValue("住友电工运营(无锡)有限公司");
        companyNameCell.setCellStyle(companyInfoStyle);
        sheet.addMergedRegion(new CellRangeAddress(0, 0, 1, 6));

        // 页码：1/1
        HSSFCell pageCell = logoRow.createCell(9);
        pageCell.setCellValue("页码：1/1");
        pageCell.setCellStyle(dataStyle);

        // 第二行：地址和邮编
        HSSFRow addressRow = sheet.createRow(rowNum++);
        HSSFCell addressCell = addressRow.createCell(0);
        addressCell.setCellValue("地址：无锡市新吴区工业园区创新路3号");
        addressCell.setCellStyle(dataStyle);
        sheet.addMergedRegion(new CellRangeAddress(1, 1, 0, 4));

        HSSFCell zipCell = addressRow.createCell(5);
        zipCell.setCellValue("邮编：214028");
        zipCell.setCellStyle(dataStyle);
        sheet.addMergedRegion(new CellRangeAddress(1, 1, 5, 9));

        // 第三行：电话和传真（传真在邮编下方）
        HSSFRow phoneRow = sheet.createRow(rowNum++);
        HSSFCell phoneCell = phoneRow.createCell(0);
        phoneCell.setCellValue("电话：0510-85280011");
        phoneCell.setCellStyle(dataStyle);
        sheet.addMergedRegion(new CellRangeAddress(2, 2, 0, 4));

        HSSFCell faxCell = phoneRow.createCell(5);
        faxCell.setCellValue("传真：0510-85280022");
        faxCell.setCellStyle(dataStyle);
        sheet.addMergedRegion(new CellRangeAddress(2, 2, 5, 9));

        // 空行
        rowNum++;

        // 第五行：签约方
        HSSFRow contractorRow = sheet.createRow(rowNum++);
        HSSFCell contractorCell = contractorRow.createCell(0);
        contractorCell.setCellValue("签约方：编码国际运营工厂船舶运营有限公司");
        contractorCell.setCellStyle(dataStyle);
        sheet.addMergedRegion(new CellRangeAddress(4, 4, 0, 9));

        // 第六行：需求方
        HSSFRow demandRow = sheet.createRow(rowNum++);
        HSSFCell demandCell = demandRow.createCell(0);
        demandCell.setCellValue("需求方：编码国际运营工厂船舶运营有限公司");
        demandCell.setCellStyle(dataStyle);
        sheet.addMergedRegion(new CellRangeAddress(5, 5, 0, 4));

        // 标题在右侧
        HSSFCell titleCell = demandRow.createCell(5);
        titleCell.setCellValue("支付请求书");
        titleCell.setCellStyle(titleStyle);
        sheet.addMergedRegion(new CellRangeAddress(5, 5, 5, 9));

        // 空行
        rowNum++;

        // 创建表头 - 完全按照PDF模板
        HSSFRow headerRow = sheet.createRow(rowNum++);
        String[] headers = { "出货日", "到货日", "型号、尺寸", "客户品目号", "客户单号", "数量(kg)", "单价(RMB/kg)", "金额(RMB)", "税(RMB)",
                "调单价" };
        for (int i = 0; i < headers.length; i++) {
            HSSFCell cell = headerRow.createCell(i);
            cell.setCellValue(headers[i]);
            cell.setCellStyle(headerStyle);
        }

        // 填充数据
        BigDecimal totalAmount = BigDecimal.ZERO;
        BigDecimal totalTax = BigDecimal.ZERO;
        BigDecimal totalWithTax = BigDecimal.ZERO;

        for (PaymentRequestExportDTO data : exportData) {
            HSSFRow dataRow = sheet.createRow(rowNum++);

            // 出货日
            HSSFCell cell0 = dataRow.createCell(0);
            cell0.setCellValue(data.getShipmentDate() != null ? data.getShipmentDate() : "");
            cell0.setCellStyle(borderStyle);

            // 到货日
            HSSFCell cell1 = dataRow.createCell(1);
            cell1.setCellValue(data.getArrivalDate() != null ? data.getArrivalDate() : "");
            cell1.setCellStyle(borderStyle);

            // 型号、尺寸
            HSSFCell cell2 = dataRow.createCell(2);
            cell2.setCellValue(data.getProductCode() != null ? data.getProductCode() : "");
            cell2.setCellStyle(borderStyle);

            // 客户品目号
            HSSFCell cell3 = dataRow.createCell(3);
            cell3.setCellValue(data.getCustomerProductCode() != null ? data.getCustomerProductCode() : "");
            cell3.setCellStyle(borderStyle);

            // 客户单号
            HSSFCell cell4 = dataRow.createCell(4);
            cell4.setCellValue(data.getCustomerOrderNo() != null ? data.getCustomerOrderNo() : "");
            cell4.setCellStyle(borderStyle);

            // 数量(kg)
            HSSFCell cell5 = dataRow.createCell(5);
            if (data.getQuantity() != null) {
                cell5.setCellValue(data.getQuantity().doubleValue());
            } else {
                cell5.setCellValue("");
            }
            cell5.setCellStyle(numberStyle);

            // 单价(RMB/kg)
            HSSFCell cell6 = dataRow.createCell(6);
            if (data.getUnitPrice() != null) {
                cell6.setCellValue(data.getUnitPrice().doubleValue());
            } else {
                cell6.setCellValue("");
            }
            cell6.setCellStyle(numberStyle);

            // 金额(RMB)
            HSSFCell cell7 = dataRow.createCell(7);
            if (data.getAmount() != null) {
                cell7.setCellValue(data.getAmount().doubleValue());
                totalAmount = totalAmount.add(data.getAmount());
            } else {
                cell7.setCellValue("");
            }
            cell7.setCellStyle(numberStyle);

            // 税(RMB)
            HSSFCell cell8 = dataRow.createCell(8);
            if (data.getTax() != null) {
                cell8.setCellValue(data.getTax().doubleValue());
                totalTax = totalTax.add(data.getTax());
            } else {
                cell8.setCellValue("");
            }
            cell8.setCellStyle(numberStyle);

            // 调单价
            HSSFCell cell9 = dataRow.createCell(9);
            if (data.getTotalPriceWithTax() != null) {
                cell9.setCellValue(data.getTotalPriceWithTax().doubleValue());
                totalWithTax = totalWithTax.add(data.getTotalPriceWithTax());
            } else {
                cell9.setCellValue("");
            }
            cell9.setCellStyle(numberStyle);
        }

        // 合计行
        HSSFRow totalRow = sheet.createRow(rowNum++);
        HSSFCell totalLabelCell = totalRow.createCell(0);
        totalLabelCell.setCellValue("合计:");
        totalLabelCell.setCellStyle(headerStyle);
        sheet.addMergedRegion(new CellRangeAddress(rowNum - 1, rowNum - 1, 0, 6));

        HSSFCell totalAmountCell = totalRow.createCell(7);
        totalAmountCell.setCellValue(totalAmount.doubleValue());
        totalAmountCell.setCellStyle(numberStyle);

        HSSFCell totalTaxCell = totalRow.createCell(8);
        totalTaxCell.setCellValue(totalTax.doubleValue());
        totalTaxCell.setCellStyle(numberStyle);

        HSSFCell totalWithTaxCell = totalRow.createCell(9);
        totalWithTaxCell.setCellValue(totalWithTax.doubleValue());
        totalWithTaxCell.setCellStyle(numberStyle);

        // 空行
        rowNum++;

        // 备注区域 - 左侧大框
        HSSFRow remarkRow1 = sheet.createRow(rowNum++);
        HSSFCell remarkCell = remarkRow1.createCell(0);
        remarkCell.setCellValue("备注:");
        remarkCell.setCellStyle(borderStyle);

        // 备注内容区域 - 多行空白区域
        for (int i = 0; i < 4; i++) {
            HSSFRow remarkContentRow = sheet.createRow(rowNum++);
            for (int j = 0; j < 7; j++) {
                HSSFCell cell = remarkContentRow.createCell(j);
                cell.setCellValue("");
                cell.setCellStyle(borderStyle);
            }
        }

        // 合并备注区域 (A-G列)
        sheet.addMergedRegion(new CellRangeAddress(rowNum - 5, rowNum - 1, 0, 6));

        // 右侧签字区域 - 整体合并为一个大单元格
        int signRowStart = rowNum - 5;
        int signRowEnd = rowNum + 1; // 包含作成日行

        // 创建右侧整体区域的所有单元格
        for (int i = signRowStart; i <= signRowEnd; i++) {
            HSSFRow row = sheet.getRow(i);
            if (row == null) {
                row = sheet.createRow(i);
            }
            // H和I列有边框，J列无边框
            for (int j = 7; j <= 9; j++) {
                HSSFCell cell = row.createCell(j);
                cell.setCellValue("");
                if (j < 9) { // H和I列有边框
                    cell.setCellStyle(borderStyle);
                } else { // J列无边框
                    cell.setCellStyle(noBorderStyle);
                }
            }
        }

        // 合并整个右侧区域 (H-I列，不包括J列)
        sheet.addMergedRegion(new CellRangeAddress(signRowStart, signRowEnd, 7, 8));

        // 在合并区域内添加内容
        HSSFRow paymentDateRow = sheet.getRow(signRowStart);
        HSSFCell paymentDateCell = paymentDateRow.getCell(7);
        paymentDateCell.setCellValue("付款请求日：2023年07月30日\n\n确认          作成\n\n\n\n\n作成日：2023年07月10日");
        paymentDateCell.setCellStyle(borderStyle);

        // 写入输出流
        workbook.write(out);
        workbook.close();
    }

    /**
     * 导出销售额Excel
     *
     * @param out         输出流
     * @param exportData  导出数据
     * @param companyInfo 公司信息
     * @throws IOException
     */
    public static void exportSalesAmountExcel(OutputStream out, List<SalesAmountExportDTO> exportData,
            String companyInfo) throws IOException {

        HSSFWorkbook workbook = new HSSFWorkbook();
        HSSFSheet sheet = workbook.createSheet("销售额登录");

        // 设置列宽
        sheet.setColumnWidth(0, 2800); // 出货日
        sheet.setColumnWidth(1, 2800); // 到货日
        sheet.setColumnWidth(2, 3500); // 型号、尺寸
        sheet.setColumnWidth(3, 4500); // 客户品目号
        sheet.setColumnWidth(4, 4500); // 客户单号
        sheet.setColumnWidth(5, 3000); // 数量(kg)
        sheet.setColumnWidth(6, 3500); // 单价(RMB/kg)
        sheet.setColumnWidth(7, 3500); // 金额(RMB)
        sheet.setColumnWidth(8, 3000); // 税(RMB)
        sheet.setColumnWidth(9, 3000); // 财年
        sheet.setColumnWidth(10, 3500); // 总成本

        // 创建样式
        HSSFCellStyle headerStyle = createHeaderStyle(workbook);
        HSSFCellStyle dataStyle = createDataStyle(workbook);
        HSSFCellStyle numberStyle = createNumberStyle(workbook);
        HSSFCellStyle titleStyle = createTitleStyle(workbook);
        HSSFCellStyle companyInfoStyle = createCompanyInfoStyle(workbook);
        HSSFCellStyle borderStyle = createBorderStyle(workbook);
        HSSFCellStyle noBorderStyle = createNoBorderStyle(workbook);

        int rowNum = 0;

        // 第一行：Logo和公司信息
        HSSFRow logoRow = sheet.createRow(rowNum++);
        logoRow.setHeight((short) 800);

        // 添加Logo
        try {
            addCompanyLogo(workbook, sheet, 0, 0);
        } catch (Exception e) {
            System.out.println("Logo加载失败: " + e.getMessage());
        }

        // 公司名称
        HSSFCell companyNameCell = logoRow.createCell(1);
        companyNameCell.setCellValue("住友电工运营(无锡)有限公司");
        companyNameCell.setCellStyle(companyInfoStyle);
        sheet.addMergedRegion(new CellRangeAddress(0, 0, 1, 7));

        // 页码
        HSSFCell pageCell = logoRow.createCell(10);
        pageCell.setCellValue("页码：1/1");
        pageCell.setCellStyle(dataStyle);

        // 第二行：地址信息
        HSSFRow addressRow = sheet.createRow(rowNum++);
        HSSFCell addressCell = addressRow.createCell(0);
        addressCell.setCellValue("地址：无锡市新吴区工业园区创新路3号");
        addressCell.setCellStyle(dataStyle);
        sheet.addMergedRegion(new CellRangeAddress(1, 1, 0, 5));

        HSSFCell phoneCell = addressRow.createCell(6);
        phoneCell.setCellValue("电话：21-1028");
        phoneCell.setCellStyle(dataStyle);
        sheet.addMergedRegion(new CellRangeAddress(1, 1, 6, 8));

        // 第三行：传真信息
        HSSFRow faxRow = sheet.createRow(rowNum++);
        HSSFCell faxCell = faxRow.createCell(0);
        faxCell.setCellValue("传真：0510-85280011  传真：0510-85280022");
        faxCell.setCellStyle(dataStyle);
        sheet.addMergedRegion(new CellRangeAddress(2, 2, 0, 10));

        // 空行
        rowNum++;

        // 第五行：签约方
        HSSFRow contractorRow = sheet.createRow(rowNum++);
        HSSFCell contractorCell = contractorRow.createCell(0);
        contractorCell.setCellValue("签约方：编码国际运营工厂船舶运营有限公司");
        contractorCell.setCellStyle(dataStyle);
        sheet.addMergedRegion(new CellRangeAddress(4, 4, 0, 10));

        // 第六行：需求方
        HSSFRow demandRow = sheet.createRow(rowNum++);
        HSSFCell demandCell = demandRow.createCell(0);
        demandCell.setCellValue("需求方：编码国际运营工厂船舶运营有限公司");
        demandCell.setCellStyle(dataStyle);
        sheet.addMergedRegion(new CellRangeAddress(5, 5, 0, 5));

        // 标题在右侧
        HSSFCell titleCell = demandRow.createCell(6);
        titleCell.setCellValue("销售额登录");
        titleCell.setCellStyle(titleStyle);
        sheet.addMergedRegion(new CellRangeAddress(5, 5, 6, 10));

        // 空行
        rowNum++;

        // 创建表头
        HSSFRow headerRow = sheet.createRow(rowNum++);
        String[] headers = { "出货日", "到货日", "型号、尺寸", "客户品目号", "客户单号", "数量(kg)", "单价(RMB/kg)", "金额(RMB)", "税(RMB)", "财年",
                "总成本" };
        for (int i = 0; i < headers.length; i++) {
            HSSFCell cell = headerRow.createCell(i);
            cell.setCellValue(headers[i]);
            cell.setCellStyle(headerStyle);
        }

        // 填充数据
        BigDecimal totalAmount = BigDecimal.ZERO;
        BigDecimal totalTax = BigDecimal.ZERO;
        BigDecimal totalCost = BigDecimal.ZERO;

        for (SalesAmountExportDTO data : exportData) {
            HSSFRow dataRow = sheet.createRow(rowNum++);

            // 出货日
            HSSFCell cell0 = dataRow.createCell(0);
            cell0.setCellValue(data.getShipmentDate() != null ? data.getShipmentDate() : "");
            cell0.setCellStyle(borderStyle);

            // 到货日
            HSSFCell cell1 = dataRow.createCell(1);
            cell1.setCellValue(data.getArrivalDate() != null ? data.getArrivalDate() : "");
            cell1.setCellStyle(borderStyle);

            // 型号、尺寸
            HSSFCell cell2 = dataRow.createCell(2);
            cell2.setCellValue(data.getProductCode() != null ? data.getProductCode() : "");
            cell2.setCellStyle(borderStyle);

            // 客户品目号
            HSSFCell cell3 = dataRow.createCell(3);
            cell3.setCellValue(data.getCustomerProductCode() != null ? data.getCustomerProductCode() : "");
            cell3.setCellStyle(borderStyle);

            // 客户单号
            HSSFCell cell4 = dataRow.createCell(4);
            cell4.setCellValue(data.getCustomerOrderNo() != null ? data.getCustomerOrderNo() : "");
            cell4.setCellStyle(borderStyle);

            // 数量(kg)
            HSSFCell cell5 = dataRow.createCell(5);
            if (data.getQuantity() != null) {
                cell5.setCellValue(data.getQuantity().doubleValue());
            } else {
                cell5.setCellValue("");
            }
            cell5.setCellStyle(numberStyle);

            // 单价(RMB/kg)
            HSSFCell cell6 = dataRow.createCell(6);
            if (data.getUnitPrice() != null) {
                cell6.setCellValue(data.getUnitPrice().doubleValue());
            } else {
                cell6.setCellValue("");
            }
            cell6.setCellStyle(numberStyle);

            // 金额(RMB)
            HSSFCell cell7 = dataRow.createCell(7);
            if (data.getAmount() != null) {
                cell7.setCellValue(data.getAmount().doubleValue());
                totalAmount = totalAmount.add(data.getAmount());
            } else {
                cell7.setCellValue("");
            }
            cell7.setCellStyle(numberStyle);

            // 税(RMB)
            HSSFCell cell8 = dataRow.createCell(8);
            if (data.getTax() != null) {
                cell8.setCellValue(data.getTax().doubleValue());
                totalTax = totalTax.add(data.getTax());
            } else {
                cell8.setCellValue("");
            }
            cell8.setCellStyle(numberStyle);

            // 财年
            HSSFCell cell9 = dataRow.createCell(9);
            cell9.setCellValue(data.getFiscalYear() != null ? data.getFiscalYear() : "");
            cell9.setCellStyle(borderStyle);

            // 总成本
            HSSFCell cell10 = dataRow.createCell(10);
            if (data.getTotalCost() != null) {
                cell10.setCellValue(data.getTotalCost().doubleValue());
                totalCost = totalCost.add(data.getTotalCost());
            } else {
                cell10.setCellValue("");
            }
            cell10.setCellStyle(numberStyle);
        }

        // 合计行
        HSSFRow totalRow = sheet.createRow(rowNum++);
        HSSFCell totalLabelCell = totalRow.createCell(0);
        totalLabelCell.setCellValue("合计:");
        totalLabelCell.setCellStyle(headerStyle);
        sheet.addMergedRegion(new CellRangeAddress(rowNum - 1, rowNum - 1, 0, 6));

        HSSFCell totalAmountCell = totalRow.createCell(7);
        totalAmountCell.setCellValue(totalAmount.doubleValue());
        totalAmountCell.setCellStyle(numberStyle);

        HSSFCell totalTaxCell = totalRow.createCell(8);
        totalTaxCell.setCellValue(totalTax.doubleValue());
        totalTaxCell.setCellStyle(numberStyle);

        // 跳过财年列

        HSSFCell totalCostCell = totalRow.createCell(10);
        totalCostCell.setCellValue(totalCost.doubleValue());
        totalCostCell.setCellStyle(numberStyle);

        // 空行
        rowNum++;

        // 备注区域 - 左侧大框
        HSSFRow remarkRow1 = sheet.createRow(rowNum++);
        HSSFCell remarkCell = remarkRow1.createCell(0);
        remarkCell.setCellValue("备注:");
        remarkCell.setCellStyle(borderStyle);

        // 备注内容区域 - 多行空白区域
        for (int i = 0; i < 4; i++) {
            HSSFRow remarkContentRow = sheet.createRow(rowNum++);
            for (int j = 0; j < 8; j++) {
                HSSFCell cell = remarkContentRow.createCell(j);
                cell.setCellValue("");
                cell.setCellStyle(borderStyle);
            }
        }

        // 合并备注区域 (A-G列)
        sheet.addMergedRegion(new CellRangeAddress(rowNum - 5, rowNum - 1, 0, 6));

        // 右侧签字区域 - 整体合并为一个大单元格
        int signRowStart = rowNum - 5;
        int signRowEnd = rowNum + 1; // 包含作成日行

        // 创建右侧整体区域的所有单元格
        for (int i = signRowStart; i <= signRowEnd; i++) {
            HSSFRow row = sheet.getRow(i);
            if (row == null) {
                row = sheet.createRow(i);
            }
            // I和J列有边框，K列无边框
            for (int j = 8; j <= 10; j++) {
                HSSFCell cell = row.createCell(j);
                cell.setCellValue("");
                if (j < 10) { // I和J列有边框
                    cell.setCellStyle(borderStyle);
                } else { // K列无边框
                    cell.setCellStyle(noBorderStyle);
                }
            }
        }

        // 合并整个右侧区域 (I-J列，不包括K列)
        sheet.addMergedRegion(new CellRangeAddress(signRowStart, signRowEnd, 8, 9));

        // 在合并区域内添加内容
        HSSFRow salesDateRow = sheet.getRow(signRowStart);
        HSSFCell salesDateCell = salesDateRow.getCell(8);
        salesDateCell.setCellValue("销售额登录日：2023年07月30日\n\n确认          作成\n\n\n\n\n作成日：2023年07月10日");
        salesDateCell.setCellStyle(borderStyle);

        // 写入输出流
        workbook.write(out);
        workbook.close();
    }

    /**
     * 添加公司Logo
     */
    private static void addCompanyLogo(HSSFWorkbook workbook, HSSFSheet sheet, int row, int col) throws IOException {
        // 尝试加载Logo文件
        InputStream logoStream = null;

        try {
            // 尝试多个可能的Logo路径
            String[] logoPaths = {
                    "static/hongru/ico/favicon.png",
                    "/static/hongru/ico/favicon.png",
                    "prjback/WebContent/static/hongru/ico/favicon.png",
                    "WebContent/static/hongru/ico/favicon.png",
                    "static/images/logo.png",
                    "images/logo.png",
                    "logo.png"
            };

            for (String logoPath : logoPaths) {
                try {
                    // 尝试从类路径加载
                    logoStream = BusinessExcelExportUtil.class.getClassLoader().getResourceAsStream(logoPath);
                    if (logoStream == null) {
                        // 尝试从文件系统加载
                        logoStream = new FileInputStream(logoPath);
                    }
                    if (logoStream != null) {
                        break;
                    }
                } catch (Exception e) {
                    // 继续尝试下一个路径
                }
            }

            if (logoStream != null) {
                byte[] logoBytes = IOUtils.toByteArray(logoStream);
                int pictureIdx = workbook.addPicture(logoBytes, HSSFWorkbook.PICTURE_TYPE_PNG);

                HSSFPatriarch patriarch = sheet.createDrawingPatriarch();
                // 调整Logo锚点，使其更加可见
                HSSFClientAnchor anchor = new HSSFClientAnchor(0, 0, 1023, 255, (short) col, row, (short) (col + 1),
                        row + 1);
                anchor.setAnchorType(ClientAnchor.AnchorType.MOVE_AND_RESIZE);
                HSSFPicture picture = patriarch.createPicture(anchor, pictureIdx);
                picture.resize(0.8); // 调整大小为80%

                System.out.println("Logo加载成功");
            } else {
                System.out.println("Logo文件未找到，跳过Logo显示");
            }
        } catch (Exception e) {
            System.out.println("Logo加载失败: " + e.getMessage());
        } finally {
            if (logoStream != null) {
                try {
                    logoStream.close();
                } catch (IOException e) {
                    // 忽略关闭异常
                }
            }
        }
    }

    /**
     * 创建标题样式
     */
    private static HSSFCellStyle createTitleStyle(HSSFWorkbook workbook) {
        HSSFCellStyle style = workbook.createCellStyle();
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);

        HSSFFont font = workbook.createFont();
        font.setFontName("微软雅黑");
        font.setFontHeightInPoints((short) 16);
        font.setBold(true);
        style.setFont(font);

        return style;
    }

    /**
     * 创建公司信息样式
     */
    private static HSSFCellStyle createCompanyInfoStyle(HSSFWorkbook workbook) {
        HSSFCellStyle style = workbook.createCellStyle();
        style.setAlignment(HorizontalAlignment.LEFT);
        style.setVerticalAlignment(VerticalAlignment.CENTER);

        HSSFFont font = workbook.createFont();
        font.setFontName("微软雅黑");
        font.setFontHeightInPoints((short) 14);
        font.setBold(true);
        style.setFont(font);

        return style;
    }

    /**
     * 创建表头样式
     */
    private static HSSFCellStyle createHeaderStyle(HSSFWorkbook workbook) {
        HSSFCellStyle style = workbook.createCellStyle();
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);

        // 设置边框
        style.setBorderTop(BorderStyle.THIN);
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);

        HSSFFont font = workbook.createFont();
        font.setFontName("微软雅黑");
        font.setFontHeightInPoints((short) 12);
        font.setBold(true);
        style.setFont(font);

        return style;
    }

    /**
     * 创建数据样式
     */
    private static HSSFCellStyle createDataStyle(HSSFWorkbook workbook) {
        HSSFCellStyle style = workbook.createCellStyle();
        style.setAlignment(HorizontalAlignment.LEFT);
        style.setVerticalAlignment(VerticalAlignment.CENTER);

        HSSFFont font = workbook.createFont();
        font.setFontName("微软雅黑");
        font.setFontHeightInPoints((short) 10);
        style.setFont(font);

        return style;
    }

    /**
     * 创建数字样式
     */
    private static HSSFCellStyle createNumberStyle(HSSFWorkbook workbook) {
        HSSFCellStyle style = workbook.createCellStyle();
        style.setAlignment(HorizontalAlignment.RIGHT);
        style.setVerticalAlignment(VerticalAlignment.CENTER);

        // 设置边框
        style.setBorderTop(BorderStyle.THIN);
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);

        HSSFFont font = workbook.createFont();
        font.setFontName("微软雅黑");
        font.setFontHeightInPoints((short) 10);
        style.setFont(font);

        return style;
    }

    /**
     * 创建边框样式
     */
    private static HSSFCellStyle createBorderStyle(HSSFWorkbook workbook) {
        HSSFCellStyle style = workbook.createCellStyle();
        style.setAlignment(HorizontalAlignment.LEFT);
        style.setVerticalAlignment(VerticalAlignment.CENTER);

        // 设置边框
        style.setBorderTop(BorderStyle.THIN);
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);

        HSSFFont font = workbook.createFont();
        font.setFontName("微软雅黑");
        font.setFontHeightInPoints((short) 10);
        style.setFont(font);

        return style;
    }

    /**
     * 创建无边框样式
     */
    private static HSSFCellStyle createNoBorderStyle(HSSFWorkbook workbook) {
        HSSFCellStyle style = workbook.createCellStyle();
        style.setAlignment(HorizontalAlignment.LEFT);
        style.setVerticalAlignment(VerticalAlignment.CENTER);

        // 设置字体
        HSSFFont font = workbook.createFont();
        font.setFontName("微软雅黑");
        font.setFontHeightInPoints((short) 10);
        style.setFont(font);

        // 不设置边框，保持默认无边框状态
        return style;
    }
}
