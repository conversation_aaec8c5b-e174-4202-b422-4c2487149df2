package com.hongru.entity.businessOps;

import com.baomidou.mybatisplus.annotations.TableField;
import com.baomidou.mybatisplus.annotations.TableId;
import com.baomidou.mybatisplus.annotations.TableName;
import com.baomidou.mybatisplus.enums.IdType;


@TableName("客户详情表")//CostPrice
public class CustomerDetails {
	/* 状态-正常 */
	public static final String STATE_NORMAL = "0";
	/* 状态-删除 */
	public static final String STATE_DELETED = "9";
	
	/* 流水号 */
	@TableId(value="流水号", type= IdType.AUTO)
	protected int id;
	/* 状态 */
	protected String status;
	/* 客户代码 */
	protected String customerCode;
	/* 客户类别代码 */
	protected String customerTypeCode;
	/* 客户组代码 */
	protected String customerGroupCode;
	/* 分野代码 */
	protected String fieldCode;
	/* 国家 */
	protected String country;
	/* 货币 */
	protected String currency;
	/* 税率代码 */
	protected String taxRateCode;
	/* 交易条件 */
	protected String tradeCondition;
	/* 付款条件 */
	protected String payCondition;
	/* 运输条件 */
	protected String transportCondition;
	/* 创建人姓名 */
	protected String creatorName;
	/* 创建时间 */
	protected String createdTime;
	/* 更新人姓名 */
	protected String updaterName;
	/* 更新时间 */
	protected String updatedTime;
	
	/* 客户简称 */
	@TableField(exist = false)
	protected String customerAlias;
	
	/* 客户类别名 */
	protected String customerCategory;
	
	public int getId() {
		return id;
	}
	public void setId(int id) {
		this.id = id;
	}
	public String getStatus() {
		return status;
	}
	public void setStatus(String status) {
		this.status = status;
	}
	public String getCustomerCode() {
		return customerCode;
	}
	public void setCustomerCode(String customerCode) {
		this.customerCode = customerCode;
	}
	public String getCustomerTypeCode() {
		return customerTypeCode;
	}
	public void setCustomerTypeCode(String customerTypeCode) {
		this.customerTypeCode = customerTypeCode;
	}
	public String getCustomerGroupCode() {
		return customerGroupCode;
	}
	public void setCustomerGroupCode(String customerGroupCode) {
		this.customerGroupCode = customerGroupCode;
	}
	public String getFieldCode() {
		return fieldCode;
	}
	public void setFieldCode(String fieldCode) {
		this.fieldCode = fieldCode;
	}
	public String getCountry() {
		return country;
	}
	public void setCountry(String country) {
		this.country = country;
	}
	public String getCurrency() {
		return currency;
	}
	public void setCurrency(String currency) {
		this.currency = currency;
	}
	public String getTaxRateCode() {
		return taxRateCode;
	}
	public void setTaxRateCode(String taxRateCode) {
		this.taxRateCode = taxRateCode;
	}
	public String getTradeCondition() {
		return tradeCondition;
	}
	public void setTradeCondition(String tradeCondition) {
		this.tradeCondition = tradeCondition;
	}
	public String getPayCondition() {
		return payCondition;
	}
	public void setPayCondition(String payCondition) {
		this.payCondition = payCondition;
	}
	public String getTransportCondition() {
		return transportCondition;
	}
	public void setTransportCondition(String transportCondition) {
		this.transportCondition = transportCondition;
	}
	public String getCreatorName() {
		return creatorName;
	}
	public void setCreatorName(String creatorName) {
		this.creatorName = creatorName;
	}
	public String getCreatedTime() {
		return createdTime;
	}
	public void setCreatedTime(String createdTime) {
		this.createdTime = createdTime;
	}
	public String getUpdaterName() {
		return updaterName;
	}
	public void setUpdaterName(String updaterName) {
		this.updaterName = updaterName;
	}
	public String getUpdatedTime() {
		return updatedTime;
	}
	public void setUpdatedTime(String updatedTime) {
		this.updatedTime = updatedTime;
	}
	public String getCustomerAlias() {
		return customerAlias;
	}
	public void setCustomerAlias(String customerAlias) {
		this.customerAlias = customerAlias;
	}
	public String getCustomerCategory() {
		return customerCategory;
	}
	public void setCustomerCategory(String customerCategory) {
		this.customerCategory = customerCategory;
	}
}