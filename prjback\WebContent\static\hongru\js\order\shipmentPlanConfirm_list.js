layui.use(['laydate', 'layer', 'table', 'element','form'], function(){
    var laydate = layui.laydate //日期
        ,layer = layui.layer //弹层
        ,table = layui.table //表格
        ,element = layui.element //元素操作
    var form = layui.form;

    // 出库开始日期
    var outboundInsStart = layui.laydate.render({
        elem: '#outboundStartDate'
        ,done: function(value, date){
            // if (value) {
            //     //更新结束日期的最小日期
            //     outboundInsEnd.config.min = lay.extend({}, date, {
            //         month: date.month - 1
            //     });

            //     //自动弹出结束日期的选择器
            //     outboundInsEnd.config.elem[0].focus();
            // } 
        }
    });

    // 出库结束日期
    var outboundInsEnd = layui.laydate.render({
        elem: '#outboundEndDate'
        ,done: function(value, date){
            // if (value) {
            //     //更新开始日期的最大日期
            //     outboundInsStart.config.max = lay.extend({}, date, {
            //         month: date.month - 1
            //     });
            // }
        }
    });

    // 监听状态选择框变化
    form.on('select(planStatusFn)', function(data){
        search();
    });
    
    //执行一个 table 实例
    var url = baselocation+'/order/shipmentPlanConfirm/list';
    table.render({
        elem: '#demo'
        ,height: 'full-70'
        ,url: url //数据接口
        ,cellMinWidth: 80 //全局定义常规单元格的最小宽度
        ,parseData:function(res){ //res 即为原始返回的数据
            return {
                "code": 0, //解析接口状态
                "msg": '', //解析提示文本
                "count": res.total, //解析数据长度
                "data": res.rows //解析数据列表
            };
        }
        ,method:'post'
        ,title: '订单确认列表'
        ,page: true //开启分页
        ,limits: [10,20,50,100]
        ,where:$("#formSearch").serializeJsonObject()
        ,toolbar: '#toolbarDemo' //开启工具栏，此处显示默认图标，可以自定义模板，详见文档
        ,defaultToolbar: ['filter']
        ,totalRow: false //开启合计行
        ,cols: [[ //表头
            {type: 'checkbox', fixed: 'left'}
            ,{field: 'outboundDate',title: '出库日期',align:'center'}
            ,{field: 'demandCustomerCode',title: '需求方',align:'center'}
            ,{field: 'demandCustomerAlias',title: '需求方简称',align:'center'}
            ,{field: 'contractPartyCustomerCode',title: '合同方',align:'center'}
            ,{field: 'contractPartyCustomerAlias',title: '合同方简称',align:'center'}
            ,{field: 'customerAddress',title: '收货地址',align:'center'}
            ,{field: 'orderCount',title: '件数',align:'center'}
            ,{title: '操作',minWidth:70, align:'center',fixed: 'right', toolbar: '#barDemo'}
        ]]
    });

    //监听头工具栏事件
    table.on('toolbar(test)', function(obj){
        var checkStatus = table.checkStatus(obj.config.id)
            ,data = checkStatus.data; //获取选中的数据
        switch(obj.event){
            case 'confirm':
            case 'cancelConfirm':
                var info = obj.event == 'confirm' ? '确认' : '取消确认';
                var url = obj.event == 'confirm' ? baselocation + '/order/shipmentPlanConfirm/confirm' : baselocation + '/order/shipmentPlanConfirm/cancelConfirm';

                console.log(info + '按钮点击');
                // 获取所有被勾选的数据
                if(data.length === 0){
                    layer.msg('请选择需要' + info + '的出库计划', {icon: 0});
                    return;
                }
                
               // 收集所有选中行的出库日期、需求方、合同方
               var confirmDataParamList = [];
               $.each(data, function(index, item) {
                   confirmDataParamList.push(item.outboundDate + '-' + item.demandCustomerCode + '-' + item.contractPartyCustomerCode);
               });

               var confirmDataParamStr = confirmDataParamList.join(',');
                
                // 确认操作
                layer.confirm('确定要' + info + '选中的' + data.length + '条出库计划吗？', {
                    btn: ['确定', '取消']
                }, function() {
                    // 发送确认请求
                    $.ajax({
                        type: 'post',
                        url: url,
                        data: {
                            confirmDataParamStr: confirmDataParamStr
                        },
                        dataType: 'json',
                        success: function(result) {
                            if(result.code == 1) {
                                layer.msg(info + '成功!', {
                                    icon: 1,
                                    time: 1500
                                }, function() {
                                    // 刷新表格
                                    search();
                                });
                            } else {
                                layer.alert(result.data || '操作失败，请重试！', {icon: 2});
                            }
                        },
                        error: function() {
                            layer.alert('网络错误，请稍后重试！', {icon: 2});
                        }
                    });
                });
                // layer_show('确认', baselocation+"/order/orderConfirm/confirm/view", document.body.clientWidth-10, document.body.clientHeight-10);
                break;
            case 'emergencyExport':
                // 紧急出库导出
                var outboundStartDate = $("#outboundStartDate").val();
                var outboundEndDate = $("#outboundEndDate").val();
                var demandCustomerCode = $("#demandCustomerCode").val();

                if(outboundStartDate == null || outboundStartDate == ''){
                    layer.alert('请选择出库开始日期', {icon: 2});
                    return;
                }

                // 构建导出参数
                var exportParams = {
                    outboundStartDate: outboundStartDate,
                    outboundEndDate: outboundEndDate,
                    demandCustomerCode: demandCustomerCode,
                    exportType: 'emergency'
                };

                // 发起导出请求
                var form = $('<form method="post" action="' + baselocation + '/order/shipmentPlanConfirm/export"></form>');
                $.each(exportParams, function(key, value) {
                    if(value) {
                        form.append('<input type="hidden" name="' + key + '" value="' + value + '">');
                    }
                });
                $('body').append(form);
                form.submit();
                form.remove();
                break;
            case 'weeklyExport':
                // 周出库计划导出
                var outboundStartDate = $("#outboundStartDate").val();
                var outboundEndDate = $("#outboundEndDate").val();
                var demandCustomerCode = $("#demandCustomerCode").val();

                if(outboundStartDate == null || outboundStartDate == ''){
                    layer.alert('请选择出库开始日期', {icon:2});
                    return;
                }

                // 如果没有选择结束日期，默认为开始日期后6天（一周）
                if(outboundEndDate == null || outboundEndDate == ''){
                    var startDate = new Date(outboundStartDate);
                    startDate.setDate(startDate.getDate() + 6);
                    outboundEndDate = startDate.toISOString().split('T')[0];
                }

                // 构建导出参数
                var exportParams = {
                    outboundStartDate: outboundStartDate,
                    outboundEndDate: outboundEndDate,
                    demandCustomerCode: demandCustomerCode,
                    exportType: 'weekly'
                };

                // 发起导出请求
                var form = $('<form method="post" action="' + baselocation + '/order/shipmentPlanConfirm/export"></form>');
                $.each(exportParams, function(key, value) {
                    if(value) {
                        form.append('<input type="hidden" name="' + key + '" value="' + value + '">');
                    }
                });
                $('body').append(form);
                form.submit();
                form.remove();
                break;
            case 'refresh':
                var temp = $("#formSearch").serializeJsonObject();

                planStatus = temp.planStatus;

                console.info(temp);
                layui.table.reload('demo', {
                    page: {
                        curr: 1
                    }
                    ,where: temp
                }, 'data');

                // 切换按钮显示的函数
                if(planStatus === "0") {
                    $("#confirmBtn").show();
                    $("#cancelConfirmBtn").hide();
                } else if(planStatus === "1") {
                    $("#confirmBtn").hide();
                    $("#cancelConfirmBtn").show();
                }
                break;
        };
    });

    //监听行工具事件
    table.on('tool(test)', function(obj){ //注：tool 是工具条事件名，test 是 table 原始容器的属性 lay-filter="对应的值"
        var data = obj.data //获得当前行数据
            ,layEvent = obj.event; //获得 lay-event 对应的值
        if(layEvent === 'detail'){
            layer_show('详情',baselocation+"/order/shipmentPlanEntry/detail/view?outboundDate="+data.outboundDate+"&demandCustomerCode="+data.demandCustomerCode+"&contractPartyCustomerCode="+data.contractPartyCustomerCode+"&planStatus="+planStatus,document.body.clientWidth-10, document.body.clientHeight-10);
        }
    });
});

var planStatus = '0';
function search() {
	var temp = $("#formSearch").serializeJsonObject();
	console.info(temp);
    
    planStatus = temp.planStatus;

	layui.table.reload('demo', {
		page: {
			curr: 1
		}
		,where: temp
	}, 'data');

    // 切换按钮显示的函数
    if(planStatus === "0") {
        $("#confirmBtn").show();
        $("#cancelConfirmBtn").hide();
    } else if(planStatus === "1") {
        $("#confirmBtn").hide();
        $("#cancelConfirmBtn").show();
    }
}

// 订单附件下载功能
function downloadOrderAttachment(attachmentId) {
    if (!attachmentId || attachmentId.trim() === '') {
        layer.msg('未找到附件', {icon: 2});
        return;
    }

    // 直接打开下载链接
    window.open(baselocation + '/common/download?id=' + attachmentId);

    // 或者使用异步方式下载 - 如果需要先验证权限等操作
    /*
    $.ajax({
        url: baselocation + '/common/checkDownloadPermission',
        type: 'post',
        data: {id: attachmentId},
        success: function(res) {
            if (res.code == 1) {
                window.open(baselocation + '/common/download?id=' + attachmentId);
            } else {
                layer.msg(res.message || '下载失败', {icon: 2});
            }
        },
        error: function() {
            layer.msg('下载请求失败', {icon: 2});
        }
    });
    */
}
