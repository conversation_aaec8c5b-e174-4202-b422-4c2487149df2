package com.hongru.entity.businessOps;

import java.math.BigDecimal;

import com.baomidou.mybatisplus.annotations.TableField;
import com.baomidou.mybatisplus.annotations.TableId;
import com.baomidou.mybatisplus.annotations.TableName;
import com.baomidou.mybatisplus.enums.IdType;

@TableName("销售额表")
public class SalesAmount {

    /* 据点 */
    @TableField("据点")
    protected String stronghold;
    /* 销售额NO */
    @TableId(value = "销售额NO", type = IdType.INPUT)
    protected String salesAmountNo;
    /* 销售额日 */
    @TableField("销售额日")
    protected String salesAmountDate;
    /* 接单种类 */
    @TableField("接单种类")
    protected String orderType;
    /* 客户代码 */
    @TableField("客户代码")
    protected String customerCode;
    /* 客户简称 */
    @TableField("客户简称")
    protected String customerAlias;
    /* 结算货币 */
    @TableField("结算货币")
    protected String settlementCurrency;
    /* 税代码 */
    @TableField("税代码")
    protected String taxCode;
    /* 会计年月 */
    @TableField("会计年月")
    protected String accountingYearMonth;
    /* 销售额确认 */
    @TableField("销售额确认")
    protected String salesAmountConfirm;
    /* 创建人姓名 */
    @TableField("创建人")
    protected String creatorName;
    /* 创建时间 */
    @TableField("创建时间")
    protected String createdTime;
    /* 更新人姓名 */
    @TableField("更新人")
    protected String updaterName;
    /* 更新时间 */
    @TableField("更新时间")
    protected String updatedTime;
    /* 交易条件 */
    @TableField("交易条件")
    protected String tradeCondition;
    /* 付款条件 */
    @TableField("付款条件")
    protected String payCondition;
    /* 运输条件 */
    @TableField("运输条件")
    protected String transportCondition;


    public String getStronghold() {
        return stronghold;
    }

    public void setStronghold(String stronghold) {
        this.stronghold = stronghold;
    }
    public String getSalesAmountNo() {
        return salesAmountNo;
    }
    public void setSalesAmountNo(String salesAmountNo) {
        this.salesAmountNo = salesAmountNo;
    }
    public String getSalesAmountDate() {
        return salesAmountDate;
    }
    public void setSalesAmountDate(String salesAmountDate) {
        this.salesAmountDate = salesAmountDate;
    }

    public String getOrderType() {
        return orderType;
    }

    public void setOrderType(String orderType) {
        this.orderType = orderType;
    }

    public String getCustomerCode() {
        return customerCode;
    }

    public void setCustomerCode(String customerCode) {
        this.customerCode = customerCode;
    }
    public String getCustomerAlias() {
        return customerAlias;
    }
    public void setCustomerAlias(String customerAlias) {
        this.customerAlias = customerAlias;
    }
    public String getSettlementCurrency() {
        return settlementCurrency;
    }
    public void setSettlementCurrency(String settlementCurrency) {
        this.settlementCurrency = settlementCurrency;
    }

    public String getTaxCode() {
        return taxCode;
    }

    public void setTaxCode(String taxCode) {
        this.taxCode = taxCode;
    }

    public String getAccountingYearMonth() {
        return accountingYearMonth;
    }

    public void setAccountingYearMonth(String accountingYearMonth) {
        this.accountingYearMonth = accountingYearMonth;
    }

    public String getSalesAmountConfirm() {
        return salesAmountConfirm;
    }

    public void setSalesAmountConfirm(String salesAmountConfirm) {
        this.salesAmountConfirm = salesAmountConfirm;
    }
    public String getCreatorName() {
        return creatorName;
    }
    public void setCreatorName(String creatorName) {
        this.creatorName = creatorName;
    }
    public String getCreatedTime() {
        return createdTime;
    }
    public void setCreatedTime(String createdTime) {
        this.createdTime = createdTime;
    }
    public String getUpdaterName() {
        return updaterName;
    }
    public void setUpdaterName(String updaterName) {
        this.updaterName = updaterName;
    }
    public String getUpdatedTime() {
        return updatedTime;
    }
    public void setUpdatedTime(String updatedTime) {
        this.updatedTime = updatedTime;
    }

    public String getTradeCondition() {
        return tradeCondition;
    }

    public void setTradeCondition(String tradeCondition) {
        this.tradeCondition = tradeCondition;
    }

    public String getPayCondition() {
        return payCondition;
    }

    public void setPayCondition(String payCondition) {
        this.payCondition = payCondition;
    }

    public String getTransportCondition() {
        return transportCondition;
    }

    public void setTransportCondition(String transportCondition) {
        this.transportCondition = transportCondition;
    }
}
