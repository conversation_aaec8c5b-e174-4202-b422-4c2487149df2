package com.hongru.pojo.dto;

import com.hongru.entity.businessOps.AuxiliaryMaterialCost;
import com.hongru.support.page.PageInfo;

import java.util.List;

public class AuxiliaryMaterialCostDTO {

    private PageInfo pageInfo;

    private List<AuxiliaryMaterialCost> materialCostList;

    public AuxiliaryMaterialCostDTO(PageInfo pageInfo, List<AuxiliaryMaterialCost> materialCostList) {
        super();
        this.pageInfo = pageInfo;
        this.materialCostList = materialCostList;
    }

    public PageInfo getPageInfo() {
        return pageInfo;
    }

    public void setPageInfo(PageInfo pageInfo) {
        this.pageInfo = pageInfo;
    }

    public List<AuxiliaryMaterialCost> getMaterialCostList() {
        return materialCostList;
    }

    public void setMaterialCostList(List<AuxiliaryMaterialCost> materialCostList) {
        this.materialCostList = materialCostList;
    }
}
