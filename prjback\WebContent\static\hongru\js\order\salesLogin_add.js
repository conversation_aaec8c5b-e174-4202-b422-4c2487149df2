layui.use(['form', 'laydate', 'layer'], function(){
    var form = layui.form
        ,laydate = layui.laydate
        ,layer = layui.layer;

    // 页面加载时初始化下拉菜单数据
    loadTradeConditions();
    loadPayConditions();
    loadTransportConditions();

    // 初始化日期选择器
    laydate.render({
        elem: '#salesDate',
        value: new Date().toISOString().split('T')[0], // 默认今天
        format: 'yyyy-MM-dd', // 指定日期格式
        trigger: 'click', // 点击触发
        done: function(value) {
            // 确保日期值正确设置到表单字段
            $('#salesDate').val(value);
        }
    });
    
    // 初始化到货日期范围选择器
    laydate.render({
        elem: '#arrivalDateRange',
        type: 'date',
        range: true,
        format: 'yyyy-MM-dd',
        trigger: 'click',
        done: function(value) {
            // 确保日期值正确设置到表单字段
            $('#arrivalDateRange').val(value);

            // 如果已选择了客户，则根据到货日期范围重新加载数据
            var customerCode = $('#customerCode').val();
            if (customerCode && value) {
                var dateRange = value.split(' - ');
                if (dateRange.length === 2) {
                    loadDetailData(customerCode, dateRange[0], dateRange[1]);
                }
            }
        }
    });
    
    // 监听默认铜合同类别选择
    form.on('select(defaultCopperContractTypeFilter)', function(data){
        console.log('默认铜合同类别选择:', data.value);

        // 清空铜合同NO和铜条件
        $('#defaultCopperContractNo').empty().append('<option value="">请选择</option>');
        $('#defaultCopperCondition').empty().append('<option value="">请选择</option>');
        form.render('select');

        var customerCode = $('#customerCode').val();
        var arrivalDateRange = $('#arrivalDateRange').val();
        var arrivalDate = '';
        if (arrivalDateRange) {
            var dateRange = arrivalDateRange.split(' - ');
            if (dateRange.length === 2) {
                arrivalDate = dateRange[0]; // 使用开始日期
            }
        }

        console.log('级联处理参数:', {
            copperContractType: data.value,
            customerCode: customerCode,
            arrivalDate: arrivalDate
        });

        handleDefaultCopperContractTypeChange(data.value, customerCode, arrivalDate);
    });

    // 监听默认铜合同NO选择
    form.on('select(defaultCopperContractNoFilter)', function(data){
        console.log('默认铜合同NO选择:', data.value);
        handleDefaultCopperContractNoChange(data.value);
    });

    // 监听默认铜条件选择
    form.on('select(defaultCopperConditionFilter)', function(data){
        console.log('默认铜条件选择:', data.value);
        handleDefaultCopperConditionChange(data.value);
    });

    // 应用默认值到所有明细
    $('#applyDefaultValues').click(function(){
        applyDefaultValuesToAllDetails();
    });

    // 监听客户选择
    form.on('select(customerCodeFilter)', function(data){
        console.log('客户选择:', data); // 添加日志，便于调试
        console.log('客户代码:', data.value); // 添加日志，便于调试

        // 先清空货币和税率字段
        $('#currency').val('');
        $('#taxRate').val('');
        form.render('select'); // 重新渲染表单

        // 清空明细数据
        $('#detailTable tbody').remove();

        // 清空默认值设定
        $('#defaultCopperContractType').val('');
        $('#defaultCopperContractNo').empty().append('<option value="">请选择</option>');
        $('#defaultCopperCondition').empty().append('<option value="">请选择</option>');
        form.render('select');

        if (data.value) {
            // 获取客户详情信息
            $.ajax({
                url: baselocation + '/order/salesLogin/getCustomerInfo',
                type: 'POST',
                data: {customerCode: data.value},
                success: function(result) {
                    console.log('获取客户详情信息结果:', result); // 添加日志，便于调试
                    if (result.code === 1 && result.data) {
                        var customerInfo = result.data;
                        console.log('客户详情信息:', customerInfo); // 添加日志，便于调试

                        // 设置货币
                        if (customerInfo && customerInfo.结算货币) {
                            $('#currency').val(customerInfo.结算货币);
                            console.log('设置货币:', customerInfo.结算货币);
                        }

                        // 根据税代码获取税率（联动方式）
                        if (customerInfo && customerInfo.税代码) {
                            $('#taxCode').val(customerInfo.税代码);
                            getTaxRateByTaxCode(customerInfo.税代码);
                            console.log('根据税代码获取税率:', customerInfo.税代码);
                        } else {
                            console.log('客户详情信息中没有税代码');
                            // 清空税率和税代码
                            $('#taxRate').val('');
                            $('#taxCode').val('');
                        }

                        // 设置交易条件、付款条件、运输条件等字段的默认值
                        if (customerInfo) {
                            // 交易条件：设置下拉菜单的默认选中值
                            if (customerInfo.交易条件) {
                                $('#tradeCondition').val(customerInfo.交易条件);
                                console.log('设置交易条件默认值:', customerInfo.交易条件);
                            }

                            // 付款条件：设置下拉菜单的默认选中值
                            if (customerInfo.付款条件) {
                                $('#payCondition').val(customerInfo.付款条件);
                                console.log('设置付款条件默认值:', customerInfo.付款条件);
                            }

                            // 运输条件：设置下拉菜单的默认选中值
                            if (customerInfo.运输条件) {
                                $('#transportCondition').val(customerInfo.运输条件);
                                console.log('设置运输条件默认值:', customerInfo.运输条件);
                            }

                            // 重新渲染表单以更新下拉菜单显示
                            form.render('select');
                        }
                        form.render('select'); // 重新渲染表单

                        // 检查到货日期范围是否已选择
                        var arrivalDateRange = $('#arrivalDateRange').val();
                        if (arrivalDateRange) {
                            var dateRange = arrivalDateRange.split(' - ');
                            if (dateRange.length === 2) {
                                // 如果已选择到货日期范围，则加载出库数据
                                loadDetailData(data.value, dateRange[0], dateRange[1]);
                            }
                        } else {
                            // 提示用户选择到货日期范围
                            layer.msg('请选择到货日期范围', {icon: 0});
                        }
                    } else {
                        console.log('获取客户详情信息失败:', result.message); // 添加日志，便于调试
                        layer.msg('获取客户信息失败: ' + (result.message || 'failed'), {icon: 2});
                    }
                },
                error: function(xhr, status, error) {
                    console.log('获取客户详情信息异常:', status, error); // 添加日志，便于调试
                    layer.msg('获取客户信息失败: ' + error, {icon: 2});
                }
            });
        }
    });
    
    // 初始化表格 - 使用原生HTML表格（参考详情页面和11.html）
    initializeTwoRowEditableTable();

    // 初始化时计算总金额
    calculateTotalAmount();

    // 移除延时方案，税额计算将在税率获取完成后的回调中进行
    // 对于新增页面，初始化时税率为空，税额显示为0.00
    $('#taxAmount').val('0.00');

    // 注意：现在使用原生HTML表格，不再需要layui表格的编辑监听和工具事件

    // 计算总金额的函数 - 使用原生HTML表格
    function calculateTotalAmount() {
        var totalAmount = 0;

        // 从原生HTML表格中获取销售金额
        $('#detailTable tbody.record').each(function() {
            var record = $(this);
            var secondRow = record.find('tr:last-child');
            var salesAmountInput = secondRow.find('input[name="salesAmount"]');
            var salesAmount = parseFloat(salesAmountInput.val()) || 0;
            totalAmount += salesAmount;
        });

        $('#totalAmount').val(totalAmount.toFixed(2));

        // 计算税额 = 总金额 × 税率
        calculateTaxAmount(totalAmount);

        console.log('计算总金额:', totalAmount.toFixed(2)); // 添加日志便于调试
    }

    // 计算税额的函数
    function calculateTaxAmount(totalAmount) {
        var taxRate = parseFloat($('#taxRate').val()) || 0;
        var taxAmount = totalAmount * (taxRate / 100); // 税率是百分比，需要除以100
        $('#taxAmount').val(taxAmount.toFixed(2));
        console.log('计算税额 - 总金额:', totalAmount, '税率:', taxRate + '%', '税额:', taxAmount.toFixed(2));
        console.log('税率字段值:', $('#taxRate').val(), '税率字段是否存在:', $('#taxRate').length);
    }
    
    // 加载明细数据
    function loadDetailData(customerCode, startDate, endDate) {
        console.log('加载出库数据，客户代码:', customerCode); // 添加日志，便于调试
        console.log('到货日期范围:', startDate, '至', endDate); // 添加日志，便于调试

        // 验证参数
        if (!customerCode) {
            layer.msg('请选择需求方', {icon: 0});
            return;
        }

        if (!startDate || !endDate) {
            layer.msg('请选择到货日期范围', {icon: 0});
            return;
        }

        $.ajax({
            url: baselocation + '/order/salesLogin/getOutboundData',
            type: 'POST',
            data: {
                customerCode: customerCode,
                startDate: startDate, // 开始日期
                endDate: endDate // 结束日期
            },
            success: function(result) {
                console.log('获取出库数据结果:', result); // 添加日志，便于调试
                if (result.code === 1 && result.data && result.data.length > 0) {
                    console.log('出库数据:', result.data); // 添加日志，便于调试
                    
                    // 处理返回的数据，转换为表格需要的格式
                    var tableData = result.data.map(function(item) {
                        console.log('处理出库数据项:', item); // 添加日志，便于调试
                        console.log('出货计划番号:', item.出库计划番号 || item.出货计划番号 || item.shipmentPlanNo); // 添加日志，便于调试
                        console.log('产品代码:', item.品名); // 添加日志，便于调试
                        console.log('线盘:', item.线盘); // 添加日志，便于调试
                        console.log('客户代码:', item.客户品目C); // 添加日志，便于调试
                        console.log('出货日:', item.出货日); // 添加日志，便于调试
                        console.log('客户订单No:', item.客户订单No); // 添加日志，便于调试
                        console.log('明细内部备注:', item.明细内部备注); // 添加日志，便于调试
                        console.log('条码:', item.品目C); // 添加日志，便于调试
                        
                        // 处理到货日期
                        var arrivalDate = item.到货日期 || "";
                        if (arrivalDate) {
                            // 尝试转换日期格式
                            try {
                                var dateObj = new Date(arrivalDate);
                                if (!isNaN(dateObj.getTime())) {
                                    var year = dateObj.getFullYear();
                                    var month = ('0' + (dateObj.getMonth() + 1)).slice(-2);
                                    var day = ('0' + dateObj.getDate()).slice(-2);
                                    arrivalDate = year + '-' + month + '-' + day;
                                }
                            } catch (e) {
                                console.log('日期格式化出错:', e);
                            }
                        }
                        
                        // 处理出货日
                        var outboundDate = item.出货日 || "";
                        if (outboundDate) {
                            try {
                                var dateObj = new Date(outboundDate);
                                if (!isNaN(dateObj.getTime())) {
                                    var year = dateObj.getFullYear();
                                    var month = ('0' + (dateObj.getMonth() + 1)).slice(-2);
                                    var day = ('0' + dateObj.getDate()).slice(-2);
                                    outboundDate = year + '-' + month + '-' + day;
                                }
                            } catch (e) {
                                console.log('出货日格式化出错:', e);
                            }
                        }
                        
                        return {
                          productCode: item.产品代码 || "",
                          productName: item.产品代码 || item.品名 || "", // 产品代码字段
                          reelName: item.线盘 || "",
                          arrivalDate: arrivalDate,
                          outboundDate: outboundDate,
                          customerOrderNo: item.客户订单No || "",
                          deliveryNoteNo: item.deliveryNoteNo || item.出货单No || "",
                          deliveryNoteSeq: item.deliveryNoteSeq || item.送货单序号 || "",
                          productCategoryCode: item.品目C || "", // 条码字段
                          customerProductCode: item.客户品目C || customerCode || "", // 客户代码字段
                          copperContractType: item.铜合同类别 || "`1`",
                          copperContractNo: item.铜合同No || item.铜签约No || "",
                          copperCondition: item.铜条件 || "",
                          currency: item.铜货币 || "",
                          conversionRate: item.换算率 || 1.0,
                          quantity: item.quantity || item.数量 || 0,
                          copperBase: item.copperBase || item.铜base || 0,
                          premium: item.升水 || 0,
                          convertedCopperPrice: (
                            parseFloat(item.copperBase || item.铜base || 0) +
                            parseFloat(item.升水 || 0)
                          ).toFixed(2),
                          zeroBase: item.零基础 || item.零基数 || 0,
                          salesUnitPrice: (
                            parseFloat(item.copperBase || item.铜base || 0) +
                            parseFloat(item.升水 || 0) +
                            parseFloat(item.零基础 || item.零基数 || 0)
                          ).toFixed(2),
                          salesAmount: (
                            parseFloat(item.quantity || item.数量 || 0) *
                            (parseFloat(item.copperBase || item.铜base || 0) +
                              parseFloat(item.升水 || 0) +
                              parseFloat(item.零基础 || item.零基数 || 0))
                          ).toFixed(2),
                          productMiddleCategory: item.产品中分类 || "未分类", // 确保产品中分类不为空
                          shipmentPlanNo: item.shipmentPlanNo || item.出货计划番号 || item.出库计划番号 || "", // 修正字段名称，使用出库计划番号
                          remark: item.明细内部备注 || item.明细备注 || "", // 明细备注字段
                        };
                    });
                    
                    console.log('转换后的表格数据:', tableData); // 添加日志，便于调试

                    // 检查出货计划番号字段的数据
                    console.log('=== 出货计划番号字段检查 ===');
                    if (tableData && tableData.length > 0) {
                        tableData.forEach(function(row, index) {
                            console.log('行', index, '出货计划番号:', row.shipmentPlanNo);
                        });
                    }
                    
                    // 清空表格并重新加载数据
                    $('#detailTable tbody').remove();
                    tableData.forEach(function(item) {
                        addDetailRowToTwoRowTable(item);
                    });

                    // 加载数据后计算总金额
                    calculateTotalAmount();

                    // 初始化铜合同类别字段状态
                    setTimeout(function() {
                        initializeCopperContractTypeFields();
                    }, 500); // 延迟500ms确保DOM完全渲染
                    
                    // 初始化表格中的日期选择器
                    initTableDatePickers();
                    
                    // 表格重载后，延迟一点时间重新渲染表单元素
                    setTimeout(function() {
                        form.render('select'); // 重新渲染所有下拉框
                        console.log('表格重载后，重新初始化表单元素');
                        
                        // 确保下拉框可点击
                        $('.layui-table-body').find('select').each(function() {
                            $(this).parent().css('overflow', 'visible');
                            
                            // 添加样式确保下拉框不会超出单元格
                            $(this).css({
                                'width': '100%',
                                'border': 'none',
                                'height': '28px',
                                'background-color': 'transparent'
                            });
                        });
                    }, 100);
                } else {
                    console.log('获取出库数据结果:', result); // 添加日志，便于调试

                    // 区分错误和无数据的情况
                    if (result.code === 0) {
                        // 这是真正的错误，显示错误信息
                        layer.msg(result.message || '获取出库数据失败', {icon: 2});
                    } else {
                        // 这是正常的无数据情况
                        console.log('未找到出库数据或数据为空');
                        layer.msg('未找到相关出库数据，请点击"添加明细"按钮添加明细信息', {icon: 0});
                    }
                }
            },
            error: function(xhr, status, error) {
                console.log('获取出库数据异常:', status, error); // 添加日志，便于调试
                console.log('响应内容:', xhr.responseText); // 添加响应内容日志

                // 尝试解析后端返回的错误信息
                try {
                    var response = JSON.parse(xhr.responseText);
                    if (response && response.message) {
                        layer.msg(response.message, {icon: 2});
                    } else {
                        layer.msg('获取出库数据失败，请点击"添加明细"按钮添加明细信息', {icon: 2});
                    }
                } catch (e) {
                    layer.msg('获取出库数据失败，请点击"添加明细"按钮添加明细信息', {icon: 2});
                }
            }
        });
    }
    
    // 注意：现在使用原生HTML表格，通过addDetailRowToTwoRowTable()添加行
    
    // 这里不再需要旧的添加明细按钮事件，因为已经移除了该按钮

    // 新增明细按钮事件已在initializeTwoRowEditableTable函数中绑定
    
    // 初始化表格中所有行的日期选择器
    function initTableDatePickers() {
        // 下一个tick中执行，确保表格已渲染完成
        setTimeout(function() {
            var rows = $('#detailTable tr');
            if (rows.length > 0) {
                rows.each(function(index) {
                    if (index > 0) { // 跳过表头行
                        var dateCell = $(this).find('td[data-field="arrivalDate"]');
                        if (dateCell.length > 0) {
                            // 为日期单元格添加点击事件
                            dateCell.off('click').on('click', function() {
                                var that = this;
                                laydate.render({
                                    elem: that,
                                    show: true,
                                    format: 'yyyy-MM-dd', // 指定日期格式
                                    done: function(value) {
                                        // 直接更新输入框的值
                                        $(that).val(value);

                                        // 重新计算总金额
                                        calculateTotalAmount();
                                    }
                                });
                            });
                            
                            // 添加视觉提示，表明这是可点击的日期字段
                            dateCell.css('cursor', 'pointer').attr('title', '点击选择日期');
                        }
                    }
                });
            }
        }, 100);
    }
    
    // 监听表单提交
    form.on('submit(formDemo)', function(data){
        // 表单数据
        var formData = data.field;
        
        // 验证需求方
        if (!formData.customerCode) {
            layer.msg('需求方不能为空', {icon: 2});
            return false;
        }
        
        // 验证到货日期范围
        if (!formData.arrivalDateRange) {
            layer.msg('到货日期范围不能为空', {icon: 2});
            return false;
        }
        
        // 验证销售额日
        if (!formData.salesDate) {
            layer.msg('销售额日不能为空', {icon: 2});
            return false;
        }
        
        // 获取表格数据前，先同步所有下拉框的值
        console.log('=== 表单提交前同步下拉框值 ===');
        $('.layui-table-body select[name^="copperContractType_"]').each(function() {
            var $select = $(this);
            var value = $select.val();
            var uniqueId = $select.attr('data-unique-id');

            // 获取当前行的实际位置
            var $row = $select.closest('tr');
            var $tbody = $row.closest('tbody');
            var actualRowIndex = $tbody.find('tr').index($row);

            console.log('同步下拉框 - 值:', value, 'uniqueId:', uniqueId, 'actualRowIndex:', actualRowIndex);

            // 注意：现在使用原生HTML表格，下拉框值已直接更新到DOM中
        });

        // 获取表格数据
        var tableData = getTableData();

        if (!tableData.length) {
            layer.msg('请至少添加一行明细数据', {icon: 2});
            return false;
        }
        
        // 检查必填字段
        var hasEmptyRequiredField = false;
        var emptyFieldMessage = '';
        
        for (var i = 0; i < tableData.length; i++) {
            var row = tableData[i];
            
            // 检查产品中分类是否为空
            if (!row.productMiddleCategory) {
                hasEmptyRequiredField = true;
                emptyFieldMessage = '第' + (i + 1) + '行的产品中分类不能为空';
                break;
            }
            
            // 确保产品中分类不为空
            row.productMiddleCategory = row.productMiddleCategory || '未分类';
            
            // 确保明细备注字段存在
            row.remark = row.remark || '';
        }
        
        if (hasEmptyRequiredField) {
            layer.msg(emptyFieldMessage, {icon: 2});
            return false;
        }
        
        // 整理数据 - 确保使用正确的字段名
        formData.salesAmountDate = formData.salesDate; // 后端需要 salesAmountDate 字段
        // 使用保存的税代码，而不是税率值
        formData.taxCode = $('#taxCode').val() || formData.taxRate; // 优先使用税代码，如果没有则使用税率值作为兼容

        // 添加客户相关条件字段
        formData.tradeCondition = $('#tradeCondition').val() || '';
        formData.payCondition = $('#payCondition').val() || '';
        formData.transportCondition = $('#transportCondition').val() || '';

        console.log('客户条件字段 - 交易条件:', formData.tradeCondition, '付款条件:', formData.payCondition, '运输条件:', formData.transportCondition);
        
        // 处理表格数据，将productMiddleCategory映射为productCategory
        for (var i = 0; i < tableData.length; i++) {
            // 确保产品中分类不为空
            tableData[i].productCategory = tableData[i].productMiddleCategory || '未分类';
            // 确保明细备注字段正确传递
            console.log('第' + (i + 1) + '行明细备注:', tableData[i].remark);
            // 添加铜合同类别的调试信息
            console.log('第' + (i + 1) + '行铜合同类别:', tableData[i].copperContractType);
        }
        
        formData.salesDetails = JSON.stringify(tableData); // 后端需要 salesDetails 字段，并且需要是字符串
        
        // 打印提交的数据，便于调试
        console.log('提交的表单数据:', formData);
        console.log('销售额日期:', formData.salesAmountDate);
        console.log('税代码:', formData.taxCode);
        console.log('销售明细数据:', formData.salesDetails);
        
        // 发送AJAX请求保存数据
        $.ajax({
            url: baselocation + '/order/salesLogin/save',
            type: 'POST',
            contentType: 'application/json',
            data: JSON.stringify(formData),
            success: function(result) {
                console.log('保存结果:', result); // 添加日志，便于调试
                if (result.code === 1) {
                    layer.msg('保存成功', {icon: 1, time: 1000}, function(){
                        closeLayer(); // 关闭窗口
                    });
                } else {
                    layer.msg(result.message || '保存失败', {icon: 2});
                }
            },
            error: function(xhr, status, error) {
                console.log('保存失败:', xhr.responseText); // 添加错误日志
                layer.msg('保存失败，请检查网络连接', {icon: 2});
            }
        });
        
        return false; // 阻止表单默认提交
    });

    // 页面加载完成后初始化表格中的日期选择器
    $(document).ready(function() {
        initTableDatePickers();
        
        // 确保销售额日有默认值
        if (!$('#salesDate').val()) {
            var today = new Date();
            var year = today.getFullYear();
            var month = ('0' + (today.getMonth() + 1)).slice(-2);
            var day = ('0' + today.getDate()).slice(-2);
            var formattedDate = year + '-' + month + '-' + day;
            $('#salesDate').val(formattedDate);
        }
    });

    // 监听铜合同类别下拉框变更事件（使用事件委托）
    $(document).on('change', 'select[name="copperContractType"]', function() {
        var copperContractType = $(this).val();
        var $currentRow = $(this).closest('tr');
        var $tbody = $currentRow.closest('tbody.record');

        // 从当前行获取客户代码（客户品目C字段）和到货日期
        var customerCode = $currentRow.find('input[name="customerProductCode"]').val();
        var arrivalDate = $currentRow.find('input[name="arrivalDate"]').val();

        // 调试信息
        console.log('=== 铜合同类别变更调试信息 ===');
        console.log('铜合同类别:', copperContractType);
        console.log('客户代码(从当前行获取):', customerCode);
        console.log('到货日期:', arrivalDate);
        console.log('到货日期输入框数量:', $currentRow.find('input[name="arrivalDate"]').length);
        console.log('客户代码输入框数量:', $currentRow.find('input[name="customerProductCode"]').length);
        console.log('当前行所有input字段:', $currentRow.find('input').map(function() { return $(this).attr('name') + '=' + $(this).val(); }).get());
        console.log('================================');

        // 检查必要参数
        if (!customerCode) {
            layer.msg('请先填写客户品目C（客户代码）', {icon: 0, time: 2000});
            return;
        }

        if ((copperContractType === '1' || copperContractType === '2') && !arrivalDate) {
            layer.msg('预约铜/支给铜需要先填写到货日期', {icon: 0, time: 2000});
            return;
        }

        handleCopperContractTypeChange(copperContractType, $currentRow, customerCode, arrivalDate);
    });

    // 使用事件委托处理动态生成的铜条件下拉框change事件
    $(document).on('change', 'select[name="copperCondition"]', function() {
        var selectedOption = $(this).find('option:selected');
        var currency = selectedOption.data('currency');
        var copperCondition = selectedOption.val();

        console.log('铜条件选择事件触发:', copperCondition, '货币:', currency);

        // 获取货币字段（在第二行）
        var $currentRow = $(this).closest('tr');
        var $tbody = $currentRow.closest('tbody.record');
        var $secondRow = $tbody.find('tr:last-child');
        var $currencyInput = $secondRow.find('input[name="currency"]');

        console.log('找到的货币字段数量:', $currencyInput.length);
        console.log('货币字段元素:', $currencyInput);

        if (currency) {
            // 直接设置货币
            $currencyInput.val(currency);
            console.log('直接设置铜条件货币:', currency, '设置后的值:', $currencyInput.val());
        } else if (copperCondition) {
            // 如果没有货币信息，通过接口获取
            console.log('通过接口获取铜条件货币:', copperCondition);
            getCopperConditionDetail(copperCondition, $(this));
        } else {
            // 清空货币
            $currencyInput.val('');
            console.log('清空铜条件货币');
        }

        // 根据铜合同类别决定是否查询铜base
        var $copperContractTypeSelect = $currentRow.find('select[name="copperContractType"]');
        var copperContractType = $copperContractTypeSelect.val();

        if (copperContractType === '3' || copperContractType === '4') {
            // 一般铜/无偿：查询铜价表获取铜base
            var arrivalDate = $currentRow.find('input[name="arrivalDate"]').val();
            if (copperCondition && arrivalDate) {
                queryCopperBaseForGeneralCopper($tbody, copperCondition, arrivalDate);
            }
        }
    });

    // 使用事件委托处理产品代码变更事件，自动查询产品单价
    $(document).on('change blur', 'input[name="productName"]', function() {
        var productCode = $(this).val();
        var $currentRow = $(this).closest('tr');

        if (productCode) {
            // 获取客户代码和到货日期
            var customerCode = $currentRow.find('input[name="customerProductCode"]').val();
            var arrivalDate = $currentRow.find('input[name="arrivalDate"]').val();

            console.log('产品代码变更，尝试查询产品单价:', {
                productCode: productCode,
                customerCode: customerCode,
                arrivalDate: arrivalDate
            });

            if (customerCode && arrivalDate) {
                queryProductPrice(customerCode, productCode, arrivalDate, $currentRow);
            } else {
                console.log('客户代码或到货日期为空，无法查询产品单价');
            }
        }
    });

    // 使用事件委托处理到货日期变更事件，自动查询产品单价
    $(document).on('change', 'input[name="arrivalDate"]', function() {
        var arrivalDate = $(this).val();
        var $currentRow = $(this).closest('tr');

        if (arrivalDate) {
            // 获取客户代码和产品代码
            var customerCode = $currentRow.find('input[name="customerProductCode"]').val();
            var productCode = $currentRow.find('input[name="productName"]').val();

            console.log('到货日期变更，尝试查询产品单价:', {
                arrivalDate: arrivalDate,
                customerCode: customerCode,
                productCode: productCode
            });

            if (customerCode && productCode) {
                queryProductPrice(customerCode, productCode, arrivalDate, $currentRow);
            } else {
                console.log('客户代码或产品代码为空，无法查询产品单价');
            }
        }
    });

    // 使用事件委托处理0base、铜base、升水、换算率、数量字段变更事件，重新计算销售单价和销售金额
    $(document).on('change blur', 'input[name="zeroBase"], input[name="copperBase"], input[name="premium"], input[name="conversionRate"], input[name="quantity"]', function() {
        var $currentRow = $(this).closest('tr');
        var $tbody = $currentRow.closest('tbody.record');
        var $secondRow = $tbody.find('tr:last-child');

        // 获取所有相关字段的值
        var zeroBase = parseFloat($secondRow.find('input[name="zeroBase"]').val()) || 0;
        var copperBase = parseFloat($secondRow.find('input[name="copperBase"]').val()) || 0;
        var premium = parseFloat($secondRow.find('input[name="premium"]').val()) || 0;
        var conversionRate = parseFloat($secondRow.find('input[name="conversionRate"]').val()) || 1;
        var quantity = parseFloat($secondRow.find('input[name="quantity"]').val()) || 0;

        // 计算换算后铜单价 = (铜base + 升水) * 换算率
        var convertedCopperPrice = (copperBase + premium) * conversionRate;
        $secondRow.find('input[name="convertedCopperPrice"]').val(convertedCopperPrice.toFixed(4));

        // 计算销售单价 = 换算后铜单价 + 0base
        var salesUnitPrice = convertedCopperPrice + zeroBase;
        $secondRow.find('input[name="salesUnitPrice"]').val(salesUnitPrice.toFixed(4));

        // 计算销售金额 = 销售单价 * 数量
        var salesAmount = salesUnitPrice * quantity;
        $secondRow.find('input[name="salesAmount"]').val(salesAmount.toFixed(2));

        // 重新计算总金额
        calculateTotalAmount();

        // 如果是数量字段变更，进行数量验证
        if ($(this).attr('name') === 'quantity') {
            validateQuantitySplit($currentRow);
        }

        console.log('字段变更重新计算:', {
            zeroBase: zeroBase,
            copperBase: copperBase,
            premium: premium,
            conversionRate: conversionRate,
            quantity: quantity,
            convertedCopperPrice: convertedCopperPrice,
            salesUnitPrice: salesUnitPrice,
            salesAmount: salesAmount
        });
    });

    // 注意：现在使用原生HTML表格，下拉框变更事件由updateCopperContractTypeInTable处理
});

// 自定义到货日期模板
function renderArrivalDate(data) {
    if (data.arrivalDate) {
        // 尝试将日期格式化为 YYYY-MM-DD 格式
        var dateObj;
        try {
            // 如果是日期字符串，尝试转换为日期对象
            dateObj = new Date(data.arrivalDate);
            // 检查是否是有效日期
            if (!isNaN(dateObj.getTime())) {
                // 格式化为 YYYY-MM-DD
                var year = dateObj.getFullYear();
                var month = ('0' + (dateObj.getMonth() + 1)).slice(-2);
                var day = ('0' + dateObj.getDate()).slice(-2);
                return year + '-' + month + '-' + day;
            } else {
                // 如果不是有效日期，直接返回原始值
                return data.arrivalDate;
            }
        } catch (e) {
            // 如果转换出错，直接返回原始值
            return data.arrivalDate;
        }
    } else {
        return '<span style="color:#999;">点击选择日期</span>';
    }
}

function closeLayer() {
    var index = parent.layer.getFrameIndex(window.name);
    parent.layer.close(index);
    parent.location.reload();
}

// 更新铜合同类别
function updateCopperContractType(select) {
    var value = select.value;
    var uniqueId = $(select).attr('data-unique-id');
    var originalValue = $(select).attr('data-original-value');

    console.log('=== 铜合同类别更新开始 ===');
    console.log('选择的值:', value);
    console.log('唯一标识:', uniqueId);
    console.log('原始值:', originalValue);

    // 获取当前行的实际位置
    var $row = $(select).closest('tr');
    var $tbody = $row.closest('tbody');
    var actualRowIndex = $tbody.find('tr').index($row);

    console.log('DOM行索引:', actualRowIndex);

    // 注意：现在使用原生HTML表格，下拉框值已直接更新到DOM中
    console.log('铜合同类别已更新为:', value);
}

// 初始化可编辑的两行表格（参考详情页面和11.html）
function initializeTwoRowEditableTable() {
    // 添加样式
    if (!$('#twoRowEditableStyle').length) {
        var style = $('<style id="twoRowEditableStyle">');
        style.text(
            '#detailTable {' +
                'width: 100%;' +
                'border-collapse: collapse;' +
                'margin-top: 10px;' +
            '}' +
            '#detailTable th, #detailTable td {' +
                'border: 1px solid #e6e6e6;' +
                'padding: 8px 6px;' +
                'text-align: center;' +
                'word-break: break-all;' +
                'font-size: 12px;' +
                'line-height: 1.3;' +
                'vertical-align: middle;' +
            '}' +
            '/* 产品代码列宽度调整 - 第一行第4列 */' +
            '#detailTable thead tr:first-child th:nth-child(5), ' +
            '#detailTable tbody.record tr:first-child td:nth-child(5) {' +
                'min-width: 120px;' +
                'width: 120px;' +
            '}' +
            '/* 注意：删除了单位列，所以不再需要单位列的宽度调整 */' +
            '#detailTable thead th {' +
                'font-weight: bold;' +
                'white-space: nowrap;' +
            '}' +
            '/* 第一行表头 - 基础信息 */' +
            '#detailTable thead tr:first-child th {' +
                'background-color: #e6f7ff;' +
                'color: #1890ff;' +
                'border-bottom: 1px solid #1890ff;' +
            '}' +
            '/* 第二行表头 - 计算信息 */' +
            '#detailTable thead tr:last-child th {' +
                'background-color: #fff2e8;' +
                'color: #fa8c16;' +
                'border-bottom: 1px solid #fa8c16;' +
            '}' +
            '/* 每条记录用两个 <tr>，不同记录间用背景色区分 */' +
            '#detailTable tbody.record:nth-of-type(odd) td {' +
                'background: #f8f8f8;' +
            '}' +
            '#detailTable tbody.record:nth-of-type(even) td {' +
                'background: #fff;' +
            '}' +
            '/* 让第二行数据和第一行数据视觉上依然"连成一体" */' +
            '#detailTable tbody.record tr:first-child td {' +
                'border-bottom: none;' +
                'border-left: 3px solid #1890ff;' +
            '}' +
            '#detailTable tbody.record tr:last-child td {' +
                'border-top: none;' +
                'border-left: 3px solid #fa8c16;' +
                'border-bottom: 2px solid #d2d2d2;' +
            '}' +
            '.table-container {' +
                'overflow-x: auto;' +
                'max-height: 400px;' +
                'overflow-y: auto;' +
            '}' +
            '/* 输入框和下拉框样式 */' +
            '#detailTable input, #detailTable select {' +
                'width: 100%;' +
                'border: 1px solid #d9d9d9;' +
                'background: #fff;' +
                'padding: 4px;' +
                'font-size: 12px;' +
                'height: 28px;' +
                'box-sizing: border-box;' +
                'display: block;' +
                'position: relative;' +
                'z-index: 1;' +
            '}' +
            '#detailTable input:focus, #detailTable select:focus {' +
                'outline: 1px solid #1890ff;' +
                'background: #fff;' +
                'border-color: #1890ff;' +
            '}' +
            '/* 确保下拉框可见 */' +
            '#detailTable select {' +
                'appearance: auto;' +
                '-webkit-appearance: menulist;' +
                '-moz-appearance: menulist;' +
                'cursor: pointer;' +
            '}' +
            '/* 表格单元格样式调整 */' +
            '#detailTable td {' +
                'position: relative;' +
                'overflow: visible;' +
            '}' +
            '/* 删除按钮样式 */' +
            '.delete-btn {' +
                'background: #ff4d4f;' +
                'color: white;' +
                'border: none;' +
                'padding: 2px 8px;' +
                'border-radius: 2px;' +
                'cursor: pointer;' +
                'font-size: 12px;' +
            '}' +
            '.delete-btn:hover {' +
                'background: #ff7875;' +
            '}' +
            '/* 强制显示下拉框 */' +
            '#detailTable select[name="copperContractType"] {' +
                'display: block !important;' +
                'visibility: visible !important;' +
                'opacity: 1 !important;' +
                'width: 100% !important;' +
                'height: 28px !important;' +
                'border: 1px solid #d9d9d9 !important;' +
                'background: #fff !important;' +
                'font-size: 12px !important;' +
                'z-index: 999 !important;' +
                'position: relative !important;' +
            '}' +
            '/* 调试用：给铜合同类别单元格添加背景色 */' +
            '#detailTable tbody.record tr:first-child td:nth-child(12) {' +
                'background-color: #e6f7ff !important;' +
                'border: 2px solid #1890ff !important;' +
            '}' +
            '/* 隐藏Layui自动生成的下拉框 */' +
            '#detailTable .layui-form-select {' +
                'display: none !important;' +
            '}' +
            '/* 确保原生下拉框显示 */' +
            '#detailTable select[lay-ignore] {' +
                'display: block !important;' +
                'visibility: visible !important;' +
            '}'
        );
        $('head').append(style);
    }

    // 生成初始的空表格HTML
    var html = '<div class="table-container"><table id="detailTable">';

    // 生成表头 - 两行，每行14列（增加复选框列）
    html += '<thead>';
    // 第一行表头 - 基础信息标题
    html += '<tr>';
    html += '<th><input type="checkbox" id="selectAll" onchange="toggleSelectAll()"></th>';
    html += '<th>出货单No.</th><th>送货单序号</th><th>条码</th><th>产品代码</th>';
    html += '<th>线盘</th><th>客户代码</th><th>出货日</th><th>到货日期</th><th>客户订单No.</th>';
    html += '<th>明细内部备注</th><th>铜合同类别</th><th>铜合同NO</th><th>铜条件</th>';
    html += '</tr>';
    // 第二行表头 - 计算信息标题
    html += '<tr>';
    html += '<th></th>'; // 复选框列对应的空列
    html += '<th>铜货币</th><th>换算率</th><th>数量(KG)</th><th>铜base</th>';
    html += '<th>升水</th><th>换算后铜单价</th><th>0base</th><th>销售单价</th>';
    html += '<th>销售金额</th><th>产品中分类</th><th>出货计划番号</th><th>操作</th><th></th>';
    html += '</tr>';
    html += '</thead>';

    html += '</table></div>';

    // 替换原来的表格，但保留新增明细按钮
    var $container = $('#detailTable').parent();
    var $addByShipmentBtn = $container.find('#addDetailByShipmentBtn').parent();
    $container.html('');
    if ($addByShipmentBtn.length > 0) {
        $container.append($addByShipmentBtn);
    } else {
        $container.append('<div style="margin-bottom: 10px;"><button type="button" class="layui-btn layui-btn-normal" id="addDetailByShipmentBtn"><i class="layui-icon layui-icon-add-circle"></i> 新增明细</button></div>');
    }
    $container.append(html);

    console.log('已初始化可编辑的两行表格');

    // 绑定新增明细按钮事件（根据出货单No.自动填充）
    $('#addDetailByShipmentBtn').off('click').on('click', function(){
        // 弹出输入框让用户输入出货单No.
        layer.prompt({
            title: '请输入出货单No.',
            formType: 0, // 输入框类型
            value: '', // 默认值
            maxlength: 50 // 最大长度
        }, function(shipmentNo, index){
            if (shipmentNo && shipmentNo.trim()) {
                // 根据出货单No.获取明细数据
                getDetailsByShipmentNo(shipmentNo.trim());
                layer.close(index);
            } else {
                layer.msg('请输入有效的出货单No.', {icon: 2});
            }
        });
    });
}

// 添加明细行到两行表格
function addDetailRowToTwoRowTable(item) {
    var tableBody = $('#detailTable');

    // 处理数据
    var processedItem = {
        deliveryNoteNo: item.deliveryNoteNo || '',
        deliveryNoteSeq: item.deliveryNoteSeq || '',
        productCategoryCode: item.productCategoryCode || '',
        productName: item.productName || '',
        reelName: item.reelName || '',
        customerProductCode: item.customerProductCode || '',
        outboundDate: item.outboundDate || '',
        arrivalDate: item.arrivalDate || '',
        customerOrderNo: item.customerOrderNo || '',
        remark: item.remark || '',
        copperContractType: item.copperContractType || '3',
        copperContractNo: item.copperContractNo || '',
        copperCondition: item.copperCondition || '',
        currency: item.currency || '',
        conversionRate: item.conversionRate || '1.0',
        quantity: item.quantity || '',
        copperBase: item.copperBase || '',
        premium: item.premium || '',
        convertedCopperPrice: item.convertedCopperPrice || '',
        zeroBase: item.zeroBase || '',
        salesUnitPrice: item.salesUnitPrice || '',
        salesAmount: item.salesAmount || '',
        productMiddleCategory: item.productMiddleCategory || '',
        shipmentPlanNo: item.shipmentPlanNo || item.出货计划番号 || item.出库计划番号 || '',
        originalQuantity: item.originalQuantity || item.quantity || '' // 原始出库数量
    };

    // 生成铜合同类别下拉框
    var copperTypeOptions = [
        {value: '1', text: '预约铜'},
        {value: '2', text: '支给铜'},
        {value: '3', text: '一般铜'},
        {value: '4', text: '无偿'}
    ];
    var copperTypeSelect = '<select name="copperContractType" onchange="updateCopperContractTypeInTable(this)" lay-ignore>';
    copperTypeOptions.forEach(function(option) {
        var selected = (processedItem.copperContractType == option.value) ? 'selected' : '';
        copperTypeSelect += '<option value="' + option.value + '" ' + selected + '>' + option.text + '</option>';
    });
    copperTypeSelect += '</select>';

    // 生成每条记录的两行
    var recordId = 'record_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    var html = '<tbody class="record" data-record-id="' + recordId + '">';
    // 第一行：基础信息数据（14列，增加复选框列）
    html += '<tr>';
    html += '<td><input type="checkbox" name="rowSelect" class="row-checkbox" onchange="handleRowCheckboxChange(this)" checked></td>';
    html += '<td><input type="text" name="deliveryNoteNo" value="' + processedItem.deliveryNoteNo + '"></td>';
    html += '<td><input type="text" name="deliveryNoteSeq" value="' + processedItem.deliveryNoteSeq + '"></td>';
    html += '<td><input type="text" name="productCategoryCode" value="' + processedItem.productCategoryCode + '"></td>';
    html += '<td><input type="text" name="productName" value="' + processedItem.productName + '"></td>';
    html += '<td><input type="text" name="reelName" value="' + processedItem.reelName + '"></td>';
    html += '<td><input type="text" name="customerProductCode" value="' + processedItem.customerProductCode + '"></td>';
    html += '<td><input type="text" name="outboundDate" value="' + processedItem.outboundDate + '"></td>';
    html += '<td><input type="date" name="arrivalDate" value="' + processedItem.arrivalDate + '"></td>';
    html += '<td><input type="text" name="customerOrderNo" value="' + processedItem.customerOrderNo + '"></td>';
    html += '<td><input type="text" name="remark" value="' + processedItem.remark + '"></td>';
    html += '<td>' + copperTypeSelect + '</td>';
    html += '<td><input type="text" name="copperContractNo" value="' + processedItem.copperContractNo + '"></td>';
    html += '<td><input type="text" name="copperCondition" value="' + processedItem.copperCondition + '"></td>';
    html += '</tr>';
    // 第二行：计算信息数据（14列，增加复选框列对应的空列）
    html += '<tr>';
    html += '<td></td>'; // 复选框列对应的空列
    html += '<td><input type="text" name="currency" value="' + processedItem.currency + '"></td>';
    html += '<td><input type="text" name="conversionRate" value="' + processedItem.conversionRate + '"></td>';
    html += '<td><input type="text" name="quantity" value="' + processedItem.quantity + '"></td>';
    html += '<td><input type="text" name="copperBase" value="' + processedItem.copperBase + '"></td>';
    html += '<td><input type="text" name="premium" value="' + processedItem.premium + '"></td>';
    html += '<td><input type="text" name="convertedCopperPrice" value="' + processedItem.convertedCopperPrice + '" readonly></td>';
    html += '<td><input type="text" name="zeroBase" value="' + processedItem.zeroBase + '"></td>';
    html += '<td><input type="text" name="salesUnitPrice" value="' + processedItem.salesUnitPrice + '" readonly></td>';
    html += '<td><input type="text" name="salesAmount" value="' + processedItem.salesAmount + '" readonly></td>';
    html += '<td><input type="text" name="productMiddleCategory" value="' + processedItem.productMiddleCategory + '"></td>';
    html += '<td><input type="text" name="shipmentPlanNo" value="' + processedItem.shipmentPlanNo + '"></td>';
    html += '<td><button type="button" class="delete-btn" onclick="deleteRecordFromTable(this)">删除</button></td>';
    html += '<td></td>'; // 空单元格，对应第一行的铜条件位置
    // 添加隐藏字段保存原始出库数量
    html += '<input type="hidden" name="originalQuantity" value="' + processedItem.originalQuantity + '">';
    html += '</tr>';
    html += '</tbody>';

    tableBody.append(html);

    // 调试：检查下拉框是否正确插入
    setTimeout(function() {
        var selectCount = $('#detailTable select[name="copperContractType"]').length;
        console.log('新增页面下拉框数量:', selectCount);

        $('#detailTable select[name="copperContractType"]').each(function(index, select) {
            console.log('新增页面下拉框', index, ':', {
                element: select,
                visible: $(select).is(':visible'),
                display: $(select).css('display'),
                visibility: $(select).css('visibility'),
                opacity: $(select).css('opacity'),
                width: $(select).width(),
                height: $(select).height(),
                value: $(select).val(),
                html: $(select).html()
            });
        });

        // 强制显示下拉框
        $('#detailTable select[name="copperContractType"]').css({
            'display': 'block',
            'visibility': 'visible',
            'opacity': '1',
            'width': '100%',
            'height': '28px',
            'border': '1px solid #d9d9d9',
            'background': '#fff',
            'z-index': '999'
        });

        console.log('新增页面已强制设置下拉框样式');
    }, 100);
}

// 从表格中删除记录
function deleteRecordFromTable(btn) {
    var record = $(btn).closest('tbody.record');
    record.remove();
}

// 更新表格中的铜合同类别
function updateCopperContractTypeInTable(select) {
    var copperContractType = $(select).val();
    var $currentRow = $(select).closest('tr');
    var $tbody = $currentRow.closest('tbody.record');
    var $secondRow = $tbody.find('tr:last-child');

    console.log('铜合同类别已更新为:', copperContractType);

    // 获取相关字段
    var $copperContractNoSelect = $currentRow.find('select[name="copperContractNo"]');
    var $copperConditionInput = $secondRow.find('input[name="copperCondition"]');
    var $currencyInput = $secondRow.find('input[name="currency"]');

    if (copperContractType === '1' || copperContractType === '2') {
        // 预约铜/支给铜：启用铜合同NO下拉框，清空铜条件和铜货币
        $copperContractNoSelect.prop('disabled', false);
        $copperConditionInput.val('').prop('readonly', true);
        $currencyInput.val('').prop('readonly', true);

        // 加载铜合同列表
        var customerCode = $currentRow.find('input[name="customerProductCode"]').val();
        var arrivalDate = $currentRow.find('input[name="arrivalDate"]').val();
        if (customerCode && arrivalDate) {
            loadCopperContractListForRow($copperContractNoSelect, customerCode, copperContractType, arrivalDate);
        }

    } else if (copperContractType === '3' || copperContractType === '4') {
        // 一般铜/无偿：禁用铜合同NO，清空相关字段，启用铜条件选择
        $copperContractNoSelect.prop('disabled', true).empty().append('<option value="">不适用</option>');
        $copperConditionInput.val('').prop('readonly', false);
        $currencyInput.val('').prop('readonly', true);

        // 将铜条件输入框替换为下拉框
        replaceCopperConditionInputWithSelect($copperConditionInput);
    }
}

// 为单行加载铜合同列表
function loadCopperContractListForRow($select, customerCode, copperContractType, arrivalDate) {
    if (!customerCode || !copperContractType) {
        return;
    }

    // 计算使用月（从到货日期提取年月）
    var useMonth = '';
    if (arrivalDate) {
        var dateParts = arrivalDate.split('-');
        if (dateParts.length >= 2) {
            useMonth = dateParts[0] + '-' + dateParts[1];
        }
    }

    console.log('为单行加载铜合同列表:', customerCode, copperContractType, useMonth);

    $.ajax({
        url: baselocation + '/order/salesLogin/getCopperContractList',
        type: 'POST',
        data: {
            customerCode: customerCode,
            copperContractType: copperContractType,
            useMonth: useMonth,
            status: '0'
        },
        success: function(result) {
            console.log('铜合同列表返回:', result);

            if (result.code === 1 && result.data) {
                $select.empty().append('<option value="">请选择</option>');

                $.each(result.data, function(index, item) {
                    var contractNo = item.copperSignNo || '';
                    var copperCondition = item.copperCondition || '';
                    var currency = item.currency || '';

                    if (contractNo) {
                        var optionHtml = '<option value="' + contractNo + '"' +
                                       ' data-copper-condition="' + copperCondition + '"' +
                                       ' data-currency="' + currency + '">' +
                                       contractNo + '</option>';
                        $select.append(optionHtml);
                    }
                });

                // 绑定选择事件
                $select.off('change').on('change', function() {
                    var selectedOption = $(this).find('option:selected');
                    var copperCondition = selectedOption.data('copper-condition');
                    var currency = selectedOption.data('currency');
                    var copperContractNo = selectedOption.val();
                    var $currentRow = $(this).closest('tr');
                    var $tbody = $currentRow.closest('tbody.record');
                    var $secondRow = $tbody.find('tr:last-child');

                    // 填入铜条件和铜货币
                    $secondRow.find('input[name="copperCondition"]').val(copperCondition || '');
                    $secondRow.find('input[name="currency"]').val(currency || '');

                    console.log('选择了铜合同:', copperContractNo, '铜条件:', copperCondition, '货币:', currency);

                    // 查询铜合同详情获取铜base（预约铜/支给铜）
                    if (copperContractNo) {
                        var arrivalDate = $currentRow.find('input[name="arrivalDate"]').val();
                        if (arrivalDate) {
                            var useMonth = arrivalDate.substring(0, 7); // 截取年月部分
                            queryCopperBaseForContractCopper($tbody, copperContractNo, useMonth);
                        }
                    }
                });

            } else {
                $select.empty().append('<option value="">无可用合同</option>');
                console.log('获取铜合同列表失败:', result.message);
            }
        },
        error: function(xhr, status, error) {
            console.log('加载铜合同列表异常:', error);
            $select.empty().append('<option value="">加载失败</option>');
        }
    });
}

// 将铜条件输入框替换为下拉框
function replaceCopperConditionInputWithSelect($input) {
    var currentValue = $input.val();
    var inputName = $input.attr('name');
    var $parentTd = $input.parent();

    // 创建下拉框
    var $select = $('<select name="' + inputName + '"></select>');
    $select.append('<option value="">请选择</option>');

    // 替换输入框
    $input.replaceWith($select);

    // 加载铜条件列表
    loadCopperConditionListForSelect($select, currentValue);
}

// 为下拉框加载铜条件列表
function loadCopperConditionListForSelect($select, selectedValue) {
    $.ajax({
        url: baselocation + '/order/orderEntry/getCopperConditionList',
        type: 'POST',
        success: function(result) {
            console.log('铜条件列表返回:', result);

            if (result.code === 1 && result.data) {
                $select.empty().append('<option value="">请选择</option>');

                $.each(result.data, function(index, item) {
                    var condition = '';
                    var currency = '';

                    if (typeof item === 'object') {
                        condition = item.condition || item.copperCondition || '';
                        currency = item.currency || '';
                    } else {
                        condition = item;
                    }

                    if (condition) {
                        var selected = (condition === selectedValue) ? 'selected' : '';
                        var optionHtml = '<option value="' + condition + '" ' + selected +
                                       ' data-currency="' + currency + '">' +
                                       condition + '</option>';
                        $select.append(optionHtml);
                        console.log('添加铜条件选项:', condition, '货币:', currency);
                    }
                });

                // 绑定选择事件
                $select.off('change').on('change', function() {
                    var selectedOption = $(this).find('option:selected');
                    var currency = selectedOption.data('currency');
                    var copperCondition = selectedOption.val();

                    console.log('铜条件选择事件触发:', copperCondition, '货币:', currency);

                    // 获取货币字段（在第二行）
                    var $currentRow = $(this).closest('tr');
                    var $tbody = $currentRow.closest('tbody.record');
                    var $secondRow = $tbody.find('tr:last-child');
                    var $currencyInput = $secondRow.find('input[name="currency"]');

                    console.log('找到的货币字段数量:', $currencyInput.length);
                    console.log('货币字段元素:', $currencyInput);

                    if (currency) {
                        // 直接设置货币
                        $currencyInput.val(currency);
                        console.log('直接设置铜条件货币:', currency, '设置后的值:', $currencyInput.val());
                    } else if (copperCondition) {
                        // 如果没有货币信息，通过接口获取
                        console.log('通过接口获取铜条件货币:', copperCondition);
                        getCopperConditionDetail(copperCondition, $(this));
                    } else {
                        // 清空货币
                        $currencyInput.val('');
                        console.log('清空铜条件货币');
                    }
                });

            } else {
                console.log('获取铜条件列表失败:', result.message);
            }
        },
        error: function(xhr, status, error) {
            console.log('加载铜条件列表异常:', error);
        }
    });
}

// 获取铜条件详情（包括货币）
function getCopperConditionDetail(copperCondition, $select) {
    $.ajax({
        url: baselocation + '/salesCommon/copperConditions/json',
        type: 'POST',
        data: {
            copperCondition: copperCondition
        },
        success: function(result) {
            console.log('铜条件详情返回:', result);

            if (result.code === 1 && result.data) {
                var currency = result.data.currency || '';

                // 设置货币字段
                var $currentRow = $select.closest('tr');
                var $tbody = $currentRow.closest('tbody.record');
                var $secondRow = $tbody.find('tr:last-child');
                var $currencyInput = $secondRow.find('input[name="currency"]');
                $currencyInput.val(currency);

                console.log('设置铜条件货币:', currency);
            } else {
                console.log('获取铜条件详情失败:', result.message);
            }
        },
        error: function(xhr, status, error) {
            console.log('获取铜条件详情异常:', error);
        }
    });
}

// 获取表格中选中的数据
function getTableData() {
    var data = [];
    $('#detailTable tbody.record').each(function() {
        var record = $(this);

        // 检查该行是否被选中
        var checkbox = record.find('input[name="rowSelect"]');
        if (!checkbox.is(':checked')) {
            console.log('跳过未选中的记录');
            return; // 跳过未选中的行
        }

        var firstRow = record.find('tr:first-child');
        var secondRow = record.find('tr:last-child');

        var item = {
            deliveryNoteNo: firstRow.find('input[name="deliveryNoteNo"]').val(),
            deliveryNoteSeq: firstRow.find('input[name="deliveryNoteSeq"]').val(),
            productCategoryCode: firstRow.find('input[name="productCategoryCode"]').val(),
            productName: firstRow.find('input[name="productName"]').val(),
            reelName: firstRow.find('input[name="reelName"]').val(),
            customerProductCode: firstRow.find('input[name="customerProductCode"]').val(),
            outboundDate: firstRow.find('input[name="outboundDate"]').val(),
            arrivalDate: firstRow.find('input[name="arrivalDate"]').val(),
            customerOrderNo: firstRow.find('input[name="customerOrderNo"]').val(),
            remark: firstRow.find('input[name="remark"]').val(),
            copperContractType: firstRow.find('select[name="copperContractType"]').val(),
            copperContractNo: firstRow.find('input[name="copperContractNo"]').val() || firstRow.find('select[name="copperContractNo"]').val(),
            copperCondition: firstRow.find('input[name="copperCondition"]').val() || firstRow.find('select[name="copperCondition"]').val(),
            currency: secondRow.find('input[name="currency"]').val(),
            conversionRate: secondRow.find('input[name="conversionRate"]').val(),
            quantity: secondRow.find('input[name="quantity"]').val(),
            copperBase: secondRow.find('input[name="copperBase"]').val(),
            premium: secondRow.find('input[name="premium"]').val(),
            convertedCopperPrice: secondRow.find('input[name="convertedCopperPrice"]').val(),
            zeroBase: secondRow.find('input[name="zeroBase"]').val(),
            salesUnitPrice: secondRow.find('input[name="salesUnitPrice"]').val(),
            salesAmount: secondRow.find('input[name="salesAmount"]').val(),
            productMiddleCategory: secondRow.find('input[name="productMiddleCategory"]').val(),
            shipmentPlanNo: secondRow.find('input[name="shipmentPlanNo"]').val()
        };

        data.push(item);
    });

    return data;
}

// 处理铜合同类别变更逻辑
function handleCopperContractTypeChange(copperContractType, $currentRow, customerCode, arrivalDate) {
    console.log('处理铜合同类别变更:', copperContractType, customerCode, arrivalDate);
    console.log('当前行HTML:', $currentRow.html().substring(0, 200) + '...');

    // 获取铜合同NO和铜条件的输入框（都在同一行）
    var $copperContractNoInput = $currentRow.find(
      'select[name="copperContractNo"]'
    );
    var $copperConditionInput = $currentRow.find('input[name="copperCondition"]');

    console.log('找到的铜合同NO输入框数量:', $copperContractNoInput.length);
    console.log('找到的铜条件输入框数量:', $copperConditionInput.length);

    // 如果在当前行没找到，尝试查找所有相关输入框
    if ($copperContractNoInput.length === 0) {
        console.log('当前行未找到铜合同NO输入框，查找当前行所有input:');
        $currentRow.find('input').each(function(index) {
            console.log('  input[' + index + ']:', $(this).attr('name'), '=', $(this).val());
        });

        // 尝试在整个tbody中查找
        var $tbody = $currentRow.closest('tbody.record');
        $copperContractNoInput = $tbody.find('select[name="copperContractNo"]');
        $copperConditionInput = $tbody.find('input[name="copperCondition"]');
        console.log('在整个tbody中找到的铜合同NO输入框数量:', $copperContractNoInput.length);
        console.log('在整个tbody中找到的铜条件输入框数量:', $copperConditionInput.length);
    }

    if (copperContractType === '1' || copperContractType === '2') {
        // 预约铜或支给铜：铜合同下拉框可选
        console.log('预约铜/支给铜模式');

        if ($copperContractNoInput.length > 0) {
            // 清空铜合同和铜条件字段
            $copperContractNoInput.val('');
            $copperConditionInput.val('');

            // 直接在当前位置创建下拉框并加载数据
            createCopperContractSelectInPlace($copperContractNoInput, customerCode, copperContractType, arrivalDate);
        } else {
            console.log('错误：未找到铜合同NO输入框');
        }

        // 铜条件输入框禁用，等待从合同中获取
        $copperConditionInput.prop('disabled', true).val('').attr('placeholder', '选择铜合同后自动填入');

    } else if (copperContractType === '3' || copperContractType === '4') {
        // 一般铜或无偿：铜合同下拉框disabled
        console.log('一般铜/无偿模式');

        // 清空铜合同和铜条件字段
        if ($copperContractNoInput.length > 0) {
            $copperContractNoInput.val('');
        }
        $copperConditionInput.val('');

        // 将铜合同NO字段设为禁用状态
        disableCopperContractField($copperContractNoInput);

        if (copperContractType === '3') {
            // 一般铜：先创建铜条件下拉框，然后查询升水表获取默认值
            console.log('一般铜模式：查询升水表');

            // 铜条件字段启用，并加载铜条件表数据
            $copperConditionInput.prop('disabled', false).attr('placeholder', '请选择铜条件');
            createCopperConditionSelectInPlace($copperConditionInput);

            // 查询升水表数据设置默认值
            queryAscendingWaterForGeneralCopper($currentRow, customerCode, arrivalDate);
        } else {
            // 无偿：铜条件字段启用，并加载铜条件表数据
            $copperConditionInput.prop('disabled', false).attr('placeholder', '请选择铜条件');
            createCopperConditionSelectInPlace($copperConditionInput);
        }

    } else {
        // 未选择或选择了其他值：重置所有字段
        console.log('重置模式');

        // 清空所有相关字段
        if ($copperContractNoInput.length > 0) {
            $copperContractNoInput.val('');
        }
        $copperConditionInput.val('');

        // 恢复字段为可编辑的输入框状态
        restoreCopperContractField($copperContractNoInput);
        $copperConditionInput.prop('disabled', false).attr('placeholder', '');
    }
}

// 将铜合同NO输入框替换为下拉框
function replaceCopperContractInputWithSelect($input, customerCode, copperContractType, arrivalDate) {
    var currentValue = $input.val();
    var inputName = $input.attr('name');
    var $parentTd = $input.closest('td'); // 获取父级td元素

    console.log('=== 替换铜合同输入框为下拉框 ===');
    console.log('原输入框:', $input.length, '个');
    console.log('原输入框name:', inputName);
    console.log('原输入框value:', currentValue);
    console.log('父级td:', $parentTd.length, '个');

    // 创建下拉框
    var $select = $('<select name="' + inputName + '" class="layui-input"></select>');
    $select.append('<option value="">加载中...</option>');

    console.log('创建的下拉框HTML:', $select[0].outerHTML);

    // 替换输入框
    $input.replaceWith($select);

    console.log('替换后，页面中该name的元素:', $('*[name="' + inputName + '"]').length, '个');
    console.log('替换后的元素类型:', $('*[name="' + inputName + '"]')[0] ? $('*[name="' + inputName + '"]')[0].tagName : 'none');
    console.log('===============================');

    // 直接加载铜合同列表，传递父级td用于精确定位
    loadCopperContractList(customerCode, copperContractType, arrivalDate, $parentTd, currentValue);
}

// 将铜合同NO下拉框替换为禁用的输入框
function replaceCopperContractSelectWithInput($element) {
    var inputName = $element.attr('name');
    var $input = $('<input type="text" name="' + inputName + '" class="layui-input" disabled value="不适用">');
    $element.replaceWith($input);
}

// 加载铜合同列表
function loadCopperContractList(customerCode, copperContractType, arrivalDate, $parentTd, selectedValue) {
    console.log('=== 开始加载铜合同列表 ===');
    console.log('父级td:', $parentTd.length, '个');
    // 详细检查每个必要参数
    var missingParams = [];
    if (!customerCode) missingParams.push('客户品目C（客户代码）');
    if (!copperContractType) missingParams.push('铜合同类别');
    if (!arrivalDate) missingParams.push('到货日期');

    if (missingParams.length > 0) {
        var message = '无法加载铜合同列表，请先填写：' + missingParams.join('、');
        console.log(message);
        layer.msg(message, {icon: 0, time: 3000});
        $select.empty().append('<option value="">请先填写' + missingParams.join('、') + '</option>');
        return;
    }

    // 从到货日期提取年月
    var useMonth = arrivalDate.substring(0, 7); // YYYY-MM格式

    console.log('加载铜合同列表参数:', {
        customerCode: customerCode,
        copperContractType: copperContractType,
        useMonth: useMonth
    });

    $.ajax({
        url: baselocation + '/order/salesLogin/getCopperContractList',
        type: 'POST',
        data: {
            customerCode: customerCode,
            copperContractType: copperContractType,
            useMonth: useMonth,
            status: '0'
        },
        success: function(result) {
            console.log('=== 铜合同列表AJAX返回 ===');
            console.log('完整返回结果:', result);
            console.log('返回码:', result.code);
            console.log('返回数据:', result.data);
            console.log('数据类型:', typeof result.data);
            console.log('数据长度:', result.data ? result.data.length : 'null');

            if (result.code === 1 && result.data) {
                console.log('开始填充下拉框选项...');

                // 使用父级td来精确定位下拉框
                var $domSelect = $parentTd.find('select[name="copperContractNo"]');
                console.log('通过父级td找到的下拉框数量:', $domSelect.length);

                if ($domSelect.length > 0) {
                    // 使用DOM中实际的下拉框
                    $domSelect.empty().append('<option value="">请选择</option>');

                    $.each(result.data, function(index, item) {
                        console.log('处理第' + index + '项数据:', item);
                        console.log('铜合同NO字段:', item.copperSignNo);
                        console.log('铜条件字段:', item.copperCondition);

                        // 使用正确的字段名
                        var contractNo = item.copperSignNo || '';
                        var copperCondition = item.copperCondition || '';
                        var currency = item.currency || '';

                        if (contractNo) {
                            var selected = (contractNo === selectedValue) ? 'selected' : '';
                            var optionHtml = '<option value="' + contractNo + '" ' + selected +
                                           ' data-copper-condition="' + copperCondition + '"' +
                                           ' data-currency="' + currency + '">' +
                                           contractNo + '</option>';
                            $domSelect.append(optionHtml);
                            console.log('已添加选项到DOM:', contractNo, '铜条件:', copperCondition, '货币:', currency);
                        } else {
                            console.log('警告：第' + index + '项数据没有有效的合同号');
                        }
                    });

                    console.log('DOM下拉框最终选项数量:', $domSelect.find('option').length);
                    console.log('DOM下拉框HTML:', $domSelect.html());

                    // 绑定选择事件到DOM中的下拉框
                    $domSelect.off('change').on('change', function() {
                        var selectedOption = $(this).find('option:selected');
                        var copperCondition = selectedOption.data('copper-condition');
                        var currency = selectedOption.data('currency');
                        var copperContractNo = selectedOption.val();
                        var $copperConditionInput = $(this).closest('tr').find('input[name="copperCondition"]');

                        // 填入铜条件
                        $copperConditionInput.val(copperCondition || '');

                        // 获取铜货币字段（在第二行）
                        var $currentRow = $(this).closest('tr');
                        var $tbody = $currentRow.closest('tbody.record');
                        var $currencyInput = $tbody.find('input[name="currency"]');

                        // 直接设置货币信息（从选项的data属性中获取）
                        $currencyInput.val(currency || '');

                        console.log('选择了铜合同:', copperContractNo, '铜条件:', copperCondition, '货币:', currency);
                    });

                } else {
                    console.log('错误：DOM中未找到铜合同下拉框');
                }

            } else {
                console.log('获取铜合同列表失败或无数据');
                // 也要操作DOM中的下拉框
                var $domSelect = $parentTd.find('select[name="copperContractNo"]');
                if ($domSelect.length > 0) {
                    $domSelect.empty().append('<option value="">无可用合同</option>');
                }
                layer.msg(result.message || '获取铜合同列表失败', {icon: 2});
            }
            console.log('=========================');
        },
        error: function(xhr, status, error) {
            console.log('加载铜合同列表失败:', error);
            var $domSelect = $parentTd.find('select[name="copperContractNo"]');
            if ($domSelect.length > 0) {
                $domSelect.empty().append('<option value="">加载失败</option>');
            }
            layer.msg('加载铜合同列表失败', {icon: 2});
        }
    });
}

// 加载铜条件选项
function loadCopperConditionOptions($input) {
    $.ajax({
        url: baselocation + '/order/orderEntry/getCopperConditionList',
        type: 'POST',
        success: function(result) {
            console.log('铜条件列表返回:', result);
            if (result.code === 1 && result.data) {
                // 将输入框替换为下拉框
                var inputName = $input.attr('name');
                var currentValue = $input.val();
                var $select = $('<select name="' + inputName + '" class="layui-input"></select>');
                $select.append('<option value="">请选择</option>');

                $.each(result.data, function(index, item) {
                    var value = typeof item === 'object' ? item.copperCondition : item;
                    var selected = (value === currentValue) ? 'selected' : '';
                    $select.append('<option value="' + value + '" ' + selected + '>' + value + '</option>');
                });

                $input.replaceWith($select);
            } else {
                layer.msg(result.message || '获取铜条件列表失败', {icon: 2});
            }
        },
        error: function(xhr, status, error) {
            console.log('加载铜条件列表失败:', error);
            layer.msg('加载铜条件列表失败', {icon: 2});
        }
    });
}

// 测试函数：手动创建下拉框并添加选项
function testCopperContractDropdown() {
    console.log('=== 开始测试铜合同下拉框 ===');

    // 找到第一个铜合同NO输入框
    var $input = $('input[name="copperContractNo"]').first();
    if ($input.length === 0) {
        console.log('未找到铜合同NO输入框');
        return;
    }

    console.log('找到输入框:', $input.length, '个');

    // 创建测试下拉框
    var $testSelect = $('<select name="copperContractNo" class="layui-input" style="background-color: yellow; border: 2px solid red;"></select>');
    $testSelect.append('<option value="">请选择</option>');
    $testSelect.append('<option value="TEST001">测试合同001</option>');
    $testSelect.append('<option value="TEST002">测试合同002</option>');

    console.log('创建的测试下拉框HTML:', $testSelect[0].outerHTML);

    // 替换输入框
    $input.replaceWith($testSelect);

    console.log('替换完成，检查页面中的下拉框...');
    var $checkSelect = $('select[name="copperContractNo"]');
    console.log('页面中的下拉框数量:', $checkSelect.length);
    if ($checkSelect.length > 0) {
        console.log('下拉框HTML:', $checkSelect.html());
        console.log('下拉框选项数量:', $checkSelect.find('option').length);
    }

    console.log('=== 测试完成 ===');
}

// 将测试函数暴露到全局，方便在控制台调用
window.testCopperContractDropdown = testCopperContractDropdown;

// 调试函数：查找所有铜合同相关字段
function debugCopperContractFields() {
    console.log('=== 调试铜合同相关字段 ===');

    // 查找所有铜合同类别下拉框
    var $copperTypeSelects = $('select[name="copperContractType"]');
    console.log('找到铜合同类别下拉框数量:', $copperTypeSelects.length);

    $copperTypeSelects.each(function(index) {
        var $select = $(this);
        var $row = $select.closest('tr');
        var $tbody = $select.closest('tbody.record');

        console.log('--- 铜合同类别下拉框 ' + index + ' ---');
        console.log('当前值:', $select.val());
        console.log('所在行的所有input字段:');
        $row.find('input').each(function(i) {
            console.log('  input[' + i + ']:', $(this).attr('name'), '=', $(this).val());
        });

        console.log('所在tbody的铜合同NO字段数量:', $tbody.find('input[name="copperContractNo"]').length);
        console.log('所在tbody的铜条件字段数量:', $tbody.find('input[name="copperCondition"]').length);
    });

    // 查找所有铜合同NO字段
    var $copperContractInputs = $('input[name="copperContractNo"]');
    console.log('全局铜合同NO输入框数量:', $copperContractInputs.length);

    // 查找所有铜条件字段
    var $copperConditionInputs = $('input[name="copperCondition"]');
    console.log('全局铜条件输入框数量:', $copperConditionInputs.length);

    console.log('=== 调试完成 ===');
}

// 暴露调试函数到全局
window.debugCopperContractFields = debugCopperContractFields;
window.initializeCopperContractTypeFields = initializeCopperContractTypeFields;

// 初始化所有铜合同类别字段的状态
function initializeCopperContractTypeFields() {
    console.log('=== 初始化铜合同类别字段状态 ===');

    // 查找所有铜合同类别下拉框
    $('select[name="copperContractType"]').each(function() {
        var $select = $(this);
        var copperContractType = $select.val();
        var $currentRow = $select.closest('tr');

        console.log('初始化铜合同类别:', copperContractType);

        if (copperContractType && copperContractType !== '') {
            // 获取必要的参数
            var customerCode = $currentRow.find('input[name="customerProductCode"]').val();
            var arrivalDate = $currentRow.find('input[name="arrivalDate"]').val();

            console.log('初始化参数:', {
                copperContractType: copperContractType,
                customerCode: customerCode,
                arrivalDate: arrivalDate
            });

            // 触发铜合同类别变更逻辑，但不清空现有值
            handleCopperContractTypeChangeForInit(copperContractType, $currentRow, customerCode, arrivalDate);
        }
    });

    console.log('=== 初始化完成 ===');
}

// 初始化时的铜合同类别变更处理（不清空现有值）
function handleCopperContractTypeChangeForInit(copperContractType, $currentRow, customerCode, arrivalDate) {
    console.log('初始化处理铜合同类别变更:', copperContractType);

    // 获取铜合同NO和铜条件的输入框
    var $copperContractNoInput = $currentRow.find('input[name="copperContractNo"]');
    var $copperConditionInput = $currentRow.find('input[name="copperCondition"]');

    // 如果在当前行没找到，尝试在整个tbody中查找
    if ($copperContractNoInput.length === 0) {
        var $tbody = $currentRow.closest('tbody.record');
        $copperContractNoInput = $tbody.find('input[name="copperContractNo"]');
        $copperConditionInput = $tbody.find('input[name="copperCondition"]');
    }

    console.log('找到的字段数量 - 铜合同NO:', $copperContractNoInput.length, '铜条件:', $copperConditionInput.length);

    if (copperContractType === '1' || copperContractType === '2') {
        // 预约铜或支给铜：铜合同下拉框可选
        console.log('初始化：预约铜/支给铜模式');

        if ($copperContractNoInput.length > 0) {
            var currentCopperContractValue = $copperContractNoInput.val();

            // 如果当前是输入框且有值，需要创建下拉框并保持选中状态
            if ($copperContractNoInput[0].tagName === 'INPUT') {
                createCopperContractSelectInPlaceForInit($copperContractNoInput, customerCode, copperContractType, arrivalDate, currentCopperContractValue);
            }
        }

        // 铜条件输入框禁用
        $copperConditionInput.prop('disabled', true).attr('placeholder', '选择铜合同后自动填入');

    } else if (copperContractType === '3' || copperContractType === '4') {
        // 一般铜或无偿：铜合同下拉框disabled
        console.log('初始化：一般铜/无偿模式');

        // 将铜合同NO字段设为禁用状态
        disableCopperContractField($copperContractNoInput);

        if (copperContractType === '3') {
            // 一般铜：查询升水表获取铜条件和升水单价
            console.log('初始化：一般铜模式，查询升水表');

            // 如果已有铜条件值，保持现有值；否则查询升水表
            if ($copperConditionInput.length > 0) {
                var currentCopperConditionValue = $copperConditionInput.val();

                if (currentCopperConditionValue) {
                    // 已有值，保持只读状态
                    $copperConditionInput.prop('disabled', false).prop('readonly', true);
                } else {
                    // 无值，查询升水表
                    $copperConditionInput.prop('disabled', false).prop('readonly', true).attr('placeholder', '查询升水表中...');
                    queryAscendingWaterForGeneralCopper($currentRow, customerCode, arrivalDate);
                }
            }
        } else {
            // 无偿：铜条件字段启用，如果是输入框且有值，需要创建下拉框并保持选中状态
            if ($copperConditionInput.length > 0) {
                var currentCopperConditionValue = $copperConditionInput.val();

                if ($copperConditionInput[0].tagName === 'INPUT') {
                    createCopperConditionSelectInPlaceForInit($copperConditionInput, currentCopperConditionValue);
                }
            }
        }
    }
}

// 禁用铜合同字段
function disableCopperContractField($input) {
    if ($input.length === 0) return;

    // 如果是下拉框，禁用它
    if ($input[0].tagName === 'SELECT') {
        $input.prop('disabled', true).empty().append('<option value="">不适用</option>');
    } else {
        // 如果是输入框，设为禁用状态
        $input.prop('disabled', true).val('不适用');
    }
}

// 恢复铜合同字段为可编辑输入框
function restoreCopperContractField($input) {
    if ($input.length === 0) return;

    var inputName = $input.attr('name');

    // 如果当前是下拉框，替换为输入框
    if ($input[0].tagName === 'SELECT') {
        var $newInput = $('<input type="text" name="' + inputName + '" class="layui-input" value="">');
        $input.replaceWith($newInput);
    } else {
        // 如果是输入框，启用并清空
        $input.prop('disabled', false).val('');
    }
}

// 在原地创建铜条件下拉框并加载数据
function createCopperConditionSelectInPlace($input) {
    console.log('=== 在原地创建铜条件下拉框 ===');

    var currentValue = $input.val();
    var inputName = $input.attr('name');

    // 创建下拉框
    var $select = $('<select name="' + inputName + '" class="layui-input"></select>');
    $select.append('<option value="">加载中...</option>');

    // 替换输入框
    $input.replaceWith($select);

    console.log('已替换为铜条件下拉框，开始加载数据...');

    $.ajax({
        url: baselocation + '/order/orderEntry/getCopperConditionList',
        type: 'POST',
        success: function(result) {
            console.log('=== 铜条件列表AJAX返回 ===');
            console.log('返回结果:', result);

            if (result.code === 1 && result.data) {
                console.log('开始填充铜条件选项，数据数量:', result.data.length);

                // 清空并重新填充选项
                $select.empty().append('<option value="">请选择</option>');

                $.each(result.data, function(index, item) {
                    var condition = '';
                    var currency = '';

                    if (typeof item === 'object') {
                        condition = item.condition || item.copperCondition || '';
                        currency = item.currency || '';
                    } else {
                        condition = item;
                    }

                    if (condition) {
                        var selected = (condition === currentValue) ? 'selected' : '';
                        var optionHtml = '<option value="' + condition + '" ' + selected +
                                       ' data-currency="' + currency + '">' +
                                       condition + '</option>';
                        $select.append(optionHtml);
                        console.log('添加铜条件选项:', condition, '货币:', currency);
                    }
                });

                console.log('铜条件最终选项数量:', $select.find('option').length);

                // 注意：不在这里绑定事件，而是使用全局事件委托

            } else {
                console.log('获取铜条件列表失败或无数据');
                $select.empty().append('<option value="">无可用铜条件</option>');
                layer.msg(result.message || '获取铜条件列表失败', {icon: 2});
            }
            console.log('=========================');
        },
        error: function(xhr, status, error) {
            console.log('加载铜条件列表失败:', error);
            $select.empty().append('<option value="">加载失败</option>');
            layer.msg('加载铜条件列表失败', {icon: 2});
        }
    });

    console.log('=== 铜条件创建完成 ===');
}

// 初始化时创建铜合同下拉框（保持现有值）
function createCopperContractSelectInPlaceForInit($input, customerCode, copperContractType, arrivalDate, currentValue) {
    console.log('=== 初始化创建铜合同下拉框 ===');
    console.log('保持现有值:', currentValue);

    var inputName = $input.attr('name');

    // 创建下拉框
    var $select = $('<select name="' + inputName + '" class="layui-input"></select>');
    $select.append('<option value="">加载中...</option>');

    // 替换输入框
    $input.replaceWith($select);

    // 检查必要参数
    if (!customerCode || !copperContractType || !arrivalDate) {
        $select.empty().append('<option value="">参数不完整</option>');
        return;
    }

    // 从到货日期提取年月
    var useMonth = arrivalDate.substring(0, 7);

    $.ajax({
        url: baselocation + '/order/salesLogin/getCopperContractList',
        type: 'POST',
        data: {
            customerCode: customerCode,
            copperContractType: copperContractType,
            useMonth: useMonth,
            status: '0'
        },
        success: function(result) {
            if (result.code === 1 && result.data) {
                $select.empty().append('<option value="">请选择</option>');

                var hasCurrentValue = false;
                $.each(result.data, function(index, item) {
                    var contractNo = item.copperSignNo || '';
                    var copperCondition = item.copperCondition || '';
                    var currency = item.currency || '';

                    if (contractNo) {
                        var selected = (contractNo === currentValue) ? 'selected' : '';
                        if (contractNo === currentValue) {
                            hasCurrentValue = true;
                        }

                        $select.append('<option value="' + contractNo + '" ' + selected +
                                     ' data-copper-condition="' + copperCondition + '"' +
                                     ' data-currency="' + currency + '">' +
                                     contractNo + '</option>');
                    }
                });

                // 如果当前值不在列表中，添加为选项
                if (currentValue && !hasCurrentValue) {
                    $select.append('<option value="' + currentValue + '" selected>' + currentValue + '</option>');
                }

                // 绑定选择事件
                $select.off('change').on('change', function() {
                    var selectedOption = $(this).find('option:selected');
                    var copperCondition = selectedOption.data('copper-condition');
                    var currency = selectedOption.data('currency');
                    var copperContractNo = selectedOption.val();
                    var $copperConditionInput = $(this).closest('tr').find('input[name="copperCondition"]');

                    // 填入铜条件
                    $copperConditionInput.val(copperCondition || '');

                    // 获取铜货币字段（在第二行）
                    var $currentRow = $(this).closest('tr');
                    var $tbody = $currentRow.closest('tbody.record');
                    var $currencyInput = $tbody.find('input[name="currency"]');

                    // 直接设置货币信息（从选项的data属性中获取）
                    $currencyInput.val(currency || '');

                    console.log('选择了铜合同:', copperContractNo, '铜条件:', copperCondition, '货币:', currency);
                });

                console.log('初始化铜合同下拉框完成，当前值:', $select.val());

            } else {
                $select.empty().append('<option value="">无可用合同</option>');
            }
        },
        error: function(xhr, status, error) {
            $select.empty().append('<option value="">加载失败</option>');
        }
    });
}

// 初始化时创建铜条件下拉框（保持现有值）
function createCopperConditionSelectInPlaceForInit($input, currentValue) {
    console.log('=== 初始化创建铜条件下拉框 ===');
    console.log('保持现有值:', currentValue);

    var inputName = $input.attr('name');

    // 创建下拉框
    var $select = $('<select name="' + inputName + '" class="layui-input"></select>');
    $select.append('<option value="">加载中...</option>');

    // 替换输入框
    $input.replaceWith($select);

    $.ajax({
        url: baselocation + '/order/orderEntry/getCopperConditionList',
        type: 'POST',
        success: function(result) {
            if (result.code === 1 && result.data) {
                $select.empty().append('<option value="">请选择</option>');

                var hasCurrentValue = false;
                $.each(result.data, function(index, item) {
                    var condition = '';
                    var currency = '';

                    if (typeof item === 'object') {
                        condition = item.condition || item.copperCondition || '';
                        currency = item.currency || '';
                    } else {
                        condition = item;
                    }

                    if (condition) {
                        var selected = (condition === currentValue) ? 'selected' : '';
                        if (condition === currentValue) {
                            hasCurrentValue = true;
                        }
                        var optionHtml = '<option value="' + condition + '" ' + selected +
                                       ' data-currency="' + currency + '">' +
                                       condition + '</option>';
                        $select.append(optionHtml);
                    }
                });

                // 如果当前值不在列表中，添加为选项
                if (currentValue && !hasCurrentValue) {
                    $select.append('<option value="' + currentValue + '" selected>' + currentValue + '</option>');
                }

                // 注意：事件绑定已通过全局事件委托处理，这里不需要重复绑定

                console.log('初始化铜条件下拉框完成，当前值:', $select.val());

            } else {
                $select.empty().append('<option value="">无可用铜条件</option>');
            }
        },
        error: function(xhr, status, error) {
            $select.empty().append('<option value="">加载失败</option>');
        }
    });
}

// 直接在原地创建铜合同下拉框并加载数据
function createCopperContractSelectInPlace($input, customerCode, copperContractType, arrivalDate) {
    console.log('=== 在原地创建铜合同下拉框 ===');

    var currentValue = $input.val();
    var inputName = $input.attr('name');

    // 创建下拉框
    var $select = $('<select name="' + inputName + '" class="layui-input"></select>');
    $select.append('<option value="">加载中...</option>');

    // 替换输入框
    $input.replaceWith($select);

    console.log('已替换为下拉框，开始加载数据...');

    // 检查必要参数
    var missingParams = [];
    if (!customerCode) missingParams.push('客户品目C（客户代码）');
    if (!copperContractType) missingParams.push('铜合同类别');
    if (!arrivalDate) missingParams.push('到货日期');

    if (missingParams.length > 0) {
        var message = '无法加载铜合同列表，请先填写：' + missingParams.join('、');
        console.log(message);
        layer.msg(message, {icon: 0, time: 3000});
        $select.empty().append('<option value="">请先填写' + missingParams.join('、') + '</option>');
        return;
    }

    // 从到货日期提取年月
    var useMonth = arrivalDate.substring(0, 7); // YYYY-MM格式

    console.log('加载铜合同列表参数:', {
        customerCode: customerCode,
        copperContractType: copperContractType,
        useMonth: useMonth
    });

    $.ajax({
        url: baselocation + '/order/salesLogin/getCopperContractList',
        type: 'POST',
        data: {
            customerCode: customerCode,
            copperContractType: copperContractType,
            useMonth: useMonth,
            status: '0'
        },
        success: function(result) {
            console.log('=== 铜合同列表AJAX返回 ===');
            console.log('返回结果:', result);

            if (result.code === 1 && result.data) {
                console.log('开始填充下拉框选项，数据数量:', result.data.length);

                // 清空并重新填充选项
                $select.empty().append('<option value="">请选择</option>');

                $.each(result.data, function(index, item) {
                    var contractNo = item.copperSignNo || '';
                    var copperCondition = item.copperCondition || '';
                    var currency = item.currency || '';

                    if (contractNo) {
                        var selected = (contractNo === currentValue) ? 'selected' : '';
                        var optionHtml = '<option value="' + contractNo + '" ' + selected +
                                       ' data-copper-condition="' + copperCondition + '"' +
                                       ' data-currency="' + currency + '">' +
                                       contractNo + '</option>';
                        $select.append(optionHtml);
                        console.log('添加选项:', contractNo, '铜条件:', copperCondition, '货币:', currency);
                    }
                });

                console.log('最终选项数量:', $select.find('option').length);
                console.log('下拉框HTML:', $select.html());

                // 绑定选择事件
                $select.off('change').on('change', function() {
                    var selectedOption = $(this).find('option:selected');
                    var copperCondition = selectedOption.data('copper-condition');
                    var currency = selectedOption.data('currency');
                    var copperContractNo = selectedOption.val();
                    var $copperConditionInput = $(this).closest('tr').find('input[name="copperCondition"]');

                    // 填入铜条件
                    $copperConditionInput.val(copperCondition || '');

                    // 获取铜货币字段（在第二行）
                    var $currentRow = $(this).closest('tr');
                    var $tbody = $currentRow.closest('tbody.record');
                    var $currencyInput = $tbody.find('input[name="currency"]');

                    // 直接设置货币信息（从选项的data属性中获取）
                    $currencyInput.val(currency || '');

                    console.log('选择了铜合同:', copperContractNo, '铜条件:', copperCondition, '货币:', currency);
                });

            } else {
                console.log('获取铜合同列表失败或无数据');
                $select.empty().append('<option value="">无可用合同</option>');
                layer.msg(result.message || '获取铜合同列表失败', {icon: 2});
            }
            console.log('=========================');
        },
        error: function(xhr, status, error) {
            console.log('加载铜合同列表失败:', error);
            $select.empty().append('<option value="">加载失败</option>');
            layer.msg('加载铜合同列表失败', {icon: 2});
        }
    });

    console.log('=== 创建完成 ===');
}

// 查询升水表数据（用于一般铜）
function queryAscendingWaterForGeneralCopper($currentRow, customerCode, arrivalDate) {
    console.log('=== 查询升水表数据 ===');
    console.log('客户代码:', customerCode, '到货日期:', arrivalDate);

    if (!customerCode || !arrivalDate) {
        console.log('客户代码或到货日期为空，无法查询升水表');
        var $copperConditionInput = $currentRow.find('input[name="copperCondition"]');
        $copperConditionInput.prop('readonly', false).attr('placeholder', '请先填写客户代码和到货日期');
        return;
    }

    $.ajax({
        url: baselocation + '/salesCommon/getAscendingWaterByCustomerAndDate',
        type: 'POST',
        data: {
            customerCode: customerCode,
            arrivalDate: arrivalDate
        },
        success: function(result) {
            console.log('升水表查询返回:', result);

            var $tbody = $currentRow.closest('tbody.record');
            var $copperConditionSelect = $currentRow.find('select[name="copperCondition"]');
            var $currencyInput = $tbody.find('input[name="currency"]');
            var $premiumInput = $tbody.find('input[name="premium"]'); // 升水字段

            if (result.code === 1 && result.data && result.data.length > 0) {
                // 获取第一条匹配的升水表数据
                var ascendingWaterData = result.data[0];

                console.log('升水表数据:', ascendingWaterData);

                // 设置铜条件（下拉框选中）
                if ($copperConditionSelect.length > 0) {
                    $copperConditionSelect.val(ascendingWaterData.copperCondition || '');
                }

                // 设置铜货币（从铜条件表关联获取）
                $currencyInput.val(ascendingWaterData.currency || '');

                // 设置升水单价
                if ($premiumInput.length > 0) {
                    $premiumInput.val(ascendingWaterData.ascendingWaterPrice || '');
                }

                console.log('已设置升水表数据 - 铜条件:', ascendingWaterData.copperCondition,
                           '货币:', ascendingWaterData.currency,
                           '升水:', ascendingWaterData.ascendingWaterPrice);

                layer.msg('已自动填入升水表数据', {icon: 1, time: 2000});

            } else {
                console.log('未找到匹配的升水表数据');

                // 未找到数据时，允许手动选择铜条件
                var $copperConditionInput = $currentRow.find('input[name="copperCondition"]');
                if ($copperConditionInput.length > 0) {
                    $copperConditionInput.prop('readonly', false).attr('placeholder', '未找到升水表数据，请手动选择铜条件');
                    createCopperConditionSelectInPlace($copperConditionInput);
                }

                layer.msg('未找到匹配的升水表数据，请手动选择铜条件', {icon: 0, time: 3000});
            }
        },
        error: function(xhr, status, error) {
            console.log('查询升水表数据异常:', error);

            var $copperConditionInput = $currentRow.find('input[name="copperCondition"]');
            $copperConditionInput.prop('readonly', false).attr('placeholder', '查询升水表失败，请手动选择铜条件');
            createCopperConditionSelectInPlace($copperConditionInput);

            layer.msg('查询升水表数据失败，请手动选择铜条件', {icon: 2, time: 3000});
        }
    });
}

// 货币换算函数
function convertCurrencyToRMB(originalPrice, currency, arrivalDate, callback) {
    console.log('=== 货币换算 ===');
    console.log('原价格:', originalPrice, '货币:', currency, '到货日期:', arrivalDate);

    // 如果是人民币，直接返回原价格
    if (!currency || currency === 'RMB') {
        console.log('货币为RMB，无需换算');
        callback(originalPrice, 1.0);
        return;
    }

    // 查询汇率
    $.ajax({
        url: baselocation + '/salesCommon/getCurrencyExchangeRate',
        type: 'POST',
        data: {
            originalCurrency: currency,
            arrivalDate: arrivalDate
        },
        success: function(result) {
            console.log('汇率查询返回:', result);

            if (result.code === 1 && result.data && result.data.exchangeRate) {
                var exchangeRate = parseFloat(result.data.exchangeRate);
                var convertedPrice = (parseFloat(originalPrice) * exchangeRate).toFixed(4);

                console.log('汇率:', exchangeRate, '换算后价格:', convertedPrice);
                callback(convertedPrice, exchangeRate);
            } else {
                console.log('未找到汇率数据:', result.message || '无数据');
                layer.msg(result.message || '货币换算汇率未登录！', {icon: 0, time: 3000});
                callback(originalPrice, 1.0); // 返回原价格
            }
        },
        error: function(xhr, status, error) {
            console.log('查询汇率异常:', error);
            layer.msg('查询汇率失败，使用原价格', {icon: 2, time: 3000});
            callback(originalPrice, 1.0); // 返回原价格
        }
    });
}

// 查询产品单价作为0base
function queryProductPrice(customerCode, productCode, arrivalDate, $currentRow) {
    console.log('=== 查询产品单价作为0base ===');
    console.log('客户代码:', customerCode, '产品代码:', productCode, '到货日期:', arrivalDate);

    if (!customerCode || !productCode || !arrivalDate) {
        console.log('参数不完整，无法查询产品单价');
        return;
    }

    $.ajax({
        url: baselocation + '/salesCommon/getProductPrice',
        type: 'POST',
        data: {
            customerCode: customerCode,
            productCode: productCode,
            arrivalDate: arrivalDate
        },
        success: function(result) {
            console.log('产品单价查询返回:', result);

            if (result.code === 1 && result.data) {
                var productPrice = result.data;
                var $tbody = $currentRow.closest('tbody.record');
                var $secondRow = $tbody.find('tr:last-child');
                var $zeroBaseInput = $secondRow.find('input[name="zeroBase"]');

                if (productPrice.productPrice) {
                    // 获取产品价格的货币信息
                    var productCurrency = productPrice.currency || 'RMB';

                    console.log('产品单价:', productPrice.productPrice, '货币:', productCurrency);

                    // 进行货币换算
                    convertCurrencyToRMB(productPrice.productPrice, productCurrency, arrivalDate, function(convertedPrice, exchangeRate) {
                        // 设置0base（换算后的价格）
                        $zeroBaseInput.val(convertedPrice);
                        console.log('自动设置0base（换算后）:', convertedPrice, '汇率:', exchangeRate);

                        // 触发0base变更事件，重新计算销售单价和销售金额
                        $zeroBaseInput.trigger('change');

                        var message = '已自动填入0base: ' + convertedPrice;
                        if (exchangeRate !== 1.0) {
                            message += ' (原价: ' + productPrice.productPrice + ' ' + productCurrency + ', 汇率: ' + exchangeRate + ')';
                        }
                        layer.msg(message, {icon: 1, time: 3000});
                    });
                } else {
                    console.log('产品价格数据中没有单价信息');
                }
            } else {
                console.log('未找到匹配的产品价格:', result.message || '无数据');
                // 不显示错误提示，因为可能是正常情况（没有配置价格）
            }
        },
        error: function(xhr, status, error) {
            console.log('查询产品单价异常:', error);
            // 不显示错误提示，避免影响用户体验
        }
    });
}

// 查询铜价表获取铜base（用于一般铜/无偿）
function queryCopperBaseForGeneralCopper($tbody, copperCondition, arrivalDate) {
    console.log('=== 查询铜价表获取铜base ===');
    console.log('铜条件:', copperCondition, '到货日期:', arrivalDate);

    if (!copperCondition || !arrivalDate) {
        console.log('铜条件或到货日期为空，无法查询铜base');
        return;
    }

    $.ajax({
        url: baselocation + '/salesCommon/getCopperBase',
        type: 'POST',
        data: {
            copperCondition: copperCondition,
            arrivalDate: arrivalDate
        },
        success: function(result) {
            console.log('铜base查询返回:', result);

            if (result.code === 1 && result.data) {
                var copperPriceData = result.data;
                var $copperBaseInput = $tbody.find('input[name="copperBase"]');

                if (copperPriceData.copperPrice) {
                    // 铜base通常已经是人民币价格，但为了保险起见，检查是否需要换算
                    var copperCurrency = 'RMB'; // 铜价表中的价格通常是人民币

                    console.log('铜base:', copperPriceData.copperPrice, '货币:', copperCurrency);

                    // 进行货币换算（虽然通常是RMB，但保持一致性）
                    convertCurrencyToRMB(copperPriceData.copperPrice, copperCurrency, arrivalDate, function(convertedPrice, exchangeRate) {
                        // 设置铜base（换算后的价格）
                        $copperBaseInput.val(convertedPrice);
                        console.log('自动设置铜base（换算后）:', convertedPrice, '汇率:', exchangeRate);

                        // 触发铜base变更事件，重新计算相关金额
                        $copperBaseInput.trigger('change');

                        var message = '已自动填入铜base: ' + convertedPrice;
                        if (exchangeRate !== 1.0) {
                            message += ' (原价: ' + copperPriceData.copperPrice + ' ' + copperCurrency + ', 汇率: ' + exchangeRate + ')';
                        }
                        layer.msg(message, {icon: 1, time: 2000});
                    });
                } else {
                    console.log('铜价格数据中没有铜base信息');
                }
            } else {
                console.log('未找到匹配的铜base数据:', result.message || '无数据');
                layer.msg('未找到匹配的铜base数据，请手动输入', {icon: 0, time: 3000});
            }
        },
        error: function(xhr, status, error) {
            console.log('查询铜base异常:', error);
            layer.msg('查询铜base失败，请手动输入', {icon: 2, time: 3000});
        }
    });
}

// 查询铜合同详情获取铜base（用于预约铜/支给铜）
function queryCopperBaseForContractCopper($tbody, copperSignNo, useMonth) {
    console.log('=== 查询铜合同详情获取铜base ===');
    console.log('铜签约NO:', copperSignNo, '使用月:', useMonth);

    if (!copperSignNo || !useMonth) {
        console.log('铜签约NO或使用月为空，无法查询铜合同详情');
        return;
    }

    $.ajax({
        url: baselocation + '/salesCommon/getCopperContractDetailForBase',
        type: 'POST',
        data: {
            copperSignNo: copperSignNo,
            useMonth: useMonth
        },
        success: function(result) {
            console.log('铜合同详情查询返回:', result);

            if (result.code === 1 && result.data) {
                var contractData = result.data;
                var $copperBaseInput = $tbody.find('input[name="copperBase"]');

                if (contractData.signedCopperPriceExTax) {
                    // 获取铜合同的货币信息
                    var contractCurrency = contractData.currency || 'RMB';

                    console.log('铜base（免税签约铜价）:', contractData.signedCopperPriceExTax, '货币:', contractCurrency);

                    // 进行货币换算
                    convertCurrencyToRMB(contractData.signedCopperPriceExTax, contractCurrency, useMonth + '-01', function(convertedPrice, exchangeRate) {
                        // 设置铜base（换算后的价格）
                        $copperBaseInput.val(convertedPrice);
                        console.log('自动设置铜base（换算后）:', convertedPrice, '汇率:', exchangeRate);

                        // 触发铜base变更事件，重新计算相关金额
                        $copperBaseInput.trigger('change');

                        var message = '已自动填入铜base: ' + convertedPrice;
                        if (exchangeRate !== 1.0) {
                            message += ' (原价: ' + contractData.signedCopperPriceExTax + ' ' + contractCurrency + ', 汇率: ' + exchangeRate + ')';
                        }
                        layer.msg(message, {icon: 1, time: 2000});
                    });
                } else {
                    console.log('铜合同数据中没有免税签约铜价信息');
                }
            } else {
                console.log('未找到匹配的铜合同数据:', result.message || '无数据');
                layer.msg('未找到匹配的铜合同数据，请手动输入铜base', {icon: 0, time: 3000});
            }
        },
        error: function(xhr, status, error) {
            console.log('查询铜合同详情异常:', error);
            layer.msg('查询铜合同详情失败，请手动输入铜base', {icon: 2, time: 3000});
        }
    });
}

// 加载交易条件下拉菜单数据
function loadTradeConditions() {
    $.ajax({
        url: baselocation + '/order/salesLogin/getTradeConditions',
        type: 'POST',
        success: function(result) {
            if (result.code === 1 && result.data) {
                var $select = $('#tradeCondition');
                $select.empty().append('<option value="">请选择</option>');

                $.each(result.data, function(index, item) {
                    var optionText = item.交易条件 + ' ' + (item.交易条件名 || '');
                    $select.append('<option value="' + item.交易条件 + '">' + optionText + '</option>');
                });

                layui.form.render('select');
                console.log('交易条件下拉菜单数据加载完成');
            } else {
                console.log('获取交易条件数据失败:', result.message);
            }
        },
        error: function(xhr, status, error) {
            console.error('获取交易条件数据异常:', error);
        }
    });
}

// 加载付款条件下拉菜单数据
function loadPayConditions() {
    $.ajax({
        url: baselocation + '/order/salesLogin/getPayConditions',
        type: 'POST',
        success: function(result) {
            if (result.code === 1 && result.data) {
                var $select = $('#payCondition');
                $select.empty().append('<option value="">请选择</option>');

                $.each(result.data, function(index, item) {
                    var optionText = item.付款条件 + ' ' + (item.付款条件名 || '');
                    $select.append('<option value="' + item.付款条件 + '">' + optionText + '</option>');
                });

                layui.form.render('select');
                console.log('付款条件下拉菜单数据加载完成');
            } else {
                console.log('获取付款条件数据失败:', result.message);
            }
        },
        error: function(xhr, status, error) {
            console.error('获取付款条件数据异常:', error);
        }
    });
}

// 加载运输条件下拉菜单数据
function loadTransportConditions() {
    $.ajax({
        url: baselocation + '/order/salesLogin/getTransportConditions',
        type: 'POST',
        success: function(result) {
            if (result.code === 1 && result.data) {
                var $select = $('#transportCondition');
                $select.empty().append('<option value="">请选择</option>');

                $.each(result.data, function(index, item) {
                    var optionText = item.运输条件 + ' ' + (item.运输条件名 || '');
                    $select.append('<option value="' + item.运输条件 + '">' + optionText + '</option>');
                });

                layui.form.render('select');
                console.log('运输条件下拉菜单数据加载完成');
            } else {
                console.log('获取运输条件数据失败:', result.message);
            }
        },
        error: function(xhr, status, error) {
            console.error('获取运输条件数据异常:', error);
        }
    });
}

// 全选/取消全选功能
function toggleSelectAll() {
    var selectAllCheckbox = document.getElementById('selectAll');
    var rowCheckboxes = document.querySelectorAll('.row-checkbox');

    for (var i = 0; i < rowCheckboxes.length; i++) {
        rowCheckboxes[i].checked = selectAllCheckbox.checked;
    }

    // 更新选中金额
    updateSelectedAmount();
}

// 更新选中项目的合计金额
function updateSelectedAmount() {
    var selectedTotal = 0;
    var allCheckboxes = document.querySelectorAll('.row-checkbox');
    var checkedCount = 0;

    // 计算选中项目的总金额
    allCheckboxes.forEach(function(checkbox) {
        if (checkbox.checked) {
            checkedCount++;
            var row = checkbox.closest('tbody.record');
            var salesAmountInput = row.querySelector('input[name="salesAmount"]');
            if (salesAmountInput && salesAmountInput.value) {
                selectedTotal += parseFloat(salesAmountInput.value) || 0;
            }
        }
    });

    // 更新总金额显示
    document.getElementById('totalAmount').value = selectedTotal.toFixed(2);

    // 更新全选复选框状态
    var selectAllCheckbox = document.getElementById('selectAll');
    if (selectAllCheckbox) {
        if (checkedCount === 0) {
            selectAllCheckbox.indeterminate = false;
            selectAllCheckbox.checked = false;
        } else if (checkedCount === allCheckboxes.length) {
            selectAllCheckbox.indeterminate = false;
            selectAllCheckbox.checked = true;
        } else {
            selectAllCheckbox.indeterminate = true;
            selectAllCheckbox.checked = false;
        }
    }

    console.log('已选中', checkedCount, '项，合计金额:', selectedTotal.toFixed(2));
}

// 处理行复选框变化
function handleRowCheckboxChange(checkbox) {
    // 更新选中金额
    updateSelectedAmount();
}

// 处理默认铜合同类别变化
function handleDefaultCopperContractTypeChange(copperContractType, customerCode, arrivalDate) {
    console.log('处理默认铜合同类别变化:', copperContractType, '客户代码:', customerCode, '到货日期:', arrivalDate);

    var $copperContractNoSelect = $('#defaultCopperContractNo');
    var $copperConditionSelect = $('#defaultCopperCondition');

    // 清空下级字段
    $copperContractNoSelect.empty().append('<option value="">请选择</option>');
    $copperConditionSelect.empty().append('<option value="">请选择</option>');

    if (copperContractType === '1' || copperContractType === '2') {
        // 预约铜/支给铜：加载铜合同NO下拉框
        console.log('预约铜/支给铜模式，准备加载铜合同列表');
        if (customerCode && arrivalDate) {
            console.log('客户代码和到货日期都存在，开始加载铜合同列表');
            loadDefaultCopperContractList(customerCode, copperContractType, arrivalDate);
        } else {
            console.log('客户代码或到货日期缺失，无法加载铜合同列表');
            if (!customerCode) {
                layer.msg('请先选择客户', {icon: 0});
            }
            if (!arrivalDate) {
                layer.msg('请先选择到货日期范围', {icon: 0});
            }
        }
        // 铜条件禁用
        $copperConditionSelect.prop('disabled', true);
    } else if (copperContractType === '3' || copperContractType === '4') {
        // 一般铜/无偿：加载铜条件下拉框
        console.log('一般铜/无偿模式，加载铜条件列表');
        loadDefaultCopperConditionList();
        // 铜合同NO禁用并显示"不适用"
        $copperContractNoSelect.prop('disabled', true).empty().append('<option value="">不适用</option>');
        $copperConditionSelect.prop('disabled', false);
    } else {
        // 其他情况：都启用
        console.log('其他情况，启用所有字段');
        $copperContractNoSelect.prop('disabled', false);
        $copperConditionSelect.prop('disabled', false);
    }

    layui.form.render('select');
}

// 处理默认铜合同NO变化
function handleDefaultCopperContractNoChange(copperContractNo) {
    console.log('处理默认铜合同NO变化:', copperContractNo);

    if (copperContractNo) {
        // 从选中的铜合同获取铜条件
        var selectedOption = $('#defaultCopperContractNo option:selected');
        var copperCondition = selectedOption.data('copper-condition');

        if (copperCondition) {
            $('#defaultCopperCondition').empty()
                .append('<option value="' + copperCondition + '" selected>' + copperCondition + '</option>');
            layui.form.render('select');
        }
    }
}

// 处理默认铜条件变化
function handleDefaultCopperConditionChange(copperCondition) {
    console.log('处理默认铜条件变化:', copperCondition);
    // 这里可以添加额外的处理逻辑，比如更新相关的货币信息等
}

// 加载默认铜合同列表
function loadDefaultCopperContractList(customerCode, copperContractType, arrivalDate) {
    console.log('加载默认铜合同列表:', customerCode, copperContractType, arrivalDate);

    $.ajax({
        url: baselocation + '/order/salesLogin/getCopperContractList',
        type: 'POST',
        data: {
            customerCode: customerCode,
            copperContractType: copperContractType,
            useMonth: arrivalDate ? arrivalDate.substring(0, 7) : '',
            status: '0'  // 修正状态参数，应该是'0'表示有效
        },
        success: function(result) {
            console.log('默认铜合同列表返回结果:', result);
            var $select = $('#defaultCopperContractNo');
            $select.empty().append('<option value="">请选择</option>');

            if (result.code === 1 && result.data && result.data.length > 0) {
                $.each(result.data, function(index, item) {
                    // 修正数据字段名，参照明细中的处理方式
                    var contractNo = item.copperSignNo || item.铜合同NO || '';
                    var copperCondition = item.copperCondition || item.铜条件 || '';
                    var currency = item.currency || item.铜货币 || '';

                    if (contractNo) {
                        var optionText = contractNo + (copperCondition ? ' (' + copperCondition + ')' : '');
                        var option = '<option value="' + contractNo + '" ' +
                            'data-copper-condition="' + copperCondition + '" ' +
                            'data-currency="' + currency + '">' + optionText + '</option>';
                        $select.append(option);
                        console.log('添加默认铜合同选项:', contractNo, '铜条件:', copperCondition, '货币:', currency);
                    }
                });
            } else {
                console.log('没有获取到铜合同数据或数据为空');
            }

            layui.form.render('select');
        },
        error: function(xhr, status, error) {
            console.error('加载默认铜合同列表失败:', error);
        }
    });
}

// 加载默认铜条件列表
function loadDefaultCopperConditionList() {
    console.log('加载默认铜条件列表');

    $.ajax({
        url: baselocation + '/order/salesLogin/getCopperConditionList',
        type: 'POST',
        success: function(result) {
            var $select = $('#defaultCopperCondition');
            $select.empty().append('<option value="">请选择</option>');

            if (result.code === 1 && result.data && result.data.length > 0) {
                $.each(result.data, function(index, item) {
                    var optionText = item.copperCondition + ' (' + item.currency + ')';
                    var option = '<option value="' + item.copperCondition + '" ' +
                        'data-currency="' + item.currency + '">' + optionText + '</option>';
                    $select.append(option);
                });
            }

            layui.form.render('select');
        },
        error: function(xhr, status, error) {
            console.error('加载默认铜条件列表失败:', error);
        }
    });
}

// 应用默认值到所有明细
function applyDefaultValuesToAllDetails() {
    var defaultCopperContractType = $('#defaultCopperContractType').val();
    var defaultCopperContractNo = $('#defaultCopperContractNo').val();
    var defaultCopperCondition = $('#defaultCopperCondition').val();

    if (!defaultCopperContractType) {
        layer.msg('请先选择默认铜合同类别', {icon: 2});
        return;
    }

    console.log('应用默认值到所有明细:', {
        copperContractType: defaultCopperContractType,
        copperContractNo: defaultCopperContractNo,
        copperCondition: defaultCopperCondition
    });

    // 获取默认值的相关数据
    var defaultCurrency = '';
    if (defaultCopperContractType === '1' || defaultCopperContractType === '2') {
        // 预约铜/支给铜：从铜合同NO获取货币
        var selectedContractOption = $('#defaultCopperContractNo option:selected');
        defaultCurrency = selectedContractOption.data('currency') || '';
    } else if (defaultCopperContractType === '3' || defaultCopperContractType === '4') {
        // 一般铜/无偿：从铜条件获取货币
        var selectedConditionOption = $('#defaultCopperCondition option:selected');
        defaultCurrency = selectedConditionOption.data('currency') || '';
    }

    // 应用到所有明细行
    $('#detailTable tbody.record').each(function() {
        var $record = $(this);
        var $firstRow = $record.find('tr:first-child');
        var $secondRow = $record.find('tr:last-child');

        // 设置铜合同类别
        var $copperContractTypeSelect = $firstRow.find('select[name="copperContractType"]');
        $copperContractTypeSelect.val(defaultCopperContractType);

        // 获取当前行的客户代码和到货日期
        var customerCode = $firstRow.find('input[name="customerProductCode"]').val();
        var arrivalDate = $firstRow.find('input[name="arrivalDate"]').val();

        console.log('应用默认值到明细行:', {
            copperContractType: defaultCopperContractType,
            copperContractNo: defaultCopperContractNo,
            copperCondition: defaultCopperCondition,
            customerCode: customerCode,
            arrivalDate: arrivalDate
        });

        // 触发铜合同类别变更逻辑，这会正确处理字段的级联关系
        handleCopperContractTypeChange(defaultCopperContractType, $firstRow, customerCode, arrivalDate);

        // 等待级联处理完成后，再设置具体的值
        setTimeout(function() {
            if (defaultCopperContractType === '1' || defaultCopperContractType === '2') {
                // 预约铜/支给铜：复制铜合同NO选项并设置值
                if (defaultCopperContractNo) {
                    var $copperContractNoSelect = $firstRow.find('select[name="copperContractNo"]');
                    if ($copperContractNoSelect.length > 0) {
                        // 复制默认值区域的铜合同NO选项到明细
                        var $defaultCopperContractNoSelect = $('#defaultCopperContractNo');
                        var defaultOptions = $defaultCopperContractNoSelect.html();

                        console.log('复制铜合同NO选项到明细:', defaultOptions);
                        $copperContractNoSelect.html(defaultOptions);

                        // 设置选中值
                        $copperContractNoSelect.val(defaultCopperContractNo);
                        console.log('设置明细铜合同NO值:', defaultCopperContractNo);

                        // 触发change事件以更新铜条件
                        $copperContractNoSelect.trigger('change');
                    }
                }
            } else if (defaultCopperContractType === '3' || defaultCopperContractType === '4') {
                // 一般铜/无偿：复制铜条件选项并设置值
                if (defaultCopperCondition) {
                    var $copperConditionSelect = $firstRow.find('select[name="copperCondition"]');
                    if ($copperConditionSelect.length > 0) {
                        // 复制默认值区域的铜条件选项到明细
                        var $defaultCopperConditionSelect = $('#defaultCopperCondition');
                        var defaultConditionOptions = $defaultCopperConditionSelect.html();

                        console.log('复制铜条件选项到明细:', defaultConditionOptions);
                        $copperConditionSelect.html(defaultConditionOptions);

                        // 设置选中值
                        $copperConditionSelect.val(defaultCopperCondition);
                        console.log('设置明细铜条件值:', defaultCopperCondition);

                        // 触发change事件以更新货币
                        $copperConditionSelect.trigger('change');
                    }
                }
            }

            // 设置货币
            if (defaultCurrency) {
                var $currencyInput = $secondRow.find('input[name="currency"]');
                $currencyInput.val(defaultCurrency);
            }
        }, 500); // 给级联处理一些时间
    });

    layer.msg('已应用默认值到所有明细', {icon: 1});
}

// 关闭弹窗函数
function closeLayer() {
    var index = parent.layer.getFrameIndex(window.name);
    parent.layer.close(index);
    parent.location.reload();
}

// 根据税代码获取税率
function getTaxRateByTaxCode(taxCode) {
    if (!taxCode) {
        return;
    }

    $.ajax({
        url: baselocation + '/salesCommon/taxRateCodeFun/json',
        type: 'POST',
        data: {taxRateCode: taxCode},
        success: function(result) {
            console.log('获取税率结果:', result);
            if (result.code === 1 && result.data && result.data.taxRate) {
                $('#taxRate').val(result.data.taxRate);
                console.log('设置税率:', result.data.taxRate);
                // 税率变更后重新计算税额
                var totalAmount = parseFloat($('#totalAmount').val()) || 0;
                calculateTaxAmount(totalAmount);
            } else {
                console.log('未找到对应的税率数据');
                // 如果找不到税率，显示税代码作为兼容
                $('#taxRate').val(taxCode);
                // 清空税额
                $('#taxAmount').val('0.00');
            }
        },
        error: function(xhr, status, error) {
            console.error('获取税率失败:', error);
            // 如果请求失败，显示税代码作为兼容
            $('#taxRate').val(taxCode);
            // 清空税额
            $('#taxAmount').val('0.00');
        }
    });
}

// 根据出货单No.获取明细数据
function getDetailsByShipmentNo(shipmentNo) {
    console.log('根据出货单No.获取明细数据:', shipmentNo);

    if (!shipmentNo) {
        layer.msg('出货单No.不能为空', {icon: 2});
        return;
    }

    // 显示加载提示
    var loadingIndex = layer.load(2, {shade: [0.3, '#000']});

    $.ajax({
        url: baselocation + '/order/salesLogin/getDetailsByShipmentNo',
        type: 'POST',
        data: {shipmentNo: shipmentNo},
        success: function(result) {
            layer.close(loadingIndex);
            console.log('根据出货单No.获取明细数据结果:', result);

            if (result.code === 1 && result.data && result.data.length > 0) {
                // 添加明细数据到表格
                var addedCount = 0;
                result.data.forEach(function(detail) {
                    addDetailRowToTwoRowTable(detail);
                    addedCount++;
                });

                layer.msg('成功添加 ' + addedCount + ' 条明细数据', {icon: 1});
                console.log('已添加明细数据:', result.data);

                // 重新计算总金额
                calculateTotalAmount();
            } else {
                layer.msg('未找到出货单No. [' + shipmentNo + '] 的明细数据', {icon: 0});
            }
        },
        error: function(xhr, status, error) {
            layer.close(loadingIndex);
            console.error('获取出货单明细数据失败:', error);
            layer.msg('获取出货单明细数据失败: ' + error, {icon: 2});
        }
    });
}

// 验证数量拆分是否正确
function validateQuantitySplit($currentRow) {
    var $tbody = $currentRow.closest('tbody.record');
    var deliveryNoteNo = $tbody.find('input[name="deliveryNoteNo"]').val();
    var deliveryNoteSeq = $tbody.find('input[name="deliveryNoteSeq"]').val();
    var originalQuantity = parseFloat($tbody.find('input[name="originalQuantity"]').val()) || 0;

    // 如果没有出货单No.或原始数量，则不进行验证
    if (!deliveryNoteNo || originalQuantity <= 0) {
        return;
    }

    console.log('开始验证数量拆分 - 出货单No.:', deliveryNoteNo, '序号:', deliveryNoteSeq, '原始数量:', originalQuantity);

    // 查找所有相同出货单No.和序号的明细行
    var totalQuantity = 0;
    var relatedRows = [];

    $('#detailTable tbody.record').each(function() {
        var $record = $(this);
        var recordDeliveryNoteNo = $record.find('input[name="deliveryNoteNo"]').val();
        var recordDeliveryNoteSeq = $record.find('input[name="deliveryNoteSeq"]').val();
        var recordOriginalQuantity = parseFloat($record.find('input[name="originalQuantity"]').val()) || 0;

        // 检查是否是相同的出货单明细
        if (recordDeliveryNoteNo === deliveryNoteNo &&
            recordDeliveryNoteSeq === deliveryNoteSeq &&
            recordOriginalQuantity === originalQuantity) {

            var quantity = parseFloat($record.find('input[name="quantity"]').val()) || 0;
            totalQuantity += quantity;
            relatedRows.push($record);

            console.log('找到相关行 - 数量:', quantity, '累计总量:', totalQuantity);
        }
    });

    console.log('验证结果 - 相关行数:', relatedRows.length, '总数量:', totalQuantity, '原始数量:', originalQuantity);

    // 如果只有一行，不需要验证
    if (relatedRows.length <= 1) {
        return;
    }

    // 验证数量总和是否与原始数量相等
    var quantityDifference = Math.abs(totalQuantity - originalQuantity);
    var tolerance = 0.001; // 允许的误差范围

    if (quantityDifference > tolerance) {
        // 数量不匹配，显示警告信息
        var message = '出货单No. [' + deliveryNoteNo + '] 序号 [' + deliveryNoteSeq + '] 的数量拆分不正确！\n' +
                     '原始数量: ' + originalQuantity + ' KG\n' +
                     '拆分后总量: ' + totalQuantity + ' KG\n' +
                     '差额: ' + (totalQuantity - originalQuantity).toFixed(3) + ' KG';

        console.warn('数量验证失败:', message);

        // 高亮显示相关的数量输入框
        relatedRows.forEach(function($record) {
            var $quantityInput = $record.find('input[name="quantity"]');
            $quantityInput.addClass('quantity-error');
            setTimeout(function() {
                $quantityInput.removeClass('quantity-error');
            }, 5000);
        });

        // 显示提示信息
        layer.alert(message, {
            icon: 0,
            title: '数量验证提醒',
            btn: ['确定'],
            yes: function(index) {
                layer.close(index);
            }
        });
    } else {
        console.log('数量验证通过');

        // 移除错误样式
        relatedRows.forEach(function($record) {
            var $quantityInput = $record.find('input[name="quantity"]');
            $quantityInput.removeClass('quantity-error');
        });
    }
}