-- 为出库数量表添加两个新字段
IF NOT EXISTS (SELECT 1 FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[出库数量表]') AND name = N'送货单号')
    ALTER TABLE [dbo].[出库数量表] ADD [送货单号] varchar(50) NULL;

IF NOT EXISTS (SELECT 1 FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[出库数量表]') AND name = N'送货单序号')
    ALTER TABLE [dbo].[出库数量表] ADD [送货单序号] int NULL;

PRINT '出库数量表字段添加完成：送货单号、送货单序号';


-- 为销售额明细表添加两个新字段
IF NOT EXISTS (SELECT 1 FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[销售额明细表]') AND name = N'送货单号')
    ALTER TABLE [dbo].[销售额明细表] ADD [送货单号] varchar(50) NULL;

IF NOT EXISTS (SELECT 1 FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[销售额明细表]') AND name = N'送货单序号')
    ALTER TABLE [dbo].[销售额明细表] ADD [送货单序号] int NULL;

PRINT '销售额明细表字段添加完成：送货单号、送货单序号';