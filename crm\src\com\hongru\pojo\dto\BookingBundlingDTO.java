package com.hongru.pojo.dto;

import com.hongru.entity.businessOps.AuxiliaryRecyclingArtificial;
import com.hongru.entity.businessOps.BookingBundling;
import com.hongru.support.page.PageInfo;

import java.util.List;

public class BookingBundlingDTO {

    private PageInfo pageInfo;

    private List<BookingBundling> bookingBundlings;

    public PageInfo getPageInfo() {
        return pageInfo;
    }

    public void setPageInfo(PageInfo pageInfo) {
        this.pageInfo = pageInfo;
    }

    public List<BookingBundling> getBookingBundlings() {
        return bookingBundlings;
    }

    public void setBookingBundlings(List<BookingBundling> bookingBundlings) {
        this.bookingBundlings = bookingBundlings;
    }

    public BookingBundlingDTO(PageInfo pageInfo, List<BookingBundling> bookingBundlings) {
        this.pageInfo = pageInfo;
        this.bookingBundlings = bookingBundlings;
    }
}
