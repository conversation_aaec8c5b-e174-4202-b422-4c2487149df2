layui.use(['form', 'laydate', 'table', 'layer'], function(){
    var form = layui.form
        ,laydate = layui.laydate
        ,table = layui.table
        ,layer = layui.layer;

    // 初始化两行表头表格
    initializePaymentRequestTwoRowTable();

    // 获取支付请求NO
    var paymentRequestNo = $('#paymentRequestNo').val() || getUrlParam('paymentRequestNo');

    // 获取URL参数的函数
    function getUrlParam(name) {
        var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
        var r = window.location.search.substr(1).match(reg);
        if (r != null) return unescape(r[2]);
        return null;
    }

    // 页面加载时自动加载数据
    if (paymentRequestNo) {
        loadDataByNo(paymentRequestNo);
    } else {
        layer.msg('支付请求NO参数缺失', {icon: 2});
    }

    // 根据支付请求NO加载数据
    function loadDataByNo(paymentRequestNo) {
        // 发送AJAX请求获取支付请求数据
        $.ajax({
            url: baselocation + '/order/paymentRequestLogin/detailByNo',
            type: 'POST',
            data: {paymentRequestNo: paymentRequestNo},
            success: function(result) {
                console.log('获取详情数据结果:', result);
                if (result.code === 1) {
                    var paymentAmount = result.data.paymentAmount;
                    var paymentAmountDetails = result.data.paymentAmountDetails;

                    console.log('支付请求数据:', paymentAmount);
                    console.log('支付请求明细数据:', paymentAmountDetails);

                    // 填充表单数据
                    $('#paymentRequestNoDisplay').val(paymentAmount.paymentRequestNo);
                    $('#paymentRequestDate').val(paymentAmount.paymentRequestDate);
                    $('#paymentDate').val(paymentAmount.paymentDate);
                    $('#customerAlias').val(paymentAmount.customerAlias);
                    $('#currency').val(paymentAmount.settlementCurrency);

                    // 直接使用后端返回的税率值
                    if (paymentAmount.taxRate) {
                        $('#taxRate').val(paymentAmount.taxRate);
                        console.log('支付请求详情页面设置税率:', paymentAmount.taxRate);
                    } else {
                        $('#taxRate').val('');
                    }

                    // 格式化显示已保存的客户条件字段值（显示为"代码 名称"格式）
                    formatAndDisplayConditionFields(paymentAmount.tradeCondition, paymentAmount.paymentCondition, paymentAmount.transportCondition);

                    console.log('支付请求详情页面显示条件字段 - 交易条件:', paymentAmount.tradeCondition, '付款条件:', paymentAmount.paymentCondition, '运输条件:', paymentAmount.transportCondition);

                    // 渲染明细表格
                    if (paymentAmountDetails && paymentAmountDetails.length > 0) {
                        renderDetailTable(paymentAmountDetails);
                        calculateTotalAmount();
                    } else {
                        // 如果没有明细数据，直接显示支付金额
                        if (paymentAmount.paymentAmount) {
                            var totalAmount = parseFloat(paymentAmount.paymentAmount);
                            $('#totalAmount').val('¥' + totalAmount.toFixed(2));
                            // 计算并显示税额
                            calculateAndDisplayTaxAmount(totalAmount);
                        }
                    }
                } else {
                    layer.msg('获取数据失败: ' + (result.message || '未知错误'), {icon: 2});
                }
            },
            error: function(xhr, status, error) {
                console.error('获取数据失败:', error);
                layer.msg('获取数据失败: ' + error, {icon: 2});
            }
        });
    }

    // 渲染明细表格（只读模式）
    function renderDetailTable(detailsData) {
        // 清空现有数据
        $('#detailTable tbody.record').remove();

        detailsData.forEach(function(item, index) {
            // 使用新的添加行函数
            addPaymentRequestDetailRow(item);
        });
    }

    // 计算总金额
    function calculateTotalAmount() {
        var total = 0;
        // 修正选择器：选择每个记录的第二行（计算信息行）的第9列（请求金额）
        $('#detailTable tbody.record tr:nth-child(2) td:nth-child(9)').each(function() {
            var amount = parseFloat($(this).text()) || 0;
            total += amount;
        });
        updateTotalAmount(total);
    }

    // 更新总金额显示
    function updateTotalAmount(total) {
        $('#totalAmount').val('¥' + total.toFixed(2));

        // 计算并显示税额
        calculateAndDisplayTaxAmount(total);
    }

    // 计算并显示税额
    function calculateAndDisplayTaxAmount(totalAmount) {
        // 直接从页面获取税率值
        var taxRate = parseFloat($('#taxRate').val()) || 0;
        var taxAmount = totalAmount * (taxRate / 100); // 税率是百分比，需要除以100
        $('#taxAmount').val(taxAmount.toFixed(2));
        console.log('支付请求详情页面计算税额 - 总金额:', totalAmount, '税率:', taxRate + '%', '税额:', taxAmount.toFixed(2));
        console.log('支付请求详情页面税率字段值:', $('#taxRate').val(), '税率字段是否存在:', $('#taxRate').length);
    }
});

function closeLayer() {
    var index = parent.layer.getFrameIndex(window.name);
    parent.layer.close(index);
    parent.location.reload();
}

// 初始化支付请求登录的两行表头表格（详情页面只读版本）
function initializePaymentRequestTwoRowTable() {
    // 添加样式
    if (!$('#paymentRequestTwoRowStyle').length) {
        var style = $('<style id="paymentRequestTwoRowStyle">');
        style.text(
            '#detailTable {' +
                'width: 100%;' +
                'border-collapse: collapse;' +
                'margin-top: 10px;' +
            '}' +
            '#detailTable th, #detailTable td {' +
                'border: 1px solid #e6e6e6;' +
                'padding: 8px 6px;' +
                'text-align: center;' +
                'word-break: break-all;' +
                'font-size: 12px;' +
                'line-height: 1.3;' +
                'vertical-align: middle;' +
            '}' +
            '#detailTable thead th {' +
                'font-weight: bold;' +
                'white-space: nowrap;' +
            '}' +
            '/* 第一行表头 - 基础信息 */' +
            '#detailTable thead tr:first-child th {' +
                'background-color: #e6f7ff;' +
                'color: #1890ff;' +
                'border-bottom: 1px solid #1890ff;' +
            '}' +
            '/* 第二行表头 - 计算信息 */' +
            '#detailTable thead tr:last-child th {' +
                'background-color: #fff2e8;' +
                'color: #fa8c16;' +
                'border-bottom: 1px solid #fa8c16;' +
            '}' +
            '/* 每条记录用两个 <tr>，不同记录间用背景色区分 */' +
            '#detailTable tbody.record:nth-of-type(odd) td {' +
                'background: #f8f8f8;' +
            '}' +
            '#detailTable tbody.record:nth-of-type(even) td {' +
                'background: #fff;' +
            '}' +
            '/* 让第二行数据和第一行数据视觉上依然"连成一体" */' +
            '#detailTable tbody.record tr:first-child td {' +
                'border-bottom: none;' +
                'border-left: 3px solid #1890ff;' +
            '}' +
            '#detailTable tbody.record tr:last-child td {' +
                'border-top: none;' +
                'border-left: 3px solid #fa8c16;' +
                'border-bottom: 2px solid #d2d2d2;' +
            '}' +
            '.table-container {' +
                'overflow-x: auto;' +
                'max-height: 400px;' +
                'overflow-y: auto;' +
            '}'
        );
        $('head').append(style);
    }

    // 生成初始的空表格HTML
    var html = '<div class="table-container"><table id="detailTable">';

    // 生成表头 - 两行，每行13列，与销售额登录完全一致
    html += '<thead>';
    // 第一行表头 - 基础信息标题
    html += '<tr>';
    html += '<th>销售额No.</th><th>序号</th><th>条码</th><th>产品代码</th>';
    html += '<th>线盘</th><th>客户代码</th><th>出货日</th><th>到货日期</th><th>客户订单No.</th>';
    html += '<th>明细内部备注</th><th>铜合同类别</th><th>铜合同NO</th><th>铜条件</th>';
    html += '</tr>';
    // 第二行表头 - 计算信息标题
    html += '<tr>';
    html += '<th>铜货币</th><th>换算率</th><th>数量(KG)</th><th>铜base</th>';
    html += '<th>升水</th><th>换算后铜单价</th><th>0base</th><th>支付单价</th>';
    html += '<th>请求金额</th><th>产品中分类</th><th>出货计划番号</th><th></th><th></th>';
    html += '</tr>';
    html += '</thead>';

    html += '</table></div>';

    // 替换原来的表格
    var $container = $('#detailTable').parent();
    console.log('表格容器:', $container.length, $container.html());

    $container.html(html);

    console.log('已初始化支付请求登录的两行表格（详情页面），容器内容:', $container.html());
}

// 添加支付请求明细行到两行表格（详情页面只读版本）
function addPaymentRequestDetailRow(item) {
    var tableBody = $('#detailTable');

    // 处理数据
    var processedItem = {
        salesAmountNo: item.salesAmountNo || '',  // 修复：使用销售额NO而不是送货单号
        deliveryNoteSeq: item.deliveryNoteSeq || item.salesAmountSeq || '',
        productCode: item.productCode || '',
        wireDrum: item.wireDrum || '',
        customerCode: item.customerCode || '',
        shipmentDate: item.shipmentDate || '',
        arrivalDate: item.arrivalDate || '',
        customerOrderNo: item.customerOrderNo || '',
        internalRemark: item.internalRemark || item.detailRemark || '',
        copperContractType: item.copperContractType || '',
        copperContractNo: item.copperContractNo || '',
        copperCondition: item.copperCondition || '',
        copperCurrency: item.copperCurrency || item.currency || '',
        conversionRate: item.conversionRate || '',
        quantity: item.quantity || '',
        copperBase: item.copperBase || '',
        premium: item.premium || '',
        convertedCopperPrice: item.convertedCopperPrice || '',
        zeroBase: item.zeroBase || '',
        paymentUnitPrice: item.paymentUnitPrice || '',
        requestAmount: item.requestAmount || '',
        productMiddleCategory: item.productMiddleCategory || '',
        shipmentPlanNo: item.shipmentPlanNo || ''
    };

    // 生成唯一ID
    var recordId = 'record_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);

    // 第一行：基础信息（13列）
    var firstRowHtml = '<tbody class="record" data-record-id="' + recordId + '">';
    firstRowHtml += '<tr>';
    firstRowHtml += '<td>' + processedItem.salesAmountNo + '</td>';  // 修复：显示销售额NO
    firstRowHtml += '<td>' + processedItem.deliveryNoteSeq + '</td>';
    firstRowHtml += '<td>' + processedItem.barcode + '</td>';
    firstRowHtml += '<td>' + processedItem.productCode + '</td>';
    firstRowHtml += '<td>' + processedItem.wireDrum + '</td>';
    firstRowHtml += '<td>' + processedItem.customerCode + '</td>';
    firstRowHtml += '<td>' + processedItem.shipmentDate + '</td>';
    firstRowHtml += '<td>' + processedItem.arrivalDate + '</td>';
    firstRowHtml += '<td>' + processedItem.customerOrderNo + '</td>';
    firstRowHtml += '<td>' + processedItem.internalRemark + '</td>';
    // 转换铜合同类别数字为中文
    var copperContractTypeText = '';
    switch(processedItem.copperContractType) {
        case '1': copperContractTypeText = '预约铜'; break;
        case '2': copperContractTypeText = '支给铜'; break;
        case '3': copperContractTypeText = '一般铜'; break;
        case '4': copperContractTypeText = '无偿'; break;
        default: copperContractTypeText = processedItem.copperContractType || '';
    }
    firstRowHtml += '<td>' + copperContractTypeText + '</td>';
    firstRowHtml += '<td>' + processedItem.copperContractNo + '</td>';
    firstRowHtml += '<td>' + processedItem.copperCondition + '</td>';
    firstRowHtml += '</tr>';

    // 第二行：计算信息（13列）
    firstRowHtml += '<tr>';
    firstRowHtml += '<td>' + processedItem.copperCurrency + '</td>';
    firstRowHtml += '<td>' + processedItem.conversionRate + '</td>';
    firstRowHtml += '<td>' + processedItem.quantity + '</td>';
    firstRowHtml += '<td>' + processedItem.copperBase + '</td>';
    firstRowHtml += '<td>' + processedItem.premium + '</td>';
    firstRowHtml += '<td>' + processedItem.convertedCopperPrice + '</td>';
    firstRowHtml += '<td>' + processedItem.zeroBase + '</td>';
    firstRowHtml += '<td>' + processedItem.paymentUnitPrice + '</td>';
    firstRowHtml += '<td>' + processedItem.requestAmount + '</td>';
    firstRowHtml += '<td>' + (processedItem.productMiddleCategory || '') + '</td>';
    firstRowHtml += '<td>' + (processedItem.shipmentPlanNo || '') + '</td>';
    firstRowHtml += '<td></td>';
    firstRowHtml += '<td></td>';
    firstRowHtml += '</tr>';
    firstRowHtml += '</tbody>';

    // 添加到表格
    tableBody.append(firstRowHtml);

    console.log('已添加支付请求明细行（详情页面），记录ID:', recordId);
}

// 获取客户详情信息（用于支付请求详情页面显示）
function getCustomerInfoForPaymentDetail(customerCode) {
    if (!customerCode) {
        return;
    }

    console.log('支付请求详情页面获取客户详情信息，客户代码:', customerCode);

    $.ajax({
        url: baselocation + '/order/salesLogin/getCustomerInfo',
        type: 'POST',
        data: {customerCode: customerCode},
        success: function(result) {
            console.log('支付请求详情页面获取客户详情信息结果:', result);
            if (result.code === 1 && result.data) {
                var customerInfo = result.data;
                console.log('支付请求详情页面客户详情信息:', customerInfo);

                // 设置交易条件、付款条件、运输条件、税率等字段的显示值
                if (customerInfo) {
                    // 交易条件：显示格式为 "代码 名称"
                    if (customerInfo.交易条件) {
                        var tradeConditionText = customerInfo.交易条件;
                        if (customerInfo.交易条件名) {
                            tradeConditionText += ' ' + customerInfo.交易条件名;
                        }
                        $('#tradeCondition').val(tradeConditionText);
                        console.log('支付请求详情页面设置交易条件:', tradeConditionText);
                    }

                    // 付款条件：显示格式为 "代码 名称"
                    if (customerInfo.付款条件) {
                        var payConditionText = customerInfo.付款条件;
                        if (customerInfo.付款条件名) {
                            payConditionText += ' ' + customerInfo.付款条件名;
                        }
                        $('#payCondition').val(payConditionText);
                        console.log('支付请求详情页面设置付款条件:', payConditionText);
                    }

                    // 运输条件：显示格式为 "代码 名称"
                    if (customerInfo.运输条件) {
                        var transportConditionText = customerInfo.运输条件;
                        if (customerInfo.运输条件名) {
                            transportConditionText += ' ' + customerInfo.运输条件名;
                        }
                        $('#transportCondition').val(transportConditionText);
                        console.log('支付请求详情页面设置运输条件:', transportConditionText);
                    }

                    // 税率：显示格式为 "税率代码 税率计算名"
                    // if (customerInfo.税代码) {
                    //     var taxRateText = customerInfo.税代码;
                    //     if (customerInfo.税率计算名) {
                    //         taxRateText += ' ' + customerInfo.税率计算名;
                    //     }
                    //     $('#taxRateCode').val(taxRateText);
                    //     console.log('支付请求详情页面设置税率代码:', taxRateText);
                    // }
                }
            } else {
                console.log('支付请求详情页面获取客户详情信息失败:', result.message);
            }
        },
        error: function(xhr, status, error) {
            console.error('支付请求详情页面获取客户详情信息异常:', error);
        }
    });
}

// 格式化显示条件字段（显示为"代码 名称"格式）
function formatAndDisplayConditionFields(tradeCondition, payCondition, transportCondition) {
    // 加载条件数据并格式化显示
    loadConditionDataAndFormat(tradeCondition, payCondition, transportCondition);
}

// 加载条件数据并格式化显示
function loadConditionDataAndFormat(tradeCondition, payCondition, transportCondition) {
    var conditionsLoaded = 0;
    var totalConditions = 3;
    var conditionData = {
        trade: {},
        pay: {},
        transport: {}
    };

    // 加载交易条件数据
    $.ajax({
        url: baselocation + '/order/salesLogin/getTradeConditions',
        type: 'POST',
        success: function(result) {
            if (result.code === 1 && result.data) {
                result.data.forEach(function(item) {
                    conditionData.trade[item.交易条件] = item.交易条件名;
                });
            }
            conditionsLoaded++;
            if (conditionsLoaded === totalConditions) {
                displayFormattedConditions(tradeCondition, payCondition, transportCondition, conditionData);
            }
        },
        error: function() {
            conditionsLoaded++;
            if (conditionsLoaded === totalConditions) {
                displayFormattedConditions(tradeCondition, payCondition, transportCondition, conditionData);
            }
        }
    });

    // 加载付款条件数据
    $.ajax({
        url: baselocation + '/order/salesLogin/getPayConditions',
        type: 'POST',
        success: function(result) {
            if (result.code === 1 && result.data) {
                result.data.forEach(function(item) {
                    conditionData.pay[item.付款条件] = item.付款条件名;
                });
            }
            conditionsLoaded++;
            if (conditionsLoaded === totalConditions) {
                displayFormattedConditions(tradeCondition, payCondition, transportCondition, conditionData);
            }
        },
        error: function() {
            conditionsLoaded++;
            if (conditionsLoaded === totalConditions) {
                displayFormattedConditions(tradeCondition, payCondition, transportCondition, conditionData);
            }
        }
    });

    // 加载运输条件数据
    $.ajax({
        url: baselocation + '/order/salesLogin/getTransportConditions',
        type: 'POST',
        success: function(result) {
            if (result.code === 1 && result.data) {
                result.data.forEach(function(item) {
                    conditionData.transport[item.运输条件] = item.运输条件名;
                });
            }
            conditionsLoaded++;
            if (conditionsLoaded === totalConditions) {
                displayFormattedConditions(tradeCondition, payCondition, transportCondition, conditionData);
            }
        },
        error: function() {
            conditionsLoaded++;
            if (conditionsLoaded === totalConditions) {
                displayFormattedConditions(tradeCondition, payCondition, transportCondition, conditionData);
            }
        }
    });
}

// 显示格式化的条件字段
function displayFormattedConditions(tradeCondition, payCondition, transportCondition, conditionData) {
    // 格式化交易条件
    var tradeConditionText = '';
    if (tradeCondition && conditionData.trade[tradeCondition]) {
        tradeConditionText = tradeCondition + ' ' + conditionData.trade[tradeCondition];
    } else if (tradeCondition) {
        tradeConditionText = tradeCondition;
    }
    $('#tradeCondition').val(tradeConditionText);

    // 格式化付款条件
    var payConditionText = '';
    if (payCondition && conditionData.pay[payCondition]) {
        payConditionText = payCondition + ' ' + conditionData.pay[payCondition];
    } else if (payCondition) {
        payConditionText = payCondition;
    }
    $('#payCondition').val(payConditionText);

    // 格式化运输条件
    var transportConditionText = '';
    if (transportCondition && conditionData.transport[transportCondition]) {
        transportConditionText = transportCondition + ' ' + conditionData.transport[transportCondition];
    } else if (transportCondition) {
        transportConditionText = transportCondition;
    }
    $('#transportCondition').val(transportConditionText);

    console.log('详情页面格式化显示 - 交易条件:', tradeConditionText, '付款条件:', payConditionText, '运输条件:', transportConditionText);
}

// 根据税代码获取税率（详情页面专用）
function getTaxRateByTaxCodeForDetail(taxCode) {
    if (!taxCode) {
        return;
    }

    $.ajax({
        url: baselocation + '/salesCommon/taxRateCodeFun/json',
        type: 'POST',
        data: {taxRateCode: taxCode},
        success: function(result) {
            console.log('支付请求详情页面获取税率结果:', result);
            if (result.code === 1 && result.data && result.data.taxRate) {
                $('#taxRate').val(result.data.taxRate);
                console.log('支付请求详情页面设置税率:', result.data.taxRate);
            } else {
                console.log('支付请求详情页面未找到对应的税率数据');
                // 如果找不到税率，显示税代码作为兼容
                $('#taxRate').val(taxCode);
            }
        },
        error: function(xhr, status, error) {
            console.error('支付请求详情页面获取税率失败:', error);
            // 如果请求失败，显示税代码作为兼容
            $('#taxRate').val(taxCode);
        }
    });
}

function closeLayer() {
    var index = parent.layer.getFrameIndex(window.name);
    parent.layer.close(index);
    parent.location.reload();
}
