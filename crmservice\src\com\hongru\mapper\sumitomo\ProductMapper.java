package com.hongru.mapper.sumitomo;

import com.baomidou.mybatisplus.mapper.BaseMapper;
import com.hongru.entity.sumitomo.Product;

import java.util.List;

import org.apache.ibatis.annotations.Param;

public interface ProductMapper extends BaseMapper<Product> {

    /**
     * 获取客户表详情
     * @throws
     * @return
     */
    List<String> getProductListByCustomerCode(@Param("customerCode")String customerCode, @Param("printShield")String printShield);

    /**
     * 根据产品代码获取产品信息
     * @param productCode 产品代码
     * @param printShield 打印屏蔽
     * @throws
     * @return
     */
    Product getProductInfo(@Param("productCode")String productCode, @Param("printShield")String printShield);

    /**
     * 获取产品尺寸列表
     * @param customerCode 客户代码
     * @param productCode 产品代码
     * @param printShield 打印屏蔽
     * @throws
     * @return
     */
    List<String> getProductSizeList(@Param("customerCode")String customerCode, @Param("productCode")String productCode, @Param("printShield")String printShield);

    /**
     * 获取产品线盘名称列表
     * @param customerCode 客户代码
     * @param productCode 产品代码
     * @param size 尺寸
     * @param printShield 打印屏蔽
     * @throws
     * @return
     */
    List<String> getProductWireReelNameList(@Param("customerCode")String customerCode, @Param("productCode")String productCode, @Param("size")String size, @Param("printShield")String printShield);
}
