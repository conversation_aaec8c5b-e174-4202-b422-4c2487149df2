package com.hongru.mapper.businessOps;

import com.baomidou.mybatisplus.mapper.BaseMapper;
import com.hongru.entity.businessOps.TaxRateCalculation;

import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

public interface TaxRateCalculationMapper extends BaseMapper<TaxRateCalculation> {
	int insertTaxRateCalculation(@Param("taxRateCalculation") TaxRateCalculation taxRateCalculation);
  
	TaxRateCalculation selectTaxRateCalculationById(@Param("id") int id);

    List<TaxRateCalculation> listTaxRateCalculation(@Param("taxCalcName") String taxCalcName, @Param("taxRate") BigDecimal taxRate);

    void updateTaxRateCalculation(@Param("taxRateCalculation") TaxRateCalculation taxratecalculation);

    void deleteTaxRateCalculation(@Param("id") Integer id);
    
    int selectMaxTaxCode();
    
    TaxRateCalculation findTaxRateByTaxCode(@Param("taxRateCode") String taxRateCode);
}
