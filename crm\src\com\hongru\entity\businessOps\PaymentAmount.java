package com.hongru.entity.businessOps;

import java.math.BigDecimal;

import com.baomidou.mybatisplus.annotations.TableField;
import com.baomidou.mybatisplus.annotations.TableId;
import com.baomidou.mybatisplus.annotations.TableName;
import com.baomidou.mybatisplus.enums.IdType;

@TableName("支付额表")
public class PaymentAmount {
    /* 支付额ID */
    @TableId(value = "支付额ID", type = IdType.AUTO)
    protected int id;
    /* 据点 */
    @TableField("据点")
    protected String stronghold;
    /* 支付请求NO */
    @TableField("支付请求NO")
    protected String paymentRequestNo;
    /* 支付请求日 */
    @TableField("支付请求日")
    protected String paymentRequestDate;
    /* 支付日期 */
    @TableField("支付日期")
    protected String paymentDate;
    /* 支付金额 */
    @TableField("支付金额")
    protected BigDecimal paymentAmount;
    /* 签约方代码 */
    @TableField("签约方代码")
    protected String contractorCode;
    /* 客户代码 */
    @TableField("客户代码")
    protected String customerCode;
    /* 客户简称 */
    @TableField("客户简称")
    protected String customerAlias;
    /* 交易条件 */
    @TableField("交易条件")
    protected String tradeCondition;
    /* 付款条件 */
    @TableField("付款条件")
    protected String paymentCondition;
    /* 运输条件 */
    @TableField("运输条件")
    protected String transportCondition;
    /* 结算货币 */
    @TableField("结算货币")
    protected String settlementCurrency;
    /* 税代码 */
    @TableField("税代码")
    protected String taxCode;
    /* 税率 */
    @TableField(exist = false)
    protected String taxRate;
    /* 支付请求确认 */
    @TableField("支付请求确认")
    protected String paymentRequestConfirm;
    /* 创建人姓名 */
    @TableField("创建人")
    protected String creatorName;
    /* 创建时间 */
    @TableField("创建时间")
    protected String createdTime;
    /* 更新人姓名 */
    @TableField("更新人")
    protected String updaterName;
    /* 更新时间 */
    @TableField("更新时间")
    protected String updatedTime;
    /* 确认者 */
    @TableField("确认者")
    protected String confirmer;
    /* 确认时间 */
    @TableField("确认时间")
    protected String confirmTime;
    
    public int getId() {
        return id;
    }
    public void setId(int id) {
        this.id = id;
    }

    public String getStronghold() {
        return stronghold;
    }

    public void setStronghold(String stronghold) {
        this.stronghold = stronghold;
    }
    
    public String getPaymentRequestNo() {
        return paymentRequestNo;
    }
    public void setPaymentRequestNo(String paymentRequestNo) {
        this.paymentRequestNo = paymentRequestNo;
    }
    
    public String getPaymentRequestDate() {
        return paymentRequestDate;
    }
    public void setPaymentRequestDate(String paymentRequestDate) {
        this.paymentRequestDate = paymentRequestDate;
    }

    public String getPaymentDate() {
        return paymentDate;
    }
    public void setPaymentDate(String paymentDate) {
        this.paymentDate = paymentDate;
    }

    public BigDecimal getPaymentAmount() {
        return paymentAmount;
    }
    public void setPaymentAmount(BigDecimal paymentAmount) {
        this.paymentAmount = paymentAmount;
    }

    public String getContractorCode() {
        return contractorCode;
    }
    public void setContractorCode(String contractorCode) {
        this.contractorCode = contractorCode;
    }
    
    public String getCustomerCode() {
        return customerCode;
    }
    public void setCustomerCode(String customerCode) {
        this.customerCode = customerCode;
    }
    
    public String getCustomerAlias() {
        return customerAlias;
    }
    public void setCustomerAlias(String customerAlias) {
        this.customerAlias = customerAlias;
    }
    
    public String getTradeCondition() {
        return tradeCondition;
    }
    public void setTradeCondition(String tradeCondition) {
        this.tradeCondition = tradeCondition;
    }
    
    public String getPaymentCondition() {
        return paymentCondition;
    }
    public void setPaymentCondition(String paymentCondition) {
        this.paymentCondition = paymentCondition;
    }
    
    public String getTransportCondition() {
        return transportCondition;
    }
    public void setTransportCondition(String transportCondition) {
        this.transportCondition = transportCondition;
    }
    
    public String getSettlementCurrency() {
        return settlementCurrency;
    }
    public void setSettlementCurrency(String settlementCurrency) {
        this.settlementCurrency = settlementCurrency;
    }
    
    public String getTaxCode() {
        return taxCode;
    }
    public void setTaxCode(String taxCode) {
        this.taxCode = taxCode;
    }

    public String getPaymentRequestConfirm() {
        return paymentRequestConfirm;
    }
    public void setPaymentRequestConfirm(String paymentRequestConfirm) {
        this.paymentRequestConfirm = paymentRequestConfirm;
    }
    
    public String getCreatorName() {
        return creatorName;
    }
    public void setCreatorName(String creatorName) {
        this.creatorName = creatorName;
    }
    
    public String getCreatedTime() {
        return createdTime;
    }
    public void setCreatedTime(String createdTime) {
        this.createdTime = createdTime;
    }
    
    public String getUpdaterName() {
        return updaterName;
    }
    public void setUpdaterName(String updaterName) {
        this.updaterName = updaterName;
    }
    
    public String getUpdatedTime() {
        return updatedTime;
    }
    public void setUpdatedTime(String updatedTime) {
        this.updatedTime = updatedTime;
    }
    
    public String getConfirmer() {
        return confirmer;
    }
    public void setConfirmer(String confirmer) {
        this.confirmer = confirmer;
    }
    
    public String getConfirmTime() {
        return confirmTime;
    }
    public void setConfirmTime(String confirmTime) {
        this.confirmTime = confirmTime;
    }

    public String getTaxRate() {
        return taxRate;
    }

    public void setTaxRate(String taxRate) {
        this.taxRate = taxRate;
    }
}
