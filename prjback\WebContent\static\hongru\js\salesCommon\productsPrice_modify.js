layui.use(['laydate','upload','element','layer','form'], function() {
    var form = layui.form;
    var $ = layui.jquery
        ,upload = layui.upload
        ,element = layui.element
        ,layer = layui.layer;
     var laydate = layui.laydate;
     
     //开始日期
     var insStart = layui.laydate.render({
         elem: '#applyDateStart'
         ,done: function(value, date){
       	//更新结束日期的最小日期
//       	insEnd.config.min = lay.extend({}, date, {
//       	  month: date.month - 1
//       	});
       	
       	//自动弹出结束日期的选择器
          insEnd.config.elem[0].focus();
         }
       });

     //结束日期
     var insEnd = layui.laydate.render({
         elem: '#applyDateEnd'
         ,done: function(value, date){
       	//更新开始日期的最大日期
//       	insStart.config.max = lay.extend({}, date, {
//       	  month: date.month - 1
//       	});
         }
       }); 

     //客户代码变更检索产品代码List
     form.on('select(customerCodeFun)', function(data){
         var customerCode = $("#customerCode").val();
         var index = layer.load(2,{
             shade:[0.1,'#fff']
         });
         $.ajax({
             url : baselocation+'/salesCommon/productListByCustomerCode/json',
             type : 'post',
             data : {"customerCode":customerCode},
             success : function(result) {
                 layer.closeAll();
                 var html ='<option value="">请选择</option>';
                 if(result.code == 1){
 					var productCodes = result.data;
 					if(productCodes != null && productCodes.length > 0){
 						for(var i = 0; i < productCodes.length; i++) {
 							var productCode = productCodes[i];
 							html+='<option value="'+productCode+'">'+productCode+'</option>';
 						}
 					}
                 }else{
                     layer.alert(result.message);
                 }
                 $("#productCode").html(html);
                 form.render();//重新渲染
             }
         });
     });   
     
    form.on('submit(formDemo)', function(data) {
        var index = layer.load(2, {
            shade : [ 0.1, '#fff' ]
        });
        var actionUrl = $("#submitForm").attr('action');
        $.ajax({
            url : actionUrl,
            type : 'post',
            data : $('#submitForm').serialize(),
            success : function(result) {
                layer.closeAll();
                if (result.code == 1) {
                    parent.layer.msg("操作成功!", {
                        shade : 0.3,
                        time : 1500
                    }, function() {
                        parent.window.location.reload();
                    });
                } else {
                    layer.alert(result.message);
                }
            }
        });
        return false;
    });
});

function closeAll() {
    parent.layer.closeAll();
}
/**
 * 实时动态强制更改用户录入
 * arg1 inputObject
 **/
function amount(th){
    var regStrs = [
        ['^0(\\d+)$', '$1'], //禁止录入整数部分两位以上，但首位为0
        ['[^\\d\\.]+$', ''], //禁止录入任何非数字和点
        ['\\.(\\d?)\\.+', '.$1'], //禁止录入两个以上的点
        ['^(\\d+\\.\\d{4}).+', '$1'] //禁止录入小数点后五位以上
    ];
    for(i=0; i<regStrs.length; i++){
        var reg = new RegExp(regStrs[i][0]);
        th.value = th.value.replace(reg, regStrs[i][1]);
    }
}

function overFormat(th){
    var v = th.value;
    if(v === ''){
        v = "";
    }else if(v === '0'){//表示输入框值为0
        v = '0.0000';
    }else if(v === '0.'){//表示输入框值为0.
        v = '0.0000';
    }else if(/^0+\d+\.?\d*.*$/.test(v)){// ^0+表示以一个或多个0开头  \d+表示之后需出现一个或多个数字  \.?表示之后需出现0个或一个小数点  \d*表示之后需出现0个或多个的数字 .*$表示之后需出现0个或多个除换行和回车以外的任意字符结尾
        v = v.replace(/^0+(\d+\.?\d*).*$/, '$1'); //^0+表示以一个或多个0开头 (\d+\.?\d*)表示之后需出现（一个或多个数字，0个或一个小数点，0个或多个数字） .*$表示以0个或多个除换行和回车以外的任意字符结尾
        v = inp.getRightPriceFormat(v).val;
    }else if(/^0\.\d$/.test(v)){// ^0表示以0开头 \.表示之后需出现一个小数点 \d$表示以一个数字结尾
        v = v + '0';
    }else if(!/^\d+\.\d{4}$/.test(v)){// ^\d+表示以一个或多个数字开头 \.表示之后需出现一个小数点 \d{2}$表示以两个数字结尾，因为前面有一个!，所以是前面的相反
        if(/^\d+\.\d{4}.+/.test(v)){// ^\d+表示以一个或多个数字开头 \.表示之后需出现一个小数点 \d{2}表示两个数字 .+表示一个或多个除换行和回车以外的任意字符
            v = v.replace(/^(\d+\.\d{4}).*$/, '$1'); //^(\d+\.\d{2})表示以（之后需出现一个或多个数字，之后需出现一个小数点，之后需出现两个数字）开头  .*$表示0个或多个除换行和回车以外的任意字符结尾
        }else if(/^\d+$/.test(v)){// ^\d+$表示以一个或多个数字开头并且以一个或多个数字结尾
            v = v + '.0000';
        }else if(/^\d+\.$/.test(v)){// ^\d+表示以一个或多个数字开头 \.$表示以一个小数点结尾
            v = v + '0000';
        }else if(/^\d+\.\d$/.test(v)){// ^\d+表示以一个或多个数字开头 \.表示之后需出现一个小数点 \d$表示以一个数字结尾
            v = v + '0';
        }else if(/^[^\d]+\d+\.?\d*$/.test(v)){// ^[^\d]+表示以一个或多个不是数字的字符开头 \d+表示之后需出现一个或多个数字 \.?表示之后需出现0个或一个小数点 \d*$表示以0个或多个数字结尾
            v = v.replace(/^[^\d]+(\d+\.?\d*)$/, '$1');//^[^\d]+表示以一个或多个不是数字的字符开头 (\d+\.?\d*)$表示以（之后需出现一个或多个数字，之后需出现0个或一个小数点，之后需出现0个或多个数字）结尾
        }else if(/\d+/.test(v)){// \d+表示之后需出现一个或多个数字 /.表示之后需出现一个小数点
            v = v.replace(/^[^\d]*(\d+\.?\d*).*$/, '$1'); //^[^\d]*表示以0个或多个不是数字的字符开头 (\d+\.?\d*)表示（之后需出现一个或多个数字，之后需出现0个或一个小数点，之后需出现0个或多个数字） .*$表示以（0个或多个除换行和回车以外的任意字符）结尾
            ty = false;
        }else if(/^0+\d+\.?\d*$/.test(v)){// ^0+表示以一个或多个0开头 \d+表示之后需出现一个或多个数字 \.?表示之后需出现0个或一个小数点 \d*$表示以0个或多个数字结尾
            v = v.replace(/^0+(\d+\.?\d*)$/, '$1'); //^0+表示以一个或多个0开头 (\d+\.?\d*)$表示以（之后需出现一个或多个数字，之后需出现0个或一个小数点，之后需出现0个或多个数字）结尾
            ty = false;
        }else{
            v = "";
        }
    }
    th.value = v;
}