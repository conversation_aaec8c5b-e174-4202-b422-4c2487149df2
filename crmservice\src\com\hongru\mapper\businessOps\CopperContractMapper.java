package com.hongru.mapper.businessOps;

import com.baomidou.mybatisplus.mapper.BaseMapper;
import com.hongru.entity.businessOps.CopperContract;
import com.hongru.support.page.PageInfo;

import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface CopperContractMapper extends BaseMapper<CopperContract> {
	int insertCopperContract(@Param("copperContract") CopperContract copperContract);
  
	CopperContract selectCopperContractById(@Param("id") int id);

    List<CopperContract> copperContractListByPage(@Param("pageInfo") PageInfo pageInfo, @Param("customerCode") String customerCode, @Param("copperSignNo") String copperSignNo, @Param("copperSignType") String copperSignType, @Param("copperCondition") String copperCondition, @Param("appointmentDate") String appointmentDate, @Param("useMonth") String useMonth, @Param("status") String status);
    Integer copperContractListByPageCount(@Param("customerCode") String customerCode, @Param("copperSignNo") String copperSignNo, @Param("copperSignType") String copperSignType, @Param("copperCondition") String copperCondition, @Param("appointmentDate") String appointmentDate, @Param("useMonth") String useMonth, @Param("status") String status);

    void updateCopperContract(@Param("copperContract") CopperContract copperContract);

    void updateCopperContractStatus(@Param("status") Short status, @Param("id") Integer id);

    /**
     * 获取铜合同列表（用于销售额登录）
     * @param customerCode 客户代码
     * @param copperContractType 铜合同类别
     * @param useMonth 使用月（年月格式：YYYY-MM）
     * @param status 状态
     * @return List<CopperContract>
     */
    List<CopperContract> getCopperContractListForSalesLogin(@Param("customerCode") String customerCode,
                                                           @Param("copperContractType") String copperContractType,
                                                           @Param("useMonth") String useMonth,
                                                           @Param("status") String status);

    /**
     * 根据铜签约NO和使用月查询铜合同详情（关联铜条件表）
     * 
     * @param copperSignNo 铜签约NO
     * @param useMonth     使用月（年月格式：YYYY-MM）
     * @return CopperContract
     */
    CopperContract getCopperContractDetailWithCondition(@Param("copperSignNo") String copperSignNo,
            @Param("useMonth") String useMonth);

    /**
     * 更新铜合同的实际数量和剩余数量
     * 
     * @param copperSignNo      铜签约NO
     * @param actualQuantity    实际数量
     * @param remainingQuantity 剩余数量
     */
    void updateCopperContractQuantity(@Param("copperSignNo") String copperSignNo,
            @Param("actualQuantity") java.math.BigDecimal actualQuantity,
            @Param("remainingQuantity") java.math.BigDecimal remainingQuantity);
}
