<%@ page language="java" import="java.util.*"
         contentType="text/html; charset=utf-8"%>
<%@ include file="/WEB-INF/layouts/base.jsp"%>
<html>
<head>
    <title>出库数量新增列表</title>
    <style>
        /*设置数据表表头字体*/
        .layui-table th .layui-table-cell{
            text-align: center;
        }
    </style>
</head>
<body class="gray-bg">
<div class="ibox float-e-margins">
    <div class="ibox-content">
        <div class="layui-collapse">
            <div class="layui-colla-item">
                <div class="layui-colla-content layui-show">
                    <form id="formSearch" class="layui-form hr-form-search toolbar table-tool-mini" method="post" action="">
                        <div class="layui-form-item">
                            <div class="layui-inline layui-col-md3">
                                <label class="layui-form-label">到货日期:</label>
                                <div class="layui-input-block" style="display: flex;justify-content: space-between;align-items: center;align-content: center">
									<input type="text" name="outboundStartDate" autocomplete="off" class="layui-input"
											id="outboundStartDate" style="display: block;width: 48%" >
											 ~ 
									<input type="text" name="outboundEndDate" autocomplete="off" class="layui-input"
											id="outboundEndDate" style="display: block;width: 48%" >
								</div>
                            </div>
                        	<div class="layui-inline layui-col-md2">
		                        <label class="layui-form-label">客户:</label>
		                        <div class="layui-input-block">
		                            <select class="layui-select" id="customerCode" name="customerCode" lay-search="true">
		                                <option value="">请选择</option>
		                                <c:forEach items="${customersList}" var="customers">
		                                    <option value="${customers.customerCode}" >${customers.customerCode} - ${customers.customerAlias}</option>
		                                </c:forEach>
		                            </select>
		                        </div>
		                    </div>

                            <div class="layui-inline layui-col-md1 hr-div-btn">
                                <button type="button" id="btn_query" onclick="search();" class="layui-btn layui-bg-blue"><i class="layui-icon layui-icon-search"></i>检索</button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        <%--初始化table--%>
        <table class="layui-hide" id="demo" lay-filter="test"></table>
        <!-- 自定义表中工具栏 -->
        <script type="text/html" id="barDemo">
            <!-- <a class="layui-btn layui-btn-xs layui-bg-blue" lay-event="detail"><i class="layui-icon layui-icon-search"></i>详情</a> -->
        </script>
        <!-- 自定义头部工具栏 -->
        <script type="text/html" id="toolbarDemo">
            <div class="layui-btn-container">
                <button class="layui-btn layui-btn-sm layui-bg-blue" lay-event="confirm"><i class="layui-icon layui-icon-ok"></i>确认</button>
                <!-- <button class="layui-btn layui-btn-sm layui-bg-green" lay-event="export"><i class="layui-icon layui-icon-read"></i>预览</button> -->
                <button class="layui-btn layui-btn-primary layui-btn-sm" lay-event="refresh"><i class="layui-icon layui-icon-refresh"></i>刷新</button>
            </div>
        </script>
    </div>
</div>
<myfooter>
    <script src="${ctxsta}/hongru/js/order/shipmentQuantityEntry_add.js?time=2"></script>
</myfooter>
</body>
</html>
