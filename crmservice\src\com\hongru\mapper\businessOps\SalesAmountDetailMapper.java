package com.hongru.mapper.businessOps;

import com.baomidou.mybatisplus.mapper.BaseMapper;
import com.hongru.entity.businessOps.SalesAmountDetail;

import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface SalesAmountDetailMapper extends BaseMapper<SalesAmountDetail> {

    /**
     * 根据销售额NO获取销售额明细列表
     *
     * @param salesAmountNo 销售额NO
     * @return 销售额明细列表
     */
    List<SalesAmountDetail> getSalesAmountDetailsByNo(@Param("salesAmountNo") String salesAmountNo);

    /**
     * 更新销售额明细数据
     *
     * @param detail 销售额明细
     * @return 更新结果
     */
    int updateSalesAmountDetail(@Param("detail") SalesAmountDetail detail);

    /**
     * 根据销售额NO删除销售额明细数据
     *
     * @param salesAmountNo 销售额NO
     * @return 删除结果
     */
    int deleteBySalesAmountNo(@Param("salesAmountNo") String salesAmountNo);
}
