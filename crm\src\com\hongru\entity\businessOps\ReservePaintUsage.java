package com.hongru.entity.businessOps;

import com.baomidou.mybatisplus.annotations.TableId;
import com.baomidou.mybatisplus.annotations.TableName;
import com.baomidou.mybatisplus.enums.IdType;

import java.math.BigDecimal;

@TableName("预定油漆使用量")//CostPrice
public class ReservePaintUsage {
	/* 流水号 */
	@TableId(value="流水号", type= IdType.AUTO)
	protected int reservePaintUsageId;
	/* 导入标识 */
	protected int importId;
	/* 年月 */
	protected String yearMonth;
	/* 年 */
	protected int year;
	/* 月 */
	protected int month;
	/* 区分 */
	protected String distinguish;
	/* 原料 */
	protected String rawMaterial;
	/* 油漆品名 */
	protected String paintName;
	/* 预定使用量 */
	protected BigDecimal reserveUsage;
	/* 金额 */
	protected BigDecimal reserveamountOfMoney;
	/* 创建人标识 */
	protected int creatorId;
	/* 创建人姓名 */
	protected String creatorName;
	/* 创建时间 */
	protected String createdTime;

	public int getReservePaintUsageId() {
		return reservePaintUsageId;
	}

	public void setReservePaintUsageId(int reservePaintUsageId) {
		this.reservePaintUsageId = reservePaintUsageId;
	}

	public String getYearMonth() {
		return yearMonth;
	}

	public void setYearMonth(String yearMonth) {
		this.yearMonth = yearMonth;
	}

	public String getDistinguish() {
		return distinguish;
	}

	public void setDistinguish(String distinguish) {
		this.distinguish = distinguish;
	}

	public String getRawMaterial() {
		return rawMaterial;
	}

	public void setRawMaterial(String rawMaterial) {
		this.rawMaterial = rawMaterial;
	}

	public String getPaintName() {
		return paintName;
	}

	public void setPaintName(String paintName) {
		this.paintName = paintName;
	}

	public BigDecimal getReserveUsage() {
		return reserveUsage;
	}

	public void setReserveUsage(BigDecimal reserveUsage) {
		this.reserveUsage = reserveUsage;
	}

	public BigDecimal getReserveamountOfMoney() {
		return reserveamountOfMoney;
	}

	public void setReserveamountOfMoney(BigDecimal reserveamountOfMoney) {
		this.reserveamountOfMoney = reserveamountOfMoney;
	}

	public int getYear() {
		return year;
	}

	public void setYear(int year) {
		this.year = year;
	}

	public int getMonth() {
		return month;
	}

	public void setMonth(int month) {
		this.month = month;
	}

	public int getCreatorId() {
		return creatorId;
	}

	public void setCreatorId(int creatorId) {
		this.creatorId = creatorId;
	}

	public String getCreatorName() {
		return creatorName;
	}

	public void setCreatorName(String creatorName) {
		this.creatorName = creatorName;
	}

	public String getCreatedTime() {
		return createdTime;
	}

	public void setCreatedTime(String createdTime) {
		this.createdTime = createdTime;
	}

	public int getImportId() {
		return importId;
	}

	public void setImportId(int importId) {
		this.importId = importId;
	}
}