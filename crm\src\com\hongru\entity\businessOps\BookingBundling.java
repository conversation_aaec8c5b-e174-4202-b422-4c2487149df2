package com.hongru.entity.businessOps;

import com.baomidou.mybatisplus.annotations.TableField;
import com.baomidou.mybatisplus.annotations.TableId;
import com.baomidou.mybatisplus.annotations.TableName;
import com.baomidou.mybatisplus.enums.IdType;

import java.math.BigDecimal;

@TableName("预定捆包费表")//BookingBundling
public class BookingBundling {
	/* 流水号 */
	@TableId(value="流水号", type= IdType.AUTO)
	protected int costId;
	/* 导入标识 */
	protected int importId;
	/* 年月 */
	protected String yearMonth;
	/* 年 */
	protected int year;
	/* 月 */
	protected int month;
	/* 区分 */
	protected String bookingCode;
	/* 捆包方法 */
	protected String bundlingMethod;
	/* 捆包费 */
	protected BigDecimal bundlingCost;
	/* 创建人标识 */
	protected int creatorId;
	/* 创建人姓名 */
	protected String creatorName;
	/* 创建时间 */
	protected String createdTime;

	@TableField(exist = false)
	/* 直接部门名称 */
	protected String directDepartmentName;
	@TableField(exist = false)
	/* 辅助部门名称 */
	protected String auxiliaryDepartmentName;

	public int getCostId() {
		return costId;
	}

	public void setCostId(int costId) {
		this.costId = costId;
	}

	public String getYearMonth() {
		return yearMonth;
	}

	public void setYearMonth(String yearMonth) {
		this.yearMonth = yearMonth;
	}

	public int getYear() {
		return year;
	}

	public void setYear(int year) {
		this.year = year;
	}

	public int getMonth() {
		return month;
	}

	public void setMonth(int month) {
		this.month = month;
	}

	public String getBookingCode() {
		return bookingCode;
	}

	public void setBookingCode(String bookingCode) {
		this.bookingCode = bookingCode;
	}

	public String getBundlingMethod() {
		return bundlingMethod;
	}

	public void setBundlingMethod(String bundlingMethod) {
		this.bundlingMethod = bundlingMethod;
	}

	public BigDecimal getBundlingCost() {
		return bundlingCost;
	}

	public void setBundlingCost(BigDecimal bundlingCost) {
		this.bundlingCost = bundlingCost;
	}

	public int getCreatorId() {
		return creatorId;
	}

	public void setCreatorId(int creatorId) {
		this.creatorId = creatorId;
	}

	public String getCreatorName() {
		return creatorName;
	}

	public void setCreatorName(String creatorName) {
		this.creatorName = creatorName;
	}

	public String getCreatedTime() {
		return createdTime;
	}

	public void setCreatedTime(String createdTime) {
		this.createdTime = createdTime;
	}

	public int getImportId() {
		return importId;
	}

	public void setImportId(int importId) {
		this.importId = importId;
	}

	public String getDirectDepartmentName() {
		return directDepartmentName;
	}

	public void setDirectDepartmentName(String directDepartmentName) {
		this.directDepartmentName = directDepartmentName;
	}

	public String getAuxiliaryDepartmentName() {
		return auxiliaryDepartmentName;
	}

	public void setAuxiliaryDepartmentName(String auxiliaryDepartmentName) {
		this.auxiliaryDepartmentName = auxiliaryDepartmentName;
	}
}