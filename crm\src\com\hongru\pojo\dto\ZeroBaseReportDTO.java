package com.hongru.pojo.dto;

import java.math.BigDecimal;
import java.util.List;

import com.hongru.support.page.PageInfo;

/**
 * 0-base报表DTO
 */
public class ZeroBaseReportDTO {
    
    private String productMiddleCategory; // 品目中分类名
    private String customerAlias; // 需求方略称
    private String productCode; // 品名
    private String labelSizeName; // 尺寸
    private String currency; // 货币
    private BigDecimal zeroBaseDirectCost; // 0base直接成本（单价・功能货币）
    private BigDecimal zeroBaseSalesUnitPrice; // 销售额0base单价（不含税・功能货币）
    private BigDecimal salesQuantity; // 销售额数
    private String accountingYear; // 会计年
    private String accountingYearMonth; // 会计年月
    
    // 分页信息
    private PageInfo pageInfo;
    
    // 查询结果列表
    private List<ZeroBaseReportDTO> zeroBaseReportList;
    
    public String getProductMiddleCategory() {
        return productMiddleCategory;
    }
    
    public void setProductMiddleCategory(String productMiddleCategory) {
        this.productMiddleCategory = productMiddleCategory;
    }
    
    public String getCustomerAlias() {
        return customerAlias;
    }
    
    public void setCustomerAlias(String customerAlias) {
        this.customerAlias = customerAlias;
    }
    
    public String getProductCode() {
        return productCode;
    }
    
    public void setProductCode(String productCode) {
        this.productCode = productCode;
    }
    
    public String getLabelSizeName() {
        return labelSizeName;
    }
    
    public void setLabelSizeName(String labelSizeName) {
        this.labelSizeName = labelSizeName;
    }
    
    public String getCurrency() {
        return currency;
    }
    
    public void setCurrency(String currency) {
        this.currency = currency;
    }
    
    public BigDecimal getZeroBaseDirectCost() {
        return zeroBaseDirectCost;
    }
    
    public void setZeroBaseDirectCost(BigDecimal zeroBaseDirectCost) {
        this.zeroBaseDirectCost = zeroBaseDirectCost;
    }
    
    public BigDecimal getZeroBaseSalesUnitPrice() {
        return zeroBaseSalesUnitPrice;
    }
    
    public void setZeroBaseSalesUnitPrice(BigDecimal zeroBaseSalesUnitPrice) {
        this.zeroBaseSalesUnitPrice = zeroBaseSalesUnitPrice;
    }
    
    public BigDecimal getSalesQuantity() {
        return salesQuantity;
    }
    
    public void setSalesQuantity(BigDecimal salesQuantity) {
        this.salesQuantity = salesQuantity;
    }
    
    public String getAccountingYear() {
        return accountingYear;
    }
    
    public void setAccountingYear(String accountingYear) {
        this.accountingYear = accountingYear;
    }
    
    public String getAccountingYearMonth() {
        return accountingYearMonth;
    }
    
    public void setAccountingYearMonth(String accountingYearMonth) {
        this.accountingYearMonth = accountingYearMonth;
    }
    
    public PageInfo getPageInfo() {
        return pageInfo;
    }
    
    public void setPageInfo(PageInfo pageInfo) {
        this.pageInfo = pageInfo;
    }
    
    public List<ZeroBaseReportDTO> getZeroBaseReportList() {
        return zeroBaseReportList;
    }
    
    public void setZeroBaseReportList(List<ZeroBaseReportDTO> zeroBaseReportList) {
        this.zeroBaseReportList = zeroBaseReportList;
    }
}
