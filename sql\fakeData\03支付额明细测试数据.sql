-- Insert test data for payment details table
INSERT INTO 支付额明细表 (销售额NO, 产品代码, 产品中分类, 数量, 销售单价, 铜base, 创建人, 更新人) VALUES
-- SSK1 data
('S202502001', 'MW-EF-001', 'Round Wire MW EF', 400.00, 128.80, 65.20, 'admin', 'admin'),

-- WINTEC2 data
('S202502002', 'MW-FL-001', 'Flat Wire MW', 145000.00, 100.15, 55.30, 'admin', 'admin'),

-- Shanghai Mitsubishi data
('S202502003', 'MW-EF-002', 'Round Wire MW EF', 12500.00, 79.05, 45.80, 'admin', 'admin'),

-- Shanghai Sanchong data
('S202502004', 'MW-EF-003', 'Round Wire MW EF', 7000.00, 78.45, 45.50, 'admin', 'admin'),

-- Shanghai Weike data
('S202502005', 'MW-EF-004', 'Round Wire MW EF', 4300.00, 96.80, 58.20, 'admin', 'admin'),

-- Fuji Kiko data
('S202502006', 'MW-EF-005', 'Round Wire MW EF', 4800.00, 79.55, 45.80, 'admin', 'admin'),

-- Toyo Electric data
('S202502007', 'UF-001', 'Ultra Fine Wire', 100.00, 359.20, 180.50, 'admin', 'admin'),

-- Dongguan Lisheng data
('S202502008', 'MW-EF-006', 'Round Wire MW EF', 2400.00, 82.00, 48.50, 'admin', 'admin'),

-- Delta Electronics data
('S202502009', 'MW-FL-002', 'Flat Wire MW', 8000.00, 88.55, 52.30, 'admin', 'admin'),

-- Foshan Aisan data
('S202502010', 'MW-EF-007', 'Round Wire MW EF', 500.00, 100.90, 60.20, 'admin', 'admin'),

-- India Denso data
('S202502011', 'UF-002', 'Ultra Fine Wire', 1550.00, 20.03, 10.50, 'admin', 'admin'),

-- Pakistan Honda data (multiple lines)
('S202502012', 'MW-EF-008', 'Round Wire MW EF', 7100.00, 17.54, 9.80, 'admin', 'admin'),
('S202502012', 'UF-003', 'Ultra Fine Wire', 380.00, 27.23, 15.50, 'admin', 'admin'),

-- Japan Citizen data
('S202502013', 'UF-004', 'Ultra Fine Wire', 135.00, 103.05, 60.80, 'admin', 'admin'),

-- Korea Denso data
('S202502014', 'UF-005', 'Ultra Fine Wire', 1850.00, 20.88, 11.20, 'admin', 'admin');

-- Verify data insertion
SELECT 
    a.客户简称,
    a.结算货币,
    b.产品中分类,
    SUM(b.数量) as 支付数量,
    SUM(b.数量 * b.销售单价) as 支付金额,
    SUM(b.数量 * b.铜base) as 铜金额
FROM 销售额表 a
LEFT JOIN 支付额明细表 b ON a.销售额NO = b.销售额NO
GROUP BY a.客户简称, a.结算货币, b.产品中分类
ORDER BY a.客户简称, b.产品中分类; 