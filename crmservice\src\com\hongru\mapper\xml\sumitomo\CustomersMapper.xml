<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hongru.mapper.sumitomo.CustomersMapper">
  <sql id="customers_sql">
	pc.[流水号] AS id,pc.[客户代码] AS customerCode,pc.[客户名称] AS customerName,pc.[客户地址] AS customerAddress,
	pc.[客户英文] AS customerEnglish,pc.[客户简称] AS customerAlias,pc.[客户电话] AS customerPhone,pc.[客户传真] AS customerFax,
	pc.[税号] AS taxNumber,pc.[帐号] AS accountNumber,pc.[收货人] AS recipient,pc.[性质] AS nature
  </sql>
 
  <select id="selectCustomersList" resultType="com.hongru.entity.sumitomo.Customers">
	 SELECT
 	 <include refid="customers_sql"/>
	 FROM [sumitomo].[dbo].[客户表] pc
	 ORDER BY pc.[客户代码]
  </select>

  <select id="selectByCustomerCode" resultType="com.hongru.entity.sumitomo.Customers">
	 SELECT
 	 <include refid="customers_sql"/>
	 FROM [sumitomo].[dbo].[客户表] pc
	 WHERE pc.[客户代码] = #{customerCode}
  </select>
</mapper>