package com.hongru.entity.sumitomo;

import com.baomidou.mybatisplus.annotations.TableId;
import com.baomidou.mybatisplus.annotations.TableName;
import com.baomidou.mybatisplus.enums.IdType;

@TableName("产品表")//sumitomo
public class Product {

	/* 流水号 */
	@TableId(value="productId", type= IdType.AUTO)
	protected int productId;
	/* 产品代码 */
	protected String productCode;
	/* 条码 */
	protected String barCode;
	/* 尺寸 */
	protected Double size;
	/* 平角线尺寸 */
	protected Double pjxSize;
	/* 最大重量 */
	protected Double maximumWeight;
	/* 标准重量 */
	protected Double standardWeight;
	/* 最小重量 */
	protected Double minimumWeight;
	/* 包装物重量 */
	protected Double weightOfPackage;
	/* 木托容量 */
	protected Short woodenPalletCapacity;
	/* 皮重 */
	protected Double tare;
	/* 用户名 */
	protected String userName;
	/* 上行 */
	protected String upstream;
	/* 字体1 */
	protected String font1;
	/* DOT */
	protected String dot;
	/* 下口线标记 */
	protected String bottomLineMark;
	/* 下行 */
	protected String down;
	/* 字体2 */
	protected String font2;
	/* 线盘名称 */
	protected String wireReelName;
	/* 标签尺寸名称 */
	protected String labelSizeName;
	/* 木托标签 */
	protected String woodenPalletLabel;
	/* 部品编号 */
	protected String partNo;
	/* 标记 */
	protected String mark;
	/* 量少品重量 */
	protected Double weightOfLessProduct;
	/* 量少品重量2 */
	protected Double weightOfLessProduct2;
	/* 量少品数量 */
	protected Double quantityOfLessProduct;
	/* 在库量 */
	protected Double quantityInStock;
	/* 需求量 */
	protected Double requirement;
	/* FBT */
	protected String fbt;
	/* 混入率 */
	protected Double mixingRate;
	/* 混入率2 */
	protected Double mixingRate2;
	/* 汽车客户 */
	protected String automobileCustomer;
	/* 打印屏蔽 */
	protected String printShield;
	/* 包装要求 */
	protected String packagingRequirement;
	/* 包装方式 */
	protected String packagingMethod;
	/* 机型 */
	protected String model;
	/* 备注 */
	protected String remark;
	/* 标签数量 */
	protected String numberOfLabel;
	/* 创建人姓名 */
	protected String creatName;
	/* 创建时间 */
	protected String creatTime;
	/* 最后修改人姓名 */
	protected String modifieName;
	/* 最后修改时间 */
	protected String modifieTime;

	public int getProductId() {
		return productId;
	}

	public void setProductId(int productId) {
		this.productId = productId;
	}

	public String getProductCode() {
		return productCode;
	}

	public void setProductCode(String productCode) {
		this.productCode = productCode;
	}

	public String getBarCode() {
		return barCode;
	}

	public void setBarCode(String barCode) {
		this.barCode = barCode;
	}

	public Double getSize() {
		return size;
	}

	public void setSize(Double size) {
		this.size = size;
	}

	public Double getPjxSize() {
		return pjxSize;
	}

	public void setPjxSize(Double pjxSize) {
		this.pjxSize = pjxSize;
	}

	public Double getMaximumWeight() {
		return maximumWeight;
	}

	public void setMaximumWeight(Double maximumWeight) {
		this.maximumWeight = maximumWeight;
	}

	public Double getStandardWeight() {
		return standardWeight;
	}

	public void setStandardWeight(Double standardWeight) {
		this.standardWeight = standardWeight;
	}

	public Double getMinimumWeight() {
		return minimumWeight;
	}

	public void setMinimumWeight(Double minimumWeight) {
		this.minimumWeight = minimumWeight;
	}

	public Double getWeightOfPackage() {
		return weightOfPackage;
	}

	public void setWeightOfPackage(Double weightOfPackage) {
		this.weightOfPackage = weightOfPackage;
	}

	public Short getWoodenPalletCapacity() {
		return woodenPalletCapacity;
	}

	public void setWoodenPalletCapacity(Short woodenPalletCapacity) {
		this.woodenPalletCapacity = woodenPalletCapacity;
	}

	public Double getTare() {
		return tare;
	}

	public void setTare(Double tare) {
		this.tare = tare;
	}

	public String getUserName() {
		return userName;
	}

	public void setUserName(String userName) {
		this.userName = userName;
	}

	public String getUpstream() {
		return upstream;
	}

	public void setUpstream(String upstream) {
		this.upstream = upstream;
	}

	public String getFont1() {
		return font1;
	}

	public void setFont1(String font1) {
		this.font1 = font1;
	}

	public String getDot() {
		return dot;
	}

	public void setDot(String dot) {
		this.dot = dot;
	}

	public String getBottomLineMark() {
		return bottomLineMark;
	}

	public void setBottomLineMark(String bottomLineMark) {
		this.bottomLineMark = bottomLineMark;
	}

	public String getDown() {
		return down;
	}

	public void setDown(String down) {
		this.down = down;
	}

	public String getFont2() {
		return font2;
	}

	public void setFont2(String font2) {
		this.font2 = font2;
	}

	public String getWireReelName() {
		return wireReelName;
	}

	public void setWireReelName(String wireReelName) {
		this.wireReelName = wireReelName;
	}

	public String getLabelSizeName() {
		return labelSizeName;
	}

	public void setLabelSizeName(String labelSizeName) {
		this.labelSizeName = labelSizeName;
	}

	public String getWoodenPalletLabel() {
		return woodenPalletLabel;
	}

	public void setWoodenPalletLabel(String woodenPalletLabel) {
		this.woodenPalletLabel = woodenPalletLabel;
	}

	public String getPartNo() {
		return partNo;
	}

	public void setPartNo(String partNo) {
		this.partNo = partNo;
	}

	public String getMark() {
		return mark;
	}

	public void setMark(String mark) {
		this.mark = mark;
	}

	public Double getWeightOfLessProduct() {
		return weightOfLessProduct;
	}

	public void setWeightOfLessProduct(Double weightOfLessProduct) {
		this.weightOfLessProduct = weightOfLessProduct;
	}

	public Double getWeightOfLessProduct2() {
		return weightOfLessProduct2;
	}

	public void setWeightOfLessProduct2(Double weightOfLessProduct2) {
		this.weightOfLessProduct2 = weightOfLessProduct2;
	}

	public Double getQuantityOfLessProduct() {
		return quantityOfLessProduct;
	}

	public void setQuantityOfLessProduct(Double quantityOfLessProduct) {
		this.quantityOfLessProduct = quantityOfLessProduct;
	}

	public Double getQuantityInStock() {
		return quantityInStock;
	}

	public void setQuantityInStock(Double quantityInStock) {
		this.quantityInStock = quantityInStock;
	}

	public Double getRequirement() {
		return requirement;
	}

	public void setRequirement(Double requirement) {
		this.requirement = requirement;
	}

	public String getFbt() {
		return fbt;
	}

	public void setFbt(String fbt) {
		this.fbt = fbt;
	}

	public Double getMixingRate() {
		return mixingRate;
	}

	public void setMixingRate(Double mixingRate) {
		this.mixingRate = mixingRate;
	}

	public Double getMixingRate2() {
		return mixingRate2;
	}

	public void setMixingRate2(Double mixingRate2) {
		this.mixingRate2 = mixingRate2;
	}

	public String getAutomobileCustomer() {
		return automobileCustomer;
	}

	public void setAutomobileCustomer(String automobileCustomer) {
		this.automobileCustomer = automobileCustomer;
	}

	public String getPrintShield() {
		return printShield;
	}

	public void setPrintShield(String printShield) {
		this.printShield = printShield;
	}

	public String getPackagingRequirement() {
		return packagingRequirement;
	}

	public void setPackagingRequirement(String packagingRequirement) {
		this.packagingRequirement = packagingRequirement;
	}

	public String getPackagingMethod() {
		return packagingMethod;
	}

	public void setPackagingMethod(String packagingMethod) {
		this.packagingMethod = packagingMethod;
	}

	public String getModel() {
		return model;
	}

	public void setModel(String model) {
		this.model = model;
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

	public String getNumberOfLabel() {
		return numberOfLabel;
	}

	public void setNumberOfLabel(String numberOfLabel) {
		this.numberOfLabel = numberOfLabel;
	}

	public String getCreatName() {
		return creatName;
	}

	public void setCreatName(String creatName) {
		this.creatName = creatName;
	}

	public String getCreatTime() {
		return creatTime;
	}

	public void setCreatTime(String creatTime) {
		this.creatTime = creatTime;
	}

	public String getModifieName() {
		return modifieName;
	}

	public void setModifieName(String modifieName) {
		this.modifieName = modifieName;
	}

	public String getModifieTime() {
		return modifieTime;
	}

	public void setModifieTime(String modifieTime) {
		this.modifieTime = modifieTime;
	}
}
