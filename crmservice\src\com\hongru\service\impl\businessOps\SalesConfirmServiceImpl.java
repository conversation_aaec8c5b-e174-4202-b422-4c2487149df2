package com.hongru.service.impl.businessOps;

import com.hongru.common.util.DateUtils;
import com.hongru.entity.businessOps.PaymentAmount;
import com.hongru.entity.businessOps.PaymentAmountDetail;
import com.hongru.mapper.businessOps.PaymentAmountDetailMapper;
import com.hongru.mapper.businessOps.PaymentAmountMapper;
import com.hongru.pojo.dto.SalesConfirmListDTO;
import com.hongru.service.businessOps.ISalesConfirmService;
import com.hongru.support.page.PageInfo;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class SalesConfirmServiceImpl implements ISalesConfirmService {

    @Autowired
    private PaymentAmountMapper paymentAmountMapper;

    @Autowired
    private PaymentAmountDetailMapper paymentAmountDetailMapper;

    @Override
    public List<SalesConfirmListDTO> listSalesConfirmByPage(PageInfo pageInfo, String customerCode, String paymentRequestDate) throws Exception {
        return paymentAmountMapper.listSalesConfirmByPage(pageInfo, customerCode, paymentRequestDate);
    }

    @Override
    public Integer listSalesConfirmByPageCount(String customerCode, String paymentRequestDate) throws Exception {
        return paymentAmountMapper.listSalesConfirmByPageCount(customerCode, paymentRequestDate);
    }

    @Override
    public PaymentAmount getPaymentAmountByNo(String paymentRequestNo) throws Exception {
        return paymentAmountMapper.selectByPaymentRequestNo(paymentRequestNo);
    }

    @Override
    public List<PaymentAmountDetail> getPaymentAmountDetailsByNo(String paymentRequestNo) throws Exception {
        return paymentAmountDetailMapper.selectByPaymentRequestNo(paymentRequestNo);
    }

    @Override
    public int confirmSalesAmount(String paymentRequestNo, String confirmer) throws Exception {
        String currentTime = DateUtils.convertIntToDateString(DateUtils.getCurrentTime(), "yyyy-MM-dd HH:mm:ss");
        return paymentAmountMapper.updatePaymentRequestConfirmStatus(paymentRequestNo, "1", confirmer, currentTime);
    }

    @Override
    public int cancelConfirmSalesAmount(String paymentRequestNo, String confirmer) throws Exception {
        String currentTime = DateUtils.convertIntToDateString(DateUtils.getCurrentTime(), "yyyy-MM-dd HH:mm:ss");
        return paymentAmountMapper.updatePaymentRequestConfirmStatus(paymentRequestNo, "0", confirmer, currentTime);
    }
}
