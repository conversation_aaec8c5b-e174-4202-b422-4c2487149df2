package com.hongru.mapper.xieFenXi;

import com.baomidou.mybatisplus.mapper.BaseMapper;
import com.hongru.entity.xieFenXi.EMScrapAmountReport;
import com.hongru.entity.xieFenXi.ProductParametricBean;
import com.hongru.entity.xieFenXi.XieFenXiBean;

import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface ScrapAmountMapper extends BaseMapper<EMScrapAmountReport> {

    /**
     * 屑量(月报表中使用)
     * @param machineId
     * @param
     * @return
     * <AUTHOR>
     * @create 2022年2月25日
     * 说明：machineId 只是前3位  如4011 传401
     */
    List<EMScrapAmountReport> listDailyScrapAmountForMonth(@Param("machineId") String machineId, @Param("productDate")String productDate)throws Exception;

    /**
     * 获取其他原因停机时间
     * @param timeStartStr
     * @param timeEndStr
     * @return
     * <AUTHOR>
     * @create 2024/01/03 14:28
     */
    List<XieFenXiBean> listEFbreakNumInfo(@Param("timeStartStr") String timeStartStr, @Param("timeEndStr")String timeEndStr);
    
    /**
     * 获取其他原因停机时间
     * @param timeStartStr
     * @param timeEndStr
     * @return
     * <AUTHOR>
     * @create 2024/01/03 14:28
     */
    List<ProductParametricBean> listProductParametricInfo();
    
    /**
     * 获取其他原因停机时间
     * @param timeStartStr
     * @param timeEndStr
     * @return
     * <AUTHOR>
     * @create 2024/01/03 14:28
     */
    List<XieFenXiBean> listDiffDate(@Param("timeStartStr") String timeStartStr, @Param("timeEndStr")String timeEndStr);
    
}