package com.hongru.pojo.dto;

import java.io.Serializable;

/**
 * 月出货明细DTO
 * 用于月出货明细报表的数据传输
 */
public class MonthlyShipmentDetailDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 会计年月
     */
    private String accountingYearMonth;

    /**
     * 需求方略称（客户简称）
     */
    private String customerAlias;

    /**
     * 品名（产品代码）
     */
    private String productCode;

    /**
     * 尺寸
     */
    private String size;

    /**
     * 品目中分类名（产品中分类）
     */
    private String productMiddleCategory;

    /**
     * 出货数
     */
    private Double shipmentQuantity;

    /**
     * 线盘名称
     */
    private String wireReelName;

    // Getters and Setters
    public String getAccountingYearMonth() {
        return accountingYearMonth;
    }

    public void setAccountingYearMonth(String accountingYearMonth) {
        this.accountingYearMonth = accountingYearMonth;
    }

    public String getCustomerAlias() {
        return customerAlias;
    }

    public void setCustomerAlias(String customerAlias) {
        this.customerAlias = customerAlias;
    }

    public String getProductCode() {
        return productCode;
    }

    public void setProductCode(String productCode) {
        this.productCode = productCode;
    }

    public String getSize() {
        return size;
    }

    public void setSize(String size) {
        this.size = size;
    }

    public String getProductMiddleCategory() {
        return productMiddleCategory;
    }

    public void setProductMiddleCategory(String productMiddleCategory) {
        this.productMiddleCategory = productMiddleCategory;
    }

    public Double getShipmentQuantity() {
        return shipmentQuantity;
    }

    public void setShipmentQuantity(Double shipmentQuantity) {
        this.shipmentQuantity = shipmentQuantity;
    }

    public String getWireReelName() {
        return wireReelName;
    }

    public void setWireReelName(String wireReelName) {
        this.wireReelName = wireReelName;
    }
}