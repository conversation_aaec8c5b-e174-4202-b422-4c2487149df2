layui.use(['laydate', 'layer', 'table', 'element','form'], function(){
    var laydate = layui.laydate //日期
        ,layer = layui.layer //弹层
        ,table = layui.table //表格
        ,element = layui.element //元素操作
    var form = layui.form;

    var today = new Date();
    var year = today.getFullYear();
    var month = today.getMonth() + 1;
    var day = today.getDate();
    
    // 格式化日期为yyyy-MM-dd
    month = month < 10 ? '0' + month : month;
    day = day < 10 ? '0' + day : day;
    
    var todayStr = year + '-' + month + '-' + day;
    // 出库日期默认今天
    $('#outboundStartDate').val(todayStr);
    $('#outboundEndDate').val(todayStr);

    // 出库开始日期
    var outboundInsStart = layui.laydate.render({
        elem: '#outboundStartDate'
        ,done: function(value, date){
            // if (value) {
            //     //更新结束日期的最小日期
            //     outboundInsEnd.config.min = lay.extend({}, date, {
            //         month: date.month - 1
            //     });

            //     //自动弹出结束日期的选择器
            //     outboundInsEnd.config.elem[0].focus();
            // } 

            if (!value) {
                $('#outboundStartDate').val(todayStr);
            }
        }
    });
    
    // 出库结束日期
    var outboundInsEnd = layui.laydate.render({
        elem: '#outboundEndDate'
        ,done: function(value, date){
            // if (value) {
            //     //更新开始日期的最大日期
            //     outboundInsStart.config.max = lay.extend({}, date, {
            //         month: date.month - 1
            //     });
            // }

            if (!value) {
                $('#outboundEndDate').val(todayStr);
            }
        }
    });
    
    //执行一个 table 实例
    var url = baselocation+'/order/shipmentQuantityEntry/list';
    table.render({
        elem: '#demo'
        ,height: 'full-70'
        ,url: url //数据接口
        ,cellMinWidth: 80 //全局定义常规单元格的最小宽度
        ,parseData:function(res){ //res 即为原始返回的数据
            return {
                "code": 0, //解析接口状态
                "msg": '', //解析提示文本
                "count": res.total, //解析数据长度
                "data": res.rows //解析数据列表
            };
        }
        ,method:'post'
        ,title: '出库数量录入列表'
        ,page: true //开启分页
        ,limits: [10,20,50,100]
        ,where:$("#formSearch").serializeJsonObject()
        ,toolbar: '#toolbarDemo' //开启工具栏，此处显示默认图标，可以自定义模板，详见文档
        ,defaultToolbar: ['filter']
        ,totalRow: false //开启合计行
        ,cols: [[ //表头
            {field: 'outboundDate',title: '到货日期',align:'center'}
            ,{field: 'customerCode',title: '客户代码',align:'center'}
            ,{field: 'customerAlias',title: '客户简称',align:'center'}
            ,{field: 'productCode',title: '产品代码',align:'center'}
            ,{field: 'shipmentQuantity',title: '出库数量',align:'center'}
            ,{field: 'customerOrderNo',title: '客户订单号',align:'center'}
            ,{field: 'productCategory',title: '品目分类名',align:'center'}
            ,{field: 'shipmentPlanNo',title: '出库计划番号',align:'center'}
            ,{title: '操作',minWidth:150, align:'center',fixed: 'right', toolbar: '#barDemo'}
        ]]
    });

    //监听头工具栏事件
    table.on('toolbar(test)', function(obj){
        var checkStatus = table.checkStatus(obj.config.id)
            ,data = checkStatus.data; //获取选中的数据
        switch(obj.event){
            case 'add':
                console.log('新增按钮点击');
               
                layer_show('新增', baselocation+"/order/shipmentQuantityEntry/add/view", document.body.clientWidth-10, document.body.clientHeight-10);
                break;
            case 'refresh':
                var temp = $("#formSearch").serializeJsonObject();
                console.info(temp);
                layui.table.reload('demo', {
                    page: {
                        curr: 1
                    }
                    ,where: temp
                }, 'data');
                break;
            case 'export':
                var temp = $("#formSearch").serializeJsonObject();
                var orderNoAndSerialNumList = [];
                $.each(data, function(index, item) {
                    orderNoAndSerialNumList.push(item.orderNo + '-' + item.orderSerialNum);
                });


                // 打开预览页面
                layer_show('Excel预览', baselocation + "/order/manufacturingContactForm/preview/view", document.body.clientWidth-10, document.body.clientHeight-10);
                break;
        };
    });

    //监听行工具事件
    table.on('tool(test)', function(obj){ //注：tool 是工具条事件名，test 是 table 原始容器的属性 lay-filter="对应的值"
        var data = obj.data //获得当前行数据
            ,layEvent = obj.event; //获得 lay-event 对应的值

        if(layEvent === 'edit'){
            // layer_show('编辑',baselocation+"/order/shipmentQuantityEntry/edit/view?id="+data.id,document.body.clientWidth-10, document.body.clientHeight-10);
            // 打开一个弹框，表单字段·出库日期
            // ·客户代码
            // ·客户简称
            // ·产品代码
            // ·尺寸
            // ·出库数量 
            // ·送货单号
            // ·送货单序号
            // ·客户订单号
            // ·产品中分类
            // ·出库计划番号

            // 其中只有 出库数量  可以编辑
            // 其他字段只读
            // 点击确定按钮，调用接口，更新出库数量
            // 接口返回成功后，关闭弹框，刷新表格
            // 接口返回失败，提示失败信息

            layer.open({
                title: '编辑出库数量',
                content: `
                <form class="layui-form" action="">
                    <div class="layui-form-item">
                        <label class="layui-form-label" style="width: 85px;">到货日期</label>
                        <div class="layui-input-inline">
                            <input type="text" name="outboundDate" value="${data.outboundDate}" readonly class="layui-input layui-disabled" style="border: none; background: transparent;">
                        </div>
                        <label class="layui-form-label" style="width: 85px;">客户代码</label>
                        <div class="layui-input-inline">
                            <input type="text" name="customerCode" value="${data.customerCode}" readonly class="layui-input layui-disabled" style="border: none; background: transparent;">
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label" style="width: 85px;">客户简称</label>
                        <div class="layui-input-inline">
                            <input type="text" name="customerAlias" value="${data.customerAlias}" readonly class="layui-input layui-disabled" style="border: none; background: transparent;">
                        </div>
                        <label class="layui-form-label" style="width: 85px;">产品代码</label>
                        <div class="layui-input-inline">
                            <input type="text" name="productCode" value="${data.productCode}" readonly class="layui-input layui-disabled" style="border: none; background: transparent;">
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label" style="width: 85px;">尺寸</label>
                        <div class="layui-input-inline">
                            <input type="text" name="size" value="${data.size}" readonly class="layui-input layui-disabled" style="border: none; background: transparent;">
                        </div>
                        <label class="layui-form-label" style="width: 85px;">客户订单号</label>
                        <div class="layui-input-inline">
                            <input type="text" name="customerOrderNo" value="${data?.customerOrderNo || ''}" readonly class="layui-input layui-disabled" style="border: none; background: transparent;">
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label" style="width: 85px;">产品中分类</label>
                        <div class="layui-input-inline">
                            <input type="text" name="productCategory" value="${data.productCategory}" readonly class="layui-input layui-disabled" style="border: none; background: transparent;">
                        </div>
                        <label class="layui-form-label" style="width: 85px;">出库计划番号</label>
                        <div class="layui-input-inline">
                            <input type="text" name="shipmentPlanNo" value="${data?.shipmentPlanNo || ''}" readonly class="layui-input layui-disabled" style="border: none; background: transparent;">
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label" style="width: 85px;">出库数量</label>
                        <div class="layui-input-block">
                            <input type="text" style="width: 95%;" name="shipmentQuantity" lay-verify="required" placeholder="请输入出库数量" value="${data.shipmentQuantity}" class="layui-input">
                        </div>
                    </div>
                </form>
                `,
                success: function(layero, index){
                    form.render();
                },
                btn: ['确定', '取消'],
                yes: function(index, layero) {
                    var shipmentQuantity = $('input[name="shipmentQuantity"]').val() || "0";
                    var outboundDate = $('input[name="outboundDate"]').val()?.trim() || '';
                    var productCode = $('input[name="productCode"]').val()?.trim() || '';
                    var customerOrderNo = $('input[name="customerOrderNo"]').val()?.trim() || '';

                    var outboundDateAndProductCodeAndCustomerOrderNoStr = outboundDate + "-" + productCode + "-" + customerOrderNo

                    $.ajax({
                        type: 'post',
                        url: baselocation + '/order/shipmentQuantityEntry/update',
                        data: {id: data.id, outboundDateAndProductCodeAndCustomerOrderNoStr: outboundDateAndProductCodeAndCustomerOrderNoStr, shipmentQuantity: shipmentQuantity},
                        success: function(result){
                            if (result.code == 1) {
                                layer.msg("操作成功!", {
                                    shade : 0.3,
                                    time : 1500
                                }, function() {
                                    search();
                                    layer.closeAll();
                                });
                            } else {
                                layer.alert(result.data, {
                                    icon : 2
                                });
                            }
                        }
                    });
                }
            })
        }else if(layEvent === 'remove'){
            layer.confirm('确认要删除吗？', {
                btn : [ '确定', '取消' ] //按钮
            }, function() {
                $.ajax({
                    type : 'post',
                    dataType : 'json',
                    data: {"id":data.id},
                    url : baselocation + '/order/shipmentQuantityEntry/delete',
                    success : function(result) {
                        if (result.code == 1) {
                            layer.msg("操作成功!", {
                                shade : 0.3,
                                time : 1500
                            }, function() {
                                search();
                                layer.closeAll();
                            });
                        } else {
                            layer.alert(result.data, {
                                icon : 2
                            });
                        }
                    }
                })
            });
        }
    });
});

function search() {
	var temp = $("#formSearch").serializeJsonObject();
	console.info(temp);

   if ( !temp?.outboundStartDate && !temp?.outboundEndDate) {
       var today = new Date();
       var year = today.getFullYear();
       var month = today.getMonth() + 1;
       var day = today.getDate();
       
       // 格式化日期为yyyy-MM-dd
       month = month < 10 ? '0' + month : month;
       day = day < 10 ? '0' + day : day;
       
       var todayStr = year + '-' + month + '-' + day;
       // 出库日期默认今天
       $('#outboundStartDate').val(todayStr);
       $('#outboundEndDate').val(todayStr);
       temp.outboundStartDate = todayStr;
       temp.outboundEndDate = todayStr;
   }

	layui.table.reload('demo', {
		page: {
			curr: 1
		}
		,where: temp
	}, 'data');
}

// 订单附件下载功能
function downloadOrderAttachment(attachmentId) {
    if (!attachmentId || attachmentId.trim() === '') {
        layer.msg('未找到附件', {icon: 2});
        return;
    }

    // 直接打开下载链接
    window.open(baselocation + '/common/download?id=' + attachmentId);

    // 或者使用异步方式下载 - 如果需要先验证权限等操作
    /*
    $.ajax({
        url: baselocation + '/common/checkDownloadPermission',
        type: 'post',
        data: {id: attachmentId},
        success: function(res) {
            if (res.code == 1) {
                window.open(baselocation + '/common/download?id=' + attachmentId);
            } else {
                layer.msg(res.message || '下载失败', {icon: 2});
            }
        },
        error: function() {
            layer.msg('下载请求失败', {icon: 2});
        }
    });
    */
}
