package com.hongru.entity.businessOps;

import com.baomidou.mybatisplus.annotations.TableField;
import com.baomidou.mybatisplus.annotations.TableId;
import com.baomidou.mybatisplus.annotations.TableName;
import com.baomidou.mybatisplus.enums.IdType;

import java.math.BigDecimal;

@TableName("人件费用表")//CostPrice
public class HumanPriceCost {
	/* 状态-正常 */
	public static final short STATE_NORMAL = 0;
	/* 状态-删除 */
	public static final short STATE_DELETED = 9;

	/* 人件费区分-MW */
	public static final short HUMANPRICECODE_MW = 1;
	/* 人件费区分-EH */
	public static final short HUMANPRICECODE_EH = 2;
	/* 人件费区分-UF */
	public static final short HUMANPRICECODE_UF = 3;
	/* 人件费区分-保全 */
	public static final short HUMANPRICECODE_BAOQUAN = 4;
	/* 人件费区分-其他 */
	public static final short HUMANPRICECODE_OTHER = 5;

	/* 流水号 */
	@TableId(value="流水号", type= IdType.AUTO)
	protected int costId;
	/* 状态 */
	protected short state;
	/* 年月 */
	protected String yearMonth;
	/* 年份 */
	protected int year;
	/* 月份 */
	protected int month;
	/* 导入标识 */
	protected int importId;
	/* 人件费区分 */
	protected String humanPriceCode;
	/* 人件费 */
	protected BigDecimal humanPrice;
	/* 工种 */
	protected String workType;
	/* 创建人标识 */
	protected int creatorId;
	/* 创建人姓名 */
	protected String creatorName;
	/* 创建时间 */
	protected String createdTime;
	/* 最后修改人标识 */
	protected int lastModifierId;
	/* 最后修改人姓名 */
	protected String lastModifierName;
	/* 最后修改时间 */
	protected String lastModifiedTime;

	@TableField(exist = false)
	protected Integer supplierId;

	public int getCostId() {
		return costId;
	}

	public void setCostId(int costId) {
		this.costId = costId;
	}

	public short getState() {
		return state;
	}

	public void setState(short state) {
		this.state = state;
	}

	public int getCreatorId() {
		return creatorId;
	}

	public void setCreatorId(int creatorId) {
		this.creatorId = creatorId;
	}

	public String getCreatorName() {
		return creatorName;
	}

	public void setCreatorName(String creatorName) {
		this.creatorName = creatorName;
	}

	public String getCreatedTime() {
		return createdTime;
	}

	public void setCreatedTime(String createdTime) {
		this.createdTime = createdTime;
	}

	public int getLastModifierId() {
		return lastModifierId;
	}

	public void setLastModifierId(int lastModifierId) {
		this.lastModifierId = lastModifierId;
	}

	public String getLastModifierName() {
		return lastModifierName;
	}

	public void setLastModifierName(String lastModifierName) {
		this.lastModifierName = lastModifierName;
	}

	public String getLastModifiedTime() {
		return lastModifiedTime;
	}

	public void setLastModifiedTime(String lastModifiedTime) {
		this.lastModifiedTime = lastModifiedTime;
	}

	public Integer getSupplierId() {
		return supplierId;
	}

	public void setSupplierId(Integer supplierId) {
		this.supplierId = supplierId;
	}

	public int getYear() {
		return year;
	}
	public void setYear(int year) {
		this.year = year;
	}
	public int getMonth() {
		return month;
	}
	public void setMonth(int month) {
		this.month = month;
	}

	public String getHumanPriceCode() {
		return humanPriceCode;
	}

	public void setHumanPriceCode(String humanPriceCode) {
		this.humanPriceCode = humanPriceCode;
	}

	public BigDecimal getHumanPrice() {
		return humanPrice;
	}

	public void setHumanPrice(BigDecimal humanPrice) {
		this.humanPrice = humanPrice;
	}

	public String getYearMonth() {
		return yearMonth;
	}
	public void setYearMonth(String yearMonth) {
		this.yearMonth = yearMonth;
	}

	public String getWorkType() {
		return workType;
	}

	public void setWorkType(String workType) {
		this.workType = workType;
	}

	public int getImportId() {
		return importId;
	}

	public void setImportId(int importId) {
		this.importId = importId;
	}
}