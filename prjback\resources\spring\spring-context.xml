<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:context="http://www.springframework.org/schema/context" xmlns:p="http://www.springframework.org/schema/p"
	xmlns:aop="http://www.springframework.org/schema/aop" xmlns:tx="http://www.springframework.org/schema/tx"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xmlns:task="http://www.springframework.org/schema/task"
	xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
	http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd
	http://www.springframework.org/schema/task
    http://www.springframework.org/schema/task/spring-task-3.0.xsd
	http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop.xsd http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx.xsd
	http://www.springframework.org/schema/util http://www.springframework.org/schema/util/spring-util.xsd">

	<description>spring Configuration</description>
	
	<!-- 加载配置文件 -->
	<context:property-placeholder  ignore-resource-not-found="true" location="classpath*:properties/*.properties" />
                                  
	<!-- 使用Annotation自动注册Bean,解决事物失效问题：在主容器中不扫描@Controller注解,在SpringMvc中只扫描@Controller注解。  -->
	<context:component-scan base-package="com.hongru"><!-- base-package 如果多个,用“,”分隔 -->
		<context:exclude-filter type="annotation" expression="org.springframework.stereotype.Controller"/><!-- 过滤其中这四个注解中的一个 -->
	</context:component-scan>

	<task:annotation-driven/>
	<context:component-scan base-package="com.hongru.controller.timer"></context:component-scan>

	<!-- 验证码KAPTCHA:配置文件引入-->
	<import resource="classpath:spring/applicationContext-kaptcha.xml" />
	
	<!-- 持久层框架MYBATIS:配置文件引入 -->
	<import resource="classpath:spring/applicationContext-mybatis.xml" />
	
	<!-- 缓存框架EHCACHE:配置文件引入 -->
	<import resource="classpath:spring/applicationContext-ehcache.xml" />
	
	<!-- 安全框架SHIRO:配置文件引入 -->
	<import resource="classpath:spring/applicationContext-shiro.xml" />
	
</beans>