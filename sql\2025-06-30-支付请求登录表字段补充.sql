-- 支付请求登录表字段补充脚本
-- 检查现有支付额表和支付额明细表，添加缺失的字段
-- 注意：需求方下拉框数据来源已从[sumitomo].[dbo].[签约方]改为[sumitomo].[dbo].[客户表]

PRINT '开始检查支付额表字段...';

-- 检查支付额表是否存在，如果不存在则报错
IF OBJECT_ID(N'[dbo].[支付额表]', N'U') IS NULL
BEGIN
    PRINT '错误：支付额表不存在，请先创建支付额表';
    RETURN;
END

-- 为支付额表添加缺失字段
IF NOT EXISTS (SELECT 1 FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[支付额表]') AND name = N'据点')
    ALTER TABLE [dbo].[支付额表] ADD [据点] varchar(10) COLLATE SQL_Latin1_General_CP1_CI_AS NULL;

IF NOT EXISTS (SELECT 1 FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[支付额表]') AND name = N'支付请求NO')
    ALTER TABLE [dbo].[支付额表] ADD [支付请求NO] nvarchar(50) COLLATE SQL_Latin1_General_CP1_CI_AS NULL;

IF NOT EXISTS (SELECT 1 FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[支付额表]') AND name = N'支付请求日')
    ALTER TABLE [dbo].[支付额表] ADD [支付请求日] date NULL;

-- 签约方代码字段改为从客户表获取，但数据库字段名保持不变
IF NOT EXISTS (SELECT 1 FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[支付额表]') AND name = N'签约方代码')
    ALTER TABLE [dbo].[支付额表] ADD [签约方代码] char(5) COLLATE SQL_Latin1_General_CP1_CI_AS NULL;

IF NOT EXISTS (SELECT 1 FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[支付额表]') AND name = N'客户代码')
    ALTER TABLE [dbo].[支付额表] ADD [客户代码] char(5) COLLATE SQL_Latin1_General_CP1_CI_AS NULL;

IF NOT EXISTS (SELECT 1 FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[支付额表]') AND name = N'客户简称')
    ALTER TABLE [dbo].[支付额表] ADD [客户简称] nvarchar(100) COLLATE SQL_Latin1_General_CP1_CI_AS NULL;

IF NOT EXISTS (SELECT 1 FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[支付额表]') AND name = N'交易条件')
    ALTER TABLE [dbo].[支付额表] ADD [交易条件] varchar(10) COLLATE SQL_Latin1_General_CP1_CI_AS NULL;

IF NOT EXISTS (SELECT 1 FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[支付额表]') AND name = N'付款条件')
    ALTER TABLE [dbo].[支付额表] ADD [付款条件] varchar(10) COLLATE SQL_Latin1_General_CP1_CI_AS NULL;

IF NOT EXISTS (SELECT 1 FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[支付额表]') AND name = N'运输条件')
    ALTER TABLE [dbo].[支付额表] ADD [运输条件] char(3) COLLATE SQL_Latin1_General_CP1_CI_AS NULL;

IF NOT EXISTS (SELECT 1 FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[支付额表]') AND name = N'结算货币')
    ALTER TABLE [dbo].[支付额表] ADD [结算货币] nvarchar(10) COLLATE SQL_Latin1_General_CP1_CI_AS NULL;

IF NOT EXISTS (SELECT 1 FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[支付额表]') AND name = N'税代码')
    ALTER TABLE [dbo].[支付额表] ADD [税代码] int NULL;

IF NOT EXISTS (SELECT 1 FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[支付额表]') AND name = N'支付请求确认')
    ALTER TABLE [dbo].[支付额表] ADD [支付请求确认] smallint DEFAULT ((0)) NOT NULL;

IF NOT EXISTS (SELECT 1 FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[支付额表]') AND name = N'确认者')
    ALTER TABLE [dbo].[支付额表] ADD [确认者] nvarchar(50) COLLATE SQL_Latin1_General_CP1_CI_AS NULL;

IF NOT EXISTS (SELECT 1 FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[支付额表]') AND name = N'确认时间')
    ALTER TABLE [dbo].[支付额表] ADD [确认时间] datetime NULL;

PRINT '支付额表字段检查完成';

-- 检查支付额明细表是否存在，如果不存在则报错
IF OBJECT_ID(N'[dbo].[支付额明细表]', N'U') IS NULL
BEGIN
    PRINT '错误：支付额明细表不存在，请先创建支付额明细表';
    RETURN;
END

PRINT '开始检查支付额明细表字段...';

-- 为支付额明细表添加缺失字段
IF NOT EXISTS (SELECT 1 FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[支付额明细表]') AND name = N'支付请求NO')
    ALTER TABLE [dbo].[支付额明细表] ADD [支付请求NO] nvarchar(50) COLLATE SQL_Latin1_General_CP1_CI_AS NULL;

IF NOT EXISTS (SELECT 1 FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[支付额明细表]') AND name = N'销售额NO')
    ALTER TABLE [dbo].[支付额明细表] ADD [销售额NO] nvarchar(50) COLLATE SQL_Latin1_General_CP1_CI_AS NULL;

IF NOT EXISTS (SELECT 1 FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[支付额明细表]') AND name = N'销售额NO序号')
    ALTER TABLE [dbo].[支付额明细表] ADD [销售额NO序号] int NULL;

IF NOT EXISTS (SELECT 1 FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[支付额明细表]') AND name = N'条码')
    ALTER TABLE [dbo].[支付额明细表] ADD [条码] nvarchar(50) COLLATE SQL_Latin1_General_CP1_CI_AS NULL;

IF NOT EXISTS (SELECT 1 FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[支付额明细表]') AND name = N'出货日')
    ALTER TABLE [dbo].[支付额明细表] ADD [出货日] date NULL;

IF NOT EXISTS (SELECT 1 FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[支付额明细表]') AND name = N'到货日')
    ALTER TABLE [dbo].[支付额明细表] ADD [到货日] date NULL;

IF NOT EXISTS (SELECT 1 FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[支付额明细表]') AND name = N'客户订单号')
    ALTER TABLE [dbo].[支付额明细表] ADD [客户订单号] nvarchar(50) COLLATE SQL_Latin1_General_CP1_CI_AS NULL;

IF NOT EXISTS (SELECT 1 FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[支付额明细表]') AND name = N'明细备注')
    ALTER TABLE [dbo].[支付额明细表] ADD [明细备注] nvarchar(200) COLLATE SQL_Latin1_General_CP1_CI_AS NULL;

IF NOT EXISTS (SELECT 1 FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[支付额明细表]') AND name = N'铜合同类别')
    ALTER TABLE [dbo].[支付额明细表] ADD [铜合同类别] nvarchar(50) COLLATE SQL_Latin1_General_CP1_CI_AS NULL;

IF NOT EXISTS (SELECT 1 FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[支付额明细表]') AND name = N'铜合同NO')
    ALTER TABLE [dbo].[支付额明细表] ADD [铜合同NO] nvarchar(50) COLLATE SQL_Latin1_General_CP1_CI_AS NULL;

IF NOT EXISTS (SELECT 1 FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[支付额明细表]') AND name = N'铜条件')
    ALTER TABLE [dbo].[支付额明细表] ADD [铜条件] nvarchar(100) COLLATE SQL_Latin1_General_CP1_CI_AS NULL;

IF NOT EXISTS (SELECT 1 FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[支付额明细表]') AND name = N'换算率')
    ALTER TABLE [dbo].[支付额明细表] ADD [换算率] decimal(18,6) NULL;

IF NOT EXISTS (SELECT 1 FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[支付额明细表]') AND name = N'升水')
    ALTER TABLE [dbo].[支付额明细表] ADD [升水] decimal(18,2) DEFAULT ((0)) NULL;

IF NOT EXISTS (SELECT 1 FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[支付额明细表]') AND name = N'换算后铜单价')
    ALTER TABLE [dbo].[支付额明细表] ADD [换算后铜单价] decimal(18,2) DEFAULT ((0)) NULL;

IF NOT EXISTS (SELECT 1 FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[支付额明细表]') AND name = N'零基础')
    ALTER TABLE [dbo].[支付额明细表] ADD [零基础] decimal(18,2) DEFAULT ((0)) NULL;

IF NOT EXISTS (SELECT 1 FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[支付额明细表]') AND name = N'支付单价')
    ALTER TABLE [dbo].[支付额明细表] ADD [支付单价] decimal(18,2) DEFAULT ((0)) NULL;

IF NOT EXISTS (SELECT 1 FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[支付额明细表]') AND name = N'请求金额')
    ALTER TABLE [dbo].[支付额明细表] ADD [请求金额] decimal(18,2) DEFAULT ((0)) NULL;

PRINT '支付额明细表字段检查完成';

-- 为销售额表添加支付请求相关字段（如果不存在）
PRINT '开始检查销售额表支付请求相关字段...';

IF NOT EXISTS (SELECT 1 FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[销售额表]') AND name = N'支付请求确认')
    ALTER TABLE [dbo].[销售额表] ADD [支付请求确认] smallint DEFAULT ((0)) NOT NULL;

IF NOT EXISTS (SELECT 1 FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[销售额表]') AND name = N'确认者')
    ALTER TABLE [dbo].[销售额表] ADD [确认者] nvarchar(50) COLLATE SQL_Latin1_General_CP1_CI_AS NULL;


IF NOT EXISTS (SELECT 1 FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[销售额表]') AND name = N'确认时间')
    ALTER TABLE [dbo].[销售额表] ADD [确认时间] datetime NULL;

PRINT '销售额表字段检查完成';

PRINT '支付请求登录表字段补充脚本执行完成';

-- 删除支付额表和支付额明细表的销售额NO字段
ALTER TABLE [dbo].[支付额表] DROP CONSTRAINT [FK_支付额_销售额];
DROP INDEX [IDX_支付额_销售额NO] ON [dbo].[支付额表];
ALTER TABLE [dbo].[支付额明细表] DROP CONSTRAINT [FK_支付额明细_销售额];

ALTER TABLE [dbo].[支付额表] DROP COLUMN [销售额NO];


-- 修改支付状态字段为可空
ALTER TABLE [dbo].[支付额表] ALTER COLUMN [支付状态] nvarchar(20) COLLATE SQL_Latin1_General_CP1_CI_AS NULL;

-- 修改销售单价字段为可空(这里只关注支付单价，销售单价这里可以不用，若需要关联销售额明细表可以拿到)
ALTER TABLE [dbo].[支付额明细表] ALTER COLUMN [销售单价] decimal(18,2) NULL;





-- 支付额明细表新增字段脚本
-- 执行日期：2025-07-02
-- 说明：为支付额明细表添加与销售额登录页面一致的新字段

PRINT '开始为支付额明细表添加新字段...';

-- 检查支付额明细表是否存在
IF OBJECT_ID (N'[dbo].[支付额明细表]', N'U') IS NULL BEGIN PRINT '错误：支付额明细表不存在，请先创建支付额明细表';

RETURN;

END

-- 添加出货单No.字段
IF NOT EXISTS (SELECT 1 FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[支付额明细表]') AND name = N'出货单No.')
BEGIN
    ALTER TABLE [dbo].[支付额明细表] ADD [出货单No.] nvarchar(50) NULL;

PRINT '已添加[出货单No.]字段';

END ELSE BEGIN PRINT '[出货单No.]字段已存在，跳过';

END

-- 添加送货单序号字段
IF NOT EXISTS (SELECT 1 FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[支付额明细表]') AND name = N'送货单序号')
BEGIN
    ALTER TABLE [dbo].[支付额明细表] ADD [送货单序号] nvarchar(50) NULL;

PRINT '已添加[送货单序号]字段';

END ELSE BEGIN PRINT '[送货单序号]字段已存在，跳过';

END

-- 添加线盘字段
IF NOT EXISTS (SELECT 1 FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[支付额明细表]') AND name = N'线盘')
BEGIN
    ALTER TABLE [dbo].[支付额明细表] ADD [线盘] nvarchar(100) NULL;

PRINT '已添加[线盘]字段';

END ELSE BEGIN PRINT '[线盘]字段已存在，跳过';

END

-- 添加出货计划番号
IF NOT EXISTS (SELECT 1 FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[支付额明细表]') AND name = N'出货计划番号')
BEGIN
    ALTER TABLE [dbo].[支付额明细表] ADD [出货计划番号] char(9) NULL;

PRINT '已添加[出货计划番号]字段';

END ELSE BEGIN PRINT '[出货计划番号]字段已存在，跳过';


-- 删除支付状态和支付方式字段
DROP INDEX [IDX_支付额_支付状态] ON [dbo].[支付额表]

ALTER TABLE [dbo].[支付额表] DROP COLUMN [支付状态], COLUMN [支付方式]