layui.use(['laydate', 'layer', 'table', 'element', 'form'], function(){
    var laydate = layui.laydate //日期
        ,layer = layui.layer //弹层
        ,table = layui.table //表格
        ,element = layui.element //元素操作
    var form = layui.form;

    // 初始化日期范围选择器
    laydate.render({
        elem: '#salesAmountDateRange',
        type: 'date',
        range: true,
        format: 'yyyy-MM-dd'
    });

    // 出库日期
    laydate.render({
        elem: '#outboundDate'
    });

    //执行一个 table 实例
    var url = baselocation+'/order/salesLogin/list';
    table.render({
        elem: '#demo'
        ,height: 'full-70'
        ,url: url //数据接口
        ,cellMinWidth: 80 //全局定义常规单元格的最小宽度
        ,parseData:function(res){ //res 即为原始返回的数据
            return {
                "code": 0, //解析接口状态
                "msg": '', //解析提示文本
                "count": res.total, //解析数据长度
                "data": res.rows //解析数据列表
            };
        }
        ,method:'post'
        ,title: '销售额登录列表'
        ,page: true //开启分页
        ,limits: [10,20,50,100]
        ,where:$("#formSearch").serializeJsonObject()
        ,toolbar: '#toolbarDemo' //开启工具栏，此处显示默认图标，可以自定义模板，详见文档
        ,defaultToolbar: ['filter']
        ,totalRow: false //开启合计行
        ,cols: [[ //表头
            {field: 'zizeng', title: 'NO.', width:50, fixed:'left', type:"numbers", align:'center'}
            ,{field: 'salesNo',title: '销售额NO',align:'center'}
            ,{field: 'salesDate',title: '销售额日',align:'center'}
            ,{field: 'customerAlias',title: '需求方',align:'center'}
            ,{field: 'currency',title: '货币',align:'center'}
            ,{field: 'salesAmount',title: '销售金额',align:'center'}
            ,{field: 'taxRate',title: '税率(%)',align:'center'}
            ,{field: 'creatorName',title: '创建者',align:'center'}
            ,{field: 'createdTime',title: '创建时间',align:'center'}
            ,{field: 'updaterName',title: '更新者',align:'center'}
            ,{field: 'updatedTime',title: '更新时间',align:'center'}
            ,{title: '操作',minWidth:225, align:'left',fixed: 'right', toolbar: '#barDemo'}
        ]]
    });

    //监听头工具栏事件
    table.on('toolbar(test)', function(obj){
        var checkStatus = table.checkStatus(obj.config.id)
            ,data = checkStatus.data; //获取选中的数据
        switch(obj.event){
            case 'toAdd':
                console.log('新增按钮点击');
                layer_show('销售额登录', baselocation+"/order/salesLogin/add/view", document.body.clientWidth-10, document.body.clientHeight-10);
                break;
            case 'refresh':
                var temp = $("#formSearch").serializeJsonObject();
                console.info(temp);
                layui.table.reload('demo', {
                    page: {
                        curr: 1
                    }
                    ,where: temp
                }, 'data');
                break;
        };
    });

    //监听行工具事件
    table.on('tool(test)', function(obj){ //注：tool 是工具条事件名，test 是 table 原始容器的属性 lay-filter="对应的值"
        var data = obj.data //获得当前行数据
            ,layEvent = obj.event; //获得 lay-event 对应的值

        if(layEvent === 'detail'){
            layer_show('详情',baselocation+"/order/salesLogin/detail/view?salesAmountNo="+data.salesNo,document.body.clientWidth-10, document.body.clientHeight-10);
        }else if(layEvent === 'edit'){
            layer_show('编辑',baselocation+"/order/salesLogin/edit/view?salesAmountNo="+data.salesNo,document.body.clientWidth-10, document.body.clientHeight-10);
        }else if(layEvent === 'remove'){
            console.log('删除数据:', data); // 调试信息
            console.log('销售额NO:', data.salesNo); // 调试信息

            if (!data.salesNo) {
                layer.msg("销售额NO为空，无法删除!", {time: 1500});
                return;
            }

            layer.confirm('确认要删除吗？', {
                btn : [ '确定', '取消' ]
            }, function() {
                $.ajax({
                    url: baselocation + '/order/salesLogin/delete',
                    type: 'POST',
                    data: {
                        salesAmountNo: data.salesNo
                    },
                    success: function(result) {
                        if (result.code === 1) {
                            layer.msg("删除成功!", {time: 1500}, function(){
                                search();
                            });
                        } else {
                            layer.msg(result.message || "删除失败!", {time: 1500});
                        }
                    },
                    error: function() {
                        layer.msg("删除失败!", {time: 1500});
                    }
                });
            });
        } else if(obj.event === 'export'){
            // 导出单个销售额
            exportSingleSalesAmount(data.salesNo);
        }
    });
});

function search() {
    var temp = $("#formSearch").serializeJsonObject();
    console.info(temp);
    layui.table.reload('demo', {
        page: {
            curr: 1
        }
        ,where: temp
    }, 'data');
}

// 导出Excel功能
function exportExcel() {
    var customerCode = $('#customerCode').val();
    var salesAmountDateRange = $('#salesAmountDateRange').val();

    // 创建隐藏的表单进行提交
    var form = document.createElement('form');
    form.method = 'POST';
    form.action = baselocation + '/order/salesLogin/export';
    form.style.display = 'none';

    // 添加参数
    var customerInput = document.createElement('input');
    customerInput.name = 'customerCode';
    customerInput.value = customerCode || '';
    form.appendChild(customerInput);

    var dateInput = document.createElement('input');
    dateInput.name = 'salesAmountDateRange';
    dateInput.value = salesAmountDateRange || '';
    form.appendChild(dateInput);

    document.body.appendChild(form);
    form.submit();
    document.body.removeChild(form);

    layui.layer.msg('正在导出Excel，请稍候...', {icon: 16, time: 2000});
}

// 导出单个销售额
function exportSingleSalesAmount(salesAmountNo) {
    if (!salesAmountNo) {
        layui.layer.msg('销售额NO不能为空', {icon: 2});
        return;
    }

    // 创建隐藏的表单进行提交
    var form = document.createElement('form');
    form.method = 'POST';
    form.action = baselocation + '/order/salesLogin/exportByNo';
    form.style.display = 'none';

    // 添加参数
    var noInput = document.createElement('input');
    noInput.name = 'salesAmountNo';
    noInput.value = salesAmountNo;
    form.appendChild(noInput);

    document.body.appendChild(form);
    form.submit();
    document.body.removeChild(form);

    layui.layer.msg('正在导出Excel，请稍候...', {icon: 16, time: 2000});
}