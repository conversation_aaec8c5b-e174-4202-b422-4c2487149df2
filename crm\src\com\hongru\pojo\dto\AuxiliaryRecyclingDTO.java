package com.hongru.pojo.dto;

import com.hongru.entity.businessOps.AuxiliaryRecycling;
import com.hongru.support.page.PageInfo;

import java.util.List;

public class AuxiliaryRecyclingDTO {

    private PageInfo pageInfo;

    private List<AuxiliaryRecycling> auxiliaryRecyclings;

    public PageInfo getPageInfo() {
        return pageInfo;
    }

    public void setPageInfo(PageInfo pageInfo) {
        this.pageInfo = pageInfo;
    }

    public List<AuxiliaryRecycling> getAuxiliaryRecyclings() {
        return auxiliaryRecyclings;
    }

    public void setAuxiliaryRecyclings(List<AuxiliaryRecycling> auxiliaryRecyclings) {
        this.auxiliaryRecyclings = auxiliaryRecyclings;
    }

    public AuxiliaryRecyclingDTO(PageInfo pageInfo, List<AuxiliaryRecycling> auxiliaryRecyclings) {
        this.pageInfo = pageInfo;
        this.auxiliaryRecyclings = auxiliaryRecyclings;
    }
}
