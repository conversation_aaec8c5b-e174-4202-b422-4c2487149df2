-- 创建支付额表
CREATE TABLE 支付额表 (
    支付额ID INT IDENTITY(1,1) PRIMARY KEY,    -- 自增主键
    销售额NO NVARCHAR(50) NOT NULL,            -- 关联销售额表的外键
    支付日期 DATE NOT NULL,                    -- 支付日期
    支付金额 DECIMAL(18,2) NOT NULL,           -- 支付金额
    支付状态 NVARCHAR(20) NOT NULL,            -- 支付状态（已支付/未支付/部分支付）
    支付方式 NVARCHAR(50),                     -- 支付方式
    备注 NVARCHAR(500),                        -- 备注信息
    创建时间 DATETIME DEFAULT GETDATE(),       -- 记录创建时间
    更新时间 DATETIME DEFAULT GETDATE(),       -- 记录更新时间
    创建人 NVARCHAR(50),                       -- 创建人
    更新人 NVARCHAR(50),                       -- 更新人
    CONSTRAINT FK_支付额_销售额 FOREIGN KEY (销售额NO) REFERENCES 销售额表(销售额NO)
);

-- 创建索引
CREATE INDEX IDX_支付额_销售额NO ON 支付额表(销售额NO);
CREATE INDEX IDX_支付额_支付日期 ON 支付额表(支付日期);
CREATE INDEX IDX_支付额_支付状态 ON 支付额表(支付状态);

-- 添加表注释
EXEC sp_addextendedproperty 'MS_Description', '支付额表', 'SCHEMA', 'dbo', 'TABLE', '支付额表';

-- 添加字段注释
EXEC sp_addextendedproperty 'MS_Description', '支付额ID', 'SCHEMA', 'dbo', 'TABLE', '支付额表', 'COLUMN', '支付额ID';
EXEC sp_addextendedproperty 'MS_Description', '销售额编号', 'SCHEMA', 'dbo', 'TABLE', '支付额表', 'COLUMN', '销售额NO';
EXEC sp_addextendedproperty 'MS_Description', '支付日期', 'SCHEMA', 'dbo', 'TABLE', '支付额表', 'COLUMN', '支付日期';
EXEC sp_addextendedproperty 'MS_Description', '支付金额', 'SCHEMA', 'dbo', 'TABLE', '支付额表', 'COLUMN', '支付金额';
EXEC sp_addextendedproperty 'MS_Description', '支付状态', 'SCHEMA', 'dbo', 'TABLE', '支付额表', 'COLUMN', '支付状态';
EXEC sp_addextendedproperty 'MS_Description', '支付方式', 'SCHEMA', 'dbo', 'TABLE', '支付额表', 'COLUMN', '支付方式';
EXEC sp_addextendedproperty 'MS_Description', '备注信息', 'SCHEMA', 'dbo', 'TABLE', '支付额表', 'COLUMN', '备注'; 