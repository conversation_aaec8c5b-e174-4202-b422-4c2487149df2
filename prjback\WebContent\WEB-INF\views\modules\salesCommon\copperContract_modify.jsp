<%@ page language="java" import="java.util.*"
         contentType="text/html; charset=utf-8"%>
<%@ include file="/WEB-INF/layouts/base.jsp"%>
<html>
<head>
    <title>铜签约修改</title>
    <style>
        /*设置数据表表头字体*/
        .layui-table th .layui-table-cell{
            text-align: center;
        }
    </style>
</head>
<body>
<form class="layui-form hr-form-add" action="${ctx}/salesCommon/copperContract/edit" method="post" id="submitForm">
    <div class="layui-fluid hr-layui-fluid">
        <div class="layui-card">
            <div class="layui-card-body">
                <div class="layui-form-item layui-row">
                    <div class="layui-inline layui-col-md3">
		                <label class="layui-form-label"><span class="star">*</span>铜签约No:</label>
		                <div class="layui-input-block">
 							<input type="text" class="layui-input" id="copperSignNo" name="copperSignNo" value="${copperContract.copperSignNo}" lay-verify="required" required/>
		                 </div>
                    </div>
                    <div class="layui-inline layui-col-md3">
		                <label class="layui-form-label"><span class="star">*</span>铜签约类别:</label>
                        <div class="layui-input-block"  style="display: flex;align-items: center;align-content: center;">
                            <input type="radio" name="copperSignType" id="copperSignType" value="1" title="预约铜" ${copperContract.copperSignType eq '1'?'checked':''}/>
                            <input type="radio" name="copperSignType" id="copperSignType" value="2" title="支付铜" ${copperContract.copperSignType eq '2'?'checked':''}>
                        </div>
                    </div>
                    <div class="layui-inline layui-col-md3">
                        <label class="layui-form-label"><span class="star">*</span>铜条件:</label>
                        <div class="layui-input-block">
			                <select class="layui-select" id="copperCondition" name="copperCondition" lay-filter="copperConditionFun" lay-search="true" lay-verify="required" required>
			               		<c:forEach items="${copperConditionList}" var="copperCondition">
			                  		<option value="${copperCondition}" ${copperContract.copperCondition eq copperCondition ?'selected="selected"':''}>${copperCondition}</option>
			                  	</c:forEach>
			                </select>
		                 </div>
                    </div>
                    <div class="layui-inline layui-col-md3">
		                <label class="layui-form-label"><span class="star">*</span>客户简称:</label>
		                <div class="layui-input-block">
		                    <select class="layui-select" id="customerCode" name="customerCode" lay-search="true" lay-verify="required" required>
		                        <option value="">请选择</option>
		                        <c:forEach items="${customersList}" var="customers">
		                             <option value="${customers.customerCode}" ${copperContract.customerCode eq customers.customerCode ?'selected="selected"':''}>${customers.customerCode} - ${customers.customerAlias}</option>
		                        </c:forEach>
		                    </select>
		                 </div>
                    </div>
                    <div class="layui-inline layui-col-md3">
		                <label class="layui-form-label"><span class="star">*</span>签约铜基本:</label>
				        <div class="layui-input-block" style="display: flex;justify-content: space-between;align-items: center;align-content: center">
							<input type="text" name="signedCopperPrice" id="signedCopperPrice"  lay-verify="required" required autocomplete="off" class="layui-input" style="display: block;width: 78%" value="${copperContract.signedCopperPrice}" onKeyUp="amount(this)" onBlur="overFormat(this)">
							<input type="text" name="currency"  autocomplete="off" class="layui-input" id="currency" style="display: block;width: 18%" readonly="readonly" value="${copperContract.currency}" >
				        </div>
                    </div>
                    <div class="layui-inline layui-col-md3">
		                <label class="layui-form-label"><span class="star">*</span>税率(%):</label>
		                <div class="layui-input-block">
 							<input type="text" class="layui-input" id="taxRate" name="taxRate" value="${copperContract.taxRate}" lay-verify="required" required onKeyUp="amountV3(this)" onBlur="overFormatV3(this)"/>
		                 </div>
                    </div>
                    <div class="layui-inline layui-col-md3">
		                <label class="layui-form-label" >签约铜基本(免税):</label>
		                <div class="layui-input-block">
 							<input type="text" class="layui-input" id="signedCopperPriceExTax" name="signedCopperPriceExTax" value="${copperContract.signedCopperPriceExTax}" readonly="readonly" onKeyUp="amount(this)" onBlur="overFormat(this)"/>
		                 </div>
                    </div>
                    <div class="layui-inline layui-col-md3">
                        <label class="layui-form-label"><span class="star">*</span>签约数量(KG):</label>
                        <div class="layui-input-block">
                             <input type="text" class="layui-input" id="signedQuantity" name="signedQuantity" value="${copperContract.signedQuantity}" lay-verify="required" required onKeyUp="amountV3(this)" onBlur="overFormatV3(this)"/>
                        </div>
                    </div>
                    <div class="layui-inline layui-col-md3">
                        <label class="layui-form-label"><span class="star">*</span>实际数量(KG):</label>
                        <div class="layui-input-block">
                             <input type="text" class="layui-input" id="actualQuantity" name="actualQuantity" value="${copperContract.actualQuantity}" readonly="readonly" required onKeyUp="amountV3(this)" onBlur="overFormatV3(this)"/>
                        </div>
                    </div>
                    <div class="layui-inline layui-col-md3">
                        <label class="layui-form-label">剩余数量(KG):</label>
                        <div class="layui-input-block">
                             <input type="text" class="layui-input" id="remainingQuantity" name="remainingQuantity" value="${copperContract.remainingQuantity}" readonly="readonly" onKeyUp="amountV3(this)" onBlur="overFormatV3(this)"/>
                        </div>
                    </div>
		            <div class="layui-inline layui-col-md3">
						<label class="layui-form-label"><span class="star">*</span>预约日:</label>
						<div class="layui-input-block" style="display: flex;justify-content: space-between;align-items: center;align-content: center">
							<input type="text" class="layui-input" id="appointmentDate" name="appointmentDate" value="${copperContract.appointmentDate}" placeholder="yyyy-MM-dd" lay-verify="required" required>
						</div>
		            </div>
                    <div class="layui-inline layui-col-md3">
                        <label class="layui-form-label"><span class="star">*</span>使用月:</label>
                        <div class="layui-input-block">
                            <input type="text" class="layui-input" id="useMonth" name="useMonth" value="${copperContract.useMonth}"  placeholder="yyyy-MM" lay-verify="required" required/>
                        </div>
                    </div>                   
                </div>
                <div class="layui-form-item layui-row">
                    <div class="layui-inline layui-col-md12">
                        <label class="layui-form-label"></label>
                        <div class="layui-input-block">
                        	<input type="hidden" id="id" name="id" value="${copperContract.id}"/>
                            <button type="button" class="layui-btn layui-bg-blue" lay-submit lay-filter="formDemo">保存</button>
                            <button type="button" class="layui-btn layui-btn-primary" onclick="closeAll();">取消</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</form>
<myfooter>
	<script>
	  document.getElementById('signedCopperPrice').addEventListener('change', function() {
	   var signedCopperPrice = document.getElementById('signedCopperPrice').value;
	   var taxRate = document.getElementById('taxRate').value/100;
	   // 签约铜基本/(1+税率)
	   var signedCopperPriceExTax = signedCopperPrice/(1+taxRate);
	   document.getElementById('signedCopperPriceExTax').value = parseFloat(signedCopperPriceExTax.toFixed(5));
	  });
	  document.getElementById('taxRate').addEventListener('change', function() {
		   var signedCopperPrice = document.getElementById('signedCopperPrice').value;
		   var taxRate = document.getElementById('taxRate').value/100;
		   var signedCopperPriceExTax = signedCopperPrice/(1+taxRate);
		   document.getElementById('signedCopperPriceExTax').value = parseFloat(signedCopperPriceExTax.toFixed(5));
	  });
	  
	  document.getElementById('signedQuantity').addEventListener('change', function() {
		   var signedQuantity = document.getElementById('signedQuantity').value; // 签约数量
		   var actualQuantity = document.getElementById('actualQuantity').value;// 实际数量
		   var remainingQuantity = signedQuantity-actualQuantity; // 剩余数量 = 签约数量 - 实际数量
		   document.getElementById('remainingQuantity').value = parseFloat(remainingQuantity.toFixed(3));
	  });
	</script>
    <script src="${ctxsta}/hongru/js/salesCommon/copperContract_modify.js?time=1"></script>
</myfooter>
</body>
</html>
