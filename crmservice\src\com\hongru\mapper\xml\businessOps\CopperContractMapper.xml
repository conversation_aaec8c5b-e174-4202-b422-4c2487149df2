<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hongru.mapper.businessOps.CopperContractMapper">

	<insert id="insertCopperContract" parameterType="com.hongru.entity.businessOps.CopperContract">
		INSERT INTO [businessOps].[dbo].[铜合同表]
		(
		[铜签约No],
		[铜签约类别],
		[铜条件],
		[客户代码],
		[签约铜价],
		[税率],
		[免税签约铜价],
		[签约数量],
		[实际数量],
		[剩余数量],
		[预约日期],
		[使用月],
		[状态],
		[创建人姓名],
		[创建时间],
		[更新人姓名],
		[更新时间]
		)VALUES(
		#{copperContract.copperSignNo},
		#{copperContract.copperSignType},
		#{copperContract.copperCondition},
		#{copperContract.customerCode},
		#{copperContract.signedCopperPrice},
		#{copperContract.taxRate},
		#{copperContract.signedCopperPriceExTax},
		#{copperContract.signedQuantity},
		#{copperContract.actualQuantity},
		#{copperContract.remainingQuantity},
		#{copperContract.appointmentDate},
		#{copperContract.useMonth},
		#{copperContract.status},
		#{copperContract.creatorName},
		#{copperContract.createdTime},
		#{copperContract.updaterName},
		#{copperContract.updatedTime}
		)
	</insert>

	<select id="selectCopperContractById" resultType="com.hongru.entity.businessOps.CopperContract">
		SELECT a.[流水号] AS id, a.[铜签约No] AS copperSignNo, a.[铜签约类别] AS copperSignType, a.[铜条件] AS copperCondition,
			   a.[客户代码] AS customerCode, b.[客户简称] AS customerAlias, a.[签约铜价] AS signedCopperPrice,[税率] AS taxRate,
			   a.[免税签约铜价]AS signedCopperPriceExTax, a.[签约数量] AS signedQuantity, c.[货币] AS currency,
			   a.[实际数量] AS actualQuantity, a.[剩余数量] AS remainingQuantity, a.[预约日期] AS appointmentDate, 
			   a.[使用月] AS useMonth, a.[状态] AS status, a.[创建人姓名] AS creatorName, a.[创建时间] AS createdTime,
			   a.[更新人姓名] AS updaterName, a.[更新时间] AS updatedTime
		FROM [businessOps].[dbo].[铜合同表] a
		LEFT JOIN [sumitomo].[dbo].[客户表] b ON a.[客户代码] COLLATE DATABASE_DEFAULT = b.[客户代码] COLLATE DATABASE_DEFAULT
		LEFT JOIN [businessOps].[dbo].[铜条件表] c ON a.[铜条件] COLLATE DATABASE_DEFAULT = c.[铜条件] COLLATE DATABASE_DEFAULT
		<where>
			<if test="id != null">
				AND a.[流水号] = #{id}
			</if>
		</where>
	</select>
	
	<select id="copperContractListByPage" resultType="com.hongru.entity.businessOps.CopperContract">
		SELECT a.[流水号] AS id, a.[铜签约No] AS copperSignNo, a.[铜签约类别] AS copperSignType, a.[铜条件] AS copperCondition,
			   a.[客户代码] AS customerCode, b.[客户简称] AS customerAlias, a.[签约铜价] AS signedCopperPrice,[税率] AS taxRate,
			   a.[免税签约铜价]AS signedCopperPriceExTax, a.[签约数量] AS signedQuantity, c.[货币] AS currency,
			   a.[实际数量] AS actualQuantity, a.[剩余数量] AS remainingQuantity, a.[预约日期] AS appointmentDate, 
			   a.[使用月] AS useMonth, a.[状态] AS status, a.[创建人姓名] AS creatorName, a.[创建时间] AS createdTime,
			   a.[更新人姓名] AS updaterName, a.[更新时间] AS updatedTime
		FROM [businessOps].[dbo].[铜合同表] a
		LEFT JOIN [sumitomo].[dbo].[客户表] b ON a.[客户代码] COLLATE DATABASE_DEFAULT = b.[客户代码] COLLATE DATABASE_DEFAULT
		LEFT JOIN [businessOps].[dbo].[铜条件表] c ON a.[铜条件] COLLATE DATABASE_DEFAULT = c.[铜条件] COLLATE DATABASE_DEFAULT
		<where>
			<if test="customerCode != null and customerCode != ''">
				AND a.[客户代码] = #{customerCode}
			</if>
			<if test="copperSignNo != null and copperSignNo != ''">
				AND a.[铜签约No] = #{copperSignNo}
			</if>
			<if test="copperSignType != null and copperSignType != ''">
				AND a.[铜签约类别] = #{copperSignType}
			</if>
			<if test="copperCondition != null and copperCondition != ''">
				AND a.[铜条件] = #{copperCondition}
			</if>
			<if test="appointmentDate != null">
				AND a.[预约日期] = #{appointmentDate}
			</if>
			<if test="useMonth != null ">
				AND a.[使用月] = #{useMonth}
			</if>
			<if test="status != null and status != ''">
				AND a.[状态] = #{status}
			</if>
		</where>
		ORDER BY a.[客户代码], a.[流水号] DESC
		<if test="pageInfo">
			OFFSET ${pageInfo.offset} ROWS
			FETCH NEXT ${pageInfo.limit} ROWS ONLY
		</if>
	</select>
	
	<select id="copperContractListByPageCount" resultType="integer">
		SELECT
		 	COUNT(1)
		FROM [businessOps].[dbo].[铜合同表] a
		LEFT JOIN [sumitomo].[dbo].[客户表] b ON a.[客户代码] COLLATE DATABASE_DEFAULT = b.[客户代码] COLLATE DATABASE_DEFAULT
		LEFT JOIN [businessOps].[dbo].[铜条件表] c ON a.[铜条件] COLLATE DATABASE_DEFAULT = c.[铜条件] COLLATE DATABASE_DEFAULT
		<where>
			<if test="customerCode != null and customerCode != ''">
				AND a.[客户代码] = #{customerCode}
			</if>
			<if test="copperSignNo != null and copperSignNo != ''">
				AND a.[铜签约No] = #{copperSignNo}
			</if>
			<if test="copperSignType != null and copperSignType != ''">
				AND a.[铜签约类别] = #{copperSignType}
			</if>
			<if test="copperCondition != null and copperCondition != ''">
				AND a.[铜条件] = #{copperCondition}
			</if>
			<if test="appointmentDate != null">
				AND a.[预约日期] = #{appointmentDate}
			</if>
			<if test="useMonth != null ">
				AND a.[使用月] = #{useMonth}
			</if>
			<if test="status != null and status != ''">
				AND a.[状态] = #{status}
			</if>
		</where>
	</select>
	
	<update id="updateCopperContract">
		UPDATE [businessOps].[dbo].[铜合同表]
		<set>
			<if test="copperContract.copperSignNo != null and copperContract.copperSignNo != ''">
				[铜签约No] = #{copperContract.copperSignNo},
			</if>
			<if test="copperContract.copperSignType != null and copperContract.copperSignType != ''">
				[铜签约类别] = #{copperContract.copperSignType},
			</if>
			<if test="copperContract.copperCondition != null and copperContract.copperCondition != ''">
				[铜条件 ] = #{copperContract.copperCondition},
			</if>
			<if test="copperContract.customerCode != null and copperContract.customerCode != ''">
				[客户代码] = #{copperContract.customerCode},
			</if>
			<if test="copperContract.signedCopperPrice != null">
				[签约铜价] = #{copperContract.signedCopperPrice},
			</if>
			<if test="copperContract.taxRate != null">
				[税率] = #{copperContract.taxRate},
			</if>
			<if test="copperContract.signedCopperPriceExTax != null">
				[免税签约铜价] = #{copperContract.signedCopperPriceExTax},
			</if>
			<if test="copperContract.signedQuantity != null">
				[签约数量] = #{copperContract.signedQuantity},
			</if>
			<if test="copperContract.actualQuantity != null">
				[实际数量] = #{copperContract.actualQuantity},
			</if>
			<if test="copperContract.remainingQuantity != null">
				[剩余数量] = #{copperContract.remainingQuantity},
			</if>
			<if test="copperContract.appointmentDate != null">
				[预约日期] = #{copperContract.appointmentDate},
			</if>
			<if test="copperContract.useMonth != null">
				[使用月] = #{copperContract.useMonth},
			</if>
			<if test="copperContract.updaterName != null and copperContract.updaterName != ''">
				[更新人姓名] = #{copperContract.updaterName},
			</if>
			<if test="copperContract.updatedTime != null">
				[更新时间] = #{copperContract.updatedTime},
			</if>
		</set>
		WHERE [流水号] = #{copperContract.id}
	</update>
	
	<update id="updateCopperContractStatus">
		UPDATE [businessOps].[dbo].[铜合同表] SET [状态] = #{status} WHERE [流水号] = #{id}
	</update>

	<!-- 获取铜合同列表（用于销售额登录） -->
	<select id="getCopperContractListForSalesLogin" resultType="com.hongru.entity.businessOps.CopperContract">
		SELECT
			a.[流水号] AS id,
			a.[铜签约No] AS copperSignNo,
			a.[铜签约类别] AS copperSignType,
			a.[铜条件] AS copperCondition,
			a.[客户代码] AS customerCode,
			a.[签约铜价] AS signedCopperPrice,
			a.[使用月] AS useMonth,
			a.[状态] AS status
		FROM [BusinessOps].[dbo].[铜合同表] a
		WHERE a.[客户代码] = #{customerCode}
		AND a.[铜签约类别] = #{copperContractType}
		AND a.[使用月] = #{useMonth}
		<if test="status != null and status != ''">
			AND a.[状态] = #{status}
		</if>
		ORDER BY a.[铜签约No]
	</select>

	<!-- 根据铜签约NO和使用月查询铜合同详情（关联铜条件表） -->
	<select id="getCopperContractDetailWithCondition" resultType="com.hongru.entity.businessOps.CopperContract">
		SELECT
			a.[流水号] AS id,
			a.[铜签约No] AS copperSignNo,
			a.[铜签约类别] AS copperSignType,
			a.[铜条件] AS copperCondition,
			a.[客户代码] AS customerCode,
			a.[签约铜价] AS signedCopperPrice,
			a.[税率] AS taxRate,
			a.[免税签约铜价] AS signedCopperPriceExTax,
			a.[签约数量] AS signedQuantity,
			a.[实际数量] AS actualQuantity,
			a.[剩余数量] AS remainingQuantity,
			a.[预约日期] AS appointmentDate,
			a.[使用月] AS useMonth,
			a.[状态] AS status,
			b.[铜条件名] AS copperConditionName,
			b.[货币] AS currency
		FROM [BusinessOps].[dbo].[铜合同表] a
		LEFT JOIN [BusinessOps].[dbo].[铜条件表] b ON a.[铜条件] = b.[铜条件]
		WHERE a.[铜签约No] = #{copperSignNo}
		  AND a.[使用月] = #{useMonth}
		ORDER BY a.[流水号] DESC
		OFFSET 0 ROWS FETCH NEXT 1 ROWS ONLY
	</select>

	<!-- 更新铜合同的实际数量和剩余数量 -->
	<update id="updateCopperContractQuantity">
		UPDATE [BusinessOps].[dbo].[铜合同表]
		SET [实际数量] = #{actualQuantity},
			[剩余数量] = #{remainingQuantity}
		WHERE [铜签约No] = #{copperSignNo}
	</update>
</mapper>