<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hongru.mapper.businessOps.ProductsPriceMapper">

	<insert id="insertProductsPrice" parameterType="com.hongru.entity.businessOps.ProductsPrice">
		INSERT INTO [businessOps].[dbo].[产品价格表]
		(
		[客户代码],
		[产品代码],
		[货币],
		[适用开始日期],
		[适用结束日期],
		[产品单价],
		[创建人姓名],
		[创建时间]
		)VALUES(
		#{productsPrice.customerCode},
		#{productsPrice.productCode},
		#{productsPrice.currency},
		#{productsPrice.applyDateStart},
		#{productsPrice.applyDateEnd},
		#{productsPrice.productPrice},
		#{productsPrice.creatorName},
		#{productsPrice.createdTime}
		)
	</insert>

	<select id="selectProductsPriceById" resultType="com.hongru.entity.businessOps.ProductsPrice">
		SELECT a.[流水号] AS id,  a.[客户代码] AS customerCode, RTRIM(b.[客户简称]) AS customerAlias, a.[产品代码] AS productCode,
			   a.[货币] AS currency,a.[适用开始日期] AS applyDateStart,a.[适用结束日期] AS applyDateEnd, a.[产品单价] AS productPrice,
			    a.[创建人姓名] AS creatorName,a.[创建时间] AS createdTime
		FROM [businessOps].[dbo].[产品价格表] a
		LEFT JOIN [sumitomo].[dbo].[客户表] b
		       ON a.[客户代码] COLLATE Chinese_PRC_CI_AS = RTRIM(b.[客户代码]) COLLATE Chinese_PRC_CI_AS
		LEFT JOIN [sumitomo].[dbo].[产品表] c
			   ON a.[产品代码] COLLATE Chinese_PRC_CI_AS = RTRIM(c.[产品代码]) COLLATE Chinese_PRC_CI_AS
		<where>
			<if test="id != null">
				AND a.[流水号] = #{id}
			</if>
		</where>
	</select>
	
	<select id="productsPriceListByPage" resultType="com.hongru.entity.businessOps.ProductsPrice">
		SELECT a.[流水号] AS id,  a.[客户代码] AS customerCode, RTRIM(b.[客户简称]) AS customerAlias, RTRIM(c.[条码]) AS barcode, a.[产品代码] AS productCode,
			   a.[货币] AS currency,a.[适用开始日期] AS applyDateStart,a.[适用结束日期] AS applyDateEnd, a.[产品单价] AS productPrice,
			   a.[创建人姓名] AS creatorName,a.[创建时间] AS createdTime
		FROM [businessOps].[dbo].[产品价格表] a
		LEFT JOIN [sumitomo].[dbo].[客户表] b
		       ON a.[客户代码] COLLATE Chinese_PRC_CI_AS = RTRIM(b.[客户代码]) COLLATE Chinese_PRC_CI_AS
		LEFT JOIN [sumitomo].[dbo].[产品表] c
			   ON a.[产品代码] COLLATE Chinese_PRC_CI_AS = RTRIM(c.[产品代码]) COLLATE Chinese_PRC_CI_AS
		<where>
			<if test="customerCode != null and customerCode != ''">
				AND a.[客户代码] = #{customerCode}
			</if>
			<if test="productCode != null and productCode != ''">
				AND a.[产品代码] = #{productCode}
			</if>
			<if test="applyDateStart != null">
				AND a.[适用开始日期] &gt;=#{applyDateStart}
			</if>
			<if test="applyDateEnd != null">
				AND a.[适用结束日期] &lt;= #{applyDateEnd}
			</if>
		</where>
		ORDER BY a.[客户代码], a.[产品代码]
		<if test="pageInfo">
			OFFSET ${pageInfo.offset} ROWS
			FETCH NEXT ${pageInfo.limit} ROWS ONLY
		</if>
	</select>
	
	<select id="productsPriceListByPageCount" resultType="integer">
		SELECT COUNT(1) FROM [businessOps].[dbo].[产品价格表]
		<where>
			<if test="customerCode != null and customerCode != ''">
				AND [客户代码] = #{customerCode}
			</if>
			<if test="productCode != null and productCode != ''">
				AND [产品代码] = #{productCode}
			</if>
			<if test="applyDateStart != null">
				AND [适用开始日期] &gt;=#{applyDateStart}
			</if>
			<if test="applyDateEnd != null">
				AND [适用结束日期] &lt;= #{applyDateEnd}
			</if>
		</where>
	</select>
	
	<update id="updateProductsPrice">
		UPDATE [businessOps].[dbo].[产品价格表]
		<set>
			<if test="productsPrice.customerCode != null and productsPrice.customerCode != ''">
				[客户代码] = #{productsPrice.customerCode},
			</if>
			<if test="productsPrice.productCode != null and productsPrice.productCode != ''">
				[产品代码] = #{productsPrice.productCode},
			</if>
			<if test="productsPrice.currency != null and productsPrice.currency != ''">
				[货币] = #{productsPrice.currency},
			</if>
			<if test="productsPrice.applyDateStart != null">
				[适用开始日期] = #{productsPrice.applyDateStart},
			</if>
			<if test="productsPrice.applyDateEnd != null">
				[适用结束日期] = #{productsPrice.applyDateEnd},
			</if>
			<if test="productsPrice.productPrice != null">
				[产品单价] = #{productsPrice.productPrice},
			</if>
			<if test="productsPrice.updaterName != null and productsPrice.updaterName != ''">
				[更新人姓名] = #{productsPrice.updaterName},
			</if>
			<if test="productsPrice.updatedTime != null">
				[更新时间] = #{productsPrice.updatedTime},
			</if>
		</set>
		WHERE [流水号] = #{productsPrice.id}
	</update>
	
	<delete id="deleteProductsPrice">
		DELETE [businessOps].[dbo].[产品价格表] WHERE [流水号] = #{id}
	</delete>

	<!-- 根据客户代码、产品代码、到货日期查询产品单价 -->
	<select id="getProductPriceByConditions" resultType="com.hongru.entity.businessOps.ProductsPrice">
		SELECT a.[流水号] AS id, a.[客户代码] AS customerCode, RTRIM(b.[客户简称]) AS customerAlias,
			   RTRIM(c.[条码]) AS barcode, a.[产品代码] AS productCode,
			   a.[货币] AS currency, a.[适用开始日期] AS applyDateStart, a.[适用结束日期] AS applyDateEnd,
			   a.[产品单价] AS productPrice,
			   a.[创建人姓名] AS creatorName, a.[创建时间] AS createdTime
		FROM [businessOps].[dbo].[产品价格表] a
		LEFT JOIN [sumitomo].[dbo].[客户表] b
			   ON a.[客户代码] COLLATE Chinese_PRC_CI_AS = RTRIM(b.[客户代码]) COLLATE Chinese_PRC_CI_AS
		LEFT JOIN [sumitomo].[dbo].[产品表] c
			   ON a.[产品代码] COLLATE Chinese_PRC_CI_AS = RTRIM(c.[产品代码]) COLLATE Chinese_PRC_CI_AS
		WHERE a.[客户代码] = #{customerCode}
		  AND a.[产品代码] = #{productCode}
		  AND #{arrivalDate} BETWEEN a.[适用开始日期] AND a.[适用结束日期]
		ORDER BY a.[适用开始日期] DESC
		OFFSET 0 ROWS FETCH NEXT 1 ROWS ONLY
	</select>
</mapper>