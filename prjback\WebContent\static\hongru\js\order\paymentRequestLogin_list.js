layui.use(['laydate', 'layer', 'table', 'element', 'form'], function(){
    var laydate = layui.laydate //日期
        ,layer = layui.layer //弹层
        ,table = layui.table //表格
        ,element = layui.element //元素操作
    var form = layui.form;

    // 初始化日期选择器
    laydate.render({
        elem: '#paymentRequestDate'
    });

    //执行一个 table 实例
    var url = baselocation+'/order/paymentRequestLogin/list';
    table.render({
        elem: '#demo'
        ,height: 'full-70'
        ,url: url //数据接口
        ,cellMinWidth: 80 //全局定义常规单元格的最小宽度
        ,parseData:function(res){ //res 即为原始返回的数据
            return {
                "code": 0, //解析接口状态
                "msg": '', //解析提示文本
                "count": res.total, //解析数据长度
                "data": res.rows //解析数据列表
            };
        }
        ,method:'post'
        ,title: '支付请求登录列表'
        ,page: true //开启分页
        ,limits: [10,20,50,100]
        ,where:$("#formSearch").serializeJsonObject()
        ,toolbar: '#toolbarDemo' //开启工具栏，此处显示默认图标，可以自定义模板，详见文档
        ,defaultToolbar: ['filter']
        ,totalRow: false //开启合计行
        ,cols: [[ //表头
            {field: 'zizeng', title: 'NO.', width:50, fixed:'left', type:"numbers", align:'center'}
            ,{field: 'paymentRequestNo',title: '支付请求NO',align:'center'}
            ,{field: 'paymentRequestDate',title: '支付请求日',align:'center'}
            ,{field: 'customerAlias',title: '需求方',align:'center'}
            ,{field: 'currency',title: '货币',align:'center'}
            ,{field: 'requestAmount',title: '请求金额',align:'center'}
            ,{field: 'taxRate',title: '税率(%)',align:'center'}
            ,{field: 'creatorName',title: '创建者',align:'center'}
            ,{field: 'createdTime',title: '创建时间',align:'center'}
            ,{field: 'updaterName',title: '更新者',align:'center'}
            ,{field: 'updatedTime',title: '更新时间',align:'center'}
            ,{title: '操作',minWidth:225, align:'left',fixed: 'right', toolbar: '#barDemo'}
        ]]
    });

    //监听头工具栏事件
    table.on('toolbar(test)', function(obj){
        var checkStatus = table.checkStatus(obj.config.id)
            ,data = checkStatus.data; //获取选中的数据
        switch(obj.event){
            case 'toAdd':
                console.log('新增按钮点击');
                layer_show('支付请求登录', baselocation+"/order/paymentRequestLogin/add/view", document.body.clientWidth-10, document.body.clientHeight-10);
                break;
            case 'refresh':
                var temp = $("#formSearch").serializeJsonObject();
                console.info(temp);
                layui.table.reload('demo', {
                    page: {
                        curr: 1
                    }
                    ,where: temp
                }, 'data');
                break;
        };
    });

    //监听行工具事件
    table.on('tool(test)', function(obj){ //注：tool 是工具条事件名，test 是 table 原始容器的属性 lay-filter="对应的值"
        var data = obj.data //获得当前行数据
            ,layEvent = obj.event; //获得 lay-event 对应的值

        if(layEvent === 'detail'){
            layer_show('详情',baselocation+"/order/paymentRequestLogin/detail/view?paymentRequestNo="+data.paymentRequestNo,document.body.clientWidth-10, document.body.clientHeight-10);
        }else if(layEvent === 'edit'){
            layer_show('编辑',baselocation+"/order/paymentRequestLogin/edit/view?paymentRequestNo="+data.paymentRequestNo,document.body.clientWidth-10, document.body.clientHeight-10);
        }else if(layEvent === 'remove'){
            console.log('删除数据:', data); // 调试信息
            console.log('支付请求NO:', data.paymentRequestNo); // 调试信息

            if (!data.paymentRequestNo) {
                layer.msg("支付请求NO为空，无法删除!", {time: 1500});
                return;
            }

            layer.confirm('确定删除这条支付请求记录吗？', {icon: 3, title:'提示'}, function(index){
                // 发送删除请求
                $.ajax({
                    url: baselocation + '/order/paymentRequestLogin/delete',
                    type: 'POST',
                    data: {
                        paymentRequestNo: data.paymentRequestNo
                    },
                    success: function(result) {
                        console.log('删除结果:', result); // 调试信息
                        if (result.code === 1) {
                            layer.msg("删除成功!", {time: 1500});
                            // 刷新表格
                            var temp = $("#formSearch").serializeJsonObject();
                            layui.table.reload('demo', {
                                page: {
                                    curr: 1
                                }
                                ,where: temp
                            }, 'data');
                        } else {
                            layer.msg("删除失败: " + (result.message || '未知错误'), {time: 2000});
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error('删除请求失败:', error); // 调试信息
                        layer.msg("删除请求失败: " + error, {time: 2000});
                    }
                });
                layer.close(index);
            });
        } else if(obj.event === 'export'){
            // 导出单个支付请求
            exportSinglePaymentRequest(data.paymentRequestNo);
        }
    });
});

function search() {
    var temp = $("#formSearch").serializeJsonObject();
    console.info(temp);
    layui.table.reload('demo', {
        page: {
            curr: 1
        }
        ,where: temp
    }, 'data');
}

// 导出Excel功能
function exportExcel() {
    var customerCode = $('#customerCode').val();
    var paymentRequestDate = $('#paymentRequestDate').val();

    // 创建隐藏的表单进行提交
    var form = document.createElement('form');
    form.method = 'POST';
    form.action = baselocation + '/order/paymentRequestLogin/export';
    form.style.display = 'none';

    // 添加参数
    var customerInput = document.createElement('input');
    customerInput.name = 'customerCode';
    customerInput.value = customerCode || '';
    form.appendChild(customerInput);

    var dateInput = document.createElement('input');
    dateInput.name = 'paymentRequestDate';
    dateInput.value = paymentRequestDate || '';
    form.appendChild(dateInput);

    document.body.appendChild(form);
    form.submit();
    document.body.removeChild(form);

    layui.layer.msg('正在导出Excel，请稍候...', {icon: 16, time: 2000});
}

// 导出单个支付请求
function exportSinglePaymentRequest(paymentRequestNo) {
    if (!paymentRequestNo) {
        layui.layer.msg('支付请求NO不能为空', {icon: 2});
        return;
    }

    // 创建隐藏的表单进行提交
    var form = document.createElement('form');
    form.method = 'POST';
    form.action = baselocation + '/order/paymentRequestLogin/exportByNo';
    form.style.display = 'none';

    // 添加参数
    var noInput = document.createElement('input');
    noInput.name = 'paymentRequestNo';
    noInput.value = paymentRequestNo;
    form.appendChild(noInput);

    document.body.appendChild(form);
    form.submit();
    document.body.removeChild(form);

    layui.layer.msg('正在导出Excel，请稍候...', {icon: 16, time: 2000});
}
