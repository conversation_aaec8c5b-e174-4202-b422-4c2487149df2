package com.hongru.entity.businessOps;

import java.math.BigDecimal;

import com.baomidou.mybatisplus.annotations.TableField;
import com.baomidou.mybatisplus.annotations.TableId;
import com.baomidou.mybatisplus.annotations.TableName;
import com.baomidou.mybatisplus.enums.IdType;

@TableName("销售额明细表")
public class SalesAmountDetail {
    /* 流水号 */
    @TableId(value = "销售额明细ID", type = IdType.AUTO)
    protected int id;
    /* 销售额NO */
    @TableField("销售额NO")
    protected String salesAmountNo;
    /* 送货单号 */
    @TableField("送货单号")
    protected String deliveryOrderNo;
    /* 送货单序号 */
    @TableField("送货单序号")
    protected String deliveryOrderSeq;
    /* 产品代码 */
    protected String productCode;
    /* 出货日 */
    @TableField("出库日期")
    protected String shipmentDate;
    /* 到货日 */
    protected String arrivalDate;
    /* 客户订单号 */
    @TableField("客户订单号")
    protected String customerOrderNo;
    /* 明细备注 */
    protected String detailRemark;
    /* 铜合同类别 */
    protected String copperContractType;
    /* 铜合同NO */
    protected String copperContractNo;
    /* 铜条件 */
    protected String copperCondition;
    /* 换算率 */
    protected BigDecimal conversionRate;
    /* 数量 */
    protected BigDecimal quantity;
    /* 铜base */
    protected BigDecimal copperBase;
    /* 升水 */
    protected BigDecimal premium;
    /* 换算后铜单价 */
    protected BigDecimal convertedCopperPrice;
    /* 0base */
    protected BigDecimal zeroBase;
    /* 销售单价 */
    protected BigDecimal salesUnitPrice;
    /* 销售金额 */
    protected BigDecimal salesAmount;
    /* 产品中分类 */
    protected String productMiddleCategory;
    /* 出货计划番号 */
    @TableField("出货计划番号")
    protected String shipmentPlanNo;
    /* 创建人姓名 */
    @TableField("创建人")
    protected String creatorName;
    /* 创建时间 */
    @TableField("创建时间")
    protected String createdTime;
    /* 更新人姓名 */
    @TableField("更新人")
    protected String updaterName;
    /* 更新时间 */
    @TableField("更新时间")
    protected String updatedTime;
    /* 品目C */
    @TableField("品目C")
    protected String productCategoryCode;
    /* 理由 */
    protected String reason;
    /* 线盘 */
    protected String reelName;

    // 前端期望的字段别名
    @TableField(exist = false)
    protected String currency; // 铜货币（通过前端级联操作获取）
    @TableField(exist = false)
    protected String deliveryNoteNo; // 对应 deliveryOrderNo

    @TableField(exist = false)
    protected String deliveryNoteSeq; // 对应 deliveryOrderSeq

    @TableField(exist = false)
    protected String outboundDate; // 对应 shipmentDate

    @TableField(exist = false)
    protected String remark; // 对应 detailRemark

    // 品名字段别名，对应产品代码
    @TableField(exist = false)
    protected String productName; // 对应 productCode

    // 客户品目C字段别名，对应客户代码
    @TableField(exist = false)
    protected String customerProductCode; // 对应 客户代码，但实体类中没有直接对应字段

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getSalesAmountNo() {
        return salesAmountNo;
    }

    public void setSalesAmountNo(String salesAmountNo) {
        this.salesAmountNo = salesAmountNo;
    }

    public String getDeliveryOrderNo() {
        return deliveryOrderNo;
    }

    public void setDeliveryOrderNo(String deliveryOrderNo) {
        this.deliveryOrderNo = deliveryOrderNo;
    }

    public String getDeliveryOrderSeq() {
        return deliveryOrderSeq;
    }

    public void setDeliveryOrderSeq(String deliveryOrderSeq) {
        this.deliveryOrderSeq = deliveryOrderSeq;
    }

    public String getProductCode() {
        return productCode;
    }

    public void setProductCode(String productCode) {
        this.productCode = productCode;
    }

    public String getShipmentDate() {
        return shipmentDate;
    }

    public void setShipmentDate(String shipmentDate) {
        this.shipmentDate = shipmentDate;
    }

    public String getArrivalDate() {
        return arrivalDate;
    }

    public void setArrivalDate(String arrivalDate) {
        this.arrivalDate = arrivalDate;
    }

    public String getCustomerOrderNo() {
        return customerOrderNo;
    }

    public void setCustomerOrderNo(String customerOrderNo) {
        this.customerOrderNo = customerOrderNo;
    }

    public String getDetailRemark() {
        return detailRemark;
    }

    public void setDetailRemark(String detailRemark) {
        this.detailRemark = detailRemark;
    }

    public String getCopperContractType() {
        return copperContractType;
    }

    public void setCopperContractType(String copperContractType) {
        this.copperContractType = copperContractType;
    }

    public String getCopperContractNo() {
        return copperContractNo;
    }

    public void setCopperContractNo(String copperContractNo) {
        this.copperContractNo = copperContractNo;
    }

    public String getCopperCondition() {
        return copperCondition;
    }

    public void setCopperCondition(String copperCondition) {
        this.copperCondition = copperCondition;
    }

    public BigDecimal getConversionRate() {
        return conversionRate;
    }

    public void setConversionRate(BigDecimal conversionRate) {
        this.conversionRate = conversionRate;
    }

    public BigDecimal getQuantity() {
        return quantity;
    }

    public void setQuantity(BigDecimal quantity) {
        this.quantity = quantity;
    }

    public BigDecimal getCopperBase() {
        return copperBase;
    }

    public void setCopperBase(BigDecimal copperBase) {
        this.copperBase = copperBase;
    }

    public BigDecimal getPremium() {
        return premium;
    }

    public void setPremium(BigDecimal premium) {
        this.premium = premium;
    }

    public BigDecimal getConvertedCopperPrice() {
        return convertedCopperPrice;
    }

    public void setConvertedCopperPrice(BigDecimal convertedCopperPrice) {
        this.convertedCopperPrice = convertedCopperPrice;
    }

    public BigDecimal getZeroBase() {
        return zeroBase;
    }

    public void setZeroBase(BigDecimal zeroBase) {
        this.zeroBase = zeroBase;
    }

    public BigDecimal getSalesUnitPrice() {
        return salesUnitPrice;
    }

    public void setSalesUnitPrice(BigDecimal salesUnitPrice) {
        this.salesUnitPrice = salesUnitPrice;
    }

    public BigDecimal getSalesAmount() {
        return salesAmount;
    }

    public void setSalesAmount(BigDecimal salesAmount) {
        this.salesAmount = salesAmount;
    }

    public String getProductMiddleCategory() {
        return productMiddleCategory;
    }

    public void setProductMiddleCategory(String productMiddleCategory) {
        this.productMiddleCategory = productMiddleCategory;
    }

    public String getCreatorName() {
        return creatorName;
    }

    public void setCreatorName(String creatorName) {
        this.creatorName = creatorName;
    }

    public String getCreatedTime() {
        return createdTime;
    }

    public void setCreatedTime(String createdTime) {
        this.createdTime = createdTime;
    }

    public String getUpdaterName() {
        return updaterName;
    }

    public void setUpdaterName(String updaterName) {
        this.updaterName = updaterName;
    }

    public String getUpdatedTime() {
        return updatedTime;
    }

    public void setUpdatedTime(String updatedTime) {
        this.updatedTime = updatedTime;
    }

    public String getShipmentPlanNo() {
        return shipmentPlanNo;
    }

    public void setShipmentPlanNo(String shipmentPlanNo) {
        this.shipmentPlanNo = shipmentPlanNo;
    }

    public String getProductCategoryCode() {
        return productCategoryCode;
    }

    public void setProductCategoryCode(String productCategoryCode) {
        this.productCategoryCode = productCategoryCode;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public String getReelName() {
        return reelName;
    }

    public void setReelName(String reelName) {
        this.reelName = reelName;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    // 添加前端期望字段的getter和setter方法
    public String getDeliveryNoteNo() {
        return deliveryOrderNo;
    }

    public void setDeliveryNoteNo(String deliveryNoteNo) {
        this.deliveryOrderNo = deliveryNoteNo;
        this.deliveryNoteNo = deliveryNoteNo;
    }

    public String getDeliveryNoteSeq() {
        return deliveryOrderSeq;
    }

    public void setDeliveryNoteSeq(String deliveryNoteSeq) {
        this.deliveryOrderSeq = deliveryNoteSeq;
        this.deliveryNoteSeq = deliveryNoteSeq;
    }

    public String getOutboundDate() {
        return shipmentDate;
    }

    public void setOutboundDate(String outboundDate) {
        this.shipmentDate = outboundDate;
        this.outboundDate = outboundDate;
    }

    public String getRemark() {
        return detailRemark;
    }

    public void setRemark(String remark) {
        this.detailRemark = remark;
        this.remark = remark;
    }

    // 添加品名的getter和setter方法
    public String getProductName() {
        return productCode; // 品名对应产品代码
    }

    public void setProductName(String productName) {
        this.productCode = productName; // 设置品名时同时设置产品代码
        this.productName = productName;
    }

    // 添加客户品目C的getter和setter方法
    public String getCustomerProductCode() {
        // 这里应该返回客户代码，但实体类中没有直接对应字段
        // 可能需要从其他地方获取
        return null;
    }

    public void setCustomerProductCode(String customerProductCode) {
        // 设置客户品目C，但实体类中没有直接对应字段
        this.customerProductCode = customerProductCode;
    }
}
