package com.hongru.pojo.dto;

import com.hongru.entity.businessOps.HumanPriceCost;
import com.hongru.support.page.PageInfo;

import java.util.List;

public class HumanPriceCostDTO {

    private PageInfo pageInfo;

    private List<HumanPriceCost> humanPriceCostList;

    public HumanPriceCostDTO(PageInfo pageInfo, List<HumanPriceCost> humanPriceCostList) {
        super();
        this.pageInfo = pageInfo;
        this.humanPriceCostList = humanPriceCostList;
    }

    public PageInfo getPageInfo() {
        return pageInfo;
    }

    public void setPageInfo(PageInfo pageInfo) {
        this.pageInfo = pageInfo;
    }

    public List<HumanPriceCost> getHumanPriceCostList() {
        return humanPriceCostList;
    }

    public void setHumanPriceCostList(List<HumanPriceCost> humanPriceCostList) {
        this.humanPriceCostList = humanPriceCostList;
    }
}
