package com.hongru.pojo.dto;

import java.math.BigDecimal;
import java.util.List;

import com.hongru.support.page.PageInfo;

/**
 * 客户销售额DTO
 */
public class CustomerSalesRevenueDTO {

    private String accountingYearMonth; // 会计年月
    private String customerAlias; // 客户简称
    private String productCode; // 产品代码
    private String size; // 尺寸
    private String shipmentDate; // 出库日期
    private String arrivalDate; // 到货日期
    private BigDecimal salesQuantity; // 销售额数
    private BigDecimal salesUnitPrice; // 销售单价
    private BigDecimal salesAmount; // 销售额金额
    private BigDecimal copperPrice; // 铜价
    private String productMiddleCategory; // 产品中分类

    // 分页信息
    private PageInfo pageInfo;

    // 查询结果列表
    private List<CustomerSalesRevenueDTO> customerSalesRevenueList;

    public String getAccountingYearMonth() {
        return accountingYearMonth;
    }

    public void setAccountingYearMonth(String accountingYearMonth) {
        this.accountingYearMonth = accountingYearMonth;
    }

    public String getCustomerAlias() {
        return customerAlias;
    }

    public void setCustomerAlias(String customerAlias) {
        this.customerAlias = customerAlias;
    }

    public String getProductCode() {
        return productCode;
    }

    public void setProductCode(String productCode) {
        this.productCode = productCode;
    }

    public String getSize() {
        return size;
    }

    public void setSize(String size) {
        this.size = size;
    }

    public String getShipmentDate() {
        return shipmentDate;
    }

    public void setShipmentDate(String shipmentDate) {
        this.shipmentDate = shipmentDate;
    }

    public String getArrivalDate() {
        return arrivalDate;
    }

    public void setArrivalDate(String arrivalDate) {
        this.arrivalDate = arrivalDate;
    }

    public BigDecimal getSalesQuantity() {
        return salesQuantity;
    }

    public void setSalesQuantity(BigDecimal salesQuantity) {
        this.salesQuantity = salesQuantity;
    }

    public BigDecimal getSalesUnitPrice() {
        return salesUnitPrice;
    }

    public void setSalesUnitPrice(BigDecimal salesUnitPrice) {
        this.salesUnitPrice = salesUnitPrice;
    }

    public BigDecimal getSalesAmount() {
        return salesAmount;
    }

    public void setSalesAmount(BigDecimal salesAmount) {
        this.salesAmount = salesAmount;
    }

    public BigDecimal getCopperPrice() {
        return copperPrice;
    }

    public void setCopperPrice(BigDecimal copperPrice) {
        this.copperPrice = copperPrice;
    }

    public String getProductMiddleCategory() {
        return productMiddleCategory;
    }

    public void setProductMiddleCategory(String productMiddleCategory) {
        this.productMiddleCategory = productMiddleCategory;
    }

    public PageInfo getPageInfo() {
        return pageInfo;
    }

    public void setPageInfo(PageInfo pageInfo) {
        this.pageInfo = pageInfo;
    }

    public List<CustomerSalesRevenueDTO> getCustomerSalesRevenueList() {
        return customerSalesRevenueList;
    }

    public void setCustomerSalesRevenueList(List<CustomerSalesRevenueDTO> customerSalesRevenueList) {
        this.customerSalesRevenueList = customerSalesRevenueList;
    }
}