package com.hongru.mapper.businessOps;

import com.baomidou.mybatisplus.mapper.BaseMapper;
import com.hongru.entity.businessOps.ContractDetails;
import com.hongru.support.page.PageInfo;

import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ContractDetailsMapper extends BaseMapper<ContractDetails> {
    String getMaxContractCode();

    List<ContractDetails> listContractDetailsList();

    int insertContractDetails(@Param("contractDetails") ContractDetails contractDetails);

    ContractDetails selectContractDetailsById(@Param("id") int id);

    List<ContractDetails> contractDetailsListByPage(@Param("pageInfo") PageInfo pageInfo,
            @Param("contractCode") String customerCode);

    Integer contractDetailsListByPageCount(@Param("contractCode") String contractCode);

    void updateContractDetails(@Param("contractDetails") ContractDetails contractDetails);

    void deleteContractDetailsById(@Param("id") Integer id);

    /**
     * 根据合同方代码获取合同方详情
     * 
     * @param contractCode 合同方代码
     * @return 合同方详情
     */
    ContractDetails selectByContractCode(@Param("contractCode") String contractCode);
}
