package com.hongru.pojo.dto;

import com.hongru.entity.businessOps.RepairAndAuxiliaryMaterialDepartment;
import com.hongru.entity.businessOps.SmallDepartment;
import com.hongru.support.page.PageInfo;

import java.util.List;

public class RepairAndAuxiliaryMaterialDepartmentDTO {

    private PageInfo pageInfo;

    private List<RepairAndAuxiliaryMaterialDepartment> repairAndAuxiliaryMaterialDepartmentList;

    public RepairAndAuxiliaryMaterialDepartmentDTO(PageInfo pageInfo, List<RepairAndAuxiliaryMaterialDepartment> repairAndAuxiliaryMaterialDepartmentList) {
        super();
        this.pageInfo = pageInfo;
        this.repairAndAuxiliaryMaterialDepartmentList = repairAndAuxiliaryMaterialDepartmentList;
    }

    public PageInfo getPageInfo() {
        return pageInfo;
    }

    public void setPageInfo(PageInfo pageInfo) {
        this.pageInfo = pageInfo;
    }

    public List<RepairAndAuxiliaryMaterialDepartment> getRepairAndAuxiliaryMaterialDepartmentList() {
        return repairAndAuxiliaryMaterialDepartmentList;
    }

    public void setRepairAndAuxiliaryMaterialDepartmentList(List<RepairAndAuxiliaryMaterialDepartment> repairAndAuxiliaryMaterialDepartmentList) {
        this.repairAndAuxiliaryMaterialDepartmentList = repairAndAuxiliaryMaterialDepartmentList;
    }
}
