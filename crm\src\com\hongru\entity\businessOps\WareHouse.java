package com.hongru.entity.businessOps;

import com.baomidou.mybatisplus.annotations.TableId;
import com.baomidou.mybatisplus.annotations.TableName;
import com.baomidou.mybatisplus.enums.IdType;


@TableName("仓库表")//CostPrice
public class WareHouse {

	/* 流水号 */
	@TableId(value="流水号", type= IdType.AUTO)
	protected int id;
	/* 仓库代码 */
	protected String wareHouseCode;
	/* 仓库名 */
	protected String wareHouseName;
	/* 仓库场所区分 */
	protected String wareHousePlaceDiff;
	/* 仓库区分 */
	protected String wareHouseDiff;
	/* 创建人姓名 */
	protected String creatorName;
	/* 创建时间 */
	protected String createdTime;
	/* 更新人姓名 */
	protected String updaterName;
	/* 更新时间 */
	protected String updatedTime;
	
	public int getId() {
		return id;
	}
	public void setId(int id) {
		this.id = id;
	}
	public String getWareHouseCode() {
		return wareHouseCode;
	}
	public void setWareHouseCode(String wareHouseCode) {
		this.wareHouseCode = wareHouseCode;
	}
	public String getWareHouseName() {
		return wareHouseName;
	}
	public void setWareHouseName(String wareHouseName) {
		this.wareHouseName = wareHouseName;
	}
	public String getWareHousePlaceDiff() {
		return wareHousePlaceDiff;
	}
	public void setWareHousePlaceDiff(String wareHousePlaceDiff) {
		this.wareHousePlaceDiff = wareHousePlaceDiff;
	}
	public String getWareHouseDiff() {
		return wareHouseDiff;
	}
	public void setWareHouseDiff(String wareHouseDiff) {
		this.wareHouseDiff = wareHouseDiff;
	}
	public String getCreatorName() {
		return creatorName;
	}
	public void setCreatorName(String creatorName) {
		this.creatorName = creatorName;
	}
	public String getCreatedTime() {
		return createdTime;
	}
	public void setCreatedTime(String createdTime) {
		this.createdTime = createdTime;
	}
	public String getUpdaterName() {
		return updaterName;
	}
	public void setUpdaterName(String updaterName) {
		this.updaterName = updaterName;
	}
	public String getUpdatedTime() {
		return updatedTime;
	}
	public void setUpdatedTime(String updatedTime) {
		this.updatedTime = updatedTime;
	}
}