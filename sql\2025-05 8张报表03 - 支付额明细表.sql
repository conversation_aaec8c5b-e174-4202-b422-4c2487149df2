-- 创建支付额明细表
CREATE TABLE 支付额明细表 (
    支付额明细ID INT IDENTITY(1,1) PRIMARY KEY, -- 自增主键
    销售额NO NVARCHAR(50) NOT NULL,              -- 关联销售额表的外键
    产品代码 NVARCHAR(50) NOT NULL,              -- 产品代码
    产品中分类 NVARCHAR(100) NOT NULL,          -- 产品中分类
    数量 DECIMAL(18,3) NOT NULL,                -- 支付数量
    销售单价 DECIMAL(18,2) NOT NULL,            -- 销售单价
    铜base DECIMAL(18,2) NOT NULL,              -- 铜基准价
    创建时间 DATETIME DEFAULT GETDATE(),        -- 记录创建时间
    更新时间 DATETIME DEFAULT GETDATE(),        -- 记录更新时间
    创建人 NVARCHAR(50),                        -- 创建人
    更新人 NVARCHAR(50),                        -- 更新人
    CONSTRAINT FK_支付额明细_销售额 FOREIGN KEY (销售额NO) REFERENCES 销售额表(销售额NO)
);

-- 创建索引
CREATE INDEX IDX_支付额明细_销售额NO ON 支付额明细表(销售额NO);
CREATE INDEX IDX_支付额明细_产品代码 ON 支付额明细表(产品代码);
CREATE INDEX IDX_支付额明细_产品中分类 ON 支付额明细表(产品中分类);

-- 添加表注释
EXEC sp_addextendedproperty 'MS_Description', '支付额明细表', 'SCHEMA', 'dbo', 'TABLE', '支付额明细表';

-- 添加字段注释
EXEC sp_addextendedproperty 'MS_Description', '支付额明细ID', 'SCHEMA', 'dbo', 'TABLE', '支付额明细表', 'COLUMN', '支付额明细ID';
EXEC sp_addextendedproperty 'MS_Description', '销售额编号', 'SCHEMA', 'dbo', 'TABLE', '支付额明细表', 'COLUMN', '销售额NO';
EXEC sp_addextendedproperty 'MS_Description', '产品代码', 'SCHEMA', 'dbo', 'TABLE', '支付额明细表', 'COLUMN', '产品代码';
EXEC sp_addextendedproperty 'MS_Description', '产品中分类', 'SCHEMA', 'dbo', 'TABLE', '支付额明细表', 'COLUMN', '产品中分类';
EXEC sp_addextendedproperty 'MS_Description', '支付数量', 'SCHEMA', 'dbo', 'TABLE', '支付额明细表', 'COLUMN', '数量';
EXEC sp_addextendedproperty 'MS_Description', '销售单价', 'SCHEMA', 'dbo', 'TABLE', '支付额明细表', 'COLUMN', '销售单价';
EXEC sp_addextendedproperty 'MS_Description', '铜基准价', 'SCHEMA', 'dbo', 'TABLE', '支付额明细表', 'COLUMN', '铜base'; 