<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hongru.mapper.businessOps.CurrencyExchangeRateMapper">
    <sql id="currencyExchangeRate_sql">
		pc.[流水号] AS id,pc.[换算原货币] AS originalCurrency,pc.[换算方货币] AS targetCurrency,pc.[适用开始日期] AS applyDateStart,
		pc.[适用终了日期] AS applyDateEnd,pc.[换算汇率 ] AS exchangeRate,pc.[创建人姓名] AS creatorName,pc.[创建时间] AS createdTime,
		pc.[更新人姓名] AS updaterName,pc.[更新时间] AS updatedTime
	</sql>
	
	<insert id="insertCurrencyExchangeRate" parameterType="com.hongru.entity.businessOps.CurrencyExchangeRate">
		INSERT INTO [businessOps].[dbo].[货币换算汇率表]
		(
		[换算原货币],
		[换算方货币],
		[适用开始日期],
		[适用终了日期],
		[换算汇率],
		[创建人姓名],
		[创建时间]
		)VALUES(
		#{currencyExchangeRate.originalCurrency},
		#{currencyExchangeRate.targetCurrency},
		#{currencyExchangeRate.applyDateStart},
		#{currencyExchangeRate.applyDateEnd},
		#{currencyExchangeRate.exchangeRate},
		#{currencyExchangeRate.creatorName},
		#{currencyExchangeRate.createdTime}
		)
	</insert>

	<select id="selectCurrencyExchangeRateById" resultType="com.hongru.entity.businessOps.CurrencyExchangeRate">
		SELECT
		<include refid="currencyExchangeRate_sql"/>
		FROM [businessOps].[dbo].[货币换算汇率表] pc
		<where>
			<if test="id != null">
				AND pc.[流水号] = #{id}
			</if>
		</where>
	</select>
	
	<select id="currencyExchangeRateListByPage" resultType="com.hongru.entity.businessOps.CurrencyExchangeRate">
		SELECT		
		<include refid="currencyExchangeRate_sql"/>
		FROM [businessOps].[dbo].[货币换算汇率表] pc
		<where>
			<if test="originalCurrency != null and originalCurrency != ''">
				AND pc.[换算原货币] = #{originalCurrency}
			</if>
			<if test="targetCurrency != null and targetCurrency != ''">
				AND pc.[换算方货币] = #{targetCurrency}
			</if>
			<if test="applyDateStart != null">
				AND pc.[适用开始日期] &gt;=#{applyDateStart}
			</if>
			<if test="applyDateEnd != null">
				AND pc.[适用终了日期] &lt;= #{applyDateEnd}
			</if>
		</where>
		ORDER BY pc.[适用开始日期] DESC, pc.[换算原货币]
		<if test="pageInfo">
			OFFSET ${pageInfo.offset} ROWS
			FETCH NEXT ${pageInfo.limit} ROWS ONLY
		</if>
	</select>
	
	<select id="currencyExchangeRateListByPageCount" resultType="integer">
		SELECT COUNT(1) FROM [businessOps].[dbo].[货币换算汇率表] pc
		<where>
			<if test="originalCurrency != null and originalCurrency != ''">
				AND pc.[换算原货币] = #{originalCurrency}
			</if>
			<if test="targetCurrency != null and targetCurrency != ''">
				AND pc.[换算方货币] = #{targetCurrency}
			</if>
			<if test="applyDateStart != null">
				AND pc.[适用开始日期] &gt;=#{applyDateStart}
			</if>
			<if test="applyDateEnd != null">
				AND pc.[适用终了日期] &lt;= #{applyDateEnd}
			</if>
		</where>
	</select>
	
	<update id="updateCurrencyExchangeRate">
		UPDATE [businessOps].[dbo].[货币换算汇率表]
		<set>
			<if test="currencyExchangeRate.originalCurrency != null and currencyExchangeRate.originalCurrency != ''">
				[换算原货币] = #{currencyExchangeRate.originalCurrency},
			</if>
			<if test="currencyExchangeRate.targetCurrency != null and currencyExchangeRate.targetCurrency != ''">
				[换算方货币 ] = #{currencyExchangeRate.targetCurrency},
			</if>
			<if test="currencyExchangeRate.applyDateStart != null">
				[适用开始日期] = #{currencyExchangeRate.applyDateStart},
			</if>
			<if test="currencyExchangeRate.applyDateEnd != null">
				[适用终了日期] = #{currencyExchangeRate.applyDateEnd},
			</if>
			<if test="currencyExchangeRate.exchangeRate != null">
				[换算汇率] = #{currencyExchangeRate.exchangeRate},
			</if>
			<if test="currencyExchangeRate.updaterName != null and currencyExchangeRate.updaterName != ''">
				[更新人姓名] = #{currencyExchangeRate.updaterName},
			</if>
			<if test="currencyExchangeRate.updatedTime != null">
				[更新时间] = #{currencyExchangeRate.updatedTime},
			</if>
		</set>
		WHERE [流水号] = #{currencyExchangeRate.id}
	</update>
	
	<delete id="deleteCurrencyExchangeRate">
		DELETE [businessOps].[dbo].[货币换算汇率表] WHERE [流水号] = #{id}
	</delete>

	<!-- 根据换算原货币和适用日期查询汇率 -->
	<select id="getCurrencyExchangeRateByOriginalCurrencyAndDate" resultType="com.hongru.entity.businessOps.CurrencyExchangeRate">
		SELECT
		<include refid="currencyExchangeRate_sql"/>
		FROM [businessOps].[dbo].[货币换算汇率表] pc
		WHERE pc.[换算原货币] = #{originalCurrency}
		  AND pc.[换算方货币] = 'RMB'
		  AND #{arrivalDate} BETWEEN pc.[适用开始日期] AND pc.[适用终了日期]
		ORDER BY pc.[适用开始日期] DESC
	</select>
</mapper>