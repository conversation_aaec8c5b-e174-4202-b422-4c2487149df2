package com.hongru.mapper.businessOps;

import com.baomidou.mybatisplus.mapper.BaseMapper;
import com.hongru.entity.businessOps.WareHouse;

import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface WareHouseMapper extends BaseMapper<WareHouse> {
	int insertWareHouse(@Param("wareHouse") WareHouse wareHouse);
  
	WareHouse selectWareHouseById(@Param("id") int id);

    List<WareHouse> listWareHouse(@Param("wareHouseCode") String wareHouseCode, @Param("wareHouseName") String wareHouseName);

    void updateWareHouse(@Param("wareHouse") WareHouse wareHouse);

    void deleteWareHouse(@Param("id") Integer id);
}
