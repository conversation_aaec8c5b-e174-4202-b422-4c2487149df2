package com.hongru.pojo.dto;

import com.hongru.entity.businessOps.AuxiliaryRecycling;
import com.hongru.entity.businessOps.AuxiliaryRecyclingArtificial;
import com.hongru.support.page.PageInfo;

import java.util.List;

public class AuxiliaryRecyclingArtificialDTO {

    private PageInfo pageInfo;

    private List<AuxiliaryRecyclingArtificial> auxiliaryRecyclingArtificials;

    public PageInfo getPageInfo() {
        return pageInfo;
    }

    public void setPageInfo(PageInfo pageInfo) {
        this.pageInfo = pageInfo;
    }

    public List<AuxiliaryRecyclingArtificial> getAuxiliaryRecyclingArtificials() {
        return auxiliaryRecyclingArtificials;
    }

    public void setAuxiliaryRecyclingArtificials(List<AuxiliaryRecyclingArtificial> auxiliaryRecyclingArtificials) {
        this.auxiliaryRecyclingArtificials = auxiliaryRecyclingArtificials;
    }

    public AuxiliaryRecyclingArtificialDTO(PageInfo pageInfo, List<AuxiliaryRecyclingArtificial> auxiliaryRecyclingArtificials) {
        this.pageInfo = pageInfo;
        this.auxiliaryRecyclingArtificials = auxiliaryRecyclingArtificials;
    }
}
