-- 销售额登录表字段补充脚本（简化版）
-- 日期: 2025-06-17
-- 描述: 为销售额表和销售额明细表添加必要的缺失字段
-- 为销售额表添加必要的缺失字段（避免重复添加已有字段）

IF NOT EXISTS (SELECT 1 FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[销售额表]') AND name = N'流水号')
    ALTER TABLE [dbo].[销售额表] ADD [流水号] int NULL;
-- 注意：如已存在“流水号”字段，则不会重复添加。若仍报错，说明表结构中已存在同名字段，请检查表结构。

IF NOT EXISTS (SELECT 1 FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[销售额表]') AND name = N'税代码')
    ALTER TABLE [dbo].[销售额表] ADD [税代码] nvarchar(20) NULL;

IF NOT EXISTS (SELECT 1 FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[销售额表]') AND name = N'销售额确认')
    ALTER TABLE [dbo].[销售额表] ADD [销售额确认] char(1) DEFAULT '0' NOT NULL;

IF NOT EXISTS (SELECT 1 FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[销售额表]') AND name = N'据点')
    ALTER TABLE [dbo].[销售额表] ADD [据点] nvarchar(10) DEFAULT 'CW' NOT NULL;

IF NOT EXISTS (SELECT 1 FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[销售额表]') AND name = N'接单种类')
    ALTER TABLE [dbo].[销售额表] ADD [接单种类] nvarchar(50) NULL;

IF NOT EXISTS (SELECT 1 FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[销售额表]') AND name = N'会计年月')
    ALTER TABLE [dbo].[销售额表] ADD [会计年月] nvarchar(7) NULL;

-- 为销售额明细表添加必要的缺失字段（避免重复添加已有字段）
IF NOT EXISTS (SELECT 1 FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[销售额明细表]') AND name = N'条码')
    ALTER TABLE [dbo].[销售额明细表] ADD [条码] nvarchar(50) NULL;

IF NOT EXISTS (SELECT 1 FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[销售额明细表]') AND name = N'客户订单号')
    ALTER TABLE [dbo].[销售额明细表] ADD [客户订单号] nvarchar(50) NULL;

IF NOT EXISTS (SELECT 1 FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[销售额明细表]') AND name = N'明细备注')
    ALTER TABLE [dbo].[销售额明细表] ADD [明细备注] nvarchar(200) NULL;

IF NOT EXISTS (SELECT 1 FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[销售额明细表]') AND name = N'铜合同类别')
    ALTER TABLE [dbo].[销售额明细表] ADD [铜合同类别] nvarchar(50) NULL;

IF NOT EXISTS (SELECT 1 FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[销售额明细表]') AND name = N'铜合同NO')
    ALTER TABLE [dbo].[销售额明细表] ADD [铜合同NO] nvarchar(50) NULL;

IF NOT EXISTS (SELECT 1 FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[销售额明细表]') AND name = N'铜条件')
    ALTER TABLE [dbo].[销售额明细表] ADD [铜条件] nvarchar(100) NULL;

IF NOT EXISTS (SELECT 1 FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[销售额明细表]') AND name = N'换算率')
    ALTER TABLE [dbo].[销售额明细表] ADD [换算率] decimal(18,6) NULL;

IF NOT EXISTS (SELECT 1 FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[销售额明细表]') AND name = N'升水')
    ALTER TABLE [dbo].[销售额明细表] ADD [升水] decimal(18,2) DEFAULT (0) NULL;

IF NOT EXISTS (SELECT 1 FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[销售额明细表]') AND name = N'换算后铜单价')
    ALTER TABLE [dbo].[销售额明细表] ADD [换算后铜单价] decimal(18,2) DEFAULT (0) NULL;

IF NOT EXISTS (SELECT 1 FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[销售额明细表]') AND name = N'零基础')
    ALTER TABLE [dbo].[销售额明细表] ADD [零基础] decimal(18,2) DEFAULT (0) NULL;

-- 修改代码中使用客户代码的地方，使用客户简称替代
/*
注意：需要修改以下文件中的代码，将使用客户代码的地方改为使用客户简称
1. SalesLoginController.java - list方法中的查询条件
2. SalesAmountMapper.xml - 查询条件中的客户代码改为客户简称
*/

PRINT '销售额登录表字段补充脚本执行完成'; 



-- 删除【送货单号、送货单序号】
ALTER TABLE [dbo].[出库数量表] DROP COLUMN [送货单序号];



ALTER TABLE [dbo].[销售额明细表] ADD [出货计划番号] char(9) NULL;
