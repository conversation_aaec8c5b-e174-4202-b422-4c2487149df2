package com.hongru.mapper.businessOps;

import com.baomidou.mybatisplus.mapper.BaseMapper;
import com.hongru.entity.businessOps.PaymentAmount;
import com.hongru.pojo.dto.PaymentRequestLoginListDTO;
import com.hongru.pojo.dto.SalesConfirmListDTO;
import com.hongru.support.page.PageInfo;

import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface PaymentAmountMapper extends BaseMapper<PaymentAmount> {
    
    /**
     * 分页查询支付请求登录列表
     * 
     * @param pageInfo 分页信息
     * @param customerCode 客户代码
     * @param paymentRequestDate 支付请求日
     * @return 支付请求登录列表
     */
    List<PaymentRequestLoginListDTO> listPaymentRequestLoginByPage(
            @Param("pageInfo") PageInfo pageInfo,
            @Param("customerCode") String customerCode,
            @Param("paymentRequestDate") String paymentRequestDate);

    /**
     * 查询支付请求登录列表总数
     *
     * @param customerCode       客户代码
     * @param paymentRequestDate 支付请求日
     * @return 总数
     */
    Integer listPaymentRequestLoginByPageCount(
            @Param("customerCode") String customerCode,
            @Param("paymentRequestDate") String paymentRequestDate);

    /**
     * 分页查询支付请求登录列表（按用户过滤）
     *
     * @param pageInfo           分页信息
     * @param customerCode       客户代码
     * @param paymentRequestDate 支付请求日
     * @param creatorName        创建者名称
     * @return 支付请求登录列表
     */
    List<PaymentRequestLoginListDTO> listPaymentRequestLoginByPageWithUser(
                    @Param("pageInfo") PageInfo pageInfo,
                    @Param("customerCode") String customerCode,
                    @Param("paymentRequestDate") String paymentRequestDate,
                    @Param("creatorName") String creatorName);

    /**
     * 查询支付请求登录列表总数（按用户过滤）
     *
     * @param customerCode       客户代码
     * @param paymentRequestDate 支付请求日
     * @param creatorName        创建者名称
     * @return 总数
     */
    Integer listPaymentRequestLoginByPageCountWithUser(
                    @Param("customerCode") String customerCode,
                    @Param("paymentRequestDate") String paymentRequestDate,
                    @Param("creatorName") String creatorName);

    /**
     * 根据支付请求NO获取支付额数据
     * 
     * @param paymentRequestNo 支付请求NO
     * @return 支付额数据
     */
    PaymentAmount selectByPaymentRequestNo(@Param("paymentRequestNo") String paymentRequestNo);

    /**
     * 根据支付请求NO删除支付额数据
     * 
     * @param paymentRequestNo 支付请求NO
     * @return 删除行数
     */
    int deleteByPaymentRequestNo(@Param("paymentRequestNo") String paymentRequestNo);

    /**
     * 生成支付请求NO
     *
     * @param dateStr 日期字符串(yyMMdd格式)
     * @return 支付请求NO
     */
    String generatePaymentRequestNo(@Param("dateStr") String dateStr);

    /**
     * 分页查询销售额确认列表
     *
     * @param pageInfo           分页信息
     * @param customerCode       客户代码
     * @param paymentRequestDate 支付请求日
     * @return 销售额确认列表
     */
    List<SalesConfirmListDTO> listSalesConfirmByPage(
                    @Param("pageInfo") PageInfo pageInfo,
                    @Param("customerCode") String customerCode,
                    @Param("paymentRequestDate") String paymentRequestDate);

    /**
     * 查询销售额确认列表总数
     *
     * @param customerCode       客户代码
     * @param paymentRequestDate 支付请求日
     * @return 总数
     */
    Integer listSalesConfirmByPageCount(
                    @Param("customerCode") String customerCode,
                    @Param("paymentRequestDate") String paymentRequestDate);

    /**
     * 更新支付请求确认状态
     *
     * @param paymentRequestNo 支付请求NO
     * @param confirmStatus    确认状态 (1:确认, 0:取消确认)
     * @param confirmer        确认者
     * @param confirmTime      确认时间
     * @return 更新行数
     */
    int updatePaymentRequestConfirmStatus(
                    @Param("paymentRequestNo") String paymentRequestNo,
                    @Param("confirmStatus") String confirmStatus,
                    @Param("confirmer") String confirmer,
                    @Param("confirmTime") String confirmTime);
}
