package com.hongru.pojo.dto;

import com.hongru.entity.businessOps.ProductsPrice;
import com.hongru.support.page.PageInfo;

import java.util.List;

public class ProductsPricePageDTO {

    private PageInfo pageInfo;

    private List<ProductsPrice> productsPriceList;

    public ProductsPricePageDTO(PageInfo pageInfo, List<ProductsPrice> productsPriceList) {
        super();
        this.pageInfo = pageInfo;
        this.productsPriceList = productsPriceList;
    }

    public PageInfo getPageInfo() {
        return pageInfo;
    }

    public void setPageInfo(PageInfo pageInfo) {
        this.pageInfo = pageInfo;
    }

    public List<ProductsPrice> getProductsPriceList() {
        return productsPriceList;
    }

    public void setProductsPriceList(List<ProductsPrice> productsPriceList) {
        this.productsPriceList = productsPriceList;
    }
}
