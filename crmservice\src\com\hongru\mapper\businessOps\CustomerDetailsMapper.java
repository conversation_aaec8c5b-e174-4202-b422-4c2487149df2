package com.hongru.mapper.businessOps;

import com.baomidou.mybatisplus.mapper.BaseMapper;
import com.hongru.entity.businessOps.CustomerDetails;
import com.hongru.support.page.PageInfo;

import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface CustomerDetailsMapper extends BaseMapper<CustomerDetails> {
	int insertCustomerDetails(@Param("customerDetails") CustomerDetails customerDetails);
  
	CustomerDetails selectCustomerDetailsById(@Param("id") int id);

    List<CustomerDetails> customerDetailsListByPage(@Param("pageInfo") PageInfo pageInfo, @Param("customerCode") String customerCode, @Param("status") String status);
    Integer customerDetailsListByPageCount( @Param("customerCode") String customerCode, @Param("status") String copperCondition);

    void updateCustomerDetails(@Param("customerDetails") CustomerDetails customerDetails);

    void updateCustomerDetailsStatus(@Param("id") Integer id);
}
