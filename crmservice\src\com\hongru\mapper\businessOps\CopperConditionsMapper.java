package com.hongru.mapper.businessOps;

import com.baomidou.mybatisplus.mapper.BaseMapper;
import com.hongru.entity.businessOps.CopperConditions;

import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface CopperConditionsMapper extends BaseMapper<CopperConditions> {
	int insertCopperConditions(@Param("copperConditions") CopperConditions copperConditions);
  
	CopperConditions selectCopperConditionsById(@Param("id") int id);

    List<CopperConditions> listCopperConditions(@Param("copperCondition") String copperCondition, @Param("copperConditionName") String copperConditionName);

    void updateCopperConditions(@Param("copperConditions") CopperConditions copperConditions);

    void deleteCopperConditions(@Param("id") Integer id);
    
    CopperConditions findCopperConditions(@Param("copperCondition") String copperCondition);
    
    List<String> selectCopperCondition();
}
