layui.use(['laydate', 'layer', 'table', 'element','form'], function(){
    var laydate = layui.laydate //日期
        ,layer = layui.layer //弹层
        ,table = layui.table //表格
        ,element = layui.element //元素操作
    var form = layui.form;

     // 接单开始日期
     var insStart = layui.laydate.render({
    elem: '#orderStartDate'
    ,done: function(value, date){
        //更新结束日期的最小日期
        insEnd.config.min = lay.extend({}, date, {
        month: date.month - 1
        });

        //自动弹出结束日期的选择器
        insEnd.config.elem[0].focus();
    }
    });

    // 接单结束日期
    var insEnd = layui.laydate.render({
    elem: '#orderEndDate'
    ,done: function(value, date){
        //更新开始日期的最大日期
        insStart.config.max = lay.extend({}, date, {
        month: date.month - 1
        });
    }
    });

    // 准备开始日期
    var prepareInsStart = layui.laydate.render({
    elem: '#prepareStartDate'
    ,done: function(value, date){
        //更新结束日期的最小日期
        prepareInsEnd.config.min = lay.extend({}, date, {
        month: date.month - 1
        });

        //自动弹出结束日期的选择器
        prepareInsEnd.config.elem[0].focus();
    }
    });

    // 准备结束日期
    var prepareInsEnd = layui.laydate.render({
    elem: '#prepareEndDate'
    ,done: function(value, date){
        //更新开始日期的最大日期  
        prepareInsStart.config.max = lay.extend({}, date, {
        month: date.month - 1
        });
    }
    });

    // 出库开始日期
    var outboundInsStart = layui.laydate.render({
    elem: '#outboundStartDate'
    ,done: function(value, date){
        //更新结束日期的最小日期
        outboundInsEnd.config.min = lay.extend({}, date, {
        month: date.month - 1
        });

        //自动弹出结束日期的选择器
        outboundInsEnd.config.elem[0].focus();
    }
    });
    
    // 出库结束日期
    var outboundInsEnd = layui.laydate.render({
    elem: '#outboundEndDate'
    ,done: function(value, date){
        //更新开始日期的最大日期
        outboundInsStart.config.max = lay.extend({}, date, {
        month: date.month - 1
        });
    }
    });

    // 监听状态选择器
    form.on('select(statusFn)', function(data){
        search();
    });

    //执行一个 table 实例
    var url = baselocation+'/order/orderEntry/list';
    table.render({
        elem: '#demo'
        ,height: 'full-70'
        ,url: url //数据接口
        ,cellMinWidth: 80 //全局定义常规单元格的最小宽度
        ,parseData:function(res){ //res 即为原始返回的数据
            return {
                "code": 0, //解析接口状态
                "msg": '', //解析提示文本
                "count": res.total, //解析数据长度
                "data": res.rows //解析数据列表
            };
        }
        ,method:'post'
        ,title: '客户详情列表'
        ,page: true //开启分页
        ,limits: [10,20,50,100]
        ,where:$("#formSearch").serializeJsonObject()
        ,toolbar: '#toolbarDemo' //开启工具栏，此处显示默认图标，可以自定义模板，详见文档
        ,defaultToolbar: ['filter']
        ,totalRow: false //开启合计行
        ,cols: [[ //表头
        	{field: 'zizeng', title: 'NO.', width:50, fixed:'left', type:"numbers", align:'center'}
            // ,{field: 'status',title: '状态',align:'center', templet: function (d) {
            //     if(d.status == 0){
            //         return "未删除";
            //     }else if(d.status == 9){
            //         return "已删除";
            //     } else {
            //         return "未知";
            //     }
            // }}
            ,{field: 'orderNo',title: '接单NO',align:'center'}
            ,{field: 'customerOrderNo',title: '客户订单号',align:'center'}
            ,{field: 'orderDate',title: '接单日期',align:'center'}
            ,{field: 'orderType',title: '接单种类',align:'center', templet: function(d) {
                return d.orderType == 0 ? '通用' : '样品';
            }}
            ,{field: 'demandCustomerAlias',title: '需求方',align:'center'}
            ,{field: 'contractPartyCustomerAlias',title: '合同方',align:'center'}
            ,{field: 'creatorName',title: '创建者',align:'center'}
            ,{field: 'createdTime',title: '创建时间',align:'center'}
            ,{field: 'updaterName',title: '更新者',align:'center'}
            ,{field: 'updatedTime',title: '更新时间',align:'center'}
            ,{field: 'attachmentId', title: '附件', align:'center', templet: function(d) {
                if (d.attachmentId && d.attachmentId.trim() !== '') {
                    return '<button class="layui-btn layui-btn-xs layui-btn-normal" onclick="downloadOrderAttachment(\'' + d.attachmentId + '\')"><i class="layui-icon layui-icon-download-circle"></i> 下载</button>';
                } else {
                    return '<span class="layui-badge layui-bg-gray">无附件</span>';
                }
            }}
            ,{title: '操作',minWidth:225, align:'left',fixed: 'right', toolbar: '#barDemo'}
        ]]
    });

    //监听头工具栏事件
    table.on('toolbar(test)', function(obj){
        var checkStatus = table.checkStatus(obj.config.id)
            ,data = checkStatus.data; //获取选中的数据
        switch(obj.event){
            case 'toAdd':
                console.log('批量录入按钮点击');
                orderEntryLayerShow('批量录入', baselocation+"/order/orderEntry/add/view?mode=batch", document.body.clientWidth-10, document.body.clientHeight-10);
                break;
            case 'toSingleAdd':
                console.log('单行录入按钮点击');
                // 使用try-catch捕获可能的错误
                try {
                    var url = baselocation+"/order/orderEntry/add/view?mode=single";
                    console.log('单行录入URL:', url);
                    orderEntryLayerShow('录入', url, document.body.clientWidth-10, document.body.clientHeight-10);
                } catch (error) {
                    console.error('打开单行录入页面错误:', error);
                    alert('打开单行录入页面失败，请查看控制台日志');
                }
                break;
            case 'refresh':
                var temp = $("#formSearch").serializeJsonObject();
                console.info(temp);
                layui.table.reload('demo', {
                    page: {
                        curr: 1
                    }
                    ,where: temp
                }, 'data');
                break;
            case 'export':
                var temp = $("#formSearch").serializeJsonObject();

                // 打开预览页面
                layer_show('Excel预览', baselocation + "/order/orderEntry/preview/view", document.body.clientWidth-10, document.body.clientHeight-10);
                break;
        };
    });

    //监听行工具事件
    table.on('tool(test)', function(obj){ //注：tool 是工具条事件名，test 是 table 原始容器的属性 lay-filter="对应的值"
        var data = obj.data //获得当前行数据
            ,layEvent = obj.event; //获得 lay-event 对应的值

        var status = $('#status').val();

        if(layEvent === 'detail'){
            layer_show('详情',baselocation+"/order/orderEntry/detail/view?id="+data.id+"&status="+status,document.body.clientWidth-10, document.body.clientHeight-10);
        }else if(layEvent === 'edit'){
            orderEntryLayerShow('编辑',baselocation+"/order/orderEntry/edit/view?id="+data.id+"&status="+status,document.body.clientWidth-10, document.body.clientHeight-10);
        }else if(layEvent === 'remove'){
            layer.confirm('确认要删除吗？', {
                btn : [ '确定', '取消' ] //按钮
            }, function() {
                $.ajax({
                    type : 'post',
                    dataType : 'json',
                    data: {"id":data.id},
                    url : baselocation + '/order/orderEntry/remove',
                    success : function(result) {
                        if (result.code == 1) {
                            layer.msg("操作成功!", {
                                shade : 0.3,
                                time : 1500
                            }, function() {
                                search();
                                layer.closeAll();
                            });
                        } else {
                            layer.alert(result.data, {
                                icon : 2
                            });
                        }
                    }
                })
            });
        }
    });
});

function search() {
	var temp = $("#formSearch").serializeJsonObject();
	console.info(temp);
	layui.table.reload('demo', {
		page: {
			curr: 1
		}
		,where: temp
	}, 'data');
}

// 订单附件下载功能
function downloadOrderAttachment(attachmentId) {
    if (!attachmentId || attachmentId.trim() === '') {
        layer.msg('未找到附件', {icon: 2});
        return;
    }

    // 直接打开下载链接
    window.open(baselocation + '/common/download?id=' + attachmentId);

    // 或者使用异步方式下载 - 如果需要先验证权限等操作
    /*
    $.ajax({
        url: baselocation + '/common/checkDownloadPermission',
        type: 'post',
        data: {id: attachmentId},
        success: function(res) {
            if (res.code == 1) {
                window.open(baselocation + '/common/download?id=' + attachmentId);
            } else {
                layer.msg(res.message || '下载失败', {icon: 2});
            }
        },
        error: function() {
            layer.msg('下载请求失败', {icon: 2});
        }
    });
    */
}

// 订单录入专用的弹窗函数，在关闭时会自动刷新页面
function orderEntryLayerShow(title, url, w, h) {
    if (title == null || title == '') {
        title = false;
    }
    if (w == null || w == '') {
        w = 800;
    }
    if (h == null || h == '') {
        h = ($(window).height() - 50);
    }
    layer.open({
        type : 2,
        area : [ w + 'px', h + 'px' ],
        shadeClose : true,
        shade : false,
        anim : 1,
        maxmin : true,
        fix : false,
        scrollbar : false,
        title : title,
        content : url,
        end: function() {
            // 弹窗关闭时刷新页面
            console.log('订单录入/编辑/详情窗口已关闭，正在刷新数据...');
            search();
        }
    });
}
