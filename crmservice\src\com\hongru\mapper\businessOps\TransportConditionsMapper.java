package com.hongru.mapper.businessOps;

import com.baomidou.mybatisplus.mapper.BaseMapper;
import com.hongru.entity.businessOps.TransportConditions;

import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface TransportConditionsMapper extends BaseMapper<TransportConditions> {
	int insertTransportConditions(@Param("transportConditions") TransportConditions transportConditions);
  
	TransportConditions selectTransportConditionsById(@Param("id") int id);

    List<TransportConditions> listTransportConditions(@Param("transportCondition") String transportCondition, @Param("transportConditionName") String transportConditionName);

    void updateTransportConditions(@Param("transportConditions") TransportConditions transportConditions);

    void deleteTransportConditions(@Param("id") Integer id);
}
