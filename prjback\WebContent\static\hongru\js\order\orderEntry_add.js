// 确保baselocation变量可用
// 修复说明：修复了出库日期、到货日期、准备日期为空时自动填充当前日期的问题
// 修复方法：
// 1. 移除了OrderServiceImpl.java中自动设置出库日期为当前日期的代码
// 2. 修改了OrderMapper.xml中的SQL语句，确保当日期字段为null时不会被转换为当前日期
// 3. 在数据库插入和更新操作中，对空日期值使用NULL而不是尝试转换
var baselocation = window.baselocation || '';
if (!baselocation && document.getElementById('baselocation')) {
    baselocation = document.getElementById('baselocation').value;
}

var form = null;
var mode = '';
var subOrderList = [];
var productCodeList = [];
$(document).ready(function() {
    // 获取URL中的mode参数
    mode = getUrlParam('mode') || 'batch';
    console.log('当前模式: ' + mode);

    // 根据模式设置表格样式和添加类名
    if (mode === 'single') {
        // 单行模式下添加高度限制和滚动条（限制为显示5行）
        $('.table-scroll-container').css({
            'max-height': '238px', // 减小高度以限制显示5行
            'overflow-y': 'auto'
        }).addClass('single-mode');
    } else {
        // 批量模式下移除高度限制和滚动条
        $('.table-scroll-container').css({
            'max-height': 'none',
            'overflow-y': 'visible'
        }).addClass('batch-mode');
    }

    // 预加载SheetJS库，用于Excel文件预览
    if (mode === 'single') {
        preloadSheetJSLibrary();
    }

     // 将初始化逻辑移到LayUI加载完成后
    layui.use(['laydate', 'upload', 'element', 'layer', 'form'], function() {
        form = layui.form;
        var upload = layui.upload;
        var element = layui.element;
        var layer = layui.layer;
        var laydate = layui.laydate;

        try {
            // 初始化附件上传功能 - 先初始化上传功能
            initAttachmentUpload();

            // 初始设置表格为禁用状态，等待附件上传
            disableTableOperations();

            // 然后根据模式初始化不同的功能，但此时表格是禁用的
            if (mode === 'batch') {
                console.log('初始化批量添加模式');
                // 批量模式下，添加默认行
                addSubOrder();
            } else if (mode === 'single') {
                console.log('初始化单行添加模式');
                // 单行模式下，设置预览区域可见
                $('#attachmentPreview').show();
                $('#singleRowButtonArea').show();
                // 添加单行
                addSingleSubOrder();
            } else {
                console.log('未知模式，默认使用批量模式');
                addSubOrder();
            }

            // 为了确保单行模式下JS加载完成后自动显示底部按钮区域
            if (mode === 'single') {
                $("#singleRowButtonArea").show();
            }

            // 添加表格控制功能（放大缩小功能）
            if (mode === 'single') {
                initTableControls();
            }

            form.on('select(demandCustomerCodeFn)', function(data) {
                // 获取产品尺寸列表
                $.ajax({
                    url: baselocation + '/order/orderEntry/getProductListByCustomerCode',
                    type: 'post',
                    data: {customerCode: data.value},
                    success: function(result) {
                        if (result && result.code == 1) {
                            productCodeList = result.data;
                            console.log('productCodeList :>> ', productCodeList);
                            var modelNumber = $('#modelNumber');
                            modelNumber.empty();
                            modelNumber.append('<option value="">请选择型号</option>');
                            productCodeList.forEach(function(item) {
                                modelNumber.append('<option value="' + item + '">' + item + '</option>');
                            });

                            form.render('select');
                        }
                    }
                });

                // 客户代码变更时，尝试查询产品价格
                var productCode = '';
                var arrivalDate = '';

                // 根据当前模式获取产品代码和到货日期
                if (mode === 'single') {
                    productCode = $('#subOrderTableBody tr:first-child [name="modelNumber"]').val();
                    arrivalDate = $('#subOrderTableBody tr:first-child [name="arrivalDate"]').val();
                } else {
                    productCode = $('#subOrderTableBody tr:last-child [name="modelNumber"]').val();
                    arrivalDate = $('#subOrderTableBody tr:last-child [name="arrivalDate"]').val();
                }

                if (data.value && productCode && arrivalDate) {
                    console.log('客户代码变更，尝试查询产品价格:', {
                        customerCode: data.value,
                        productCode: productCode,
                        arrivalDate: arrivalDate
                    });
                    queryProductPrice(data.value, productCode, arrivalDate);
                }
            });

            // 监听表单提交
            form.on('submit(formDemo)', function(data) {
                // 验证是否上传了附件
                if (!attachmentUploaded || !currentAttachmentId) {
                    layer.alert('请先上传附件', {icon: 2});
                    return false;
                }

                // 将附件ID添加到表单数据中
                var formData = $('#submitForm').serializeArray();
                formData.push({name: 'attachmentId', value: currentAttachmentId});

                if (mode != 'single') {
                    var $rows = $("#subOrderTableBody tr.sub-order-row");

                    console.log('$rows :>> ', $rows);

                    // 验证必填字段
                    var missingFieldsRows = [];
                    var hasError = false;

                    subOrderList = [];
                    if ($rows?.length) {
                        $rows.each(function(index) {
                            var $row = $(this);
                            var rowData = {
                                orderSerialNum: $row.find('[name="orderSerialNum"]').val(),
                                outboundDate: $row.find('[name="outboundDate"]').val(),
                                arrivalDate: $row.find('[name="arrivalDate"]').val(),
                                prepareDate: $row.find('[name="prepareDate"]').val(),
                                modelNumber: $row.find('[name="modelNumber"]').val(),
                                size: $row.find('[name="size"]').val(),
                                wireSpool: $row.find('[name="wireSpool"]').val(),
                                partNumber: $row.find('[name="partNumber"]').val(),
                                orderQuantity: $row.find('[name="orderQuantity"]').val(),
                                deliveredQuantity: $row.find('[name="deliveredQuantity"]').val(),
                                remainingQuantity: $row.find('[name="remainingQuantity"]').val(),
                                detailRemark: $row.find('[name="detailRemark"]').val()
                            };

                            // 验证必填字段
                            var missingFields = [];

                            // // 验证出库日期
                            // if (!rowData.outboundDate) {
                            //     missingFields.push('出库日期');
                            //     $row.find('[name="outboundDate"]').addClass('layui-form-danger');
                            //     hasError = true;
                            // } else {
                            //     $row.find('[name="outboundDate"]').removeClass('layui-form-danger');
                            // }

                            // // 验证到货日期
                            // if (!rowData.arrivalDate) {
                            //     missingFields.push('到货日期');
                            //     $row.find('[name="arrivalDate"]').addClass('layui-form-danger');
                            //     hasError = true;
                            // } else {
                            //     $row.find('[name="arrivalDate"]').removeClass('layui-form-danger');
                            // }

                            // // 验证准备日期
                            // if (!rowData.prepareDate) {
                            //     missingFields.push('准备日期');
                            //     $row.find('[name="prepareDate"]').addClass('layui-form-danger');
                            //     hasError = true;
                            // } else {
                            //     $row.find('[name="prepareDate"]').removeClass('layui-form-danger');
                            // }

                            // 如果有必填字段缺失，记录行号和字段
                            if (missingFields.length > 0) {
                                missingFieldsRows.push('第' + (index + 1) + '行：' + missingFields.join('、'));
                            }

                            subOrderList.push(rowData);
                        });
                    }

                    // 如果有错误，显示错误提示并返回
                    if (hasError) {
                        layer.alert('请填写必填字段：<br>' + missingFieldsRows.join('<br>'), {icon: 2});
                        return false;
                    }

                    console.log('subOrderList110 :>> ', subOrderList);
                }

                if (subOrderList?.length) {
                    // 格式化子订单信息，方便批量处理
                    var orderSerialNum = '';
                    var outboundDate = '';
                    var arrivalDate = '';
                    var prepareDate = '';
                    var modelNumber = '';
                    var size = '';
                    var wireSpool = '';
                    var partNumber = '';
                    var orderQuantity = '';
                    var deliveredQuantity = '';
                    var remainingQuantity = '';
                    var detailRemark = '';

                    subOrderList.forEach(function(item) {
                        orderSerialNum = orderSerialNum ? orderSerialNum + ',' + (item.orderSerialNum || '') : (item.orderSerialNum || '');
                        outboundDate = outboundDate ? outboundDate + ',' + (item.outboundDate || '') : (item.outboundDate || '');
                        arrivalDate = arrivalDate ? arrivalDate + ',' + (item.arrivalDate || '') : (item.arrivalDate || '');
                        prepareDate = prepareDate ? prepareDate + ',' + (item.prepareDate || '') : (item.prepareDate || '');
                        modelNumber = modelNumber ? modelNumber + ',' + (item.modelNumber || '') : (item.modelNumber || '');
                        size = size ? size + ',' + (item.size || '') : (item.size || '');
                        wireSpool = wireSpool ? wireSpool + ',' + (item.wireSpool || '') : (item.wireSpool || '');
                        partNumber = partNumber ? partNumber + ',' + (item.partNumber || '') : (item.partNumber || '');
                        orderQuantity = orderQuantity ? orderQuantity + ',' + (item.orderQuantity || '') : (item.orderQuantity || '');
                        deliveredQuantity = deliveredQuantity ? deliveredQuantity + ',' + (item.deliveredQuantity || '') : (item.deliveredQuantity || '');
                        remainingQuantity = remainingQuantity ? remainingQuantity + ',' + (item.remainingQuantity || '') : (item.remainingQuantity || '');
                        detailRemark = detailRemark ? detailRemark + ',' + (item.detailRemark || '') : (item.detailRemark || '');
                    });

                    formData.push({name: 'orderSerialNum', value: orderSerialNum});
                    formData.push({name: 'outboundDate', value: outboundDate});
                    formData.push({name: 'arrivalDate', value: arrivalDate});
                    formData.push({name: 'prepareDate', value: prepareDate});
                    formData.push({name: 'modelNumber', value: modelNumber});
                    formData.push({name: 'size', value: size});
                    formData.push({name: 'wireSpool', value: wireSpool});
                    formData.push({name: 'partNumber', value: partNumber});
                    formData.push({name: 'orderQuantity', value: orderQuantity});
                    formData.push({name: 'deliveredQuantity', value: deliveredQuantity});
                    formData.push({name: 'remainingQuantity', value: remainingQuantity});
                    formData.push({name: 'detailRemark', value: detailRemark});
                }


                // 显示加载中提示
                var loadingIndex = layer.load(2, {
                    shade : [ 0.1, '#fff' ]
                });

                // 设置超时处理，防止一直转圈
                var timeoutTimer = setTimeout(function() {
                    layer.close(loadingIndex);
                    layer.msg('请求超时，请重试', {icon: 2});
                }, 15000); // 15秒超时

                var actionUrl = $("#submitForm").attr('action');
                $.ajax({
                    url : actionUrl,
                    type : 'post',
                    data : formData,
                    success : function(result) {
                        // 清除超时计时器
                        clearTimeout(timeoutTimer);

                        // 关闭加载提示
                        layer.close(loadingIndex);

                        if (result && result.code == 1) {
                            parent.layer.msg("操作成功!", {
                                shade : 0.3,
                                time : 1500
                            }, function() {
                                parent.window.location.reload();
                            });
                        } else {
                            layer.alert(result ? (result.data || '保存失败') : '保存失败', {icon: 2});
                        }
                    },
                    error: function(xhr, _, error) {
                        // 清除超时计时器
                        clearTimeout(timeoutTimer);

                        console.error('保存失败:', error);
                        layer.close(loadingIndex);

                        // 尝试解析响应中的错误信息
                        var errorMsg = '网络错误，请重试';
                        try {
                            if (xhr && xhr.responseText) {
                                var response = JSON.parse(xhr.responseText);
                                if (response && response.data) {
                                    errorMsg = response.data;
                                }
                            }
                        } catch (e) {
                            console.error('解析错误响应失败:', e);
                        }

                        // 使用alert而不是msg，确保错误信息更明显
                        layer.alert(errorMsg, {icon: 2});
                    },
                    complete: function() {
                        // 清除超时计时器（以防其他回调未清除）
                        clearTimeout(timeoutTimer);

                        // 确保加载图标一定会关闭
                        layer.close(loadingIndex);
                    }
                });
                return false;
            });
        } catch (error) {
            console.error('初始化模式时发生错误:', error);
            console.log('具体错误:', error.message);
        }
    });
});


// 获取URL参数的辅助函数
function getUrlParam(name) {
    try {
        var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
        var r = window.location.search.substr(1).match(reg);
        if (r != null) {
            var value = decodeURI(r[2]);
            console.log('获取URL参数 ' + name + ' = ' + value);
            return value;
        }
        console.log('未找到URL参数 ' + name);
        return null;
    } catch (error) {
        console.error('获取URL参数时发生错误:', error);
        return null;
    }
}

// 记录当前是否有正在编辑的单行记录
var isEditingSingleRow = false;

// layui.use(['laydate','upload','element','layer','form'], function() {
//     var form = layui.form;
//     var $ = layui.jquery
//         ,upload = layui.upload
//         ,element = layui.element
//         ,layer = layui.layer;
//      var laydate = layui.laydate;

//     form.on('submit(formDemo)', function(data) {
//         var index = layer.load(2, {
//             shade : [ 0.1, '#fff' ]
//         });
//         var actionUrl = $("#submitForm").attr('action');
//         $.ajax({
//             url : actionUrl,
//             type : 'post',
//             data : $('#submitForm').serialize(),
//             success : function(result) {
//                 layer.closeAll();
//                 if (result.code == 1) {
//                     parent.layer.msg("操作成功!", {
//                         shade : 0.3,
//                         time : 1500
//                     }, function() {
//                         parent.window.location.reload();
//                     });
//                 } else {
//                     layer.alert(result.message);
//                 }
//             }
//         });
//         return false;
//     });
// });

function addSubOrder() {
    // 生成当前订单序号（默认1，如果存在其他子订单，则取最大订单序号+1）
    var currentOrderSerialNum = 1;
    if (subOrderList?.length) {
        var maxOrderSerialNum = 1;
        subOrderList.forEach(function(item) {
            var orderSerialNum = item.orderSerialNum;
            // 将字符串转换为数字进行比较
            var orderSerialNumInt = parseInt(orderSerialNum, 10);
            // 确保转换结果是有效数字
            if (!isNaN(orderSerialNumInt) && orderSerialNumInt > maxOrderSerialNum) {
                maxOrderSerialNum = orderSerialNumInt;
            }
        });
        currentOrderSerialNum = maxOrderSerialNum + 1;
    }

    var newRow = '<tr class="sub-order-row">' +
        '<td><input type="text" class="layui-input" name="orderSerialNum" disabled value="' + currentOrderSerialNum + '" placeholder="请输入订单序号"></td>' +
        '<td><input type="text" class="layui-input" name="prepareDate" placeholder="请选择准备日期"></td>' +
        '<td><input type="text" class="layui-input" name="outboundDate" placeholder="请选择出库日期"></td>' +
        '<td><input type="text" class="layui-input" name="arrivalDate" placeholder="请选择到货日期"></td>' +
        '<td><select class="layui-input" id="modelNumber" name="modelNumber" placeholder="请选择型号" lay-filter="modelNumberFn"><option value="">请选择型号</option></select></td>' +
        '<td><input type="text" class="layui-input" id="barcode" name="barcode" placeholder="请输入条码"></td>' +
        '<td><input type="text" class="layui-input" id="size" name="size" disabled></td>' +
        '<td><input type="text" class="layui-input" id="wireSpool" name="wireSpool" disabled></td>' +
        '<td><input type="text" class="layui-input" id="partNumber" name="partNumber" placeholder="请输入部品编号"></td>' +
        '<td><input type="number" class="layui-input" name="orderQuantity" onblur="calculateRemainingQuantity(this)" placeholder="请输入订单数量"></td>' +
        '<td><input type="number" class="layui-input" name="deliveredQuantity" onblur="calculateRemainingQuantity(this)" placeholder="请输入已送数量"></td>' +
        '<td><input type="number" class="layui-input" name="remainingQuantity" disabled ></td>' +
        '<td><input type="text" class="layui-input order-amount-input" name="orderAmount" readonly onclick="openCopperFieldsModal(this)" placeholder="点击设置接单金额" title="点击设置接单金额" style="cursor: pointer; background-color: #f8f8f8;"></td>' +
        '<td><input type="text" class="layui-input" name="detailRemark" placeholder="请输入详情备注"></td>' +
        '<td><button type="button" class="layui-btn layui-btn-danger layui-btn-sm" onclick="removeSubOrder(this, ' + currentOrderSerialNum + ')"><i class="layui-icon layui-icon-delete"></i> 删除</button></td>' +
        '</tr>';
    $("#subOrderTableBody").append(newRow);

    // 初始化新添加行中的日期选择器
    layui.use('laydate', function(){
        var laydate = layui.laydate;
        laydate.render({
            elem: '#subOrderTableBody tr:last-child [name="outboundDate"]'
        });
        laydate.render({
            elem: '#subOrderTableBody tr:last-child [name="arrivalDate"]',
            done: function(value) {
                // 到货日期变更时，尝试查询产品价格
                var customerCode = $('#demandCustomerCode').val();
                var productCode = $('#subOrderTableBody tr:last-child [name="modelNumber"]').val();

                if (customerCode && productCode && value) {
                    console.log('到货日期变更，尝试查询产品价格:', {
                        customerCode: customerCode,
                        productCode: productCode,
                        arrivalDate: value
                    });
                    queryProductPrice(customerCode, productCode, value);
                }
            }
        });
        laydate.render({
            elem: '#subOrderTableBody tr:last-child [name="prepareDate"]'
        });
    });

    // 初始化型号选择框
    var modelNumber = $('#modelNumber');
    modelNumber.empty();
    modelNumber.append('<option value="">请选择型号</option>');
    productCodeList.forEach(function(item) {
        modelNumber.append('<option value="' + item + '">' + item + '</option>');
    });

    // 初始化尺寸输入框
    var size = $('#size');
    size.val('');

    // 初始化线盘输入框
    var wireSpool = $('#wireSpool');
    wireSpool.val('');

    // 重新渲染select
    form.render('select');

    form.on('select(modelNumberFn)', function(data) {
        var size = $('#size');
        var wireSpool = $('#wireSpool');
        var partNumber = $('#partNumber');
        var barcode = $('#barcode');

        size.val('');
        wireSpool.val('');
        partNumber.val('');
        barcode.val('');

        // 获取产品尺寸列表
        $.ajax({
            url: baselocation + '/order/orderEntry/getProductInfo',
            type: 'post',
            data: {productCode: data.value},
            success: function(result) {
                if (result && result.code == 1) {
                    var productInfo = result.data || {};
                    console.log('productInfo productInfo', productInfo);

                    size.val(productInfo?.labelSizeName || '');
                    wireSpool.val(productInfo?.wireReelName || '');
                    partNumber.val(productInfo?.partNo || '');
                    barcode.val(productInfo?.barCode?.trim() || '');
                }
            }
        });

        // 产品代码变更时，尝试查询产品价格
        var customerCode = $('#demandCustomerCode').val();
        var arrivalDate = $('#subOrderTableBody tr:last-child [name="arrivalDate"]').val();

        if (customerCode && arrivalDate && data.value) {
            console.log('产品代码变更，尝试查询产品价格:', {
                customerCode: customerCode,
                productCode: data.value,
                arrivalDate: arrivalDate
            });
            queryProductPrice(customerCode, data.value, arrivalDate);
        }
    });
}

// 清理客户订单号，处理复制粘贴可能带来的特殊字符
function cleanCustomerOrderNo(input) {
    var value = input.value;

    // 移除不可见字符、换行符、制表符等
    value = value.replace(/[\r\n\t\u00A0\u2000-\u200B\u2028\u2029\uFEFF]/g, '');

    // 移除前后空格
    value = value.trim();

    // 移除多余的空格（将多个连续空格替换为单个空格）
    value = value.replace(/\s+/g, ' ');

    // 更新输入框的值
    if (input.value !== value) {
        input.value = value;
    }
}

function checkCustomerOrderNo() {
    // 先清理客户订单号
    var customerOrderNoInput = document.getElementById('customerOrderNo');
    if (customerOrderNoInput) {
        cleanCustomerOrderNo(customerOrderNoInput);
    }

    // 客户订单号改为非必填，移除验证逻辑
    $('#customerOrderNo').removeClass('layui-form-danger');

    // 从主表单中获取订单主信息
    var customerOrderNo = $('#customerOrderNo').val();
    var rowData = {
        customerOrderNo: customerOrderNo ? customerOrderNo.trim() : '',
        orderNo: orderNo,
        demandCustomerCode: $('#demandCustomerCode').val(),
        contractPartyCustomerCode: $('#contractPartyCustomerCode').val(),
        orderType: $('#orderType').val(),
        attachmentId: currentAttachmentId
    };

    $.ajax({
        url: baselocation + '/order/orderEntry/saveSingleRow',
        type: 'post',
        data: rowData,
        success: function(result) {
            if (!result || result.code != 1) {
                // 使用layer.alert显示错误信息，而不是layer.msg，这样错误信息更加明显
                layer.alert(result ? (result.data || '保存失败') : '保存失败', {icon: 2});
            } else {
                orderNo = result.data;
            }
        }
    });
}

// 单行添加
function addSingleSubOrder() {
    var demandCustomerCode = $('#demandCustomerCode').val();
    if (!demandCustomerCode) {
        layer.msg('请先选择需求方', {icon: 2});
        return;
    }

    var contractPartyCustomerCode = $('#contractPartyCustomerCode').val();
    if (!contractPartyCustomerCode) {
        layer.msg('请先选择合同方', {icon: 2});
        return;
    }

    var orderType = $('#orderType').val();
    if (!orderType) {
        layer.msg('请先选择订单种类', {icon: 2});
        return;
    }
    
    // 检查是否已有正在编辑的单行
    if (isEditingSingleRow) {
        layer.msg('请先保存当前正在编辑的行', {icon: 2});
        return;
    }

    isEditingSingleRow = true;

    // 生成当前订单序号（默认1，如果存在其他子订单，则取最大订单序号+1）
    var currentOrderSerialNum = 1;
    if (subOrderList?.length) {
        var maxOrderSerialNum = 1;
        subOrderList.forEach(function(item) {
            var orderSerialNum = item.orderSerialNum;
            // 将字符串转换为数字进行比较
            var orderSerialNumInt = parseInt(orderSerialNum, 10);
            // 确保转换结果是有效数字
            if (!isNaN(orderSerialNumInt) && orderSerialNumInt > maxOrderSerialNum) {
                maxOrderSerialNum = orderSerialNumInt;
            }
        });
        currentOrderSerialNum = maxOrderSerialNum + 1;
    }

    var newRow = '<tr class="single-edit-row">' +
        '<td><input type="text" class="layui-input" name="orderSerialNum" disabled value="' + currentOrderSerialNum + '"></td>' +
        '<td><input type="text" class="layui-input" name="prepareDate" placeholder="请选择准备日期"></td>' +
        '<td><input type="text" class="layui-input" name="outboundDate" placeholder="请选择出库日期"></td>' +
        '<td><input type="text" class="layui-input" name="arrivalDate" placeholder="请选择到货日期"></td>' +
        '<td><select class="layui-input" id="modelNumber" name="modelNumber" placeholder="请选择型号" lay-search lay-filter="modelNumberFn"><option value="">请选择型号</option></select></td>' +
        '<td><input type="text" class="layui-input" id="barcode" name="barcode" disabled></td>' +
        '<td><input type="text" class="layui-input" id="size" name="size" disabled></td>' +
        '<td><input type="text" class="layui-input" id="wireSpool" name="wireSpool" disabled></td>' +
        '<td><input type="text" class="layui-input" id="partNumber" name="partNumber" disabled></td>' +
        '<td><input type="number" class="layui-input" name="orderQuantity" onblur="calculateRemainingQuantity(this)" placeholder="请输入订单数量"></td>' +
        '<td><input type="number" class="layui-input" name="deliveredQuantity" onblur="calculateRemainingQuantity(this)" placeholder="请输入已送数量"></td>' +
        '<td><input type="number" class="layui-input" name="remainingQuantity" disabled ></td>' +
        '<td><input type="text" class="layui-input order-amount-input" name="orderAmount" readonly onclick="openCopperFieldsModal(this)" placeholder="点击设置接单金额" title="点击设置接单金额" style="cursor: pointer; background-color: #f8f8f8;"></td>' +
        '<td><input type="text" class="layui-input" name="detailRemark" placeholder="请输入详情备注"></td>' +
        '<td class="single-row-operation">' +
        '<div class="layui-btn-group" style="margin-top:5px;">' +
        '<button type="button" class="layui-btn layui-btn-primary layui-btn-sm quick-save-btn" title="点击整行快速保存"><i class="layui-icon layui-icon-ok"></i></button>' +
        '<button type="button" class="layui-btn layui-btn-primary layui-btn-sm cancel-row-btn" title="取消编辑"><i class="layui-icon layui-icon-close"></i></button>' +
        '</div>' +
        '</td>' +
        '</tr>';
    $("#subOrderTableBody").prepend(newRow);

    // 初始化新添加行中的日期选择器
    layui.use('laydate', function(){
        var laydate = layui.laydate;
        laydate.render({
            elem: '#subOrderTableBody tr:first-child [name="outboundDate"]'
        });
        laydate.render({
            elem: '#subOrderTableBody tr:first-child [name="arrivalDate"]',
            done: function(value) {
                // 到货日期变更时，尝试查询产品价格（单行录入模式）
                var customerCode = $('#demandCustomerCode').val();
                var productCode = $('#subOrderTableBody tr:first-child [name="modelNumber"]').val();

                if (customerCode && productCode && value) {
                    console.log('单行录入到货日期变更，尝试查询产品价格:', {
                        customerCode: customerCode,
                        productCode: productCode,
                        arrivalDate: value
                    });
                    queryProductPrice(customerCode, productCode, value);
                }
            }
        });
        laydate.render({
            elem: '#subOrderTableBody tr:first-child [name="prepareDate"]'
        });
    });

    // 初始化型号选择框
    var modelNumber = $('#modelNumber');
    modelNumber.empty();
    modelNumber.append('<option value="">请选择型号</option>');
    productCodeList.forEach(function(item) {
        modelNumber.append('<option value="' + item + '">' + item + '</option>');
    });

    // 初始化尺寸输入框
    var size = $('#size');
    size.val('');

    // 初始化线盘输入框
    var wireSpool = $('#wireSpool');
    wireSpool.val('');

    // 重新渲染select
    form.render('select');

    form.on('select(modelNumberFn)', function(data) {
        var size = $('#size');
        var wireSpool = $('#wireSpool');
        var partNumber = $('#partNumber');
        var barcode = $('#barcode');

        size.val('');
        wireSpool.val('');
        partNumber.val('');
        barcode.val('');

        // 获取产品尺寸列表
        $.ajax({
            url: baselocation + '/order/orderEntry/getProductInfo',
            type: 'post',
            data: {productCode: data.value},
            success: function(result) {
                if (result && result.code == 1) {
                    var productInfo = result.data || {};
                    console.log('productInfo productInfo', productInfo);

                    size.val(productInfo?.labelSizeName || '');
                    wireSpool.val(productInfo?.wireReelName || '');
                    partNumber.val(productInfo?.partNo || '');
                    barcode.val(productInfo?.barCode?.trim() || '');
                }
            }
        });

        // 产品代码变更时，尝试查询产品价格（单行录入模式）
        var customerCode = $('#demandCustomerCode').val();
        var arrivalDate = $('#subOrderTableBody tr:first-child [name="arrivalDate"]').val();

        if (customerCode && arrivalDate && data.value) {
            console.log('单行录入产品代码变更，尝试查询产品价格:', {
                customerCode: customerCode,
                productCode: data.value,
                arrivalDate: arrivalDate
            });
            queryProductPrice(customerCode, data.value, arrivalDate);
        }
    });

    // 为保存按钮添加点击事件
    $('.save-row-btn').unbind('click').click(function(e) {
        e.stopPropagation(); // 阻止事件冒泡
        saveSingleRowData();
    });

    // 为快速保存按钮添加点击事件
    $('.quick-save-btn').unbind('click').click(function(e) {
        e.stopPropagation(); // 阻止事件冒泡
        saveSingleRowData();
    });

    // 为取消按钮添加点击事件
    $('.cancel-row-btn').unbind('click').click(function(e) {
        e.stopPropagation(); // 阻止事件冒泡
        cancelSingleRowEdit();
    });

    // 为整行添加点击事件
    $('.single-edit-row').unbind('click').click(function(e) {
        // 如果点击的是输入框或按钮则不触发行点击事件
        if ($(e.target).is('input') || $(e.target).is('button') || $(e.target).is('i')) {
            return;
        }

        // 添加动画效果
        var $td = $(e.target).closest('td');
        if ($td.length) {
            $td.css('background-color', '#c4eaf3');
            setTimeout(function() {
                $td.css('background-color', '');
            }, 300);
        }

        // 立即保存，无需确认对话框
        // saveSingleRowData();
    });

    // 显示底部保存和取消按钮区域
    $("#singleRowButtonArea").show();

    // 自动聚焦第一个输入框
    // $('#subOrderTableBody tr:first-child [name="outboundDate"]').focus();
}

// 计算剩余数量
function calculateRemainingQuantity(input) {
    var orderQuantity = parseFloat($(input).closest('tr').find('[name="orderQuantity"]').val()) || 0;
    var deliveredQuantity = parseFloat($(input).closest('tr').find('[name="deliveredQuantity"]').val()) || 0;
    var remainingQuantity = orderQuantity - deliveredQuantity;
    $(input).closest('tr').find('[name="remainingQuantity"]').val(remainingQuantity);
}

// 保存单行数据（底部保存按钮）
var orderNo = null;
function saveSingleRowData() {
    if (!isEditingSingleRow) {
        layer.msg('没有可以保存的数据行', {icon: 2});
        return;
    }

    var $row = $("#subOrderTableBody tr.single-edit-row");
    var rowData = {
        orderSerialNum: $row.find('[name="orderSerialNum"]').val(),
        outboundDate: $row.find('[name="outboundDate"]').val(),
        arrivalDate: $row.find('[name="arrivalDate"]').val(),
        prepareDate: $row.find('[name="prepareDate"]').val(),
        modelNumber: $row.find('[name="modelNumber"]').val(),
        size: $row.find('[name="size"]').val(),
        wireSpool: $row.find('[name="wireSpool"]').val(),
        partNumber: $row.find('[name="partNumber"]').val(),
        orderQuantity: $row.find('[name="orderQuantity"]').val() || 0,
        deliveredQuantity: $row.find('[name="deliveredQuantity"]').val() || 0,
        remainingQuantity: $row.find('[name="remainingQuantity"]').val() || 0,
        detailRemark: $row.find('[name="detailRemark"]').val(),
        barcode: $row.find('[name="barcode"]').val(),
        // 添加铜字段数据
        copperContractType: $row.data('copperContractType') || '',
        copperContractNo: $row.data('copperContractNo') || '',
        copperCondition: $row.data('copperCondition') || '',
        copperCurrency: $row.data('copperCurrency') || '',
        conversionRate: $row.data('conversionRate') || '',
        copperBase: $row.data('copperBase') || '',
        premium: $row.data('premium') || '',
        convertedCopperPrice: $row.data('convertedCopperPrice') || '',
        zeroBase: $row.data('zeroBase') || '',
        orderUnitPrice: $row.data('orderUnitPrice') || '',
        orderAmount: $row.data('orderAmount') || ''
    };

    // 必填字段验证
    var missingFields = [];

    // 客户订单号改为非必填，只需要清理即可
    var customerOrderNoInput = document.getElementById('customerOrderNo');
    if (customerOrderNoInput) {
        cleanCustomerOrderNo(customerOrderNoInput);
        $('#customerOrderNo').removeClass('layui-form-danger');
    }

    // // 验证出库日期
    // if (!rowData.outboundDate) {
    //     missingFields.push('出库日期');
    //     $row.find('[name="outboundDate"]').addClass('layui-form-danger');
    // } else {
    //     $row.find('[name="outboundDate"]').removeClass('layui-form-danger');
    // }

    // // 验证到货日期
    // if (!rowData.arrivalDate) {
    //     missingFields.push('到货日期');
    //     $row.find('[name="arrivalDate"]').addClass('layui-form-danger');
    // } else {
    //     $row.find('[name="arrivalDate"]').removeClass('layui-form-danger');
    // }

    // 如果有必填字段未填写，显示错误提示并返回
    if (missingFields.length > 0) {
        layer.msg('请填写必填字段：' + missingFields.join('、'), {icon: 2});
        return;
    }

    subOrderList.push(rowData);

    // 显示加载中提示
    var loadingIndex = layer.load(1, {
        shade: [0.3, '#fff']
    });

    // 设置超时处理，防止一直转圈
    var timeoutTimer = setTimeout(function() {
        layer.close(loadingIndex);
        layer.msg('请求超时，请重试', {icon: 2});
    }, 10000); // 10秒超时

    // 从主表单中获取订单主信息，并清理客户订单号
    var customerOrderNoInput = document.getElementById('customerOrderNo');
    if (customerOrderNoInput) {
        cleanCustomerOrderNo(customerOrderNoInput);
    }

    var customerOrderNo = $('#customerOrderNo').val();
    rowData.customerOrderNo = customerOrderNo ? customerOrderNo.trim() : '';
    rowData.orderNo = orderNo;
    rowData.demandCustomerCode = $('#demandCustomerCode').val();
    rowData.contractPartyCustomerCode = $('#contractPartyCustomerCode').val();
    rowData.orderType = $('#orderType').val();
    rowData.attachmentId = currentAttachmentId;

    // 添加日志输出，检查orderNo是否正确设置
    console.log('保存明细记录，当前订单号：', orderNo);
    console.log('发送到后端的数据：', rowData);

    $.ajax({
        url: baselocation + '/order/orderEntry/saveSingleRow',
        type: 'post',
        data: rowData,
        success: function(result) {
            // 清除超时计时器
            clearTimeout(timeoutTimer);

            // 关闭加载提示
            layer.close(loadingIndex);

            if (result && result.code == 1) {
                // 保存返回的订单号，用于后续添加明细
                orderNo = result.data;
                console.log('保存成功，获取到订单号：', orderNo);

                // 更新行内容为只读状态
                var orderAmount = $row.data('orderAmount') || '';
                var readOnlyHtml =
                    '<td>' + (rowData.orderSerialNum || '') + '</td>' +
                    '<td>' + (rowData.prepareDate || '') + '</td>' +
                    '<td>' + (rowData.outboundDate || '') + '</td>' +
                    '<td>' + (rowData.arrivalDate || '') + '</td>' +
                    '<td>' + (rowData.modelNumber || '') + '</td>' +
                    '<td>' + (rowData.barcode || '') + '</td>' +
                    '<td>' + (rowData.size || '') + '</td>' +
                    '<td>' + (rowData.wireSpool || '') + '</td>' +
                    '<td>' + (rowData.partNumber || '') + '</td>' +
                    '<td>' + (rowData.orderQuantity || '') + '</td>' +
                    '<td>' + (rowData.deliveredQuantity || '') + '</td>' +
                    '<td>' + (rowData.remainingQuantity || '') + '</td>' +
                    '<td><input type="text" class="layui-input order-amount-input" readonly onclick="openCopperFieldsModal(this)" value="' + orderAmount + '" placeholder="点击设置接单金额" title="点击设置接单金额" style="cursor: pointer; background-color: #f8f8f8;"></td>' +
                    '<td>' + (rowData.detailRemark || '') + '</td>' +
                    '<td><button type="button" class="layui-btn layui-btn-danger layui-btn-sm" onclick="removeSubOrder(this, \'' + rowData.orderSerialNum + '\')">删除</button></td>';

                $row.html(readOnlyHtml);
                $row.removeClass('single-edit-row');
                isEditingSingleRow = false;
                layer.msg('保存成功', {icon: 1});

                // 禁用需求方选择框（因为已增加的详情信息已经有关联的数据保存在数据库中,所以不能修改）
                $('#demandCustomerCode').prop('disabled', true);

                // 自动添加新的输入行
                addSingleSubOrder();
            } else {
                // 使用layer.alert显示错误信息，而不是layer.msg，这样错误信息更加明显
                layer.alert(result ? (result.data || '保存失败') : '保存失败', {icon: 2});
            }
        },
        error: function(xhr, _, error) {
            // 清除超时计时器
            clearTimeout(timeoutTimer);

            console.error('保存失败:', error);
            layer.close(loadingIndex);

            // 尝试解析响应中的错误信息
            var errorMsg = '网络错误，请重试';
            try {
                if (xhr && xhr.responseText) {
                    var response = JSON.parse(xhr.responseText);
                    if (response && response.data) {
                        errorMsg = response.data;
                    }
                }
            } catch (e) {
                console.error('解析错误响应失败:', e);
            }

            // 使用alert而不是msg，确保错误信息更明显
            layer.alert(errorMsg, {icon: 2});
        },
        complete: function() {
            // 清除超时计时器（以防其他回调未清除）
            clearTimeout(timeoutTimer);

            // 确保加载图标一定会关闭
            layer.close(loadingIndex);
        }
    });
}

// 取消单行编辑（底部取消按钮）
function cancelSingleRowEdit() {
    if (isEditingSingleRow) {
        $("#subOrderTableBody tr.single-edit-row").remove();
        isEditingSingleRow = false;

        // 如果表格中没有其他行，隐藏底部按钮区域
        if ($("#subOrderTableBody tr").length === 0) {
            $("#singleRowButtonArea").hide();
        }
    }
}

// 原有的单行保存和取消函数保留但不再使用
function saveSingleSubOrder(button) {
    saveSingleRowData();
}

function removeSingleSubOrder(button) {
    cancelSingleRowEdit();
}

// 删除子订单
function removeSubOrder(button, orderSerialNum) {
    layer.confirm('确定要删除这一行吗？', {icon: 3, title:'提示'}, function(index){
        $.ajax({
            url: baselocation + '/order/orderEntry/removeSubOrder',
            type: 'post',
            data: {orderNo: orderNo, orderSerialNum: orderSerialNum},
            success: function(result) {
                if (result && result.code == 1) {
                    layer.msg(result.data || '删除成功', {icon: 1});
                    $(button).closest('tr').remove();

                    subOrderList = subOrderList.filter(item => item.orderSerialNum != orderSerialNum);

                    if (subOrderList.length === 0) {
                        $('#subOrderTableBody').empty();
                        $('#singleRowButtonArea').hide();

                        // 启用需求方选择框
                        $('#demandCustomerCode').prop('disabled', false);
                    }
                } else {
                    layer.msg(result ? (result.data || '删除失败') : '删除失败', {icon: 2});
                }
            },
            error: function(xhr, _, error) {
                layer.msg(errorMsg, {icon: 2});
            }
        });
    });
}

function closeAll() {
    parent.layer.closeAll();
}

// 添加播放成功声音的函数
function playSuccessSound() {
    try {
        // 尝试创建并播放一个简短的提示音
        var audioContext = new (window.AudioContext || window.webkitAudioContext)();
        var oscillator = audioContext.createOscillator();
        var gainNode = audioContext.createGain();

        oscillator.connect(gainNode);
        gainNode.connect(audioContext.destination);

        oscillator.type = 'sine';
        oscillator.frequency.value = 1046.5; // C6
        gainNode.gain.value = 0.3;

        oscillator.start(audioContext.currentTime);
        oscillator.stop(audioContext.currentTime + 0.1);
    } catch (e) {
        console.log('浏览器不支持音频API');
    }
}

// 添加表格放大缩小和滚动条功能
$(document).ready(function() {
    var mode = getUrlParam('mode') || 'batch';
    if (mode === 'single') {
        initTableControls();
    }
});

// 初始化表格控制功能
function initTableControls() {
    // 放大按钮点击事件
    $('#fullscreenButton').on('click', function() {
        enterFullscreenMode();
    });

    // 缩小按钮点击事件
    $('#exitFullscreenButton').on('click', function() {
        exitFullscreenMode();
    });

    // ESC键退出全屏
    $(document).on('keydown', function(e) {
        if (e.key === 'Escape' && $('#subOrderContainer').hasClass('table-fullscreen')) {
            exitFullscreenMode();
        }
    });
}

// 进入全屏模式
function enterFullscreenMode() {
    var $container = $('#subOrderContainer');
    var mode = getUrlParam('mode') || 'batch';

    // 保存当前滚动位置（只有在单行模式下才需要）
    var scrollTop = 0;
    if (mode === 'single') {
        scrollTop = $('.table-scroll-container').scrollTop();
    }

    // 添加全屏类
    $container.addClass('table-fullscreen');

    // 根据模式添加类名
    if (mode === 'single') {
        $('.table-scroll-container').addClass('single-mode');
    }

    // 切换按钮显示
    $('#fullscreenButton').hide();
    $('#exitFullscreenButton').show();

    // 恢复滚动位置（只有在单行模式下才需要）
    if (mode === 'single') {
        setTimeout(function() {
            $('.table-scroll-container').scrollTop(scrollTop);
        }, 100);
    }

    // 显示全屏模式提示
    layer.msg('已进入全屏模式，按ESC键或点击右上角缩小按钮退出', {time: 3000});
}

// 退出全屏模式
function exitFullscreenMode() {
    var $container = $('#subOrderContainer');
    var mode = getUrlParam('mode') || 'batch';

    // 保存当前滚动位置（只有在单行模式下才需要）
    var scrollTop = 0;
    if (mode === 'single') {
        scrollTop = $('.table-scroll-container').scrollTop();
    }

    // 移除全屏类
    $container.removeClass('table-fullscreen');

    // 移除模式类名
    $('.table-scroll-container').removeClass('single-mode');

    // 切换按钮显示
    $('#exitFullscreenButton').hide();
    $('#fullscreenButton').show();

    // 恢复滚动位置（只有在单行模式下才需要）
    if (mode === 'single') {
        setTimeout(function() {
            $('.table-scroll-container').scrollTop(scrollTop);
        }, 100);
    }
}

// ============================= 附件上传相关功能 =============================

// 全局变量：记录是否已上传附件
var attachmentUploaded = false;
// 全局变量：当前已上传的附件ID
var currentAttachmentId = null;

// 初始化附件上传功能
function initAttachmentUpload() {
    try {
        layui.use('upload', function() {
            var upload = layui.upload;
            var layer = layui.layer;

            // 初始化删除按钮点击事件
            $('#removeAttachment').on('click', function(e) {
                e.stopPropagation(); // 阻止事件冒泡
                if (currentAttachmentId) {
                    removeAttachment(currentAttachmentId);
                }
            });

            // 渲染上传组件
            var uploadInst = upload.render({
                elem: '#attachmentUpload',
                url: baselocation + '/common/upload/file', // 替换为实际的上传接口
                accept: 'file', // 允许上传所有类型文件
                exts: 'jpg|jpeg|png|gif|doc|docx|pdf|xls|xlsx|csv|txt', // 限制文件类型
                size: 10240, // 限制文件大小(KB)
                auto: true, // 自动上传
                multiple: false, // 单个文件上传
                choose: function(obj) {
                    // 新增：选择文件时立即更新文件名和类型
                    try {
                        // 重置变量，确保每次都是最新的
                        currentOriginalFileName = '';
                        currentFileType = '';

                        // 获取选择的文件对象
                        obj.preview(function(index, file, result) {
                            console.log('选择了新文件:', file.name);
                            // 立即更新文件名和类型
                            currentOriginalFileName = file.name || '未知文件';
                            currentFileType = getFileType(currentOriginalFileName);

                            // 关键修复：立即更新当前文件对象引用
                            window.lastUploadedFile = file;
                            // 保存到全局变量中，确保多处可用
                            window.currentFileObject = file;

                            console.log('更新文件名为:', currentOriginalFileName);
                            console.log('更新文件类型为:', currentFileType);
                            console.log('文件对象已更新');
                        });
                    } catch (e) {
                        console.error('选择文件时更新文件名出错:', e);
                    }
                },
                before: function(obj) {
                    // 修复: 使用更安全的方式获取文件名
                    try {
                        // 获取选择的文件对象
                        var files = document.getElementById('attachmentUpload').files;
                        if (files && files.length > 0) {
                            // 强制更新，确保是最新的文件名
                            currentOriginalFileName = files[0].name || '未知文件';
                            // 保存文件对象供本地预览使用
                            window.lastUploadedFile = files[0];
                            // 再次确保全局变量一致
                            window.currentFileObject = files[0];
                            // 更新文件类型
                            currentFileType = getFileType(currentOriginalFileName);
                            console.log('上传前更新文件名:', currentOriginalFileName);
                            console.log('上传前更新文件类型:', currentFileType);
                        } else {
                            // 备用方案：尝试从obj中获取文件信息
                            if (obj && obj.pushFile) {
                                var pushFile = obj.pushFile();
                                var fileId = Object.keys(pushFile)[0];
                                var file = pushFile[fileId];
                                if (file && file.name) {
                                    currentOriginalFileName = file.name;
                                    // 保存文件对象供本地预览使用
                                    window.lastUploadedFile = file;
                                    // 再次确保全局变量一致
                                    window.currentFileObject = file;
                                    // 更新文件类型
                                    currentFileType = getFileType(currentOriginalFileName);
                                    console.log('备用方案更新文件名:', currentOriginalFileName);
                                }
                            } else {
                                currentOriginalFileName = '文件_' + new Date().getTime();
                                currentFileType = 'unknown';
                            }
                        }
                    } catch (e) {
                        console.error('获取文件名出错:', e);
                        currentOriginalFileName = '文件_' + new Date().getTime();
                        currentFileType = 'unknown';
                    }

                    // 显示上传中的loading
                    layer.load();
                },
                done: function(res, index, upload) {
                    layer.closeAll('loading'); // 关闭loading

                    // 检查响应是否有效
                    if (!res) {
                        console.error('上传响应为空');
                        useDefaultResponse();
                        return;
                    }

                    // 注意：这里处理接口404的情况
                    if (res.code === undefined) {
                        console.log('接口未就绪，使用模拟数据');
                        useDefaultResponse();
                        return;
                    }

                    // 检查响应数据
                    if (res.code == 1) {
                        // 确保res.data存在
                        if (!res.data) {
                            console.warn('上传成功但返回数据为空，使用默认数据');
                            res.data = {
                                id: "temp_" + new Date().getTime(),
                                fileName: currentOriginalFileName || '未知文件',
                                size: window.currentFileObject ? window.currentFileObject.size : (window.lastUploadedFile ? window.lastUploadedFile.size : (1024 * 100))
                            };
                        }

                        // 修复：始终使用当前选择的文件名，覆盖服务器返回的文件名
                        // 这确保了无论上传什么类型的文件，文件名都会正确更新
                        res.data.fileName = currentOriginalFileName || res.data.fileName || '未知文件';

                        // 将当前文件名和类型添加到响应数据中，确保一致性
                        res.data.originalFileName = currentOriginalFileName;
                        res.data.fileType = currentFileType;

                        // 上传成功
                        attachmentUploaded = true;
                        currentAttachmentId = res.data.id || ("temp_" + new Date().getTime());

                        // 重要：添加文件对象到返回数据中，确保预览时能访问到最新上传的文件
                        // 优先使用刚刚选择的文件对象
                        res.data.fileObject = window.currentFileObject || window.lastUploadedFile;

                        // 更新附件信息显示
                        updateAttachmentDisplay(res.data);

                        // layer.msg('附件上传成功', {icon: 1});

                        // 如果使用的是本地预览，立即显示预览
                        if (window.currentFileObject || window.lastUploadedFile) {
                            showAttachmentPreview(res.data);
                        }
                    } else {
                        // 上传失败
                        layer.msg('附件上传失败：' + (res.message || '未知错误'), {icon: 2});
                        console.error('上传失败:', res);
                    }

                    // 内部函数：使用默认响应数据
                    function useDefaultResponse() {
                        var defaultRes = {
                            code: 1,
                            message: "上传成功(模拟)",
                            data: {
                                id: "temp_" + new Date().getTime(),
                                // 修复：确保使用当前选择的文件名
                                fileName: currentOriginalFileName || '未知文件',
                                size: window.currentFileObject ? window.currentFileObject.size : (window.lastUploadedFile ? window.lastUploadedFile.size : (1024 * 100)),
                                fileUrl: '',
                                fileObject: window.currentFileObject || window.lastUploadedFile,  // 重要：保存文件对象到响应数据中
                                originalFileName: currentOriginalFileName,
                                fileType: currentFileType
                            }
                        };

                        // 上传成功
                        attachmentUploaded = true;
                        currentAttachmentId = defaultRes.data.id;

                        // 更新附件信息显示
                        updateAttachmentDisplay(defaultRes.data);

                        // 不在这里显示消息，避免重复提示
                        // layer.msg('附件上传成功(模拟数据)', {icon: 1});

                        // 如果使用的是本地预览，立即显示预览
                        if (window.currentFileObject || window.lastUploadedFile) {
                            showAttachmentPreview(defaultRes.data);
                        }
                    }
                },
                error: function() {
                    layer.closeAll('loading'); // 关闭loading
                    console.log('上传接口错误，使用模拟数据');

                    // 模拟上传成功的响应
                    var fakeRes = {
                        code: 1,
                        message: "上传成功(模拟)",
                        data: {
                            id: "temp_" + new Date().getTime(),
                            // 修复：确保使用当前选择的文件名
                            fileName: currentOriginalFileName || '未知文件',
                            size: window.currentFileObject ? window.currentFileObject.size : (window.lastUploadedFile ? window.lastUploadedFile.size : (1024 * 100)),
                            fileUrl: '',
                            fileObject: window.currentFileObject || window.lastUploadedFile,  // 重要：使用最新的文件对象
                            originalFileName: currentOriginalFileName,
                            fileType: currentFileType
                        }
                    };

                    // 上传成功
                    attachmentUploaded = true;
                    currentAttachmentId = fakeRes.data.id;

                    // 更新附件信息显示
                    updateAttachmentDisplay(fakeRes.data);

                    // 不在这里显示消息，避免重复提示
                    // layer.msg('附件上传成功(模拟数据)', {icon: 1});

                    // 如果使用的是本地预览，立即显示预览
                    if (window.currentFileObject || window.lastUploadedFile) {
                        showAttachmentPreview(fakeRes.data);
                    }
                }
            });
        });
    } catch (error) {
        console.error('初始化附件上传功能出错:', error);
        layer.msg('初始化上传功能失败，请刷新页面重试', {icon: 2});
    }
}

function selectAttachment() {
    // 打开附件选择弹框
    layer.open({
        type: 1,
        title: '',
        area: ['500px', '350px'],
        content: '<div class="layui-form" style="padding: 0; height: 100%; width: 100%;">' +
                 '<div class="layui-form-item" style="margin: 0; height: 100%;">' +
                 '<div class="layui-input-block" style="margin: 0; height: 100%; width: 100%;">' +
                 '<div id="attachmentList" style="height: 100%; width: 100%;">' +
                 '<table class="layui-table" style="margin: 0; width: 100%;">' +
                 '<thead><tr><th width="15%">选择</th><th width="85%">附件名称</th></tr></thead>' +
                 '<tbody id="attachmentListBody">' +
                 '</tbody>' +
                 '</table>' +
                 '</div>' +
                 '</div>' +
                 '</div>' +
                 '</div>',
        success: function(layero, index) {
            // 加载示例数据
            loadAttachmentList();
            
            // 渲染表单元素
            layui.form.render();
        },
        btn: ['确定', '取消'],
        yes: function(index, layero) {
            // 获取选中的附件
            var selectedAttachmentId = $('input[name="attachment"]:checked').val();
            if (selectedAttachmentId) {
                // 获取选中附件的信息
                var selectedFileName = $('input[name="attachment"]:checked').data('filename');
                
                // 构造文件数据
                var fileData = {
                    id: selectedAttachmentId,
                    fileName: selectedFileName,
                    originalFileName: selectedFileName,
                    fileType: getFileType(selectedFileName),
                    size: 1024 * 100,
                    fileUrl: ''
                };
                
                // 更新当前附件ID
                currentAttachmentId = selectedAttachmentId;
                // 标记已上传附件
                attachmentUploaded = true;
                // 更新附件显示
                updateAttachmentDisplay(fileData);
                
                layer.close(index);
            } else {
                layer.msg('请选择一个附件', {icon: 2});
            }
        }
    });
}

// 加载附件列表
function loadAttachmentList() {
    // 示例数据
    var exampleAttachments = [
        {id: 'attach_001', fileName: '订单导入模板.xlsx'},
        {id: 'attach_002', fileName: '客户订单20231001.xlsx'},
        {id: 'attach_003', fileName: '产品清单.pdf'},
        {id: 'attach_004', fileName: '客户需求说明.docx'},
        {id: 'attach_005', fileName: '订单变更申请.pdf'},
        {id: 'attach_006', fileName: '产品规格书.pdf'},
        {id: 'attach_007', fileName: '价格表.xlsx'},
        {id: 'attach_008', fileName: '客户反馈.docx'},
        {id: 'attach_009', fileName: '生产计划.xlsx'},
        {id: 'attach_010', fileName: '质检报告.pdf'}
    ];
    
    var html = '';
    for (var i = 0; i < exampleAttachments.length; i++) {
        var attachment = exampleAttachments[i];
        html += '<tr>' +
                '<td><input type="radio" name="attachment" value="' + attachment.id + '" data-filename="' + attachment.fileName + '" title="" lay-skin="primary"></td>' +
                '<td>' + attachment.fileName + '</td>' +
                '</tr>';
    }
    
    $('#attachmentListBody').html(html);
    // 重新渲染表单
    layui.form.render('radio');
}

// 全局变量，保存原始文件名和文件类型
var currentOriginalFileName = '';
var currentFileType = '';

// 根据文件名判断文件类型
function getFileType(fileName) {
    if (!fileName) return 'unknown';

    fileName = fileName.toLowerCase();

    if (fileName.endsWith('.pdf')) {
        return 'pdf';
    } else if (fileName.endsWith('.xls') || fileName.endsWith('.xlsx') || fileName.endsWith('.csv')) {
        return 'excel';
    } else if (fileName.endsWith('.jpg') || fileName.endsWith('.jpeg') || fileName.endsWith('.png') || fileName.endsWith('.gif')) {
        return 'image';
    } else {
        return 'other';
    }
}

// 更新附件信息显示
function updateAttachmentDisplay(fileData) {
    if (!fileData) {
        console.error('更新附件显示失败：fileData为空');
        return;
    }

    // 显示文件信息 - 修复：使用与预览区域相同的文件名获取逻辑，确保一致性
    var fileName = fileData.fileName || fileData.originalFileName || currentOriginalFileName || '未知文件';
    $('#attachmentFileName').text(fileName);
    $('#attachmentId').val(fileData.id || '');
    $('#attachmentInfo').show();

    // 更新提示
    $('#attachmentTip').html('<span style="color: #5FB878;">附件已上传，可以进行表格数据录入</span>');

    // 显示预览（如果是图片）
    showAttachmentPreview(fileData);

    // 重要：启用表格操作，允许用户开始录入数据
    enableTableOperations();
}

// 显示附件预览
function showAttachmentPreview(fileData) {
    var mode = getUrlParam('mode') || 'batch';
    if (mode !== 'single') return;

    var $preview = $('#attachmentPreview').find('div');
    if (!$preview.length) {
        console.error('找不到预览容器元素');
        return;
    }

    $preview.empty();

    if (!fileData) {
        console.error('文件数据为空，无法预览');
        $preview.html('<div class="error-message">文件数据错误，无法预览</div>');
        $('#attachmentPreview').show();
        return;
    }

    // 优先使用fileData中的文件名，其次使用全局变量
    var fileName = fileData.fileName || fileData.originalFileName || currentOriginalFileName || '未知文件';
    console.log('预览文件名:', fileName);

    try {
        // 直接添加预览内容容器，不再单独显示文件名
        $preview.append('<div class="layui-card" style="margin-bottom: 0;">' +
            '<div class="layui-card-body" id="previewContent" style="padding: 5px;"></div>' +
            '</div>');

        var $previewContent = $('#previewContent');
        if (!$previewContent.length) {
            throw new Error('预览内容容器创建失败');
        }

        // 优先使用传入数据中的文件对象，其次使用全局专门存放的当前文件对象，最后使用lastUploadedFile
        var fileToPreview = fileData.fileObject || window.currentFileObject || window.lastUploadedFile;

        console.log('预览使用的文件对象:', fileToPreview ? (fileToPreview.name || '未命名文件') : '无文件对象');

        if (fileToPreview) {
            // 记录正在预览的文件名，用于调试
            console.log('正在预览文件:', fileName);
            // 优先使用fileData中的文件类型，其次使用当前文件类型，最后从文件名推断
            var fileType = fileData.fileType || currentFileType || getFileType(fileName);
            console.log('预览文件类型:', fileType);
            previewLocalFile(fileToPreview, fileType, $previewContent);
        } else {
            // 如果没有本地文件对象引用，显示紧凑的提示信息
            $previewContent.html(
                '<div style="display:flex; align-items:center; font-size:13px; padding:5px;"><i class="layui-icon layui-icon-face-surprised" style="margin-right:5px; color:#FF5722;"></i>无法获取文件内容，请直接在表格中录入数据</div>'
            );
        }
    } catch (error) {
        console.error('预览生成过程出错:', error);
        $preview.html(
            '<div style="display:flex; align-items:center; font-size:13px; padding:5px;"><i class="layui-icon layui-icon-close-fill" style="margin-right:5px; color:#FF5722;"></i>预览失败: ' + error.message + '</div>'
        );
    }

    // 显示预览区域
    $('#attachmentPreview').show();
}

// 本地文件预览函数
function previewLocalFile(file, fileType, $container) {
    if (!file || !$container) {
        console.error('预览参数无效', file, fileType, $container);
        return;
    }

    console.log('开始本地预览文件:', file.name, '类型:', fileType);

    // 针对不同文件类型使用不同的预览方式
    if (fileType === 'image' || /\.(jpg|jpeg|png|gif)$/i.test(file.name)) {
        // 图片预览 - 更紧凑的布局
        var reader = new FileReader();
        reader.onload = function(e) {
            $container.html(
                '<div style="display:flex; align-items:center; margin-bottom:5px; font-size:13px;"><i class="layui-icon layui-icon-picture" style="margin-right:5px;"></i>图片预览：<span style="margin-left:5px; font-weight:bold;">' + file.name + '</span></div>' +
                '<img src="' + e.target.result + '" style="max-width: 100%; max-height: 200px;" />'
            );
        };
        reader.onerror = function(e) {
            console.error('读取图片出错:', e);
            $container.html('<div style="display:flex; align-items:center; font-size:13px;"><i class="layui-icon layui-icon-close" style="margin-right:5px; color:#FF5722;"></i>图片加载失败</div>');
        };
        reader.readAsDataURL(file);
    }
    else if (fileType === 'pdf' || /\.pdf$/i.test(file.name)) {
        // PDF预览 - 更紧凑的布局
        var reader = new FileReader();
        reader.onload = function(e) {
            $container.html(
                '<div style="display:flex; align-items:center; margin-bottom:5px; font-size:13px;"><i class="layui-icon layui-icon-read" style="margin-right:5px;"></i>PDF文件预览：<span style="margin-left:5px; font-weight:bold;">' + file.name + '</span></div>' +
                '<div class="pdf-container" style="width:100%; height:600px; background-color:#f8f8f8;">' +
                '  <iframe src="' + e.target.result + '" type="application/pdf" width="100%" height="100%" style="border:none;"></iframe>' +
                '</div>'
            );
        };
        reader.onerror = function(e) {
            console.error('读取PDF出错:', e);
            $container.html(
                '<div style="display:flex; align-items:center; margin-bottom:5px; font-size:13px;"><i class="layui-icon layui-icon-close" style="margin-right:5px; color:#FF5722;"></i>PDF文件加载失败 <button class="layui-btn layui-btn-xs layui-btn-danger" onclick="downloadLocalFile(\'' + file.name + '\')" style="margin-left:10px;"><i class="layui-icon layui-icon-download-circle"></i> 下载</button></div>'
            );
        };
        reader.readAsDataURL(file);
    }
    else if (fileType === 'excel' || /\.(xls|xlsx|csv)$/i.test(file.name)) {
        // Excel文件预览 - 使用SheetJS库 - 更紧凑的布局
        $container.html(
            '<div style="display:flex; align-items:center; margin-bottom:5px; font-size:13px;"><i class="layui-icon layui-icon-form" style="margin-right:5px;"></i>Excel文件预览：<span style="margin-left:5px; font-weight:bold;">' + file.name + '</span><span id="excel-loading" style="margin-left:10px;"><i class="layui-icon layui-icon-loading layui-anim layui-anim-rotate layui-anim-loop" style="font-size:14px;"></i> 加载中...</span></div>' +
            '<div id="excel-preview-container" style="width:100%; overflow-x:auto; display:none;"></div>' +
            '<div class="layui-row" style="margin-top:5px;">'
        );

        // 检查是否已加载SheetJS库
        if (typeof XLSX === 'undefined') {
            // 动态加载SheetJS库
            var script = document.createElement('script');
            script.src = 'https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js';
            script.onload = function() {
                parseExcelFile(file);
            };
            script.onerror = function() {
                $('#excel-loading').html(
                    '<i class="layui-icon layui-icon-close-fill" style="font-size:30px;color:#FF5722"></i>' +
                    '<p style="margin-top:10px">无法加载Excel预览组件，请直接下载文件查看</p>'
                );
            };
            document.head.appendChild(script);
        } else {
            parseExcelFile(file);
        }
    }
    else {
        // 其他类型文件 - 更紧凑的布局
        $container.html(
            '<div style="display:flex; align-items:center; margin-bottom:5px; font-size:13px;"><i class="layui-icon layui-icon-file" style="margin-right:5px;"></i>文件预览：<span style="margin-left:5px; font-weight:bold;">' + file.name + '</span><button class="layui-btn layui-btn-xs" onclick="downloadLocalFile(\'' + file.name + '\')" style="margin-left:10px;"><i class="layui-icon layui-icon-download-circle"></i> 下载</button></div>'
        );
    }
}

// 解析Excel文件并显示
function parseExcelFile(file) {
    var reader = new FileReader();

    reader.onload = function(e) {
        try {
            // 读取Excel内容
            var data = new Uint8Array(e.target.result);
            var workbook = XLSX.read(data, {type: 'array'});

            // 获取第一个工作表
            var firstSheet = workbook.SheetNames[0];
            var worksheet = workbook.Sheets[firstSheet];

            // 修改：增强表格网格线和边框样式 - 使用更深的颜色和更明显的边框
            var tableStyles = '<style>' +
                // 添加范围限制，防止样式影响其他页面部分
                '#attachmentPreview .excel-table { border-collapse: collapse !important; width: 100%; margin: 10px 0; }' +
                // 强化所有边框 - 使用黑色边框
                '#attachmentPreview .excel-table, #attachmentPreview .excel-table th, #attachmentPreview .excel-table td { border: 1px solid black !important; }' +
                // 表格外边框加粗
                '#attachmentPreview .excel-table { border: 2px solid black !important; }' +
                // 单元格内边距和对齐
                '#attachmentPreview .excel-table th, #attachmentPreview .excel-table td { padding: 8px; text-align: left; }' +
                // 表头样式
                '#attachmentPreview .excel-table th { background-color: #e6e6e6; font-weight: bold; color: black; }' +
                // 行交替颜色
                '#attachmentPreview .excel-table tr:nth-child(even) { background-color: #f9f9f9; }' +
                '#attachmentPreview .excel-table tr:hover { background-color: #f0f0f0; }' +
                // 数字单元格右对齐
                '#attachmentPreview .excel-table td.number-cell { text-align: right; }' +
                // 单元格内容处理
                '#attachmentPreview .excel-table td, #attachmentPreview .excel-table th { min-width: 80px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap; }' +
                // 直接应用到原生表格元素，确保样式覆盖，但限制在附件预览区域内
                '#attachmentPreview table { border-collapse: collapse !important; }' +
                '#attachmentPreview th, #attachmentPreview td { border: 1px solid black !important; padding: 8px !important; }' +
                '</style>';

            // 将工作表转换为HTML表格
            var htmlTable = XLSX.utils.sheet_to_html(worksheet, {
                id: 'excel-table',
                className: 'excel-table',
                editable: false,
                header: tableStyles
            });

            // 显示HTML表格
            $('#excel-preview-container').html(htmlTable).show();
            $('#excel-loading').hide();

            // 应用额外的样式强化
            setTimeout(function() {
                applyTableEnhancements();
            }, 100);

            // 显示所有工作表的选项卡（如果有多个工作表）
            if (workbook.SheetNames.length > 1) {
                var sheetTabs = '<div class="layui-tab layui-tab-brief"><ul class="layui-tab-title">';

                workbook.SheetNames.forEach(function(sheetName, index) {
                    sheetTabs += '<li class="' + (index === 0 ? 'layui-this' : '') + '" data-sheet="' + sheetName + '">' + sheetName + '</li>';
                });

                sheetTabs += '</ul></div>';

                // 添加工作表选项卡
                $('#excel-preview-container').before(sheetTabs);

                // 绑定选项卡切换事件
                $('.layui-tab-title li').click(function() {
                    var sheetName = $(this).data('sheet');
                    var worksheet = workbook.Sheets[sheetName];
                    var htmlTable = XLSX.utils.sheet_to_html(worksheet, {
                        id: 'excel-table',
                        className: 'excel-table',
                        editable: false,
                        header: tableStyles
                    });

                    $('#excel-preview-container').html(htmlTable);
                    setTimeout(function() {
                        applyTableEnhancements();
                    }, 100);
                    $('.layui-tab-title li').removeClass('layui-this');
                    $(this).addClass('layui-this');
                });
            }

        } catch (error) {
            console.error('解析Excel出错:', error);
            $('#excel-loading').html(
                '<i class="layui-icon layui-icon-close-fill" style="font-size:30px;color:#FF5722"></i>' +
                '<p style="margin-top:10px">Excel文件解析失败，请下载后查看</p>' +
                '<p style="color:#999;font-size:12px;">错误信息: ' + error.message + '</p>'
            );
        }
    };

    reader.onerror = function() {
        $('#excel-loading').html(
            '<i class="layui-icon layui-icon-close-fill" style="font-size:30px;color:#FF5722"></i>' +
            '<p style="margin-top:10px">读取Excel文件失败，请下载后查看</p>'
        );
    };

    // 以二进制方式读取文件
    reader.readAsArrayBuffer(file);
}

// 应用表格增强功能
function applyTableEnhancements() {
    // 找到表格元素
    var $table = $('#excel-table');
    if (!$table.length) return;

    console.log('正在应用表格样式增强...');

    // 强制添加边框样式 - 直接操作DOM
    $table.attr('border', '1');
    $table.attr('cellspacing', '0');
    $table.attr('cellpadding', '8');

    // 强制为所有单元格添加边框样式 - 仅限于附件预览区域内的表格
    $table.find('td, th').css({
        'border': '1px solid black',
        'padding': '8px'
    });

    // 检测数字类型单元格并应用右对齐样式
    $table.find('td').each(function() {
        var cellText = $(this).text().trim();
        // 如果单元格内容是数字，应用右对齐样式
        if (!isNaN(cellText) && cellText !== '') {
            $(this).addClass('number-cell');
        }
    });

    // 处理单元格内容溢出
    $table.find('td, th').each(function() {
        var cellContent = $(this).text();
        if (cellContent.length > 20) {
            $(this).attr('title', cellContent);
        }
    });

    // 如果表格过宽，添加水平滚动
    var tableWidth = $table.width();
    var containerWidth = $('#excel-preview-container').width();
    if (tableWidth > containerWidth) {
        $('#excel-preview-container').css('overflow-x', 'auto');
    }

    // 添加表头固定功能（如果表格很长）
    if ($table.find('tr').length > 10) {
        $table.find('tr:first-child th').css({
            'position': 'sticky',
            'top': '0',
            'z-index': '10',
            'background-color': '#e6e6e6'
        });
    }

    // 输出确认信息
    console.log('表格样式增强已应用');
}

// 下载本地文件
function downloadLocalFile(fileName) {
    if (!window.lastUploadedFile) {
        layer.msg('文件不存在，请重新上传', {icon: 2});
        return;
    }

    // 创建临时下载链接
    var url = URL.createObjectURL(window.lastUploadedFile);
    var a = document.createElement('a');
    a.href = url;
    a.download = fileName || window.lastUploadedFile.name;
    document.body.appendChild(a);
    a.click();

    // 清理
    setTimeout(function() {
        document.body.removeChild(a);
        window.URL.revokeObjectURL(url);
    }, 0);
}

// 删除附件
function removeAttachment(fileId) {
    if (!fileId) return;

    layer.confirm('确定要删除此附件吗？<br><span style="color:#FF5722">删除后将无法继续编辑表格数据</span>', {
        icon: 3,
        title: '<i class="layui-icon layui-icon-tips"></i> 删除确认',
        btn: ['确定删除','取消'],
        btnAlign: 'c',
        shade: 0.6,
        skin: 'layui-layer-molv'
    }, function(index) {
        // 调用删除接口
        $.ajax({
            url: baselocation + '/common/upload/remove',
            type: 'post',
            data: {id: fileId},
            success: function(res) {
                if (res.code == 1) {
                    // 删除成功
                    $('#attachmentInfo').hide();
                    $('#attachmentId').val('');
                    attachmentUploaded = false;
                    currentAttachmentId = null;

                    // 清除本地文件引用
                    window.lastUploadedFile = null;
                    window.currentFileObject = null;
                    currentOriginalFileName = '';
                    currentFileType = '';

                    // 清空预览
                    $('#attachmentPreview').hide().find('div').empty();

                    // 更新提示
                    $('#attachmentTip').html(
                        '<span style="color: #FF5722;">必须上传附件后才能进行表格数据录入</span>'
                    );

                    // 禁用表格操作
                    disableTableOperations();

                    layer.msg('附件已删除', {icon: 1});
                } else {
                    layer.msg('删除失败：' + res.message, {icon: 2});
                }
            },
            error: function() {
                layer.msg('删除失败，请重试', {icon: 2});
            }
        });

        layer.close(index);
    });

}

// 禁用表格操作
function disableTableOperations() {
    try {
        // 为表格和按钮添加禁用样式
        $('#subOrderContainer').addClass('layui-disabled');

        // 禁用添加按钮
        $('button[onclick="addSubOrder()"]').attr('disabled', true).addClass('layui-btn-disabled');
        $('button[onclick="addSingleSubOrder()"]').attr('disabled', true).addClass('layui-btn-disabled');

        // 添加表格输入禁用
        $('#subOrderTableBody').find('input').prop('disabled', true);
        $('#subOrderTableBody').find('button').prop('disabled', true).addClass('layui-btn-disabled');

        // 显示提示信息 - 放在表格上方并居中显示
        if ($('#tableDisabledTip').length === 0) {
            $('#subOrderContainer').before(
                '<div id="tableDisabledTip" style="color: #FF5722; margin-bottom: 10px; text-align: center; padding: 8px; font-size: 14px; font-weight: bold; border: 1px solid #FFB800; background-color: #FFFBE7; border-radius: 2px;">' +
                '<i class="layui-icon layui-icon-about"></i> ' +
                '请先上传附件，然后才能进行表格数据录入' +
                '</div>'
            );
        } else {
            $('#tableDisabledTip').show();
        }
    } catch (error) {
        console.error('禁用表格操作出错:', error);
    }
}

// 启用表格操作
function enableTableOperations() {
    try {
        // 移除表格和按钮的禁用样式
        $('#subOrderContainer').removeClass('layui-disabled');

        // 启用添加按钮
        $('button[onclick="addSubOrder()"]').removeAttr('disabled').removeClass('layui-btn-disabled');
        $('button[onclick="addSingleSubOrder()"]').removeAttr('disabled').removeClass('layui-btn-disabled');

        // 启用表格输入
        $('#subOrderTableBody').find('input').prop('disabled', false);
        $('#subOrderTableBody').find('button').prop('disabled', false).removeClass('layui-btn-disabled');

        // 隐藏提示信息
        $('#tableDisabledTip').hide();

        // 先清除可能存在的成功提示，避免堆叠
        $('#tableEnabledTip').remove();

        // 显示成功提示 - 放在表格上方
        $('#subOrderContainer').before(
            '<div id="tableEnabledTip" style="color: #5FB878; margin-bottom: 10px; text-align: center; padding: 8px; font-size: 14px; font-weight: bold; border: 1px solid #5FB878; background-color: #F0F9EB; border-radius: 2px;">' +
            '<i class="layui-icon layui-icon-ok"></i> ' +
            '附件上传成功，现在可以录入数据了' +
            '<button type="button" onclick="$(\'#tableEnabledTip\').fadeOut(300, function() { $(this).remove(); });" ' +
            'style="background: none; border: none; color: #999; float: right; cursor: pointer;">' +
            '<i class="layui-icon layui-icon-close"></i></button>' +
            '</div>'
        );

        // 5秒后自动隐藏成功提示
        setTimeout(function() {
            $('#tableEnabledTip').fadeOut(500, function() {
                $(this).remove();
            });
        }, 50);
    } catch (error) {
        console.error('启用表格操作出错:', error);
    }
}

// 重写保存单行数据方法，增加附件验证
var originalSaveSingleRowData = saveSingleRowData;
saveSingleRowData = function() {
    // 检查是否上传了附件
    if (!attachmentUploaded || !currentAttachmentId) {
        // layer.msg('请先上传附件后再录入订单数据', {icon: 2});
        return false;
    }

    // 调用原始的保存方法
    if (typeof originalSaveSingleRowData === 'function') {
        originalSaveSingleRowData();
    }
}

// 重写addSingleSubOrder方法，增加附件验证
var originalAddSingleSubOrder = addSingleSubOrder;
addSingleSubOrder = function() {
    // 检查是否上传了附件
    if (!attachmentUploaded || !currentAttachmentId) {
        // layer.msg('请先上传附件后再录入订单数据', {icon: 2});
        return false;
    }

    // 调用原始的添加方法
    if (typeof originalAddSingleSubOrder === 'function') {
        originalAddSingleSubOrder();
    }
}

// 重写addSubOrder方法，增加附件验证
var originalAddSubOrder = addSubOrder;
addSubOrder = function() {
    // 检查是否上传了附件
    if (!attachmentUploaded || !currentAttachmentId) {
        // layer.msg('请先上传附件后再录入订单数据', {icon: 2});
        return false;
    }

    // 调用原始的添加方法
    if (typeof originalAddSubOrder === 'function') {
        originalAddSubOrder();
    }
}

// 预加载SheetJS库
function preloadSheetJSLibrary() {
    if (typeof XLSX === 'undefined') {
        console.log('预加载SheetJS库');
        var script = document.createElement('script');
        script.src = 'https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js';
        script.async = true;
        script.onload = function() {
            console.log('SheetJS库加载完成');
        };
        script.onerror = function() {
            console.error('SheetJS库加载失败');
        };
        document.head.appendChild(script);
    }
}

// ============================= 铜字段相关功能 =============================

// 铜字段相关的JavaScript函数
var copperFieldsModal;
var currentEditingRow = null;

// 打开铜字段弹窗
function openCopperFieldsModal(inputElement) {
    currentEditingRow = inputElement;
    var row = $(inputElement).closest('tr');
    var customerCode = $('#demandCustomerCode').val();

    if (!customerCode) {
        layer.msg('请先选择需求方', {icon: 2});
        return;
    }

    // 检查订单数量是否已填写
    var orderQuantity = row.find('input[name="orderQuantity"]').val();
    if (!orderQuantity || orderQuantity.trim() === '' || parseFloat(orderQuantity) <= 0) {
        layer.msg('请先设置订单数量再设置接单金额', {icon: 2, time: 3000});
        // 自动聚焦到订单数量输入框
        var orderQuantityInput = row.find('input[name="orderQuantity"]');
        if (orderQuantityInput.length > 0) {
            orderQuantityInput.focus();
            // 高亮显示输入框
            orderQuantityInput.addClass('layui-form-danger');
            setTimeout(function() {
                orderQuantityInput.removeClass('layui-form-danger');
            }, 3000);
        }
        return;
    }

    // 设置订单数量和客户代码
    $('#modalOrderQuantity').val(orderQuantity);
    $('#currentCustomerCode').val(customerCode);

    // 清空表单
    $('#copperFieldsForm')[0].reset();
    $('#modalOrderQuantity').val(orderQuantity);
    $('#currentCustomerCode').val(customerCode);

    console.log('新增页面同步订单数量到弹窗:', orderQuantity);

    // 加载铜条件列表
    loadCopperConditionList(function() {
        console.log('新增页面铜条件列表加载完成，开始处理数据反显...');

        // 设置订单数量 - 从外面的输入框同步到弹窗
        $('#modalOrderQuantity').val(orderQuantity);
        console.log('新增页面同步订单数量到弹窗:', orderQuantity);

        // 如果已有数据，加载现有的铜字段数据
        var existingData = row.data();
        if (existingData.copperContractType) {
            $('#copperContractType').val(existingData.copperContractType);

            // 根据铜合同类别设置模式
            var contractType = existingData.copperContractType;
            if (contractType === '1' || contractType === '2') {
                switchCopperBaseMode('auto');
                $('#copperContractNo').prop('disabled', false);

                // 加载铜签约No列表，然后设置现有值
                var customerCode = $('#demandCustomerCode').val();
                if (customerCode) {
                    loadCopperContractListWithCallback(contractType, function() {
                        // 加载完成后设置现有值
                        $('#copperContractNo').val(existingData.copperContractNo);
                        setCopperConditionValue(existingData.copperCondition);
                        $('#copperCurrency').val(existingData.copperCurrency);
                        $('#conversionRate').val(existingData.conversionRate);
                        $('#copperBase').val(existingData.copperBase);
                        $('#premium').val(existingData.premium);
                        $('#convertedCopperPrice').val(existingData.convertedCopperPrice);
                        $('#zeroBase').val(existingData.zeroBase);
                        $('#orderUnitPrice').val(existingData.orderUnitPrice);
                        $('#orderAmount').val(existingData.orderAmount);

                        layui.use('form', function() {
                            layui.form.render('select');
                        });
                    });
                } else {
                    // 没有客户代码时，直接设置值
                    setExistingCopperData(existingData);
                }
            } else if (contractType === '3' || contractType === '4') {
                switchCopperBaseMode('manual');
                $('#copperContractNo').prop('disabled', true);

                // 直接设置现有值
                setExistingCopperData(existingData);
            }
        } else {
            console.log('新增页面没有现有铜字段数据，清空表单并设置默认状态');
            // 清空表单
            $('#copperFieldsForm')[0].reset();
            // 重新设置订单数量（清空表单后需要重新设置）
            $('#modalOrderQuantity').val(orderQuantity);

            // 检查是否有保存的0base值
            var savedZeroBase = row.data('zeroBase');
            if (savedZeroBase) {
                console.log('恢复保存的0base值:', savedZeroBase);
                $('#zeroBase').val(savedZeroBase);
            }

            // 默认显示自动填充模式
            switchCopperBaseMode('auto');
            $('#copperContractNo').prop('disabled', false);
        }
    });

    // 打开弹窗
    copperFieldsModal = layer.open({
        type: 1,
        title: '设置接单金额',
        content: $('#copperFieldsModal'),
        area: ['800px', '600px'],
        btn: ['确定', '取消'],
        yes: function(index, layero) {
            saveCopperFields();
        },
        btn2: function(index, layero) {
            closeCopperFieldsModal();
        },
        success: function(layero, index) {
            // 重新渲染表单
            layui.use('form', function() {
                var modalForm = layui.form;
                modalForm.render();

                // 绑定事件
                modalForm.on('select(copperContractType)', function(data) {
                    loadCopperContractList(data.value);
                });

                modalForm.on('select(copperContractNo)', function(data) {
                    loadCopperContractDetail(data.value);
                });
            });

            // 弹窗打开后，延迟一点时间再设置数据，确保表单已完全渲染
            setTimeout(function() {
                console.log('新增页面弹窗已打开，确保订单数量正确设置...');
                // 确保订单数量正确设置（防止表单渲染时被清空）
                var currentOrderQuantity = row.find('input[name="orderQuantity"]').val();
                if (currentOrderQuantity && $('#modalOrderQuantity').val() !== currentOrderQuantity) {
                    $('#modalOrderQuantity').val(currentOrderQuantity);
                    console.log('新增页面重新设置订单数量:', currentOrderQuantity);
                    // 触发重新计算
                    calculateOrderAmount();
                }
            }, 200);
        }
    });
}

// 关闭铜字段弹窗
function closeCopperFieldsModal() {
    layer.close(copperFieldsModal);
    currentEditingRow = null;
}

// 加载铜条件列表
function loadCopperConditionList(callback) {
    $.post(baselocation + '/order/orderEntry/getCopperConditionList', {}, function(result) {
        if (result.code === 1) {
            var select = $('#copperCondition');
            select.empty().append('<option value="">请选择</option>');
            $.each(result.data, function(index, item) {
                // 假设返回的数据格式是 {condition: "条件名", currency: "货币"}
                if (typeof item === 'object') {
                    select.append('<option value="' + item.condition + '" data-currency="' + item.currency + '">' + item.condition + '</option>');
                } else {
                    // 兼容原来的字符串格式
                    select.append('<option value="' + item + '">' + item + '</option>');
                }
            });

            layui.use('form', function() {
                var modalForm = layui.form;
                modalForm.render('select');

                // 绑定铜条件选择事件
                modalForm.on('select(copperCondition)', function(data) {
                    var selectedOption = $(data.elem).find('option:selected');
                    var currency = selectedOption.data('currency');
                    if (currency && data.value) {
                        // 只有选择了有效值才设置货币
                        $('#copperCurrency').val(currency);
                    } else if (!data.value) {
                        // 如果清空选择，也清空货币
                        clearCopperCurrency();
                    }
                    // 触发重新计算
                    calculateOrderAmount();
                });

                // 执行回调
                if (callback && typeof callback === 'function') {
                    callback();
                }
            });
        }
    });
}

// 设置现有的铜字段数据
function setExistingCopperData(existingData) {
    console.log('新增页面设置现有铜字段数据:', existingData);

    $('#copperContractNo').val(existingData.copperContractNo);
    setCopperConditionValue(existingData.copperCondition);
    $('#copperCurrency').val(existingData.copperCurrency);
    $('#conversionRate').val(existingData.conversionRate);
    $('#copperBase').val(existingData.copperBase);
    $('#premium').val(existingData.premium);
    $('#convertedCopperPrice').val(existingData.convertedCopperPrice);
    $('#zeroBase').val(existingData.zeroBase);
    $('#orderUnitPrice').val(existingData.orderUnitPrice);
    $('#orderAmount').val(existingData.orderAmount);

    console.log('新增页面设置完成后的字段值:');
    console.log('  铜合同类别:', $('#copperContractType').val());
    console.log('  铜签约No:', $('#copperContractNo').val());
    console.log('  铜条件:', $('#copperCondition').val());
    console.log('  接单金额:', $('#orderAmount').val());

    layui.use('form', function() {
        layui.form.render('select');
    });
}

// 带回调的加载铜签约No列表
function loadCopperContractListWithCallback(copperContractType, callback) {
    var customerCode = $('#demandCustomerCode').val();
    if (!customerCode || !copperContractType) {
        if (callback) callback();
        return;
    }

    $.post(baselocation + '/order/orderEntry/getCopperContractList', {
        customerCode: customerCode,
        copperContractType: copperContractType
    }, function(result) {
        if (result.code === 1) {
            var select = $('#copperContractNo');
            select.empty().append('<option value="">请选择</option>');
            $.each(result.data, function(index, item) {
                select.append('<option value="' + item + '">' + item + '</option>');
            });

            layui.use('form', function() {
                layui.form.render('select');
            });

            if (callback) {
                callback();
            }
        } else {
            if (callback) callback();
        }
    }).fail(function() {
        if (callback) callback();
    });
}

// 铜合同类别变化时加载铜合同列表
function loadCopperContractList(copperContractType) {
    // 优先使用demandCustomerCode，因为这是页面主要的客户选择字段
    var customerCode = $('#demandCustomerCode').val() || $('#currentCustomerCode').val();
    console.log('新增页面loadCopperContractList调用:');
    console.log('  铜合同类别:', copperContractType);
    console.log('  demandCustomerCode元素存在:', $('#demandCustomerCode').length > 0);
    console.log('  demandCustomerCode值:', $('#demandCustomerCode').val());
    console.log('  currentCustomerCode元素存在:', $('#currentCustomerCode').length > 0);
    console.log('  currentCustomerCode值:', $('#currentCustomerCode').val());
    console.log('  最终使用的customerCode:', customerCode);

    if (!customerCode || !copperContractType) {
        console.log('新增页面缺少必要参数，退出加载');
        console.log('  customerCode为空:', !customerCode);
        console.log('  copperContractType为空:', !copperContractType);
        return;
    }

    // 清空相关联的字段
    clearCopperRelatedFields();

    // 先加载铜条件列表
    loadCopperConditionList(function() {
        // 在回调函数中重新获取customerCode，确保作用域正确
        var callbackCustomerCode = $('#demandCustomerCode').val() || $('#currentCustomerCode').val();
        console.log('新增页面回调函数中重新获取customerCode:', callbackCustomerCode);

        // 根据铜合同类别设置不同的逻辑
        if (copperContractType === '1' || copperContractType === '2') {
            // 预约铜/支给铜：启用铜签约No，铜base为自动填充，等待选择合同后填充铜条件
            switchCopperBaseMode('auto');
            $('#copperContractNo').prop('disabled', false);

            // 加载铜签约No列表
            console.log('新增页面发送铜签约No请求:', {
                customerCode: callbackCustomerCode,
                copperContractType: copperContractType
            });
            console.log('新增页面即将发送请求，参数为:', {
                customerCode: callbackCustomerCode,
                copperContractType: copperContractType,
                url: baselocation + '/order/orderEntry/getCopperContractList'
            });

            $.post(baselocation + '/order/orderEntry/getCopperContractList', {
                customerCode: callbackCustomerCode,
                copperContractType: copperContractType
            }, function(result) {
                console.log('新增页面铜签约No请求返回:', result);
                if (result.code === 1) {
                    var select = $('#copperContractNo');
                    select.empty().append('<option value="">请选择</option>');
                    console.log('新增页面铜签约No数据:', result.data);
                    $.each(result.data, function(index, item) {
                        select.append('<option value="' + item + '">' + item + '</option>');
                    });
                    layui.use('form', function() {
                        layui.form.render('select');
                    });
                } else {
                    console.log('新增页面铜签约No请求失败:', result.message);
                }
            }).fail(function(xhr, status, error) {
                console.log('新增页面铜签约No请求异常:', error);
            });
        } else if (copperContractType === '3' || copperContractType === '4') {
            // 一般铜/无偿：禁用铜签约No，铜base为手动输入，铜条件可自由选择
            switchCopperBaseMode('manual');
            $('#copperContractNo').prop('disabled', true).empty().append('<option value="">不适用</option>');

            // 查询升水表获取升水数据
            var arrivalDate = getArrivalDateFromCurrentRow();
            if (callbackCustomerCode && arrivalDate) {
                queryAscendingWaterForOrderEntry(callbackCustomerCode, arrivalDate);
            }

            layui.use('form', function() {
                layui.form.render('select');
            });
        }
    });
}

// 铜合同No变化时加载合同详情
function loadCopperContractDetail(copperContractNo) {
    if (!copperContractNo) {
        // 如果没有选择合同，清空自动填充的字段
        setCopperConditionValue('');
        $('#copperBase').val('');
        $('#copperCurrency').val('');
        clearPremiumValue(); // 清空升水值
        clearCalculatedFields();
        layui.use('form', function() {
            layui.form.render('select');
        });
        return;
    }

    $.post(baselocation + '/order/orderEntry/getCopperContractDetail', {
        copperContractNo: copperContractNo
    }, function(result) {
        if (result.code === 1 && result.data) {
            setCopperConditionValue(result.data.copperCondition);
            $('#copperBase').val(result.data.copperBase);
            $('#copperCurrency').val(result.data.copperCurrency);

            // 根据铜合同类别决定是否查询升水
            var copperContractType = $('#copperContractType').val();
            if (copperContractType === '3' || copperContractType === '4') {
                // 一般铜/无偿：查询升水表获取升水数据
                var customerCode = $('#demandCustomerCode').val() || $('#currentCustomerCode').val();
                var arrivalDate = getArrivalDateFromCurrentRow();
                if (customerCode && arrivalDate) {
                    queryAscendingWaterForOrderEntry(customerCode, arrivalDate);
                }
            } else {
                clearPremiumValue(); // 其他类型清空升水值
            }

            layui.use('form', function() {
                layui.form.render('select');
            });
            calculateOrderAmount();
        } else {
            // 如果获取失败，清空相关字段
            setCopperConditionValue('');
            $('#copperBase').val('');
            $('#copperCurrency').val('');
            clearPremiumValue();
            clearCalculatedFields();
        }
    });
}

// 货币换算函数
function convertCurrencyToRMB(originalPrice, currency, arrivalDate, callback) {
    console.log('=== 订单录入货币换算 ===');
    console.log('原价格:', originalPrice, '货币:', currency, '到货日期:', arrivalDate);

    // 如果是人民币，直接返回原价格
    if (!currency || currency === 'RMB') {
        console.log('货币为RMB，无需换算');
        callback(originalPrice, 1.0);
        return;
    }

    // 查询汇率
    $.ajax({
        url: baselocation + '/salesCommon/getCurrencyExchangeRate',
        type: 'POST',
        data: {
            originalCurrency: currency,
            arrivalDate: arrivalDate
        },
        success: function(result) {
            console.log('订单录入汇率查询返回:', result);

            if (result.code === 1 && result.data && result.data.exchangeRate) {
                var exchangeRate = parseFloat(result.data.exchangeRate);
                var convertedPrice = (parseFloat(originalPrice) * exchangeRate).toFixed(4);

                console.log('汇率:', exchangeRate, '换算后价格:', convertedPrice);
                callback(convertedPrice, exchangeRate);
            } else {
                console.log('未找到汇率数据:', result.message || '无数据');
                layer.msg(result.message || '货币换算汇率未登录！', {icon: 0, time: 3000});
                callback(originalPrice, 1.0); // 返回原价格
            }
        },
        error: function(xhr, status, error) {
            console.log('订单录入查询汇率异常:', error);
            layer.msg('查询汇率失败，使用原价格', {icon: 2, time: 3000});
            callback(originalPrice, 1.0); // 返回原价格
        }
    });
}

// 查询产品单价作为0base
function queryProductPrice(customerCode, productCode, arrivalDate) {
    console.log('=== 订单录入查询产品单价作为0base ===');
    console.log('客户代码:', customerCode, '产品代码:', productCode, '到货日期:', arrivalDate);

    if (!customerCode || !productCode || !arrivalDate) {
        console.log('参数不完整，无法查询产品单价');
        return;
    }

    $.ajax({
        url: baselocation + '/salesCommon/getProductPrice',
        type: 'POST',
        data: {
            customerCode: customerCode,
            productCode: productCode,
            arrivalDate: arrivalDate
        },
        success: function(result) {
            console.log('订单录入产品单价查询返回:', result);

            if (result.code === 1 && result.data) {
                var productPrice = result.data;
                var $zeroBaseInput = $('#zeroBase');

                if (productPrice.productPrice) {
                    // 获取产品价格的货币信息
                    var productCurrency = productPrice.currency || 'RMB';

                    console.log('产品单价:', productPrice.productPrice, '货币:', productCurrency);

                    // 进行货币换算
                    convertCurrencyToRMB(productPrice.productPrice, productCurrency, arrivalDate, function(convertedPrice, exchangeRate) {
                        // 设置0base（换算后的价格）
                        $zeroBaseInput.val(convertedPrice);
                        console.log('自动设置0base（换算后）:', convertedPrice, '汇率:', exchangeRate);

                        // 将0base值保存到所有相关行的数据中
                        saveZeroBaseToAllRows(convertedPrice, customerCode, productCode);

                        // 触发0base变更事件，重新计算接单金额
                        calculateOrderAmount();

                        var message = '已自动填入0base: ' + convertedPrice;
                        if (exchangeRate !== 1.0) {
                            message += ' (原价: ' + productPrice.productPrice + ' ' + productCurrency + ', 汇率: ' + exchangeRate + ')';
                        }
                        layer.msg(message, {icon: 1, time: 3000});
                    });
                } else {
                    console.log('产品价格数据中没有单价信息');
                }
            } else {
                console.log('未找到匹配的产品价格:', result.message || '无数据');
                // 不显示错误提示，因为可能是正常情况（没有配置价格）
            }
        },
        error: function(xhr, status, error) {
            console.log('订单录入查询产品单价异常:', error);
            // 不显示错误提示，避免影响用户体验
        }
    });
}

// 将0base值保存到所有相关行的数据中
function saveZeroBaseToAllRows(zeroBaseValue, customerCode, productCode) {
    console.log('保存0base值到相关行:', {
        zeroBaseValue: zeroBaseValue,
        customerCode: customerCode,
        productCode: productCode
    });

    // 遍历所有表格行，找到匹配的行并保存0base值
    $('#subOrderTableBody tr').each(function() {
        var $row = $(this);
        var rowCustomerCode = $('#demandCustomerCode').val(); // 客户代码来自表单顶部
        var rowProductCode = $row.find('input[name="modelNumber"], select[name="modelNumber"]').val();

        console.log('检查行数据:', {
            rowCustomerCode: rowCustomerCode,
            rowProductCode: rowProductCode,
            targetCustomerCode: customerCode,
            targetProductCode: productCode
        });

        // 如果客户代码和产品代码匹配，则保存0base值
        if (rowCustomerCode === customerCode && rowProductCode === productCode) {
            $row.data('zeroBase', zeroBaseValue);
            console.log('已保存0base值到行数据:', zeroBaseValue);
        }
    });
}

// 计算接单金额
function calculateOrderAmount() {
    var conversionRate = parseFloat($('#conversionRate').val()) || 0;
    var copperBase = parseFloat($('#copperBase').val()) || 0;
    var premium = parseFloat($('#premium').val()) || 0;
    var zeroBase = parseFloat($('#zeroBase').val()) || 0;
    var orderQuantity = parseFloat($('#modalOrderQuantity').val()) || 0;

    if (conversionRate > 0 && copperBase > 0) {
        // 换算后铜单价 = （铜base + 升水）* 换算率
        var convertedCopperPrice = (copperBase + premium) * conversionRate;
        $('#convertedCopperPrice').val(convertedCopperPrice.toFixed(4));

        // 接单单价 = 换算后铜单价 + 0base
        var orderUnitPrice = convertedCopperPrice + zeroBase;
        $('#orderUnitPrice').val(orderUnitPrice.toFixed(4));

        // 接单金额 = 接单单价 * 订单数量
        var orderAmount = orderUnitPrice * orderQuantity;
        $('#orderAmount').val(orderAmount.toFixed(2));
    }
}

// 保存铜字段数据
function saveCopperFields() {
    if (!currentEditingRow) {
        layer.msg('无效的操作', {icon: 2});
        return;
    }

    var orderAmount = $('#orderAmount').val();
    if (!orderAmount || parseFloat(orderAmount) <= 0) {
        layer.msg('请完善铜字段信息以计算接单金额', {icon: 2});
        return;
    }

    // 获取弹窗中的订单数量
    var modalOrderQuantity = $('#modalOrderQuantity').val();

    // 更新接单金额输入框显示
    $(currentEditingRow).val(orderAmount);

    // 同步订单数量回外面的输入框
    var row = $(currentEditingRow).closest('tr');
    var orderQuantityInput = row.find('input[name="orderQuantity"]');
    if (orderQuantityInput.length > 0) {
        orderQuantityInput.val(modalOrderQuantity);
        console.log('新增页面同步订单数量回外面:', modalOrderQuantity);
    }

    // 存储铜字段数据到行的data属性中
    var row = $(currentEditingRow).closest('tr');
    row.data('copperContractType', $('#copperContractType').val());
    row.data('copperContractNo', $('#copperContractNo').val());
    row.data('copperCondition', getCopperConditionValue());
    row.data('copperCurrency', $('#copperCurrency').val());
    row.data('conversionRate', $('#conversionRate').val());
    row.data('copperBase', $('#copperBase').val());
    row.data('premium', $('#premium').val());
    row.data('convertedCopperPrice', $('#convertedCopperPrice').val());
    row.data('zeroBase', $('#zeroBase').val());
    row.data('orderUnitPrice', $('#orderUnitPrice').val());
    row.data('orderAmount', orderAmount);

    layer.msg('设置成功', {icon: 1});
    closeCopperFieldsModal();
}

// 清空升水值
function clearPremiumValue() {
    $('#premium').val('');
}

// 获取当前行的到货日期
function getArrivalDateFromCurrentRow() {
    if (!currentEditingRow) {
        return '';
    }
    var row = $(currentEditingRow).closest('tr');
    return row.find('input[name="arrivalDate"]').val() || '';
}

// 查询升水表数据（用于订单录入）
function queryAscendingWaterForOrderEntry(customerCode, arrivalDate) {
    console.log('=== 订单录入查询升水表数据 ===');
    console.log('客户代码:', customerCode, '到货日期:', arrivalDate);

    if (!customerCode || !arrivalDate) {
        console.log('客户代码或到货日期为空，无法查询升水表');
        clearPremiumOptions();
        return;
    }

    $.ajax({
        url: baselocation + '/salesCommon/getAscendingWaterByCustomerAndDate',
        type: 'POST',
        data: {
            customerCode: customerCode,
            arrivalDate: arrivalDate
        },
        success: function(result) {
            console.log('订单录入升水表查询返回:', result);

            if (result.code === 1 && result.data && result.data.length > 0) {
                // 取第一条数据的升水值直接设置
                var firstItem = result.data[0];
                var ascendingWaterPrice = firstItem.ascendingWaterPrice || 0;

                console.log('新增页面取第一条升水数据:', {
                    copperCondition: firstItem.copperCondition || '',
                    ascendingWaterPrice: ascendingWaterPrice,
                    copperConditionName: firstItem.copperConditionName || ''
                });

                $('#premium').val(ascendingWaterPrice);
                console.log('新增页面升水值已设置为:', ascendingWaterPrice);

                // 重新计算接单金额
                calculateOrderAmount();
            } else {
                console.log('订单录入未找到匹配的升水数据:', result.message || '无数据');
                clearPremiumValue();
            }
        },
        error: function(xhr, status, error) {
            console.log('订单录入查询升水表异常:', error);
            clearPremiumValue();
        }
    });
}

// 清空铜相关字段（当合同类别改变时）
function clearCopperRelatedFields() {
    // 清空铜签约No
    $('#copperContractNo').empty().append('<option value="">请选择</option>');

    // 清空铜条件和铜货币
    setCopperConditionValue('');
    clearCopperCurrency();

    // 清空其他字段
    $('#copperBase').val('');

    // 清空升水值
    clearPremiumValue();

    // 清空计算字段
    clearCalculatedFields();

    layui.use('form', function() {
        layui.form.render('select');
    });
}

// 清空计算字段
function clearCalculatedFields() {
    $('#convertedCopperPrice').val('');
    $('#orderUnitPrice').val('');
    $('#orderAmount').val('');
}

// 清空铜货币
function clearCopperCurrency() {
    $('#copperCurrency').val('');
}



// 切换铜base和铜条件模式（根据铜合同类别）
function switchCopperBaseMode(mode) {
    if (mode === 'auto') {
        // 自动填充模式（预约铜/支给铜）
        $('#copperBase').removeClass('layui-input').addClass('layui-input auto-filled-input').prop('readonly', true).attr('placeholder', '从合同自动填充').removeAttr('oninput');
        // 铜条件也设为禁用状态
        $('#copperCondition').prop('disabled', true);
    } else if (mode === 'manual') {
        // 手动输入模式（一般铜/无偿）
        $('#copperBase').removeClass('auto-filled-input').addClass('layui-input').prop('readonly', false).attr('placeholder', '请输入铜base').attr('oninput', 'calculateOrderAmount()');
        // 铜条件设为可编辑状态
        $('#copperCondition').prop('disabled', false);
    }

    layui.use('form', function() {
        layui.form.render('select');
    });
}

// 获取当前铜条件的值
function getCopperConditionValue() {
    return $('#copperCondition').val();
}

// 设置铜条件的值
function setCopperConditionValue(value) {
    $('#copperCondition').val(value);

    // 如果清空铜条件，也清空铜货币
    if (!value) {
        clearCopperCurrency();
    }

    layui.use('form', function() {
        layui.form.render('select');
    });
}