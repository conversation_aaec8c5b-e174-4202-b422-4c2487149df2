package com.hongru.pojo.dto;

import java.util.List;

import com.hongru.entity.businessOps.CurrencyExchangeRate;
import com.hongru.support.page.PageInfo;

public class CurrencyExchangeRatePageDTO{

	/**
	 * 货币换算汇率表信息
	 */
	private List<CurrencyExchangeRate> currencyExchangeRateList;
	
	/**
	 * 分页信息
	 */
	private PageInfo pageInfo;
	
	public CurrencyExchangeRatePageDTO(PageInfo pageInfo, List<CurrencyExchangeRate> currencyExchangeRateList) {
		super();
		this.currencyExchangeRateList = currencyExchangeRateList;
		this.pageInfo = pageInfo;
	}

	public List<CurrencyExchangeRate> getCurrencyExchangeRateList() {
		return currencyExchangeRateList;
	}

	public void setCurrencyExchangeRate(List<CurrencyExchangeRate> currencyExchangeRateList) {
		this.currencyExchangeRateList = currencyExchangeRateList;
	}
	
	public PageInfo getPageInfo() {
		return pageInfo;
	}

	public void setPageInfo(PageInfo pageInfo) {
		this.pageInfo = pageInfo;
	}

}
