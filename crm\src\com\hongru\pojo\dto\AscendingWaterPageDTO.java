package com.hongru.pojo.dto;

import java.util.List;

import com.hongru.entity.businessOps.AscendingWater;
import com.hongru.support.page.PageInfo;

public class AscendingWaterPageDTO{

	/**
	 * 升水表信息
	 */
	private List<AscendingWater> ascendingWaterList;
	
	/**
	 * 分页信息
	 */
	private PageInfo pageInfo;
	
	public AscendingWaterPageDTO(PageInfo pageInfo, List<AscendingWater> ascendingWaterList) {
		super();
		this.ascendingWaterList = ascendingWaterList;
		this.pageInfo = pageInfo;
	}

	public List<AscendingWater> getAscendingWaterList() {
		return ascendingWaterList;
	}

	public void setAscendingWater(List<AscendingWater> ascendingWaterList) {
		this.ascendingWaterList = ascendingWaterList;
	}
	
	public PageInfo getPageInfo() {
		return pageInfo;
	}

	public void setPageInfo(PageInfo pageInfo) {
		this.pageInfo = pageInfo;
	}

}
