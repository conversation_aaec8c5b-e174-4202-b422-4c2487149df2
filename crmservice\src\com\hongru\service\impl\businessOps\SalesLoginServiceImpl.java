package com.hongru.service.impl.businessOps;

import com.hongru.common.util.DateUtils;
import com.hongru.common.util.JapaneseFiscalYearUtils;
import com.hongru.entity.businessOps.SalesAmount;
import com.hongru.entity.businessOps.SalesAmountDetail;
import com.hongru.mapper.businessOps.SalesAmountMapper;
import com.hongru.mapper.businessOps.SalesAmountDetailMapper;
import com.hongru.pojo.dto.SalesLoginListDTO;
import com.hongru.pojo.dto.SalesAmountExportDTO;
import com.hongru.service.businessOps.ISalesLoginService;
import com.hongru.service.businessOps.ISalesCommonService;
import com.hongru.service.sumitomo.ISumitomoService;
import com.hongru.support.page.PageInfo;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Service
public class SalesLoginServiceImpl implements ISalesLoginService {

    @Autowired
    private SalesAmountMapper salesAmountMapper;

    @Autowired
    private SalesAmountDetailMapper salesAmountDetailMapper;

    @Autowired
    private ISumitomoService sumitomoService;

    @Autowired
    private ISalesCommonService salesCommonService;

    @Override
    public List<SalesLoginListDTO> listSalesLoginByPage(PageInfo pageInfo, String customerCode, String salesAmountDate)
            throws Exception {
        return salesAmountMapper.listSalesLoginByPage(pageInfo, customerCode, salesAmountDate);
    }

    @Override
    public Integer listSalesLoginByPageCount(String customerCode, String salesAmountDate) throws Exception {
        return salesAmountMapper.listSalesLoginByPageCount(customerCode, salesAmountDate);
    }

    @Override
    public List<SalesLoginListDTO> listSalesLoginByPageWithUser(PageInfo pageInfo, String customerCode,
            String startDate, String endDate, String creatorName) throws Exception {
        return salesAmountMapper.listSalesLoginByPageWithUser(pageInfo, customerCode, startDate, endDate, creatorName);
    }

    @Override
    public Integer listSalesLoginByPageCountWithUser(String customerCode, String startDate, String endDate,
            String creatorName)
            throws Exception {
        return salesAmountMapper.listSalesLoginByPageCountWithUser(customerCode, startDate, endDate, creatorName);
    }

    @Override
    public SalesAmount getSalesAmountById(Integer id) throws Exception {
        return salesAmountMapper.selectById(id);
    }

    @Override
    public SalesAmount getSalesAmountByNo(String salesAmountNo) throws Exception {
        return salesAmountMapper.getSalesAmountByNo(salesAmountNo);
    }

    @Override
    public List<SalesAmountDetail> getSalesAmountDetailsByNo(String salesAmountNo) throws Exception {
        return salesAmountDetailMapper.getSalesAmountDetailsByNo(salesAmountNo);
    }

    @Override
    public Map<String, Object> getCustomerDetailsInfo(String customerCode) throws Exception {
        return salesAmountMapper.getCustomerDetailsInfo(customerCode);
    }

    @Override
    public List<Map<String, Object>> getOutboundDataForSalesAmount(String customerCode, String arrivalDate)
            throws Exception {
        // 检查该需求方在此到货日是否已登录
        boolean exists = salesAmountMapper.checkCustomerExists(customerCode, arrivalDate) > 0;
        if (exists) {
            throw new Exception("该需求方在此到货日已登录，不能重复登录！");
        }

        return salesAmountMapper.getOutboundDataForSalesAmountWithArrivalDate(customerCode, arrivalDate);
    }

    @Override
    public List<Map<String, Object>> getOutboundDataForSalesAmountWithDateRange(String customerCode, String startDate,
            String endDate)
            throws Exception {
        return salesAmountMapper.getOutboundDataForSalesAmountWithDateRange(customerCode, startDate, endDate);
    }

    @Override
    public List<Map<String, Object>> getDetailsByShipmentNo(String shipmentNo) throws Exception {
        return salesAmountMapper.getDetailsByShipmentNo(shipmentNo);
    }

    @Override
    public String generateSalesAmountNo() throws Exception {
        // 获取当前日期
        String nowDate = DateUtils.convertIntToDateString(DateUtils.getCurrentTime(), "yyyyMMdd");
        String prefix = "W" + nowDate.substring(2); // D + 年月日 (去掉前两位年份)

        // 获取当天最大的销售额NO
        String maxSalesAmountNo = salesAmountMapper.getMaxSalesAmountNoByPrefix(prefix);

        String currentSalesAmountNo;
        if (maxSalesAmountNo != null && !maxSalesAmountNo.trim().isEmpty()) {
            // 如果存在，则序号加1
            int sequence = Integer.parseInt(maxSalesAmountNo.substring(7)) + 1;
            currentSalesAmountNo = prefix + String.format("%02d", sequence);
        } else {
            // 如果不存在，则从01开始
            currentSalesAmountNo = prefix + "01";
        }

        return currentSalesAmountNo;
    }

    @Override
    @Transactional
    public int saveSalesLoginData(SalesAmount salesAmount, List<SalesAmountDetail> salesAmountDetails, String userName)
            throws Exception {
        // 设置创建信息
        String currentTime = DateUtils.convertIntToDateString(DateUtils.getCurrentTime(), "yyyy-MM-dd HH:mm:ss");
        salesAmount.setCreatorName(userName);
        salesAmount.setCreatedTime(currentTime);

        // 设置更新者信息，与创建者相同
        salesAmount.setUpdaterName(userName);
        salesAmount.setUpdatedTime(currentTime);

        // 设置默认值
        salesAmount.setStronghold("CW");
        salesAmount.setSalesAmountConfirm("0"); // 未确认

        // 设置会计年月
        if (salesAmount.getSalesAmountDate() != null && salesAmount.getSalesAmountDate().length() >= 7) {
            salesAmount.setAccountingYearMonth(salesAmount.getSalesAmountDate().substring(0, 7));
        }

        // 保存销售额主表
        int result = salesAmountMapper.insert(salesAmount);

        // 保存销售额明细表
        if (salesAmountDetails != null && !salesAmountDetails.isEmpty()) {
            for (SalesAmountDetail detail : salesAmountDetails) {
                detail.setSalesAmountNo(salesAmount.getSalesAmountNo());
                detail.setCreatorName(userName);
                detail.setCreatedTime(currentTime);

                // 设置更新者信息，与创建者相同
                detail.setUpdaterName(userName);
                detail.setUpdatedTime(currentTime);

                // 计算换算后铜单价、销售单价、销售金额
                calculateDetailAmounts(detail);

                salesAmountDetailMapper.insert(detail);
            }
        }

        return result;
    }

    @Override
    @Transactional
    public int updateSalesLoginData(SalesAmount salesAmount, List<SalesAmountDetail> salesAmountDetails,
            String userName) throws Exception {
        // 设置更新信息
        String currentTime = DateUtils.convertIntToDateString(DateUtils.getCurrentTime(), "yyyy-MM-dd HH:mm:ss");
        salesAmount.setUpdaterName(userName);
        salesAmount.setUpdatedTime(currentTime);

        // 设置会计年月
        if (salesAmount.getSalesAmountDate() != null && salesAmount.getSalesAmountDate().length() >= 7) {
            salesAmount.setAccountingYearMonth(salesAmount.getSalesAmountDate().substring(0, 7));
        }

        // 更新销售额主表
        int result = salesAmountMapper.updateById(salesAmount);

        // 先删除原有的明细数据
        salesAmountDetailMapper.deleteBySalesAmountNo(salesAmount.getSalesAmountNo());

        // 重新插入销售额明细表
        if (salesAmountDetails != null && !salesAmountDetails.isEmpty()) {
            for (SalesAmountDetail detail : salesAmountDetails) {
                // 设置销售额NO
                detail.setSalesAmountNo(salesAmount.getSalesAmountNo());

                // 设置创建和更新信息
                detail.setCreatorName(userName);
                detail.setCreatedTime(currentTime);
                detail.setUpdaterName(userName);
                detail.setUpdatedTime(currentTime);

                // 计算换算后铜单价、销售单价、销售金额
                calculateDetailAmounts(detail);

                // 插入新的明细数据
                salesAmountDetailMapper.insert(detail);
            }
        }

        return result;
    }

    @Override
    @Transactional
    public int deleteSalesLoginDataByNo(String salesAmountNo) throws Exception {
        System.out.println("开始删除销售额数据: " + salesAmountNo);

        // 删除明细表数据
        int detailCount = salesAmountDetailMapper.deleteBySalesAmountNo(salesAmountNo);
        System.out.println("删除明细表记录数: " + detailCount);

        // 删除主表数据
        int mainCount = salesAmountMapper.deleteBySalesAmountNo(salesAmountNo);
        System.out.println("删除主表记录数: " + mainCount);

        return mainCount;
    }

    @Override
    public List<Map<String, Object>> getTradeConditions() throws Exception {
        return salesAmountMapper.getTradeConditions();
    }

    @Override
    public List<Map<String, Object>> getPayConditions() throws Exception {
        return salesAmountMapper.getPayConditions();
    }

    @Override
    public List<Map<String, Object>> getTransportConditions() throws Exception {
        return salesAmountMapper.getTransportConditions();
    }

    @Override
    public boolean isUsedByPaymentRequest(String salesAmountNo) throws Exception {
        return salesAmountMapper.isUsedByPaymentRequest(salesAmountNo);
    }

    /**
     * 计算明细金额
     * 换算后铜单价 = (铜base + 升水) * 换算率
     * 销售单价 = 换算后铜单价 + 0基础
     * 销售金额 = 销售单价 * 数量
     *
     * @param detail 销售额明细
     */
    private void calculateDetailAmounts(SalesAmountDetail detail) {
        if (detail.getCopperBase() != null && detail.getPremium() != null) {
            // 获取换算率，默认为1
            BigDecimal conversionRate = detail.getConversionRate() != null ? detail.getConversionRate()
                    : BigDecimal.ONE;

            // 换算后铜单价 = (铜base + 升水) * 换算率
            BigDecimal convertedCopperPrice = detail.getCopperBase().add(detail.getPremium()).multiply(conversionRate);
            detail.setConvertedCopperPrice(convertedCopperPrice.setScale(4, RoundingMode.HALF_UP));

            // 销售单价 = 换算后铜单价 + 0base
            if (detail.getZeroBase() != null) {
                BigDecimal salesUnitPrice = convertedCopperPrice.add(detail.getZeroBase());
                detail.setSalesUnitPrice(salesUnitPrice.setScale(4, RoundingMode.HALF_UP));

                // 销售金额 = 销售单价 * 数量
                if (detail.getQuantity() != null) {
                    BigDecimal salesAmount = salesUnitPrice.multiply(detail.getQuantity());
                    detail.setSalesAmount(salesAmount.setScale(2, RoundingMode.HALF_UP));
                }
            }
        }
    }

    @Override
    public List<SalesAmountExportDTO> getSalesAmountExportData(String customerCode, String salesAmountDateRange)
            throws Exception {
        List<SalesAmountExportDTO> exportList = new ArrayList<>();

        // 获取当前日期作为导出日期（作成日）
        java.text.SimpleDateFormat dateFormat = new java.text.SimpleDateFormat("yyyy-MM-dd");
        String currentDate = dateFormat.format(new java.util.Date());

        // 查询销售额列表
        List<SalesLoginListDTO> salesAmountList = salesAmountMapper.listSalesLoginByPage(
                null, customerCode, salesAmountDateRange);

        for (SalesLoginListDTO salesAmount : salesAmountList) {
            // 获取销售额明细
            List<SalesAmountDetail> details = salesAmountDetailMapper.getSalesAmountDetailsByNo(
                    salesAmount.getSalesNo());

            // 获取销售额主表信息
            SalesAmount salesAmountEntity = salesAmountMapper.getSalesAmountByNo(
                    salesAmount.getSalesNo());

            for (SalesAmountDetail detail : details) {
                SalesAmountExportDTO exportDTO = new SalesAmountExportDTO();

                // 设置基本信息
                exportDTO.setSalesAmountNo(salesAmountEntity.getSalesAmountNo());
                exportDTO.setSalesAmountDate(salesAmountEntity.getSalesAmountDate());
                exportDTO.setCustomerCode(salesAmountEntity.getCustomerCode());
                exportDTO.setCustomerAlias(salesAmountEntity.getCustomerAlias());
                exportDTO.setSettlementCurrency(salesAmountEntity.getSettlementCurrency());
                exportDTO.setCreatedTime(salesAmountEntity.getCreatedTime()); // 设置创建时间
                exportDTO.setExportDate(currentDate); // 设置导出日期（作成日）
                exportDTO.setAccountingYearMonth(salesAmountEntity.getAccountingYearMonth());

                // 获取客户全称（需求方全称和签约方全称都是同一个客户）
                if (salesAmountEntity.getCustomerCode() != null
                        && !salesAmountEntity.getCustomerCode().trim().isEmpty()) {
                    try {
                        com.hongru.entity.sumitomo.Customers customer = sumitomoService
                                .getCustomerByCode(salesAmountEntity.getCustomerCode());
                        if (customer != null && customer.getCustomerName() != null) {
                            exportDTO.setCustomerFullName(customer.getCustomerName());
                            exportDTO.setContractorFullName(customer.getCustomerName()); // 销售额中签约方和需求方是同一个客户
                        }
                    } catch (Exception e) {
                        System.err.println(
                                "批量导出获取客户全称失败，客户代码：" + salesAmountEntity.getCustomerCode() + "，错误：" + e.getMessage());
                    }
                }

                // 计算财年
                try {
                    String fiscalYear = JapaneseFiscalYearUtils.getFiscalYear(salesAmountEntity.getSalesAmountDate());
                    exportDTO.setFiscalYear(fiscalYear);
                    exportDTO.setYear(fiscalYear); // 用于成本表匹配
                } catch (Exception e) {
                    exportDTO.setFiscalYear(null);
                    exportDTO.setYear(null);
                }

                // 设置明细信息
                exportDTO.setShipmentDate(detail.getShipmentDate());
                exportDTO.setProductCode(detail.getProductCode());
                exportDTO.setQuantity(formatQuantity(detail.getQuantity())); // 格式化数量（3位小数）

                // 根据产品代码查询sumitomo库的产品表获取客户部品编码
                if (detail.getProductCode() != null && !detail.getProductCode().trim().isEmpty()) {
                    try {
                        com.hongru.entity.sumitomo.Product productInfo = sumitomoService
                                .getProductInfo(detail.getProductCode(), "0");
                        if (productInfo != null && productInfo.getPartNo() != null) {
                            exportDTO.setCustomerPartCode(productInfo.getPartNo());
                        }
                    } catch (Exception e) {
                        // 如果查询失败，记录日志但不影响其他数据
                        System.out.println("查询产品部品编号失败，产品代码：" + detail.getProductCode() + "，错误：" + e.getMessage());
                    }
                }
                exportDTO.setArrivalDate(detail.getArrivalDate());
                exportDTO.setCustomerOrderNo(detail.getCustomerOrderNo());
                exportDTO.setDetailRemark(detail.getDetailRemark());
                exportDTO.setZeroBase(detail.getZeroBase());
                exportDTO.setConvertedCopperPrice(formatCopperPrice(detail.getConvertedCopperPrice())); // 格式化换算后铜单价（2位小数）
                exportDTO.setSalesUnitPrice(formatUnitPrice(detail.getSalesUnitPrice())); // 格式化销售单价（2位小数）
                exportDTO.setSalesAmount(formatAmount(detail.getSalesAmount())); // 格式化销售金额（2位小数）

                // 计算单价、金额、税额等
                if (detail.getSalesAmount() != null && detail.getQuantity() != null &&
                        detail.getQuantity().compareTo(BigDecimal.ZERO) > 0) {
                    BigDecimal unitPrice = detail.getSalesAmount().divide(detail.getQuantity(), 2,
                            BigDecimal.ROUND_HALF_UP);
                    exportDTO.setUnitPrice(formatUnitPrice(unitPrice)); // 格式化单价（2位小数）
                }
                exportDTO.setAmount(formatAmount(detail.getSalesAmount())); // 格式化金额（2位小数）

                // 计算税额 - 必须使用真实税率数据
                if (salesAmountEntity.getTaxCode() != null && detail.getSalesAmount() != null) {
                    try {
                        // 根据税代码获取真实税率
                        com.hongru.entity.businessOps.TaxRateCalculation taxRateCalc = salesCommonService
                                .getTaxRateByTaxCode(salesAmountEntity.getTaxCode());
                        if (taxRateCalc != null && taxRateCalc.getTaxRate() != null) {
                            BigDecimal taxRate = taxRateCalc.getTaxRate();
                            // 将税率转换为百分比形式进行计算：税额 = 金额 × (税率/100)
                            BigDecimal taxRatePercent = taxRate.divide(new BigDecimal("100"));
                            BigDecimal tax = detail.getSalesAmount().multiply(taxRatePercent);
                            exportDTO.setTax(formatTax(tax)); // 格式化税额（2位小数）
                            exportDTO.setTaxRate(taxRate);
                            exportDTO.setTotalPriceWithTax(formatAmount(detail.getSalesAmount().add(tax))); // 格式化含税总额（2位小数）
                        } else {
                            // 如果找不到税率数据，税额设为0，但记录错误
                            System.err.println("警告：找不到税代码 " + salesAmountEntity.getTaxCode() + " 对应的税率数据");
                            exportDTO.setTax(formatTax(BigDecimal.ZERO)); // 格式化税额（2位小数）
                            exportDTO.setTaxRate(BigDecimal.ZERO);
                            exportDTO.setTotalPriceWithTax(formatAmount(detail.getSalesAmount())); // 格式化含税总额（2位小数）
                        }
                    } catch (Exception e) {
                        System.err.println("查询税率失败，税代码：" + salesAmountEntity.getTaxCode() + "，错误：" + e.getMessage());
                        exportDTO.setTax(formatTax(BigDecimal.ZERO)); // 格式化税额（2位小数）
                        exportDTO.setTaxRate(BigDecimal.ZERO);
                        exportDTO.setTotalPriceWithTax(formatAmount(detail.getSalesAmount())); // 格式化含税总额（2位小数）
                    }
                } else {
                    // 没有税代码或金额，税额为0
                    exportDTO.setTax(formatTax(BigDecimal.ZERO)); // 格式化税额（2位小数）
                    exportDTO.setTaxRate(BigDecimal.ZERO);
                    exportDTO.setTotalPriceWithTax(formatAmount(
                            detail.getSalesAmount() != null ? detail.getSalesAmount() : BigDecimal.ZERO)); // 格式化含税总额（2位小数）
                }

                // TODO: 这里需要根据财年和成本件匹配成本表数据
                // 暂时设置为空，后续需要实现成本表查询逻辑
                exportDTO.setCostCode(null);
                exportDTO.setHumanCost(BigDecimal.ZERO);
                exportDTO.setElectricCost(BigDecimal.ZERO);
                exportDTO.setGasCost(BigDecimal.ZERO);
                exportDTO.setWaterCost(BigDecimal.ZERO);
                exportDTO.setTransportCost(BigDecimal.ZERO);
                exportDTO.setWireDiscCost(BigDecimal.ZERO);
                exportDTO.setTotalCost(BigDecimal.ZERO);

                exportList.add(exportDTO);
            }
        }

        return exportList;
    }

    @Override
    public List<SalesAmountExportDTO> getSalesAmountExportDataByNo(String salesAmountNo) throws Exception {
        List<SalesAmountExportDTO> exportList = new ArrayList<>();

        // 获取当前日期作为导出日期（作成日）
        java.text.SimpleDateFormat dateFormat = new java.text.SimpleDateFormat("yyyy-MM-dd");
        String currentDate = dateFormat.format(new java.util.Date());

        // 获取销售额主表信息
        SalesAmount salesAmountEntity = salesAmountMapper.getSalesAmountByNo(salesAmountNo);
        if (salesAmountEntity == null) {
            return exportList;
        }

        // 获取销售额明细
        List<SalesAmountDetail> details = salesAmountDetailMapper.getSalesAmountDetailsByNo(salesAmountNo);

        for (SalesAmountDetail detail : details) {
            SalesAmountExportDTO exportDTO = new SalesAmountExportDTO();

            // 设置基本信息
            exportDTO.setSalesAmountNo(salesAmountEntity.getSalesAmountNo());
            exportDTO.setSalesAmountDate(salesAmountEntity.getSalesAmountDate());
            exportDTO.setCustomerCode(salesAmountEntity.getCustomerCode());
            exportDTO.setCustomerAlias(salesAmountEntity.getCustomerAlias());
            exportDTO.setSettlementCurrency(salesAmountEntity.getSettlementCurrency());
            exportDTO.setCreatedTime(salesAmountEntity.getCreatedTime()); // 设置创建时间
            exportDTO.setExportDate(currentDate); // 设置导出日期（作成日）
            exportDTO.setAccountingYearMonth(salesAmountEntity.getAccountingYearMonth());

            // 获取客户全称（需求方全称和签约方全称都是同一个客户）
            if (salesAmountEntity.getCustomerCode() != null && !salesAmountEntity.getCustomerCode().trim().isEmpty()) {
                try {
                    com.hongru.entity.sumitomo.Customers customer = sumitomoService
                            .getCustomerByCode(salesAmountEntity.getCustomerCode());
                    if (customer != null && customer.getCustomerName() != null) {
                        exportDTO.setCustomerFullName(customer.getCustomerName());
                        exportDTO.setContractorFullName(customer.getCustomerName()); // 销售额中签约方和需求方是同一个客户
                    }
                } catch (Exception e) {
                    System.err.println(
                            "单个导出获取客户全称失败，客户代码：" + salesAmountEntity.getCustomerCode() + "，错误：" + e.getMessage());
                }
            }

            // 计算财年
            try {
                String fiscalYear = JapaneseFiscalYearUtils.getFiscalYear(salesAmountEntity.getSalesAmountDate());
                exportDTO.setFiscalYear(fiscalYear);
                exportDTO.setYear(fiscalYear); // 用于成本表匹配
            } catch (Exception e) {
                exportDTO.setFiscalYear(null);
                exportDTO.setYear(null);
            }

            // 设置明细信息
            exportDTO.setShipmentDate(detail.getShipmentDate());
            exportDTO.setProductCode(detail.getProductCode());
            exportDTO.setQuantity(formatQuantity(detail.getQuantity())); // 格式化数量（3位小数）

            // 根据产品代码查询sumitomo库的产品表获取客户部品编码
            if (detail.getProductCode() != null && !detail.getProductCode().trim().isEmpty()) {
                try {
                    com.hongru.entity.sumitomo.Product productInfo = sumitomoService
                            .getProductInfo(detail.getProductCode(), "0");
                    if (productInfo != null && productInfo.getPartNo() != null) {
                        exportDTO.setCustomerPartCode(productInfo.getPartNo());
                    }
                } catch (Exception e) {
                    // 如果查询失败，记录日志但不影响其他数据
                    System.out.println("查询产品部品编号失败，产品代码：" + detail.getProductCode() + "，错误：" + e.getMessage());
                }
            }
            exportDTO.setArrivalDate(detail.getArrivalDate());
            exportDTO.setCustomerOrderNo(detail.getCustomerOrderNo());
            exportDTO.setDetailRemark(detail.getDetailRemark());
            exportDTO.setZeroBase(detail.getZeroBase());
            exportDTO.setConvertedCopperPrice(formatCopperPrice(detail.getConvertedCopperPrice())); // 格式化换算后铜单价（2位小数）
            exportDTO.setSalesUnitPrice(formatUnitPrice(detail.getSalesUnitPrice())); // 格式化销售单价（2位小数）
            exportDTO.setSalesAmount(formatAmount(detail.getSalesAmount())); // 格式化销售金额（2位小数）

            // 计算单价、金额、税额等
            if (detail.getSalesAmount() != null && detail.getQuantity() != null &&
                    detail.getQuantity().compareTo(BigDecimal.ZERO) > 0) {
                BigDecimal unitPrice = detail.getSalesAmount().divide(detail.getQuantity(), 2,
                        BigDecimal.ROUND_HALF_UP);
                exportDTO.setUnitPrice(formatUnitPrice(unitPrice)); // 格式化单价（2位小数）
            }
            exportDTO.setAmount(formatAmount(detail.getSalesAmount())); // 格式化金额（2位小数）

            // 计算税额 - 必须使用真实税率数据
            if (salesAmountEntity.getTaxCode() != null && detail.getSalesAmount() != null) {
                try {
                    // 根据税代码获取真实税率
                    com.hongru.entity.businessOps.TaxRateCalculation taxRateCalc = salesCommonService
                            .getTaxRateByTaxCode(salesAmountEntity.getTaxCode());
                    if (taxRateCalc != null && taxRateCalc.getTaxRate() != null) {
                        BigDecimal taxRate = taxRateCalc.getTaxRate();
                        // 将税率转换为百分比形式进行计算：税额 = 金额 × (税率/100)
                        BigDecimal taxRatePercent = taxRate.divide(new BigDecimal("100"));
                        BigDecimal tax = detail.getSalesAmount().multiply(taxRatePercent);
                        exportDTO.setTax(formatTax(tax)); // 格式化税额（2位小数）
                        exportDTO.setTaxRate(taxRate);
                        exportDTO.setTotalPriceWithTax(formatAmount(detail.getSalesAmount().add(tax))); // 格式化含税总额（2位小数）
                    } else {
                        // 如果找不到税率数据，税额设为0，但记录错误
                        System.err.println("警告：找不到税代码 " + salesAmountEntity.getTaxCode() + " 对应的税率数据");
                        exportDTO.setTax(formatTax(BigDecimal.ZERO)); // 格式化税额（2位小数）
                        exportDTO.setTaxRate(BigDecimal.ZERO);
                        exportDTO.setTotalPriceWithTax(formatAmount(detail.getSalesAmount())); // 格式化含税总额（2位小数）
                    }
                } catch (Exception e) {
                    System.err.println("查询税率失败，税代码：" + salesAmountEntity.getTaxCode() + "，错误：" + e.getMessage());
                    exportDTO.setTax(formatTax(BigDecimal.ZERO)); // 格式化税额（2位小数）
                    exportDTO.setTaxRate(BigDecimal.ZERO);
                    exportDTO.setTotalPriceWithTax(formatAmount(detail.getSalesAmount())); // 格式化含税总额（2位小数）
                }
            } else {
                // 没有税代码或金额，税额为0
                exportDTO.setTax(formatTax(BigDecimal.ZERO)); // 格式化税额（2位小数）
                exportDTO.setTaxRate(BigDecimal.ZERO);
                exportDTO.setTotalPriceWithTax(formatAmount(
                        detail.getSalesAmount() != null ? detail.getSalesAmount() : BigDecimal.ZERO)); // 格式化含税总额（2位小数）
            }

            // TODO: 这里需要根据财年和成本件匹配成本表数据
            // 暂时设置为空，后续需要实现成本表查询逻辑
            exportDTO.setCostCode(null);
            exportDTO.setHumanCost(BigDecimal.ZERO);
            exportDTO.setElectricCost(BigDecimal.ZERO);
            exportDTO.setGasCost(BigDecimal.ZERO);
            exportDTO.setWaterCost(BigDecimal.ZERO);
            exportDTO.setTransportCost(BigDecimal.ZERO);
            exportDTO.setWireDiscCost(BigDecimal.ZERO);
            exportDTO.setTotalCost(BigDecimal.ZERO);

            exportList.add(exportDTO);
        }

        return exportList;
    }

    /**
     * 格式化数量字段（保留3位小数）
     */
    private BigDecimal formatQuantity(BigDecimal value) {
        if (value == null) {
            return null;
        }
        return value.setScale(3, RoundingMode.HALF_UP);
    }

    /**
     * 格式化单价字段（保留2位小数）
     */
    private BigDecimal formatUnitPrice(BigDecimal value) {
        if (value == null) {
            return null;
        }
        return value.setScale(2, RoundingMode.HALF_UP);
    }

    /**
     * 格式化金额字段（保留2位小数）
     */
    private BigDecimal formatAmount(BigDecimal value) {
        if (value == null) {
            return null;
        }
        return value.setScale(2, RoundingMode.HALF_UP);
    }

    /**
     * 格式化税额字段（保留2位小数）
     */
    private BigDecimal formatTax(BigDecimal value) {
        if (value == null) {
            return null;
        }
        return value.setScale(2, RoundingMode.HALF_UP);
    }

    /**
     * 格式化铜单价字段（保留2位小数）
     */
    private BigDecimal formatCopperPrice(BigDecimal value) {
        if (value == null) {
            return null;
        }
        return value.setScale(2, RoundingMode.HALF_UP);
    }
}
