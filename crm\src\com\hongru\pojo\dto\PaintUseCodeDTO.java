package com.hongru.pojo.dto;

import com.hongru.entity.businessOps.PaintUseCode;
import com.hongru.support.page.PageInfo;

import java.util.List;

public class PaintUseCodeDTO {

    private PageInfo pageInfo;

    private List<PaintUseCode> paintUseCodeList;

    public PaintUseCodeDTO(PageInfo pageInfo, List<PaintUseCode> paintUseCodeList) {
        super();
        this.pageInfo = pageInfo;
        this.paintUseCodeList = paintUseCodeList;
    }

    public PageInfo getPageInfo() {
        return pageInfo;
    }

    public void setPageInfo(PageInfo pageInfo) {
        this.pageInfo = pageInfo;
    }

    public List<PaintUseCode> getPaintUseCodeList() {
        return paintUseCodeList;
    }

    public void setPaintUseCodeList(List<PaintUseCode> paintUseCodeList) {
        this.paintUseCodeList = paintUseCodeList;
    }
}
