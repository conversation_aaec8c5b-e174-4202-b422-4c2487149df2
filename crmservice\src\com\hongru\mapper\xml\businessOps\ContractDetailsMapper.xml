<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hongru.mapper.businessOps.ContractDetailsMapper">

	<insert id="insertContractDetails" parameterType="com.hongru.entity.businessOps.ContractDetails">
		INSERT INTO [businessOps].[dbo].[合同方详情表]
		(
		[合同方代码],
		[合同方简称],
		[合同方名称],
		[合同方地址],
		[创建人姓名],
		[创建时间]
		)VALUES(
		#{contractDetails.contractCode},
		#{contractDetails.contractAlias},
		#{contractDetails.contractName},
		#{contractDetails.contractAddress},
		#{contractDetails.creatorName},
		#{contractDetails.createdTime}
		)
	</insert>
	
	<select id="getMaxContractCode" resultType="String">
		SELECT MAX([合同方代码])
		FROM [businessOps].[dbo].[合同方详情表]
	</select>
	
	<select id="listContractDetailsList" resultType="com.hongru.entity.businessOps.ContractDetails">
		SELECT [合同方代码] AS contractCode, [合同方简称] AS contractAlias
		FROM [businessOps].[dbo].[合同方详情表]
	</select>
	
	<select id="selectContractDetailsById" resultType="com.hongru.entity.businessOps.ContractDetails">
		SELECT [流水号] AS id, [合同方代码] AS contractCode, [合同方简称] AS contractAlias,[合同方名称] AS contractName,
			   [合同方地址] AS contractAddress, [创建人姓名] AS creatorName, [创建时间] AS createdTime,[更新人姓名] AS updaterName,
			   [更新时间] AS updatedTime
		FROM [businessOps].[dbo].[合同方详情表]
		<where>
			<if test="id != null">
				AND [流水号] = #{id}
			</if>
		</where>
	</select>
	
	<select id="contractDetailsListByPage" resultType="com.hongru.entity.businessOps.ContractDetails">
		SELECT [流水号] AS id, [合同方代码] AS contractCode, [合同方简称] AS contractAlias,[合同方名称] AS contractName,
			   [合同方地址] AS contractAddress, [创建人姓名] AS creatorName, [创建时间] AS createdTime,[更新人姓名] AS updaterName,
			   [更新时间] AS updatedTime
		FROM [businessOps].[dbo].[合同方详情表]
		<where>
			<if test="contractCode != null and contractCode != ''">
				AND [合同方代码] = #{contractCode}
			</if>
		</where>
		ORDER BY [合同方代码]
		<if test="pageInfo">
			OFFSET ${pageInfo.offset} ROWS
			FETCH NEXT ${pageInfo.limit} ROWS ONLY
		</if>
	</select>
	
	<select id="contractDetailsListByPageCount" resultType="integer">
		SELECT COUNT(1) FROM [businessOps].[dbo].[合同方详情表]
		<where>
			<if test="contractCode != null and contractCode != ''">
				AND [合同方代码] = #{contractCode}
			</if>
		</where>
	</select>
	
	<update id="updateContractDetails">
		UPDATE [businessOps].[dbo].[合同方详情表]
		<set>
			<if test="contractDetails.contractCode != null and contractDetails.contractCode != ''">
				[合同方代码] = #{contractDetails.contractCode},
			</if>
			<if test="contractDetails.contractAlias != null and contractDetails.contractAlias != ''">
				[合同方简称] = #{contractDetails.contractAlias},
			</if>
			<if test="contractDetails.contractName != null and contractDetails.contractName != ''">
				[合同方名称] = #{contractDetails.contractName},
			</if>
			<if test="contractDetails.contractAddress != null and contractDetails.contractAddress != ''">
				[合同方地址] = #{contractDetails.contractAddress},
			</if>
			<if test="contractDetails.updaterName != null and contractDetails.updaterName != ''">
				[更新人姓名] = #{contractDetails.updaterName},
			</if>
			<if test="contractDetails.updatedTime != null">
				[更新时间] = #{contractDetails.updatedTime},
			</if>
		</set>
		WHERE [流水号] = #{ContractDetails.id}
	</update>
	
	<delete id="deleteContractDetailsById">
		DELETE [businessOps].[dbo].[合同方详情表] WHERE [流水号] = #{id}
	</delete>

	<select id="selectByContractCode" resultType="com.hongru.entity.businessOps.ContractDetails">
		SELECT [流水号] AS id, [合同方代码] AS contractCode, [合同方简称] AS contractAlias,[合同方名称] AS contractName,
			   [合同方地址] AS contractAddress, [创建人姓名] AS creatorName, [创建时间] AS createdTime,[更新人姓名] AS updaterName,
			   [更新时间] AS updatedTime
		FROM [businessOps].[dbo].[合同方详情表]
		WHERE [合同方代码] = #{contractCode}
	</select>
</mapper>