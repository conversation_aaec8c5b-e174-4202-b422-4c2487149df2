<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hongru.mapper.businessOps.CopperPricesMapper">
    <sql id="copperPrices_sql">
		pc.[流水号] AS id,pc.[铜条件] AS copperCondition,pc.[适用开始日期] AS applyDateStart,pc.[适用结束日期] AS applyDateEnd,
		pc.[铜基本] AS copperPrice,pc.[创建人姓名] AS creatorName,pc.[创建时间] AS createdTime,
		pc.[更新人姓名] AS updaterName,pc.[更新时间] AS updatedTime
	</sql>

	<insert id="insertCopperPrices" parameterType="com.hongru.entity.businessOps.CopperPrices">
		INSERT INTO [businessOps].[dbo].[铜价表]
		(
		[铜条件],
		[适用开始日期],
		[适用结束日期],
		[铜基本],
		[创建人姓名],
		[创建时间]
		)VALUES(
		#{copperPrices.copperCondition},
		#{copperPrices.applyDateStart},
		#{copperPrices.applyDateEnd},
		#{copperPrices.copperPrice},
		#{copperPrices.creatorName},
		#{copperPrices.createdTime}
		)
	</insert>

	<select id="selectCopperPricesById" resultType="com.hongru.entity.businessOps.CopperPrices">
		SELECT pc.[流水号] AS id,pc.[铜条件] AS copperCondition,pc.[适用开始日期] AS applyDateStart,
			   pc.[适用结束日期] AS applyDateEnd,pc.[铜基本] AS copperPrice,
		       pc.[创建人姓名] AS creatorName,pc.[创建时间] AS createdTime,
		       pc.[更新人姓名] AS updaterName,pc.[更新时间] AS updatedTime,
		       pd.[铜条件名] AS copperConditionName,pd.[货币] AS currency
		FROM [businessOps].[dbo].[铜价表] pc
		LEFT JOIN [businessOps].[dbo].[铜条件表] pd ON pc.[铜条件] = pd.[铜条件]
		<where>
			<if test="id != null">
				AND pc.[流水号] = #{id}
			</if>
		</where>
	</select>

	<select id="copperPricesListByPage" resultType="com.hongru.entity.businessOps.CopperPrices">
		SELECT pc.[流水号] AS id,pc.[铜条件] AS copperCondition,pc.[适用开始日期] AS applyDateStart,
			   pc.[适用结束日期] AS applyDateEnd,pc.[铜基本] AS copperPrice,
		       pc.[创建人姓名] AS creatorName,pc.[创建时间] AS createdTime,
		       pc.[更新人姓名] AS updaterName,pc.[更新时间] AS updatedTime,
		       pd.[铜条件名] AS copperConditionName,pd.[货币] AS currency
		FROM [businessOps].[dbo].[铜价表] pc
		LEFT JOIN [businessOps].[dbo].[铜条件表] pd ON pc.[铜条件] = pd.[铜条件]
		<where>
			<if test="copperCondition != null and copperCondition != ''">
				AND pc.[铜条件] = #{copperCondition}
			</if>
			<if test="applyDateStart != null">
				AND pc.[适用开始日期] &gt;=#{applyDateStart}
			</if>
			<if test="applyDateEnd != null">
				AND pc.[适用结束日期] &lt;= #{applyDateEnd}
			</if>
		</where>
		ORDER BY pc.[流水号] DESC
		<if test="pageInfo">
			OFFSET ${pageInfo.offset} ROWS
			FETCH NEXT ${pageInfo.limit} ROWS ONLY
		</if>
	</select>
	
	<select id="copperPricesListByPageCount" resultType="integer">
		SELECT COUNT(1)
		FROM [businessOps].[dbo].[铜价表] pc
		LEFT JOIN [businessOps].[dbo].[铜条件表] pd ON pc.[铜条件] = pd.[铜条件]
		<where>
			<if test="copperCondition != null and copperCondition != ''">
				AND pc.[铜条件] = #{copperCondition}
			</if>
			<if test="applyDateStart != null">
				AND pc.[适用开始日期] &gt;=#{applyDateStart}
			</if>
			<if test="applyDateEnd != null">
				AND pc.[适用结束日期] &lt;= #{applyDateEnd}
			</if>
		</where>
	</select>

	<update id="updateCopperPrices">
		UPDATE [businessOps].[dbo].[铜价表]
		<set>
			<if test="copperPrices.copperCondition != null and copperPrices.copperCondition != ''">
				[铜条件] = #{copperPrices.copperCondition},
			</if>
			<if test="copperPrices.applyDateStart != null">
				[适用开始日期] = #{copperPrices.applyDateStart},
			</if>
			<if test="copperPrices.applyDateEnd != null">
				[适用结束日期] = #{copperPrices.applyDateEnd},
			</if>
			<if test="copperPrices.copperPrice != null">
				[铜基本] = #{copperPrices.copperPrice},
			</if>
			<if test="copperPrices.updaterName != null and copperPrices.updaterName != ''">
				[更新人姓名] = #{copperPrices.updaterName},
			</if>
			<if test="copperPrices.updatedTime != null">
				[更新时间] = #{copperPrices.updatedTime},
			</if>
		</set>
		WHERE [流水号] = #{copperPrices.id}
	</update>
	
	<delete id="deleteCopperPrices">
		DELETE [businessOps].[dbo].[铜价表] WHERE [流水号] = #{id}
	</delete>

	<!-- 根据铜条件和到货日期查询铜基本 -->
	<select id="getCopperBaseByConditionAndDate" resultType="com.hongru.entity.businessOps.CopperPrices">
		SELECT pc.[流水号] AS id, pc.[铜条件] AS copperCondition, pc.[适用开始日期] AS applyDateStart,
			   pc.[适用结束日期] AS applyDateEnd, pc.[铜基本] AS copperPrice,
			   pc.[创建人姓名] AS creatorName, pc.[创建时间] AS createdTime,
			   pc.[更新人姓名] AS updaterName, pc.[更新时间] AS updatedTime,
			   pd.[铜条件名] AS copperConditionName, pd.[货币] AS currency
		FROM [businessOps].[dbo].[铜价表] pc
		LEFT JOIN [businessOps].[dbo].[铜条件表] pd ON pc.[铜条件] = pd.[铜条件]
		WHERE pc.[铜条件] = #{copperCondition}
		  AND pc.[适用开始日期] &lt;= #{arrivalDate}
		  AND pc.[适用结束日期] &gt;= #{arrivalDate}
		ORDER BY pc.[适用开始日期] DESC
		OFFSET 0 ROWS FETCH NEXT 1 ROWS ONLY
	</select>
</mapper>