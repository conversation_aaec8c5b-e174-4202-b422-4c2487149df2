layui.use(['laydate','upload','element','layer','form'], function() {
    var form = layui.form;
    var $ = layui.jquery
        ,upload = layui.upload
        ,element = layui.element
        ,layer = layui.layer;
     var laydate = layui.laydate;
     
     //开始日期
     var insStart = layui.laydate.render({
         elem: '#applyDateStart'
         ,done: function(value, date){
         	//更新结束日期的最小日期
         	insEnd.config.min = lay.extend({}, date, {
         	  month: date.month - 1
         	});

        	//自动弹出结束日期的选择器
            insEnd.config.elem[0].focus();
         }
       });

     //结束日期
     var insEnd = layui.laydate.render({
         elem: '#applyDateEnd'
         ,done: function(value, date){
         	//更新开始日期的最大日期
         	insStart.config.max = lay.extend({}, date, {
         	  month: date.month - 1
         	});
         }
       }); 
       
       //铜条件选择
       form.on('select(copperConditionFun)', function(data){
           var copperCondition = $("#copperCondition option:checked").val();
           var index = layer.load(2,{
               shade:[0.1,'#fff']
           });
           $.ajax({
               url : baselocation+'/salesCommon/copperConditions/json',
               type : 'post',
               data : {"copperCondition":copperCondition},
               success : function(result) {
                   layer.closeAll();
                   var html ='<option value="">全部</option>';
                   if(result.code == 1){
                       var copperConditions = result.data;
                       $("#copperConditionName").val(copperConditions.copperConditionName)
                       $("#currency").val(copperConditions.currency)
                   }else{
                       layer.alert(result.message);
                   }
                   form.render();//重新渲染
               }
           });
       });
       
    form.on('submit(formDemo)', function(data) {
        var index = layer.load(2, {
            shade : [ 0.1, '#fff' ]
        });
        var actionUrl = $("#submitForm").attr('action');
        $.ajax({
            url : actionUrl,
            type : 'post',
            data : $('#submitForm').serialize(),
            success : function(result) {
                layer.closeAll();
                if (result.code == 1) {
                    parent.layer.msg("操作成功!", {
                        shade : 0.3,
                        time : 1500
                    }, function() {
                        parent.window.location.reload();
                    });
                } else {
                    layer.alert(result.message);
                }
            }
        });
        return false;
    });
});

function closeAll() {
    parent.layer.closeAll();
}
