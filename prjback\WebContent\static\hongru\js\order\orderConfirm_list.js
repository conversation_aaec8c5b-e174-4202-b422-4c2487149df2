layui.use(['laydate', 'layer', 'table', 'element','form'], function(){
    var laydate = layui.laydate //日期
        ,layer = layui.layer //弹层
        ,table = layui.table //表格
        ,element = layui.element //元素操作
    var form = layui.form;

    // 出库开始日期
    var outboundInsStart = layui.laydate.render({
    elem: '#outboundStartDate'
    ,done: function(value, date){
        //更新结束日期的最小日期
        insEnd.config.min = lay.extend({}, date, {
        month: date.month - 1
        });

        //自动弹出结束日期的选择器
        insEnd.config.elem[0].focus();
    }
    });
    
    // 出库结束日期
    var outboundInsEnd = layui.laydate.render({
    elem: '#outboundEndDate'
    ,done: function(value, date){
        //更新开始日期的最大日期
        insStart.config.max = lay.extend({}, date, {
        month: date.month - 1
        });
    }
    });

    //执行一个 table 实例
    var url = baselocation+'/order/orderConfirm/list';
    table.render({
        elem: '#demo'
        ,height: 'full-70'
        ,url: url //数据接口
        ,cellMinWidth: 80 //全局定义常规单元格的最小宽度
        ,parseData:function(res){ //res 即为原始返回的数据
            return {
                "code": 0, //解析接口状态
                "msg": '', //解析提示文本
                "count": res.total, //解析数据长度
                "data": res.rows //解析数据列表
            };
        }
        ,method:'post'
        ,title: '订单确认列表'
        ,page: true //开启分页
        ,limits: [10,20,50,100]
        ,where:$("#formSearch").serializeJsonObject()
        ,toolbar: '#toolbarDemo' //开启工具栏，此处显示默认图标，可以自定义模板，详见文档
        ,defaultToolbar: ['filter']
        ,totalRow: false //开启合计行
        ,cols: [[ //表头
            {type: 'checkbox', fixed: 'left'}
        	,{field: 'zizeng', title: 'NO.', width:50, fixed:'left', type:"numbers", align:'center'}
            ,{field: 'orderNo',title: '订单NO',align:'center'}
            ,{field: 'orderSerialNum',title: '订单序号',align:'center'}
            ,{field: 'customerOrderNo',title: '客户订单号',align:'center'}
            ,{field: 'outboundDate',title: '出库日期',align:'center'}
            ,{field: 'arrivalDate',title: '到货日期',align:'center'}
            ,{field: 'demandCustomerAlias',title: '需求方',align:'center'}
            ,{field: 'contractPartyCustomerAlias',title: '合同方',align:'center'}
            ,{field: 'barcode',title: '条码',align:'center'}
            ,{field: 'modelNumber',title: '产品代码',align:'center'}
            ,{field: 'orderQuantity',title: '订单数量',align:'center'}
            ,{field: 'creatorName',title: '创建者',align:'center'}
            ,{field: 'createdTime',title: '创建时间',align:'center'}
            ,{field: 'updaterName',title: '更新者',align:'center'}
            ,{field: 'updatedTime',title: '更新时间',align:'center'}
            // ,{title: '操作',minWidth:225, align:'left',fixed: 'right', toolbar: '#barDemo'}
        ]]
    });

    //监听头工具栏事件
    table.on('toolbar(test)', function(obj){
        var checkStatus = table.checkStatus(obj.config.id)
            ,data = checkStatus.data; //获取选中的数据
        switch(obj.event){
            case 'confirm':
                console.log('确认按钮点击');
                // 获取所有被勾选的数据
                if(data.length === 0){
                    layer.msg('请选择需要确认的订单', {icon: 0});
                    return;
                }
                
                // 收集所有选中行的订单号和序号
                var orderNoAndSerialNumList = [];
                $.each(data, function(index, item) {
                    orderNoAndSerialNumList.push(item.orderNo + '-' + item.orderSerialNum);
                });

                console.log('orderNoAndSerialNumList :>> ', orderNoAndSerialNumList);

                var orderNoAndSerialNumsStr = orderNoAndSerialNumList.join(',');

                $("#orderNoAndSerialNumsStr").val(orderNoAndSerialNumsStr);
                
                // 确认操作
                layer.confirm('确定要确认选中的' + data.length + '条订单吗？', {
                    btn: ['确定', '取消']
                }, function() {
                    // 发送确认请求
                    $.ajax({
                        type: 'post',
                        url: baselocation + '/order/orderConfirm/confirm',
                        data: {
                            orderNoAndSerialNumsStr: orderNoAndSerialNumsStr
                        },
                        dataType: 'json',
                        success: function(result) {
                            if(result.code == 1) {
                                layer.msg('确认成功!', {
                                    icon: 1,
                                    time: 1500
                                }, function() {
                                    // 刷新表格
                                    search();

                                    // 打开预览页面
                                    layer_show('Excel预览', baselocation + "/order/manufacturingContactForm/preview/view", document.body.clientWidth-10, document.body.clientHeight-10);
                                });
                            } else {
                                layer.alert(result.data || '操作失败，请重试！', {icon: 2});
                            }
                        },
                        error: function() {
                            layer.alert('网络错误，请稍后重试！', {icon: 2});
                        }
                    });
                });
                // layer_show('确认', baselocation+"/order/orderConfirm/confirm/view", document.body.clientWidth-10, document.body.clientHeight-10);
                break;
            case 'refresh':
                var temp = $("#formSearch").serializeJsonObject();
                console.info(temp);
                layui.table.reload('demo', {
                    page: {
                        curr: 1
                    }
                    ,where: temp
                }, 'data');
                break;
            case 'export':
                var temp = $("#formSearch").serializeJsonObject();
                var orderNoAndSerialNumList = [];
                $.each(data, function(index, item) {
                    orderNoAndSerialNumList.push(item.orderNo + '-' + item.orderSerialNum);
                });


                // 打开预览页面
                layer_show('Excel预览', baselocation + "/order/manufacturingContactForm/preview/view", document.body.clientWidth-10, document.body.clientHeight-10);
                break;
        };
    });

    //监听行工具事件
    table.on('tool(test)', function(obj){ //注：tool 是工具条事件名，test 是 table 原始容器的属性 lay-filter="对应的值"
        var data = obj.data //获得当前行数据
            ,layEvent = obj.event; //获得 lay-event 对应的值

        if(layEvent === 'detail'){
            layer_show('详情',baselocation+"/order/orderEntry/detail/view?id="+data.id,document.body.clientWidth-10, document.body.clientHeight-10);
        }else if(layEvent === 'edit'){
            layer_show('编辑',baselocation+"/order/orderEntry/edit/view?id="+data.id,document.body.clientWidth-10, document.body.clientHeight-10);
        }else if(layEvent === 'remove'){
            layer.confirm('确认要删除吗？', {
                btn : [ '确定', '取消' ] //按钮
            }, function() {
                $.ajax({
                    type : 'post',
                    dataType : 'json',
                    data: {"id":data.id},
                    url : baselocation + '/order/orderEntry/remove',
                    success : function(result) {
                        if (result.code == 1) {
                            layer.msg("操作成功!", {
                                shade : 0.3,
                                time : 1500
                            }, function() {
                                search();
                                layer.closeAll();
                            });
                        } else {
                            layer.alert(result.data, {
                                icon : 2
                            });
                        }
                    }
                })
            });
        }
    });
});

function search() {
    // 移除对需求方的强制检查，允许在不选择需求方的情况下也能查询
    // var demandCustomerCode = $('#demandCustomerCode').val();
    // if (!demandCustomerCode) {
    //     layer.msg('请选择需求方后，再进行检索', {icon: 2});
    //     return;
    // }

    var temp = $("#formSearch").serializeJsonObject();
	console.info(temp);
	layui.table.reload('demo', {
		page: {
			curr: 1
		}
		,where: temp
	}, 'data');
}

// 订单附件下载功能
function downloadOrderAttachment(attachmentId) {
    if (!attachmentId || attachmentId.trim() === '') {
        layer.msg('未找到附件', {icon: 2});
        return;
    }

    // 直接打开下载链接
    window.open(baselocation + '/common/download?id=' + attachmentId);

    // 或者使用异步方式下载 - 如果需要先验证权限等操作
    /*
    $.ajax({
        url: baselocation + '/common/checkDownloadPermission',
        type: 'post',
        data: {id: attachmentId},
        success: function(res) {
            if (res.code == 1) {
                window.open(baselocation + '/common/download?id=' + attachmentId);
            } else {
                layer.msg(res.message || '下载失败', {icon: 2});
            }
        },
        error: function() {
            layer.msg('下载请求失败', {icon: 2});
        }
    });
    */
}
