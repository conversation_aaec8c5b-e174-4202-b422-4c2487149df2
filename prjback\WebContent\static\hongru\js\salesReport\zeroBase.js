layui.use(['laydate', 'layer', 'table', 'element', 'form'], function(){
    var laydate = layui.laydate //日期
        ,layer = layui.layer //弹层
        ,table = layui.table //表格
        ,element = layui.element //元素操作
    var form = layui.form;
    
    // 会计年月选择器（开始）
    laydate.render({
        elem: '#startAccountingYearMonth'
        ,type: 'month'
        ,format: 'yyyy-MM'
    });
    
    // 会计年月选择器（结束）
    laydate.render({
        elem: '#endAccountingYearMonth'
        ,type: 'month'
        ,format: 'yyyy-MM'
    });
    
    // 执行一个 table 实例
    var url = baselocation+'/salesReport/zeroBase/list';
    table.render({
        elem: '#demo'
        ,height: 'full-170'
        ,url: url //数据接口
        ,cellMinWidth: 80 //全局定义常规单元格的最小宽度
        ,parseData:function(res){ //res 即为原始返回的数据
            return {
                "code": 0, //解析接口状态
                "msg": '', //解析提示文本
                "count": res.total, //解析数据长度
                "data": res.rows //解析数据列表
            };
        }
        ,method:'post'
        ,title: '0-base报表'
        ,page: false //关闭分页
        ,where:$("#formSearch").serializeJsonObject()
        ,toolbar: '#toolbarDemo' //开启工具栏，此处显示默认图标，可以自定义模板，详见文档
        ,defaultToolbar: ['filter']
        ,totalRow: true //开启合计行
        ,cols: [[ //表头
            {field: 'productMiddleCategory', title: '品目中分类名', align:'center', fixed: 'left'}
            ,{field: 'customerAlias', title: '需求方略称', align:'center', fixed: 'left'}
            ,{field: 'productCode', title: '品名', align:'center'}
            ,{field: 'labelSizeName', title: '尺寸', align:'center'}
            ,{field: 'currency', title: '货币', align:'center'}
            ,{field: 'zeroBaseDirectCost', title: '0base直接成本（单价・功能货币）', align:'right', totalRow: true, templet: function(d){
                if(d.zeroBaseDirectCost != null){
                    return parseFloat(d.zeroBaseDirectCost).toFixed(4);
                }else{
                    return "0.0000";
                }
            }}
            ,{field: 'zeroBaseSalesUnitPrice', title: '销售额0base单价（不含税・功能货币）', align:'right', totalRow: true, templet: function(d){
                if(d.zeroBaseSalesUnitPrice != null){
                    return parseFloat(d.zeroBaseSalesUnitPrice).toFixed(4);
                }else{
                    return "0.0000";
                }
            }}
            ,{field: 'salesQuantity', title: '销售额数', align:'right', totalRow: true, templet: function(d){
                if(d.salesQuantity != null){
                    return parseFloat(d.salesQuantity).toFixed(3);
                }else{
                    return "0.000";
                }
            }}
            ,{field: 'accountingYearMonth', title: '会计年月', align:'center'}
        ]]
    });
    
    //监听头工具栏事件
    table.on('toolbar(test)', function(obj){
        var checkStatus = table.checkStatus(obj.config.id);
        switch(obj.event){
            case 'refresh':
                search();
                break;
        };
    });
});

// 检索
function search(){
    var startAccountingYearMonth = $("#startAccountingYearMonth").val();
    var endAccountingYearMonth = $("#endAccountingYearMonth").val();
    
    layui.table.reload('demo', {
        where: {
            startAccountingYearMonth: startAccountingYearMonth,
            endAccountingYearMonth: endAccountingYearMonth
        }
    });
}

// 导出
function exportData(){
    var startAccountingYearMonth = $("#startAccountingYearMonth").val();
    var endAccountingYearMonth = $("#endAccountingYearMonth").val();
    
    // 构建导出URL
    var exportUrl = baselocation + '/salesReport/zeroBase/export';
    var params = [];
    
    if (startAccountingYearMonth) {
        params.push('startAccountingYearMonth=' + encodeURIComponent(startAccountingYearMonth));
    }
    if (endAccountingYearMonth) {
        params.push('endAccountingYearMonth=' + encodeURIComponent(endAccountingYearMonth));
    }
    
    if (params.length > 0) {
        exportUrl += '?' + params.join('&');
    }
    
    // 创建隐藏的表单进行POST提交
    var form = document.createElement('form');
    form.method = 'POST';
    form.action = baselocation + '/salesReport/zeroBase/export';
    form.style.display = 'none';
    
    if (startAccountingYearMonth) {
        var startInput = document.createElement('input');
        startInput.type = 'hidden';
        startInput.name = 'startAccountingYearMonth';
        startInput.value = startAccountingYearMonth;
        form.appendChild(startInput);
    }
    
    if (endAccountingYearMonth) {
        var endInput = document.createElement('input');
        endInput.type = 'hidden';
        endInput.name = 'endAccountingYearMonth';
        endInput.value = endAccountingYearMonth;
        form.appendChild(endInput);
    }
    
    document.body.appendChild(form);
    form.submit();
    document.body.removeChild(form);
}
