package com.hongru.service.sumitomo;

import com.hongru.entity.sumitomo.Customers;
import com.hongru.entity.sumitomo.Product;

import java.util.List;

public interface ISumitomoService {

    /* ======================客户表====================== */
    /**
     * 客户表详情list
     * 
     * @throws
     * @return java.util.List<com.hongru.entity.sumitomo.Customers>
     */
    List<Customers> listCustomersList() throws Exception;

    /**
     * 根据客户代码获取客户信息
     * 
     * @param customerCode 客户代码
     * @throws
     * @return 客户信息
     */
    Customers getCustomerByCode(String customerCode) throws Exception;

    /* ======================产品表====================== */
    /**
     * 产品表详情list
     * 
     * @param customerCode 客户代码
     * @param printShield  打印屏蔽（1:屏蔽（无效））
     * @throws
     * @return
     */
    List<String> getProductListByCustomerCode(String customerCode, String printShield);

    /**
     * 根据产品代码获取产品信息
     * 
     * @param productCode 产品代码
     * @param printShield 打印屏蔽
     * @throws
     * @return
     */
    Product getProductInfo(String productCode, String printShield);

    /**
     * 获取产品尺寸列表
     * 
     * @param customerCode 客户代码
     * @param productCode  产品代码
     * @param printShield  打印屏蔽
     * @throws
     * @return
     */
    List<String> getProductSizeList(String customerCode, String productCode, String printShield);

    /**
     * 获取产品线盘名称列表
     * 
     * @param customerCode 客户代码
     * @param productCode  产品代码
     * @param size         尺寸
     * @param printShield  打印屏蔽
     * @throws
     * @return
     */
    List<String> getProductWireReelNameList(String customerCode, String productCode, String size, String printShield);
}