package com.hongru.pojo.dto;

import java.math.BigDecimal;
import java.util.List;

import com.hongru.support.page.PageInfo;

/**
 * User-Margin报表DTO
 */
public class UserMarginDTO {
    
    // 基础字段
    private String accountingYearMonth; // 会计年月
    private String siteCode; // 据点C
    private String customerCode; // 需求方C
    private String customerAlias; // 需求方略称
    private String productCode; // 品名
    private String productGroupName; // 品目组名
    private String labelSizeName; // 尺寸
    private String customerGroupName; // 需求方组名
    private String productMiddleCategory; // 品目中分类名
    private String productCategoryCode; // 品目C
    private String orderTypeCategory; // 接单种类分类名
    private String currencyCode; // 货币C
    
    // 成本相关字段
    private BigDecimal transportationCost; // 运费（单价・功能货币）
    private BigDecimal zeroBaseDirectCost; // 0base直接成本（单价・功能货币）
    
    // 销售相关字段
    private BigDecimal zeroBaseSalesUnitPrice; // 销售额0base单价（不含税・功能货币）
    private BigDecimal copperPrice; // 铜价
    private String copperCondition; // 销售额铜条件
    private BigDecimal premiumUnitPrice; // 销售额升水单价（不含税・功能货币）
    private BigDecimal salesQuantity; // 销售额数
    
    // 计算字段
    private BigDecimal zeroBaseDirectCostAmount; // 0base直接成本（金额・功能货币）= 铜价 * 销售额数
    private BigDecimal zeroBaseSalesAmount; // 销售额0base金额（不含税・功能货币）= 销售额0base单价 * 销售额数
    private BigDecimal salesAmount; // 销售额金额（不含税）= (铜价 + 销售额0base单价) * 销售额数
    private BigDecimal zeroBaseProfitAmount; // 0B综利益额 = (销售额0base单价 - 0base直接成本) * 销售额数
    private BigDecimal copperAmount; // 铜金额 = 铜价 * 销售额数
    private BigDecimal filmWeight; // 目付量 = 皮膜重
    private BigDecimal filmWeightAmount; // 目付金额 = 皮膜重 * 销售额数
    
    // 分页信息
    private PageInfo pageInfo;
    
    // 查询结果列表
    private List<UserMarginDTO> userMarginList;
    
    public String getAccountingYearMonth() {
        return accountingYearMonth;
    }
    
    public void setAccountingYearMonth(String accountingYearMonth) {
        this.accountingYearMonth = accountingYearMonth;
    }
    
    public String getSiteCode() {
        return siteCode;
    }
    
    public void setSiteCode(String siteCode) {
        this.siteCode = siteCode;
    }
    
    public String getCustomerCode() {
        return customerCode;
    }
    
    public void setCustomerCode(String customerCode) {
        this.customerCode = customerCode;
    }
    
    public String getCustomerAlias() {
        return customerAlias;
    }
    
    public void setCustomerAlias(String customerAlias) {
        this.customerAlias = customerAlias;
    }
    
    public String getProductCode() {
        return productCode;
    }
    
    public void setProductCode(String productCode) {
        this.productCode = productCode;
    }
    
    public String getProductGroupName() {
        return productGroupName;
    }
    
    public void setProductGroupName(String productGroupName) {
        this.productGroupName = productGroupName;
    }
    
    public String getLabelSizeName() {
        return labelSizeName;
    }
    
    public void setLabelSizeName(String labelSizeName) {
        this.labelSizeName = labelSizeName;
    }
    
    public String getCustomerGroupName() {
        return customerGroupName;
    }
    
    public void setCustomerGroupName(String customerGroupName) {
        this.customerGroupName = customerGroupName;
    }
    
    public String getProductMiddleCategory() {
        return productMiddleCategory;
    }
    
    public void setProductMiddleCategory(String productMiddleCategory) {
        this.productMiddleCategory = productMiddleCategory;
    }
    
    public String getProductCategoryCode() {
        return productCategoryCode;
    }
    
    public void setProductCategoryCode(String productCategoryCode) {
        this.productCategoryCode = productCategoryCode;
    }
    
    public String getOrderTypeCategory() {
        return orderTypeCategory;
    }
    
    public void setOrderTypeCategory(String orderTypeCategory) {
        this.orderTypeCategory = orderTypeCategory;
    }
    
    public String getCurrencyCode() {
        return currencyCode;
    }
    
    public void setCurrencyCode(String currencyCode) {
        this.currencyCode = currencyCode;
    }
    
    public BigDecimal getTransportationCost() {
        return transportationCost;
    }
    
    public void setTransportationCost(BigDecimal transportationCost) {
        this.transportationCost = transportationCost;
    }
    
    public BigDecimal getZeroBaseDirectCost() {
        return zeroBaseDirectCost;
    }
    
    public void setZeroBaseDirectCost(BigDecimal zeroBaseDirectCost) {
        this.zeroBaseDirectCost = zeroBaseDirectCost;
    }
    
    public BigDecimal getZeroBaseSalesUnitPrice() {
        return zeroBaseSalesUnitPrice;
    }
    
    public void setZeroBaseSalesUnitPrice(BigDecimal zeroBaseSalesUnitPrice) {
        this.zeroBaseSalesUnitPrice = zeroBaseSalesUnitPrice;
    }
    
    public BigDecimal getCopperPrice() {
        return copperPrice;
    }
    
    public void setCopperPrice(BigDecimal copperPrice) {
        this.copperPrice = copperPrice;
    }
    
    public String getCopperCondition() {
        return copperCondition;
    }
    
    public void setCopperCondition(String copperCondition) {
        this.copperCondition = copperCondition;
    }
    
    public BigDecimal getPremiumUnitPrice() {
        return premiumUnitPrice;
    }
    
    public void setPremiumUnitPrice(BigDecimal premiumUnitPrice) {
        this.premiumUnitPrice = premiumUnitPrice;
    }
    
    public BigDecimal getSalesQuantity() {
        return salesQuantity;
    }
    
    public void setSalesQuantity(BigDecimal salesQuantity) {
        this.salesQuantity = salesQuantity;
    }
    
    public BigDecimal getZeroBaseDirectCostAmount() {
        return zeroBaseDirectCostAmount;
    }
    
    public void setZeroBaseDirectCostAmount(BigDecimal zeroBaseDirectCostAmount) {
        this.zeroBaseDirectCostAmount = zeroBaseDirectCostAmount;
    }
    
    public BigDecimal getZeroBaseSalesAmount() {
        return zeroBaseSalesAmount;
    }
    
    public void setZeroBaseSalesAmount(BigDecimal zeroBaseSalesAmount) {
        this.zeroBaseSalesAmount = zeroBaseSalesAmount;
    }
    
    public BigDecimal getSalesAmount() {
        return salesAmount;
    }
    
    public void setSalesAmount(BigDecimal salesAmount) {
        this.salesAmount = salesAmount;
    }
    
    public BigDecimal getZeroBaseProfitAmount() {
        return zeroBaseProfitAmount;
    }
    
    public void setZeroBaseProfitAmount(BigDecimal zeroBaseProfitAmount) {
        this.zeroBaseProfitAmount = zeroBaseProfitAmount;
    }
    
    public BigDecimal getCopperAmount() {
        return copperAmount;
    }
    
    public void setCopperAmount(BigDecimal copperAmount) {
        this.copperAmount = copperAmount;
    }
    
    public BigDecimal getFilmWeight() {
        return filmWeight;
    }
    
    public void setFilmWeight(BigDecimal filmWeight) {
        this.filmWeight = filmWeight;
    }
    
    public BigDecimal getFilmWeightAmount() {
        return filmWeightAmount;
    }
    
    public void setFilmWeightAmount(BigDecimal filmWeightAmount) {
        this.filmWeightAmount = filmWeightAmount;
    }
    
    public PageInfo getPageInfo() {
        return pageInfo;
    }
    
    public void setPageInfo(PageInfo pageInfo) {
        this.pageInfo = pageInfo;
    }
    
    public List<UserMarginDTO> getUserMarginList() {
        return userMarginList;
    }
    
    public void setUserMarginList(List<UserMarginDTO> userMarginList) {
        this.userMarginList = userMarginList;
    }
}
