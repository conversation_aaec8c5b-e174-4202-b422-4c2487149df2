package com.hongru.pojo.dto;

import java.math.BigDecimal;

/**
 * 销售额登录列表DTO
 */
public class SalesLoginListDTO {
    
    /* 流水号 */
    private int id;
    /* 销售额NO */
    private String salesNo;
    /* 销售额日 */
    private String salesDate;
    /* 客户代码 */
    private String customerCode;
    /* 客户简称 */
    private String customerAlias;
    /* 结算货币 */
    private String currency;
    /* 销售金额 */
    private BigDecimal salesAmount;
    /* 税代码 */
    private String taxCode;
    /* 税率 */
    private BigDecimal taxRate;
    /* 创建者 */
    private String creatorName;
    /* 创建时间 */
    private String createdTime;
    /* 更新者 */
    private String updaterName;
    /* 更新时间 */
    private String updatedTime;
    /* 销售额确认状态 */
    private String salesAmountConfirm;
    /* 支付请求确认状态 */
    private String paymentRequestConfirm;

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getSalesNo() {
        return salesNo;
    }

    public void setSalesNo(String salesNo) {
        this.salesNo = salesNo;
    }

    public String getSalesDate() {
        return salesDate;
    }

    public void setSalesDate(String salesDate) {
        this.salesDate = salesDate;
    }

    public String getCustomerCode() {
        return customerCode;
    }

    public void setCustomerCode(String customerCode) {
        this.customerCode = customerCode;
    }

    public String getCustomerAlias() {
        return customerAlias;
    }

    public void setCustomerAlias(String customerAlias) {
        this.customerAlias = customerAlias;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public BigDecimal getSalesAmount() {
        return salesAmount;
    }

    public void setSalesAmount(BigDecimal salesAmount) {
        this.salesAmount = salesAmount;
    }

    public String getTaxCode() {
        return taxCode;
    }

    public void setTaxCode(String taxCode) {
        this.taxCode = taxCode;
    }

    public BigDecimal getTaxRate() {
        return taxRate;
    }

    public void setTaxRate(BigDecimal taxRate) {
        this.taxRate = taxRate;
    }

    public String getCreatorName() {
        return creatorName;
    }

    public void setCreatorName(String creatorName) {
        this.creatorName = creatorName;
    }

    public String getCreatedTime() {
        return createdTime;
    }

    public void setCreatedTime(String createdTime) {
        this.createdTime = createdTime;
    }

    public String getUpdaterName() {
        return updaterName;
    }

    public void setUpdaterName(String updaterName) {
        this.updaterName = updaterName;
    }

    public String getUpdatedTime() {
        return updatedTime;
    }

    public void setUpdatedTime(String updatedTime) {
        this.updatedTime = updatedTime;
    }

    public String getSalesAmountConfirm() {
        return salesAmountConfirm;
    }

    public void setSalesAmountConfirm(String salesAmountConfirm) {
        this.salesAmountConfirm = salesAmountConfirm;
    }

    public String getPaymentRequestConfirm() {
        return paymentRequestConfirm;
    }

    public void setPaymentRequestConfirm(String paymentRequestConfirm) {
        this.paymentRequestConfirm = paymentRequestConfirm;
    }
}
