package com.hongru.mapper.businessOps;

import com.baomidou.mybatisplus.mapper.BaseMapper;
import com.hongru.entity.businessOps.CustomerDetails;
import com.hongru.entity.businessOps.OrderEntryDetails;
import com.hongru.entity.businessOps.ShipmentQuantityInfo;
import com.hongru.entity.sumitomo.Customers;
import com.hongru.support.page.PageInfo;

import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

public interface OrderMapper extends BaseMapper<OrderEntryDetails> {
    /**
     * 订单详情列表
     * @param pageInfo 分页信息
     * @param orderStartDate 接单日期开始
     * @param orderEndDate 接单日期结束
     * @param prepareStartDate 准备日期开始
     * @param prepareEndDate 准备日期结束
     * @param outboundStartDate 出库日期开始
     * @param outboundEndDate 出库日期结束
     * @param demandCustomerCode 需求方
     * @param contractPartyCustomerCode 合同方
     * @param orderType 订单类型
     * @param status 订单状态
     * @param userName 用户名
     * @return
     */
    List<OrderEntryDetails> orderEntryDetailsListByPage(@Param("pageInfo") PageInfo pageInfo, @Param("orderStartDate") String orderStartDate, @Param("orderEndDate") String orderEndDate, @Param("prepareStartDate") String prepareStartDate, @Param("prepareEndDate") String prepareEndDate, @Param("outboundStartDate") String outboundStartDate, @Param("outboundEndDate") String outboundEndDate, @Param("demandCustomerCode") String demandCustomerCode, @Param("contractPartyCustomerCode") String contractPartyCustomerCode, @Param("orderType") String orderType, @Param("status") String status, @Param("userName") String userName);
    
    /**
     * 订单详情列表Count
     * @param orderStartDate 接单日期开始
     * @param orderEndDate 接单日期结束
     * @param prepareStartDate 准备日期开始
     * @param prepareEndDate 准备日期结束
     * @param outboundStartDate 出库日期开始
     * @param outboundEndDate 出库日期结束
     * @param demandCustomerCode 需求方
     * @param contractPartyCustomerCode 合同方
     * @param orderType 订单类型
     * @param status 订单状态
     * @param userName 用户名
     * @return
     */
    Integer orderEntryDetailsListByPageCount(@Param("orderStartDate") String orderStartDate, @Param("orderEndDate") String orderEndDate, @Param("prepareStartDate") String prepareStartDate, @Param("prepareEndDate") String prepareEndDate, @Param("outboundStartDate") String outboundStartDate, @Param("outboundEndDate") String outboundEndDate, @Param("demandCustomerCode") String demandCustomerCode, @Param("contractPartyCustomerCode") String contractPartyCustomerCode, @Param("orderType") String orderType, @Param("status") String status, @Param("userName") String userName);


    /**
     * 订单详情列表导出
     * @param pageInfo 分页信息
     * @param orderStartDate 接单日期开始
     * @param orderEndDate 接单日期结束
     * @param prepareStartDate 准备日期开始
     * @param prepareEndDate 准备日期结束
     * @param outboundStartDate 出库日期开始
     * @param outboundEndDate 出库日期结束
     * @param demandCustomerCode 需求方
     * @param contractPartyCustomerCode 合同方
     * @param orderType 订单类型
     * @param status 订单状态
     * @param planStatus 出货计划状态
     * @param userName 用户名
     * @return
     */
    List<OrderEntryDetails> orderEntryDetailsListByPageForExport(@Param("pageInfo") PageInfo pageInfo, @Param("orderStartDate") String orderStartDate, @Param("orderEndDate") String orderEndDate, @Param("prepareStartDate") String prepareStartDate, @Param("prepareEndDate") String prepareEndDate, @Param("outboundStartDate") String outboundStartDate, @Param("outboundEndDate") String outboundEndDate, @Param("demandCustomerCode") String demandCustomerCode, @Param("contractPartyCustomerCode") String contractPartyCustomerCode, @Param("orderType") String orderType, @Param("status") String status, @Param("planStatus") String planStatus, @Param("userName") String userName);

    /**
     * 订单详情列表导出Count
     * @param orderStartDate 接单日期开始
     * @param orderEndDate 接单日期结束
     * @param prepareStartDate 准备日期开始
     * @param prepareEndDate 准备日期结束
     * @param outboundStartDate 出库日期开始
     * @param outboundEndDate 出库日期结束
     * @param demandCustomerCode 需求方
     * @param contractPartyCustomerCode 合同方
     * @param orderType 订单类型
     * @param status 订单状态
     * @param planStatus 出货计划状态
     * @param userName 用户名
     * @return
     */
    Integer orderEntryDetailsListByPageCountForExport(@Param("orderStartDate") String orderStartDate, @Param("orderEndDate") String orderEndDate, @Param("prepareStartDate") String prepareStartDate, @Param("prepareEndDate") String prepareEndDate, @Param("outboundStartDate") String outboundStartDate, @Param("outboundEndDate") String outboundEndDate, @Param("demandCustomerCode") String demandCustomerCode, @Param("contractPartyCustomerCode") String contractPartyCustomerCode, @Param("orderType") String orderType, @Param("status") String status, @Param("planStatus") String planStatus, @Param("userName") String userName);

    /**
     * 根据订单号前缀获取最大订单号
     * @param orderNoPrefix 订单号前缀 (如：D250423)
     * @return
     */
    String getMaxOrderNoByOrderNoPrefix(@Param("orderNoPrefix") String orderNoPrefix);

    /**
     * 订单详情
     * @param id 订单详情id
     * @param status 订单状态
     * @return
     */
    List<OrderEntryDetails> orderEntryDetailsById(@Param("id") Integer id, @Param("status") String status);

    /**
     * 根据订单号获取订单详情
     * @param orderNo 订单号
     * @return
     */
    List<OrderEntryDetails> orderEntryDetailsByOrderNo(@Param("orderNo") String orderNo);

    /**
     * 获取合同方信息列表
     * @return
     */
    List<Customers> getContractPartyCustomerList();

    /**
     * 新增订单信息
     * @param orderEntryInfo 订单信息
     * @return
     */
    int insertOrderEntryInfo(@Param("orderEntryInfo") OrderEntryDetails orderEntryInfo);

    /**
     * 批量新增订单详情
     * @param orderDetailsList 订单详情列表
     * @return
     */
    int batchInsertOrderEntryDetails(@Param("orderDetailsList") List<OrderEntryDetails> orderDetailsList);

    /**
     * 更新订单信息
     * @param orderEntryInfo 订单信息
     * @return
     */ 
    void updateOrderEntryInfo(@Param("orderEntryInfo") OrderEntryDetails orderEntryInfo);

    /**
     * 更新订单详情信息
     * @param orderEntryDetails 订单详情信息
     * @return
     */
    void updateOrderEntryDetail(@Param("orderEntryDetails") OrderEntryDetails orderEntryDetails);

    /**
     * 删除订单信息（更新状态等于9）
     * @param id 订单信息id
     * @return
     */
    void updateOrderEntryInfoStatus(@Param("id") Integer id);

    /**
     * 删除订单详情信息（更新状态等于9）
     * @param orderNo 订单号
     * @param orderSerialNum 订单序号
     * @return
     */
    void updateOrderEntryDetailStatus(@Param("orderNo") String orderNo, @Param("orderSerialNum") String orderSerialNum);

    /**
     * 更新订单详情已送数量信息
     * @param orderEntryInfo 订单信息
     * @return
     */
    void updateOrderEntryDetailDeliveredQuantity(@Param("orderEntryInfo") OrderEntryDetails orderEntryInfo);

    /**
     * 订单确认列表
     * 
     * @param pageInfo                  分页信息
     * @param outboundStartDate         出库日期开始
     * @param outboundEndDate           出库日期结束
     * @param demandCustomerCode        需求方
     * @param contractPartyCustomerCode 合同方
     * @param orderNoList               订单号列表
     * @param userName                  用户名
     * @return
     */
    List<OrderEntryDetails> queryOrderConfirmListByPage(@Param("pageInfo") PageInfo pageInfo,
            @Param("outboundStartDate") String outboundStartDate, @Param("outboundEndDate") String outboundEndDate,
            @Param("demandCustomerCode") String demandCustomerCode,
            @Param("contractPartyCustomerCode") String contractPartyCustomerCode,
            @Param("orderNoList") List<String> orderNoList, @Param("userName") String userName);

    /**
     * 订单确认列表Count
     * 
     * @param outboundStartDate         出库日期开始
     * @param outboundEndDate           出库日期结束
     * @param demandCustomerCode        需求方
     * @param contractPartyCustomerCode 合同方
     * @param orderNoList               订单号列表
     * @param userName                  用户名
     * @return
     */
    Integer queryOrderConfirmListByPageCount(@Param("outboundStartDate") String outboundStartDate,
            @Param("outboundEndDate") String outboundEndDate, @Param("demandCustomerCode") String demandCustomerCode,
            @Param("contractPartyCustomerCode") String contractPartyCustomerCode,
            @Param("orderNoList") List<String> orderNoList, @Param("userName") String userName);

    /**
     * 更新订单确认状态
     * @param orderNoAndSerialNumsList 订单号及订单序号拼接列表 （如：[D250423-1, D250423-2]）
     * @return
     */
    void updateOrderConfirm(@Param("orderNoAndSerialNumsList") List<String> orderNoAndSerialNumsList);

    /**
     * 出库计划录入列表
     * @param pageInfo 分页信息
     * @param outboundStartDate 出库开始日期
     * @param outboundEndDate 出库结束日期
     * @param demandCustomerCode 需求方
     * @param contractPartyCustomerCode 合同方
     * @param planStatus 出货计划状态(null: 未登录计划, 0: 待确认, 1: 已确认)
     * @param userName 用户名
     * @return
     */
    List<OrderEntryDetails> queryShipmentPlanEntryListByPage(@Param("pageInfo") PageInfo pageInfo, @Param("outboundStartDate") String outboundStartDate, @Param("outboundEndDate") String outboundEndDate, @Param("demandCustomerCode") String demandCustomerCode, @Param("contractPartyCustomerCode") String contractPartyCustomerCode, @Param("planStatus") String planStatus, @Param("userName") String userName);

    /**
     * 出库计划录入列表Count
     * @param outboundStartDate 出库开始日期
     * @param outboundEndDate 出库结束日期
     * @param demandCustomerCode 需求方
     * @param contractPartyCustomerCode 合同方
     * @param planStatus 出货计划状态(null: 未登录计划, 0: 待确认, 1: 已确认)
     * @param userName 用户名
     * @return
     */
    Integer queryShipmentPlanEntryListByPageCount(@Param("outboundStartDate") String outboundStartDate, @Param("outboundEndDate") String outboundEndDate, @Param("demandCustomerCode") String demandCustomerCode, @Param("contractPartyCustomerCode") String contractPartyCustomerCode, @Param("planStatus") String planStatus, @Param("userName") String userName);

    /**
     * 出库计划录入详情列表
     * @param outboundDate 出库日期
     * @param demandCustomerCode 需求方
     * @param contractPartyCustomerCode 合同方
     * @param confirmDataParamList 出库日期-需求方-合同方拼接列表（例：[2025-05-14-001-002, 2025-05-14-001-003]）
     * @param planStatus 出货计划状态(null: 未登录计划, 0: 待确认, 1: 已确认)
     * @return
     */
    List<OrderEntryDetails> queryShipmentPlanEntryDetailList(@Param("outboundDate") String outboundDate, @Param("demandCustomerCode") String demandCustomerCode, @Param("contractPartyCustomerCode") String contractPartyCustomerCode, @Param("confirmDataParamList") List<String> confirmDataParamList, @Param("planStatus") String planStatus);

    /**
     * 根据出货计划番号前缀获取最大出货计划番号
     * @param planNumberPrefix 出货计划番号前缀 (如：250423)
     * @return
     */
    String getMaxPlanNumberByplanNumberPrefix(@Param("planNumberPrefix") String planNumberPrefix);

    /**
     * 更新出货计划
     * @param orderNoAndSerialNumsList 订单号及订单序号拼接列表 （如：[D250423-1, D250423-2]）
     * @param planStatus 出货计划状态(0: 待确认, 1: 已确认)
     * @param planNumber 出货计划番号
     * @return
     */
    void updateShipmentPlanEntryDetails(@Param("orderNoAndSerialNumsList") List<String> orderNoAndSerialNumsList, @Param("planStatus") String planStatus, @Param("planNumber") String planNumber);

    /**
     * 查询出库数量列表
     * @param pageInfo 分页信息
     * @param outboundStartDate 出库开始日期
     * @param outboundEndDate 出库结束日期
     * @param customerCode 客户代码
     * @param userName 用户名
     * @return
     */
    List<ShipmentQuantityInfo> queryShipmentQuantityEntryListByPage(@Param("pageInfo") PageInfo pageInfo, @Param("outboundStartDate") String outboundStartDate, @Param("outboundEndDate") String outboundEndDate, @Param("customerCode") String customerCode, @Param("userName") String userName);
        
    /**
     * 查询出库数量列表Count
     * @param outboundStartDate 出库开始日期
     * @param outboundEndDate 出库结束日期
     * @param customerCode 客户代码
     * @param userName 用户名
     * @return
     */
    Integer queryShipmentQuantityEntryListByPageCount(@Param("outboundStartDate") String outboundStartDate, @Param("outboundEndDate") String outboundEndDate, @Param("customerCode") String customerCode, @Param("userName") String userName);

    /**
     * 查询出库数量详情列表
     * @param queryParamList 出库日期-产品代码-客户订单号集合拼接字符串列表（例：[2025-05-14-001-002, 2025-05-14-001-003]）
     * @return
     */
    List<ShipmentQuantityInfo> queryShipmentQuantityEntryDetailList(@Param("queryParamList") List<String> queryParamList);

    /**
     * 查询出库数量详情
     * @param id 流水号
     * @return
     */
    ShipmentQuantityInfo queryShipmentQuantityEntryDetail(@Param("id") String id);

    /**
     * 查询出库信息列表（用于新增出库数量）
     * 
     * @param pageInfo                                      分页信息
     * @param outboundStartDate                             出库开始日期
     * @param outboundEndDate                               出库结束日期
     * @param customerCode                                  客户代码
     * @param userName                                      用户名
     * @param outboundDateAndCustomerCodeAndProductCodeList 到货日期-客户编号-产品编号集合拼接字符串（例：2025-04-23-001-002,2025-04-23-003-006）
     * @return
     */
    List<ShipmentQuantityInfo> queryShipmentQuantityEntryListByPageForAdd(@Param("pageInfo") PageInfo pageInfo,
            @Param("outboundStartDate") String outboundStartDate, @Param("outboundEndDate") String outboundEndDate,
            @Param("customerCode") String customerCode, @Param("userName") String userName,
            @Param("outboundDateAndCustomerCodeAndProductCodeList") List<String> outboundDateAndCustomerCodeAndProductCodeList);

    /**
     * 查询出库信息列表（用于新增出库数量）Count
     * 
     * @param outboundStartDate                             出库开始日期
     * @param outboundEndDate                               出库结束日期
     * @param customerCode                                  客户代码
     * @param userName                                      用户名
     * @param outboundDateAndCustomerCodeAndProductCodeList 到货日期-客户编号-产品编号集合拼接字符串（例：2025-04-23-001-002,2025-04-23-003-006）
     * @return
     */
    Integer queryShipmentQuantityEntryListByPageForAddCount(@Param("outboundStartDate") String outboundStartDate,
            @Param("outboundEndDate") String outboundEndDate, @Param("customerCode") String customerCode,
            @Param("userName") String userName,
            @Param("outboundDateAndCustomerCodeAndProductCodeList") List<String> outboundDateAndCustomerCodeAndProductCodeList);

    /**
     * 查询出货计划番号列表
     * @param queryParamList 出库日期-产品代码-客户订单号拼接列表（例：[2025-05-14-001-002, 2025-05-14-001-003]）
     * @return
     */
    List<ShipmentQuantityInfo> queryShipmentPlanNoList(@Param("queryParamList") List<String> queryParamList);

    /**
     * 批量新增出库数量信息
     * @param shipmentQuantityInfoList 出库数量信息列表
     * @return
     */
    void batchAddShipmentQuantityEntryList(@Param("shipmentQuantityInfoList") List<ShipmentQuantityInfo> shipmentQuantityInfoList);

    /**
     * 更新出库数量信息
     * @param id 流水号
     * @param shipmentQuantity 出库数量
     * @param shipmentPlanNo 出库计划番号
     * @return
     */
    void updateShipmentQuantityEntry(@Param("id") Integer id, @Param("shipmentQuantity") String shipmentQuantity, @Param("shipmentPlanNo") String shipmentPlanNo);

    /**
     * 删除出库数量信息
     * @param id 流水号
     * @return
     */
    void deleteShipmentQuantityEntry(@Param("id") Integer id);

    /**
     * 根据客户代码查询产品信息
     * 
     * @param customerCode 客户代码
     * @return
     */
    List<String> getProductListByCustomerCode(@Param("customerCode") String customerCode);

    /**
     * 根据客户代码和铜合同类别查询铜合同列表
     * 
     * @param customerCode       客户代码
     * @param copperContractType 铜合同类别
     * @return
     */
    List<String> getCopperContractList(@Param("customerCode") String customerCode,
                    @Param("copperContractType") String copperContractType);

    /**
     * 根据铜合同No查询铜合同详情
     * 
     * @param copperContractNo 铜合同No
     * @return
     */
    OrderEntryDetails getCopperContractDetail(@Param("copperContractNo") String copperContractNo);

    /**
     * 查询所有铜条件列表
     *
     * @return
     */
    List<String> getCopperConditionList();

    /**
     * 查询所有铜条件列表（包含货币信息）
     *
     * @return
     */
    List<Map<String, Object>> getCopperConditionListWithCurrency();

}