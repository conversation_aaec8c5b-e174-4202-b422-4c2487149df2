package com.hongru.pojo.dto;

import com.hongru.entity.businessOps.DirectRecycling;
import com.hongru.support.page.PageInfo;

import java.util.List;

public class DirectRecyclingDTO {

    private PageInfo pageInfo;

    private List<DirectRecycling> directRecyclings;

    public PageInfo getPageInfo() {
        return pageInfo;
    }

    public void setPageInfo(PageInfo pageInfo) {
        this.pageInfo = pageInfo;
    }

    public List<DirectRecycling> getDirectRecyclings() {
        return directRecyclings;
    }

    public void setDirectRecyclings(List<DirectRecycling> directRecyclings) {
        this.directRecyclings = directRecyclings;
    }

    public DirectRecyclingDTO(PageInfo pageInfo, List<DirectRecycling> directRecyclings) {
        this.pageInfo = pageInfo;
        this.directRecyclings = directRecyclings;
    }
}
