package com.hongru.mapper.businessOps;

import com.baomidou.mybatisplus.mapper.BaseMapper;
import com.hongru.entity.businessOps.CopperPrices;
import com.hongru.support.page.PageInfo;

import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface CopperPricesMapper extends BaseMapper<CopperPrices> {
	int insertCopperPrices(@Param("copperPrices") CopperPrices copperPrices);
  
	CopperPrices selectCopperPricesById(@Param("id") int id);

    List<CopperPrices> copperPricesListByPage(@Param("pageInfo") PageInfo pageInfo, @Param("copperCondition") String copperCondition, @Param("applyDateStart") String applyDateStart, @Param("applyDateEnd") String applyDateEnd);
    Integer copperPricesListByPageCount(@Param("copperCondition") String copperCondition, @Param("applyDateStart") String applyDateStart, @Param("applyDateEnd") String applyDateEnd);

    
    void updateCopperPrices(@Param("copperPrices") CopperPrices copperPrices);

    void deleteCopperPrices(@Param("id") Integer id);

    /**
     * 根据铜条件和到货日期查询铜基本
     * 
     * @param copperCondition 铜条件
     * @param arrivalDate     到货日期
     * @return CopperPrices
     */
    CopperPrices getCopperBaseByConditionAndDate(@Param("copperCondition") String copperCondition,
            @Param("arrivalDate") String arrivalDate);

}
