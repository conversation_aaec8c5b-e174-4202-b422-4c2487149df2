package com.hongru.entity.businessOps;

import java.math.BigDecimal;

import com.baomidou.mybatisplus.annotations.TableField;
import com.baomidou.mybatisplus.annotations.TableId;
import com.baomidou.mybatisplus.annotations.TableName;
import com.baomidou.mybatisplus.enums.IdType;
import com.fasterxml.jackson.annotation.JsonProperty;

@TableName("支付额明细表")
public class PaymentAmountDetail {
    /* 流水号 */
    @TableId(value = "支付额明细ID", type = IdType.AUTO)
    protected int id;
    /* 支付请求NO */
    @TableField("支付请求NO")
    protected String paymentRequestNo;
    /* 销售额NO */
    @TableField("销售额NO")
    @JsonProperty("salesAmountNo")
    protected String salesAmountNo;
    /* 销售额NO序号 */
    @TableField("销售额NO序号")
    @JsonProperty("salesAmountSeq")
    protected String salesAmountSeq;
    /* 产品代码 */
    @TableField("产品代码")
    @JsonProperty("productCode")
    protected String productCode;
    /* 出货日 */
    @TableField("出货日")
    protected String shipmentDate;
    /* 到货日 */
    @TableField("到货日")
    protected String arrivalDate;
    /* 客户订单号 */
    @TableField("客户订单号")
    protected String customerOrderNo;
    /* 明细备注 */
    @TableField("明细备注")
    protected String detailRemark;
    /* 铜合同类别 */
    @TableField("铜合同类别")
    protected String copperContractType;
    /* 铜合同NO */
    @TableField("铜合同NO")
    protected String copperContractNo;
    /* 铜条件 */
    @TableField("铜条件")
    protected String copperCondition;
    /* 换算率 */
    @TableField("换算率")
    @JsonProperty("conversionRate")
    protected BigDecimal conversionRate;
    /* 数量 */
    @TableField("数量")
    @JsonProperty("quantity")
    protected BigDecimal quantity;
    /* 铜base */
    @TableField("铜base")
    @JsonProperty("copperBase")
    protected BigDecimal copperBase;
    /* 升水 */
    @TableField("升水")
    @JsonProperty("premium")
    protected BigDecimal premium;
    /* 换算后铜单价 */
    @TableField("换算后铜单价")
    @JsonProperty("convertedCopperPrice")
    protected BigDecimal convertedCopperPrice;
    /* 0base */
    @TableField("零基础")
    @JsonProperty("zeroBase")
    protected BigDecimal zeroBase;
    /* 支付单价 */
    @TableField("支付单价")
    @JsonProperty("paymentUnitPrice")
    protected BigDecimal paymentUnitPrice;
    /* 请求金额 */
    @TableField("请求金额")
    @JsonProperty("requestAmount")
    protected BigDecimal requestAmount;
    /* 产品中分类 */
    @TableField("产品中分类")
    protected String productMiddleCategory;

    /* 客户代码 */
    @TableField(exist = false)
    protected String customerCode;
    /* 出货计划番号 */
    @TableField(exist = false)
    @JsonProperty("shipmentPlanNo")
    protected String shipmentPlanNo;
    /* 铜货币 */
    @TableField(exist = false)
    protected String copperCurrency;
    /* 线盘 */
    @TableField("线盘")
    @JsonProperty("wireDrum")
    protected String wireDrum;
    /* 线盘名称 */
    @TableField(exist = false)
    protected String reelName;
    /* 货币 */
    @TableField(exist = false)
    protected String currency;

    /* 创建人姓名 */
    @TableField("创建人")
    protected String creatorName;
    /* 创建时间 */
    @TableField("创建时间")
    protected String createdTime;
    /* 更新人姓名 */
    @TableField("更新人")
    protected String updaterName;
    /* 更新时间 */
    @TableField("更新时间")
    protected String updatedTime;
    /* 出货单No. */
    @TableField("出货单No.")
    @JsonProperty("deliveryNoteNo")
    protected String deliveryNoteNo;
    /* 送货单序号 */
    @TableField("送货单序号")
    @JsonProperty("deliveryNoteSeq")
    protected String deliveryNoteSeq;
    /* 明细内部备注 (实际使用明细备注字段) */
    @TableField(exist = false)
    protected String internalRemark;

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getPaymentRequestNo() {
        return paymentRequestNo;
    }

    public void setPaymentRequestNo(String paymentRequestNo) {
        this.paymentRequestNo = paymentRequestNo;
    }

    public String getSalesAmountNo() {
        return salesAmountNo;
    }

    public void setSalesAmountNo(String salesAmountNo) {
        this.salesAmountNo = salesAmountNo;
    }

    public String getSalesAmountSeq() {
        return salesAmountSeq;
    }

    public void setSalesAmountSeq(String salesAmountSeq) {
        this.salesAmountSeq = salesAmountSeq;
    }

    public String getProductCode() {
        return productCode;
    }

    public void setProductCode(String productCode) {
        this.productCode = productCode;
    }

    public String getShipmentDate() {
        return shipmentDate;
    }

    public void setShipmentDate(String shipmentDate) {
        this.shipmentDate = shipmentDate;
    }

    public String getArrivalDate() {
        return arrivalDate;
    }

    public void setArrivalDate(String arrivalDate) {
        this.arrivalDate = arrivalDate;
    }

    public String getCustomerOrderNo() {
        return customerOrderNo;
    }

    public void setCustomerOrderNo(String customerOrderNo) {
        this.customerOrderNo = customerOrderNo;
    }

    public String getDetailRemark() {
        return detailRemark;
    }

    public void setDetailRemark(String detailRemark) {
        this.detailRemark = detailRemark;
    }

    public String getCopperContractType() {
        return copperContractType;
    }

    public void setCopperContractType(String copperContractType) {
        this.copperContractType = copperContractType;
    }

    public String getCopperContractNo() {
        return copperContractNo;
    }

    public void setCopperContractNo(String copperContractNo) {
        this.copperContractNo = copperContractNo;
    }

    public String getCopperCondition() {
        return copperCondition;
    }

    public void setCopperCondition(String copperCondition) {
        this.copperCondition = copperCondition;
    }

    public BigDecimal getConversionRate() {
        return conversionRate;
    }

    public void setConversionRate(BigDecimal conversionRate) {
        this.conversionRate = conversionRate;
    }

    public BigDecimal getQuantity() {
        return quantity;
    }

    public void setQuantity(BigDecimal quantity) {
        this.quantity = quantity;
    }

    public BigDecimal getCopperBase() {
        return copperBase;
    }

    public void setCopperBase(BigDecimal copperBase) {
        this.copperBase = copperBase;
    }

    public BigDecimal getPremium() {
        return premium;
    }

    public void setPremium(BigDecimal premium) {
        this.premium = premium;
    }

    public BigDecimal getConvertedCopperPrice() {
        return convertedCopperPrice;
    }

    public void setConvertedCopperPrice(BigDecimal convertedCopperPrice) {
        this.convertedCopperPrice = convertedCopperPrice;
    }

    public BigDecimal getZeroBase() {
        return zeroBase;
    }

    public void setZeroBase(BigDecimal zeroBase) {
        this.zeroBase = zeroBase;
    }

    public BigDecimal getPaymentUnitPrice() {
        return paymentUnitPrice;
    }

    public void setPaymentUnitPrice(BigDecimal paymentUnitPrice) {
        this.paymentUnitPrice = paymentUnitPrice;
    }

    public BigDecimal getRequestAmount() {
        return requestAmount;
    }

    public void setRequestAmount(BigDecimal requestAmount) {
        this.requestAmount = requestAmount;
    }

    public String getProductMiddleCategory() {
        return productMiddleCategory;
    }

    public void setProductMiddleCategory(String productMiddleCategory) {
        this.productMiddleCategory = productMiddleCategory;
    }

    public String getCreatorName() {
        return creatorName;
    }

    public void setCreatorName(String creatorName) {
        this.creatorName = creatorName;
    }

    public String getCreatedTime() {
        return createdTime;
    }

    public void setCreatedTime(String createdTime) {
        this.createdTime = createdTime;
    }

    public String getUpdaterName() {
        return updaterName;
    }

    public void setUpdaterName(String updaterName) {
        this.updaterName = updaterName;
    }

    public String getUpdatedTime() {
        return updatedTime;
    }

    public void setUpdatedTime(String updatedTime) {
        this.updatedTime = updatedTime;
    }

    // 新增字段的 getter 和 setter 方法
    public String getDeliveryNoteNo() {
        return deliveryNoteNo;
    }

    public void setDeliveryNoteNo(String deliveryNoteNo) {
        this.deliveryNoteNo = deliveryNoteNo;
    }

    public String getDeliveryNoteSeq() {
        return deliveryNoteSeq;
    }

    public void setDeliveryNoteSeq(String deliveryNoteSeq) {
        this.deliveryNoteSeq = deliveryNoteSeq;
    }

    public String getReelName() {
        return reelName;
    }

    public void setReelName(String reelName) {
        this.reelName = reelName;
    }

    public String getCustomerCode() {
        return customerCode;
    }

    public void setCustomerCode(String customerCode) {
        this.customerCode = customerCode;
    }

    public String getShipmentPlanNo() {
        return shipmentPlanNo;
    }

    public void setShipmentPlanNo(String shipmentPlanNo) {
        this.shipmentPlanNo = shipmentPlanNo;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    // 新增字段的getter和setter方法
    public String getWireDrum() {
        return wireDrum;
    }

    public void setWireDrum(String wireDrum) {
        this.wireDrum = wireDrum;
    }

    public String getInternalRemark() {
        return internalRemark;
    }

    public void setInternalRemark(String internalRemark) {
        this.internalRemark = internalRemark;
    }

    public String getCopperCurrency() {
        return copperCurrency;
    }

    public void setCopperCurrency(String copperCurrency) {
        this.copperCurrency = copperCurrency;
    }
}
