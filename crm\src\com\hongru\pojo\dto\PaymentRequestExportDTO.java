package com.hongru.pojo.dto;

import java.math.BigDecimal;

/**
 * 支付请求Excel导出DTO
 * 用于支付请求书Excel导出功能
 */
public class PaymentRequestExportDTO {

    /* 出货日 */
    private String shipmentDate;

    /* 制品号 */
    private String productCode;

    /* 客户品目号 */
    private String customerProductCode;

    /* 客户部品编码 */
    private String customerPartCode;

    /* 数量(kg) */
    private BigDecimal quantity;

    /* 单价(RMB/kg) */
    private BigDecimal unitPrice;

    /* 金额(RMB) */
    private BigDecimal amount;

    /* 税(RMB) */
    private BigDecimal tax;

    /* 含税价 */
    private BigDecimal totalPriceWithTax;

    /* 支付请求NO */
    private String paymentRequestNo;

    /* 支付请求日 */
    private String paymentRequestDate;

    /* 客户代码 */
    private String customerCode;

    /* 客户简称 */
    private String customerAlias;

    /* 客户全称（需求方全称） */
    private String customerFullName;

    /* 签约方代码 */
    private String contractorCode;

    /* 签约方全称 */
    private String contractorFullName;

    /* 税率 */
    private BigDecimal taxRate;

    /* 结算货币 */
    private String settlementCurrency;

    /* 到货日 */
    private String arrivalDate;

    /* 客户订单号 */
    private String customerOrderNo;

    /* 明细备注 */
    private String detailRemark;

    /* 0base */
    private BigDecimal zeroBase;

    /* 换算后铜单价 */
    private BigDecimal convertedCopperPrice;

    /* 销售单价 */
    private BigDecimal salesUnitPrice;

    /* 销售金额 */
    private BigDecimal salesAmount;

    /* 创建时间 */
    private String createdTime;

    /* 导出日期（作成日） */
    private String exportDate;

    public PaymentRequestExportDTO() {
    }

    public String getShipmentDate() {
        return shipmentDate;
    }

    public void setShipmentDate(String shipmentDate) {
        this.shipmentDate = shipmentDate;
    }

    public String getProductCode() {
        return productCode;
    }

    public void setProductCode(String productCode) {
        this.productCode = productCode;
    }

    public String getCustomerProductCode() {
        return customerProductCode;
    }

    public void setCustomerProductCode(String customerProductCode) {
        this.customerProductCode = customerProductCode;
    }

    public String getCustomerPartCode() {
        return customerPartCode;
    }

    public void setCustomerPartCode(String customerPartCode) {
        this.customerPartCode = customerPartCode;
    }

    public BigDecimal getQuantity() {
        return quantity;
    }

    public void setQuantity(BigDecimal quantity) {
        this.quantity = quantity;
    }

    public BigDecimal getUnitPrice() {
        return unitPrice;
    }

    public void setUnitPrice(BigDecimal unitPrice) {
        this.unitPrice = unitPrice;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public BigDecimal getTax() {
        return tax;
    }

    public void setTax(BigDecimal tax) {
        this.tax = tax;
    }

    public BigDecimal getTotalPriceWithTax() {
        return totalPriceWithTax;
    }

    public void setTotalPriceWithTax(BigDecimal totalPriceWithTax) {
        this.totalPriceWithTax = totalPriceWithTax;
    }

    public String getPaymentRequestNo() {
        return paymentRequestNo;
    }

    public void setPaymentRequestNo(String paymentRequestNo) {
        this.paymentRequestNo = paymentRequestNo;
    }

    public String getPaymentRequestDate() {
        return paymentRequestDate;
    }

    public void setPaymentRequestDate(String paymentRequestDate) {
        this.paymentRequestDate = paymentRequestDate;
    }

    public String getCustomerCode() {
        return customerCode;
    }

    public void setCustomerCode(String customerCode) {
        this.customerCode = customerCode;
    }

    public String getCustomerAlias() {
        return customerAlias;
    }

    public void setCustomerAlias(String customerAlias) {
        this.customerAlias = customerAlias;
    }

    public String getCustomerFullName() {
        return customerFullName;
    }

    public void setCustomerFullName(String customerFullName) {
        this.customerFullName = customerFullName;
    }

    public String getContractorCode() {
        return contractorCode;
    }

    public void setContractorCode(String contractorCode) {
        this.contractorCode = contractorCode;
    }

    public String getContractorFullName() {
        return contractorFullName;
    }

    public void setContractorFullName(String contractorFullName) {
        this.contractorFullName = contractorFullName;
    }

    public BigDecimal getTaxRate() {
        return taxRate;
    }

    public void setTaxRate(BigDecimal taxRate) {
        this.taxRate = taxRate;
    }

    public String getSettlementCurrency() {
        return settlementCurrency;
    }

    public void setSettlementCurrency(String settlementCurrency) {
        this.settlementCurrency = settlementCurrency;
    }

    public String getArrivalDate() {
        return arrivalDate;
    }

    public void setArrivalDate(String arrivalDate) {
        this.arrivalDate = arrivalDate;
    }

    public String getCustomerOrderNo() {
        return customerOrderNo;
    }

    public void setCustomerOrderNo(String customerOrderNo) {
        this.customerOrderNo = customerOrderNo;
    }

    public String getDetailRemark() {
        return detailRemark;
    }

    public void setDetailRemark(String detailRemark) {
        this.detailRemark = detailRemark;
    }

    public BigDecimal getZeroBase() {
        return zeroBase;
    }

    public void setZeroBase(BigDecimal zeroBase) {
        this.zeroBase = zeroBase;
    }

    public BigDecimal getConvertedCopperPrice() {
        return convertedCopperPrice;
    }

    public void setConvertedCopperPrice(BigDecimal convertedCopperPrice) {
        this.convertedCopperPrice = convertedCopperPrice;
    }

    public BigDecimal getSalesUnitPrice() {
        return salesUnitPrice;
    }

    public void setSalesUnitPrice(BigDecimal salesUnitPrice) {
        this.salesUnitPrice = salesUnitPrice;
    }

    public BigDecimal getSalesAmount() {
        return salesAmount;
    }

    public void setSalesAmount(BigDecimal salesAmount) {
        this.salesAmount = salesAmount;
    }

    public String getCreatedTime() {
        return createdTime;
    }

    public void setCreatedTime(String createdTime) {
        this.createdTime = createdTime;
    }

    public String getExportDate() {
        return exportDate;
    }

    public void setExportDate(String exportDate) {
        this.exportDate = exportDate;
    }
}
