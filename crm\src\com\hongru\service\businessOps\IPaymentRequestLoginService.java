package com.hongru.service.businessOps;

import com.hongru.entity.businessOps.PaymentAmount;
import com.hongru.entity.businessOps.PaymentAmountDetail;
import com.hongru.pojo.dto.PaymentRequestLoginListDTO;
import com.hongru.pojo.dto.PaymentRequestExportDTO;
import com.hongru.support.page.PageInfo;

import java.util.List;
import java.util.Map;

/**
 * 支付请求登录服务接口
 */
public interface IPaymentRequestLoginService {

        /**
         * 分页查询支付请求登录列表
         * 
         * @param pageInfo           分页信息
         * @param customerCode       客户代码
         * @param paymentRequestDate 支付请求日
         * @return 支付请求登录列表
         * @throws Exception
         */
        List<PaymentRequestLoginListDTO> listPaymentRequestLoginByPage(PageInfo pageInfo, String customerCode,
                        String paymentRequestDate) throws Exception;

        /**
         * 查询支付请求登录列表总数
         *
         * @param customerCode       客户代码
         * @param paymentRequestDate 支付请求日
         * @return 总数
         * @throws Exception
         */
        Integer listPaymentRequestLoginByPageCount(String customerCode, String paymentRequestDate) throws Exception;

        /**
         * 分页查询支付请求登录列表（按用户过滤）
         *
         * @param pageInfo           分页信息
         * @param customerCode       客户代码
         * @param paymentRequestDate 支付请求日
         * @param creatorName        创建者名称
         * @return 支付请求登录列表
         * @throws Exception
         */
        List<PaymentRequestLoginListDTO> listPaymentRequestLoginByPageWithUser(PageInfo pageInfo, String customerCode,
                        String paymentRequestDate, String creatorName) throws Exception;

        /**
         * 查询支付请求登录列表总数（按用户过滤）
         *
         * @param customerCode       客户代码
         * @param paymentRequestDate 支付请求日
         * @param creatorName        创建者名称
         * @return 总数
         * @throws Exception
         */
        Integer listPaymentRequestLoginByPageCountWithUser(String customerCode, String paymentRequestDate,
                        String creatorName) throws Exception;

        /**
         * 根据支付请求NO获取支付额数据
         * 
         * @param paymentRequestNo 支付请求NO
         * @return 支付额数据
         * @throws Exception
         */
        PaymentAmount getPaymentAmountByNo(String paymentRequestNo) throws Exception;

        /**
         * 根据支付请求NO获取支付额明细数据
         * 
         * @param paymentRequestNo 支付请求NO
         * @return 支付额明细数据列表
         * @throws Exception
         */
        List<PaymentAmountDetail> getPaymentAmountDetailsByNo(String paymentRequestNo) throws Exception;

        /**
         * 保存支付请求登录数据
         * 
         * @param paymentAmount        支付额数据
         * @param paymentAmountDetails 支付额明细数据列表
         * @param userName             用户名
         * @return 保存结果
         * @throws Exception
         */
        int savePaymentRequestLoginData(PaymentAmount paymentAmount, List<PaymentAmountDetail> paymentAmountDetails,
                        String userName) throws Exception;

        /**
         * 更新支付请求登录数据
         * 
         * @param paymentAmount        支付额数据
         * @param paymentAmountDetails 支付额明细数据列表
         * @param userName             用户名
         * @return 更新结果
         * @throws Exception
         */
        int updatePaymentRequestLoginData(PaymentAmount paymentAmount, List<PaymentAmountDetail> paymentAmountDetails,
                        String userName) throws Exception;

        /**
         * 根据支付请求NO删除支付请求登录数据
         * 
         * @param paymentRequestNo 支付请求NO
         * @return 删除结果
         * @throws Exception
         */
        int deletePaymentRequestLoginData(String paymentRequestNo) throws Exception;

        /**
         * 生成支付请求NO
         * 
         * @return 支付请求NO
         * @throws Exception
         */
        String generatePaymentRequestNo() throws Exception;

        /**
         * 根据客户代码查询销售额数据用于支付请求
         *
         * @param customerCode 客户代码
         * @return 销售额数据列表
         * @throws Exception
         */
        List<PaymentAmountDetail> getSalesDataForPaymentRequest(String customerCode) throws Exception;

        /**
         * 更新销售额明细的支付请求确认状态
         *
         * @param salesAmountNo 销售额NO
         * @param detailSeq     明细序号
         * @param confirmStatus 确认状态
         * @param userName      用户名
         * @return 更新结果
         * @throws Exception
         */
        int updateSalesDetailPaymentRequestConfirm(String salesAmountNo, String detailSeq, String confirmStatus,
                        String userName) throws Exception;

        /**
         * 获取支付请求Excel导出数据
         *
         * @param customerCode       客户代码
         * @param paymentRequestDate 支付请求日期范围
         * @return Excel导出数据列表
         * @throws Exception
         */
        List<PaymentRequestExportDTO> getPaymentRequestExportData(String customerCode, String paymentRequestDate)
                        throws Exception;

        /**
         * 根据支付请求NO获取Excel导出数据
         *
         * @param paymentRequestNo 支付请求NO
         * @return Excel导出数据列表
         * @throws Exception
         */
        List<PaymentRequestExportDTO> getPaymentRequestExportDataByNo(String paymentRequestNo) throws Exception;

        /**
         * 根据出货单No.获取明细数据
         *
         * @param shipmentNo 出货单No.
         * @return 明细数据列表
         * @throws Exception
         */
        List<Map<String, Object>> getDetailsByShipmentNo(String shipmentNo) throws Exception;
}
