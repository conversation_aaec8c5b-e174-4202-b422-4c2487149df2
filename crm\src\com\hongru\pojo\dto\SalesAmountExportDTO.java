package com.hongru.pojo.dto;

import java.math.BigDecimal;

/**
 * 销售额Excel导出DTO
 * 用于销售额登录Excel导出功能，包含销售额表(0base)和成本表(cost表)的字段
 */
public class SalesAmountExportDTO {

    /* 销售额表字段 */
    /* 出货日 */
    private String shipmentDate;

    /* 制品号 */
    private String productCode;

    /* 客户品目号 */
    private String customerProductCode;

    /* 客户部品编码 */
    private String customerPartCode;

    /* 数量(kg) */
    private BigDecimal quantity;

    /* 单价(RMB/kg) */
    private BigDecimal unitPrice;

    /* 金额(RMB) */
    private BigDecimal amount;

    /* 税(RMB) */
    private BigDecimal tax;

    /* 含税价 */
    private BigDecimal totalPriceWithTax;

    /* 销售额NO */
    private String salesAmountNo;

    /* 销售额日 */
    private String salesAmountDate;

    /* 客户代码 */
    private String customerCode;

    /* 客户简称 */
    private String customerAlias;

    /* 客户全称（需求方全称） */
    private String customerFullName;

    /* 签约方全称（在销售额中，签约方和需求方是同一个客户） */
    private String contractorFullName;

    /* 税率 */
    private BigDecimal taxRate;

    /* 结算货币 */
    private String settlementCurrency;

    /* 到货日 */
    private String arrivalDate;

    /* 客户订单号 */
    private String customerOrderNo;

    /* 明细备注 */
    private String detailRemark;

    /* 0base */
    private BigDecimal zeroBase;

    /* 换算后铜单价 */
    private BigDecimal convertedCopperPrice;

    /* 销售单价 */
    private BigDecimal salesUnitPrice;

    /* 销售金额 */
    private BigDecimal salesAmount;

    /* 会计年月 */
    private String accountingYearMonth;

    /* 财年 */
    private String fiscalYear;

    /* 成本表相关字段 */
    /* 成本件 */
    private String costCode;

    /* 年度 */
    private String year;

    /* 人件费 */
    private BigDecimal humanCost;

    /* 电费 */
    private BigDecimal electricCost;

    /* 燃气费 */
    private BigDecimal gasCost;

    /* 水费 */
    private BigDecimal waterCost;

    /* 运输费 */
    private BigDecimal transportCost;

    /* 线盘成本 */
    private BigDecimal wireDiscCost;

    /* 总成本 */
    private BigDecimal totalCost;

    /* 创建时间 */
    private String createdTime;

    /* 导出日期（作成日） */
    private String exportDate;

    public SalesAmountExportDTO() {
    }

    public String getShipmentDate() {
        return shipmentDate;
    }

    public void setShipmentDate(String shipmentDate) {
        this.shipmentDate = shipmentDate;
    }

    public String getProductCode() {
        return productCode;
    }

    public void setProductCode(String productCode) {
        this.productCode = productCode;
    }

    public String getCustomerProductCode() {
        return customerProductCode;
    }

    public void setCustomerProductCode(String customerProductCode) {
        this.customerProductCode = customerProductCode;
    }

    public String getCustomerPartCode() {
        return customerPartCode;
    }

    public void setCustomerPartCode(String customerPartCode) {
        this.customerPartCode = customerPartCode;
    }

    public BigDecimal getQuantity() {
        return quantity;
    }

    public void setQuantity(BigDecimal quantity) {
        this.quantity = quantity;
    }

    public BigDecimal getUnitPrice() {
        return unitPrice;
    }

    public void setUnitPrice(BigDecimal unitPrice) {
        this.unitPrice = unitPrice;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public BigDecimal getTax() {
        return tax;
    }

    public void setTax(BigDecimal tax) {
        this.tax = tax;
    }

    public BigDecimal getTotalPriceWithTax() {
        return totalPriceWithTax;
    }

    public void setTotalPriceWithTax(BigDecimal totalPriceWithTax) {
        this.totalPriceWithTax = totalPriceWithTax;
    }

    public String getSalesAmountNo() {
        return salesAmountNo;
    }

    public void setSalesAmountNo(String salesAmountNo) {
        this.salesAmountNo = salesAmountNo;
    }

    public String getSalesAmountDate() {
        return salesAmountDate;
    }

    public void setSalesAmountDate(String salesAmountDate) {
        this.salesAmountDate = salesAmountDate;
    }

    public String getCustomerCode() {
        return customerCode;
    }

    public void setCustomerCode(String customerCode) {
        this.customerCode = customerCode;
    }

    public String getCustomerAlias() {
        return customerAlias;
    }

    public void setCustomerAlias(String customerAlias) {
        this.customerAlias = customerAlias;
    }

    public String getCustomerFullName() {
        return customerFullName;
    }

    public void setCustomerFullName(String customerFullName) {
        this.customerFullName = customerFullName;
    }

    public String getContractorFullName() {
        return contractorFullName;
    }

    public void setContractorFullName(String contractorFullName) {
        this.contractorFullName = contractorFullName;
    }

    public BigDecimal getTaxRate() {
        return taxRate;
    }

    public void setTaxRate(BigDecimal taxRate) {
        this.taxRate = taxRate;
    }

    public String getSettlementCurrency() {
        return settlementCurrency;
    }

    public void setSettlementCurrency(String settlementCurrency) {
        this.settlementCurrency = settlementCurrency;
    }

    public String getArrivalDate() {
        return arrivalDate;
    }

    public void setArrivalDate(String arrivalDate) {
        this.arrivalDate = arrivalDate;
    }

    public String getCustomerOrderNo() {
        return customerOrderNo;
    }

    public void setCustomerOrderNo(String customerOrderNo) {
        this.customerOrderNo = customerOrderNo;
    }

    public String getDetailRemark() {
        return detailRemark;
    }

    public void setDetailRemark(String detailRemark) {
        this.detailRemark = detailRemark;
    }

    public BigDecimal getZeroBase() {
        return zeroBase;
    }

    public void setZeroBase(BigDecimal zeroBase) {
        this.zeroBase = zeroBase;
    }

    public BigDecimal getConvertedCopperPrice() {
        return convertedCopperPrice;
    }

    public void setConvertedCopperPrice(BigDecimal convertedCopperPrice) {
        this.convertedCopperPrice = convertedCopperPrice;
    }

    public BigDecimal getSalesUnitPrice() {
        return salesUnitPrice;
    }

    public void setSalesUnitPrice(BigDecimal salesUnitPrice) {
        this.salesUnitPrice = salesUnitPrice;
    }

    public BigDecimal getSalesAmount() {
        return salesAmount;
    }

    public void setSalesAmount(BigDecimal salesAmount) {
        this.salesAmount = salesAmount;
    }

    public String getAccountingYearMonth() {
        return accountingYearMonth;
    }

    public void setAccountingYearMonth(String accountingYearMonth) {
        this.accountingYearMonth = accountingYearMonth;
    }

    public String getFiscalYear() {
        return fiscalYear;
    }

    public void setFiscalYear(String fiscalYear) {
        this.fiscalYear = fiscalYear;
    }

    public String getCostCode() {
        return costCode;
    }

    public void setCostCode(String costCode) {
        this.costCode = costCode;
    }

    public String getYear() {
        return year;
    }

    public void setYear(String year) {
        this.year = year;
    }

    public BigDecimal getHumanCost() {
        return humanCost;
    }

    public void setHumanCost(BigDecimal humanCost) {
        this.humanCost = humanCost;
    }

    public BigDecimal getElectricCost() {
        return electricCost;
    }

    public void setElectricCost(BigDecimal electricCost) {
        this.electricCost = electricCost;
    }

    public BigDecimal getGasCost() {
        return gasCost;
    }

    public void setGasCost(BigDecimal gasCost) {
        this.gasCost = gasCost;
    }

    public BigDecimal getWaterCost() {
        return waterCost;
    }

    public void setWaterCost(BigDecimal waterCost) {
        this.waterCost = waterCost;
    }

    public BigDecimal getTransportCost() {
        return transportCost;
    }

    public void setTransportCost(BigDecimal transportCost) {
        this.transportCost = transportCost;
    }

    public BigDecimal getWireDiscCost() {
        return wireDiscCost;
    }

    public void setWireDiscCost(BigDecimal wireDiscCost) {
        this.wireDiscCost = wireDiscCost;
    }

    public BigDecimal getTotalCost() {
        return totalCost;
    }

    public void setTotalCost(BigDecimal totalCost) {
        this.totalCost = totalCost;
    }

    public String getCreatedTime() {
        return createdTime;
    }

    public void setCreatedTime(String createdTime) {
        this.createdTime = createdTime;
    }

    public String getExportDate() {
        return exportDate;
    }

    public void setExportDate(String exportDate) {
        this.exportDate = exportDate;
    }
}
