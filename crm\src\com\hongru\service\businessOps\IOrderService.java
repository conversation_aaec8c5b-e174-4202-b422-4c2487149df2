package com.hongru.service.businessOps;

import com.hongru.entity.businessOps.*;
import com.hongru.entity.sumitomo.Customers;
import com.hongru.pojo.dto.*;
import com.hongru.support.page.PageInfo;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

public interface IOrderService {

	/*======================订单详情表======================*/

	/**
	 * 订单详情列表
	 *
	 * @param pageInfo                  分页信息
	 * @param orderStartDate            接单日期开始
	 * @param orderEndDate              接单日期结束
	 * @param prepareStartDate          准备日期开始
	 * @param prepareEndDate            准备日期结束
	 * @param outboundStartDate         出库日期开始
	 * @param outboundEndDate           出库日期结束
	 * @param demandCustomerCode        需求方
	 * @param contractPartyCustomerCode 合同方
	 * @param orderType                 订单类型
	 * @param status                    订单状态
	 * @param userName                  用户名
	 * @return
	 * @throws Exception
	 */
	OrderEntryDetailsPageDTO orderEntryDetailsListByPage(PageInfo pageInfo, String orderStartDate, String orderEndDate, String prepareStartDate, String prepareEndDate, String outboundStartDate, String outboundEndDate, String demandCustomerCode, String contractPartyCustomerCode, String orderType, String status, String userName) throws Exception;

	/**
	 * 订单详情列表导出
	 *
	 * @param pageInfo                  分页信息
	 * @param orderStartDate            接单日期开始
	 * @param orderEndDate              接单日期结束
	 * @param prepareStartDate          准备日期开始
	 * @param prepareEndDate            准备日期结束
	 * @param outboundStartDate         出库日期开始
	 * @param outboundEndDate           出库日期结束
	 * @param demandCustomerCode        需求方
	 * @param contractPartyCustomerCode 合同方
	 * @param orderType                 订单类型
	 * @param status                    订单状态
	 * @param planStatus                出货计划状态
	 * @param userName                  用户名
	 * @return
	 * @throws Exception
	 */
	OrderEntryDetailsPageDTO orderEntryDetailsListByPageForExport(PageInfo pageInfo, String orderStartDate, String orderEndDate, String prepareStartDate, String prepareEndDate, String outboundStartDate, String outboundEndDate, String demandCustomerCode, String contractPartyCustomerCode, String orderType, String status, String planStatus, String userName) throws Exception;

	/**
	 * 订单详情
	 *
	 * @param id 订单详情id
	 * @param status 订单状态
	 * @return
	 * @throws Exception
	 */
	List<OrderEntryDetails> getOrderEntryDetailsById(Integer id, String status) throws Exception;

	/**
     * 根据订单号获取订单详情
     * @param orderNo 订单号
     * @return
     */
    List<OrderEntryDetails> orderEntryDetailsByOrderNo(String orderNo) throws Exception;

	/**
	 * 根据订单号前缀获取最大订单号
	 *
	 * @param orderNoPrefix 订单号前缀 (如：D250423)
	 * @return
	 * @throws Exception
	 */
	String getMaxOrderNoByOrderNoPrefix(String orderNoPrefix) throws Exception;

	/**
	 * 获取合同方信息列表
	 *
	 * @return
	 * @throws Exception
	 */
	List<Customers> getContractPartyCustomerList() throws Exception;

	/**
	 * 新增订单信息
	 *
	 * @param orderEntryDetails 订单信息
	 * @return int
	 * @throws Exception
	 */
	int insertOrderEntryInfo(OrderEntryDetails orderEntryDetails) throws Exception;

	/**
	 * 批量新增订单详情
	 *
	 * @param orderDetailsList 订单详情列表
	 * @return
	 * @throws Exception
	 */
	int batchInsertOrderEntryDetails(List<OrderEntryDetails> orderDetailsList) throws Exception;

	/**
	 * 更新订单信息
	 *
	 * @param orderEntryDetails 订单信息
	 * @return
	 * @throws Exception
	 */
	void updateOrderEntryInfo(OrderEntryDetails orderEntryDetails) throws Exception;

	/**
	 * 更新订单详情信息
	 *
	 * @param orderEntryDetails 订单详情信息
	 * @return
	 * @throws Exception
	 */
	void updateOrderEntryDetail(OrderEntryDetails orderEntryDetails) throws Exception;

	/**
	 * 更新订单详情已送数量信息
	 *
	 * @param orderEntryDetails 订单信息
	 * @return
	 * @throws Exception
	 */
	void updateOrderEntryDetailDeliveredQuantity(OrderEntryDetails orderEntryDetails) throws Exception;

	/**
	 * 删除订单信息
	 *
	 * @param id 订单信息id
	 * @return
	 * @throws Exception
	 */
	void deleteOrderEntryInfo(Integer id) throws Exception;

	/**
	 * 删除订单详情信息
	 *
	 * @param orderNo        订单号
	 * @param orderSerialNum 订单序号
	 * @return
	 * @throws Exception
	 */
	void deleteOrderEntryDetail(String orderNo, String orderSerialNum) throws Exception;

	/**
	 * 订单确认列表
	 *
	 * @param pageInfo                  分页信息
	 * @param outboundStartDate         出库日期开始
	 * @param outboundEndDate           出库日期结束
	 * @param demandCustomerCode        需求方
	 * @param contractPartyCustomerCode 合同方
	 * @param orderNoList               订单号列表
	 * @param userName                  用户名
	 * @return
	 */
	OrderEntryDetailsPageDTO queryOrderConfirmListByPage(PageInfo pageInfo, String outboundStartDate,
			String outboundEndDate, String demandCustomerCode, String contractPartyCustomerCode,
			List<String> orderNoList, String userName) throws Exception;

	/**
	 * 更新订单确认状态
	 * @param orderNoAndSerialNumsList 订单号及订单序号拼接列表 （如：[D250423-1, D250423-2]）
	 * @throws Exception
	 * @return
	*/
	void updateOrderConfirm(List<String> orderNoAndSerialNumsList) throws Exception;

	/**
	 * 出库计划录入列表
	 *
	 * @param pageInfo                  分页信息
	 * @param outboundStartDate         出库开始日期
	 * @param outboundEndDate           出库结束日期
	 * @param demandCustomerCode        需求方
	 * @param contractPartyCustomerCode 合同方
	 * @param planStatus 出货计划状态(null: 未登录计划, 0: 待确认, 1: 已确认)
	 * @param userName 用户名
	 * @return
	 */
	OrderEntryDetailsPageDTO queryShipmentPlanEntryListByPage(PageInfo pageInfo, String outboundStartDate, String outboundEndDate, String demandCustomerCode, String contractPartyCustomerCode, String planStatus, String userName) throws Exception;

	/**
	 * 出库计划录入详情列表
	 * 
	 * @param outboundDate 			出库日期
	 * @param demandCustomerCode 		需求方
	 * @param contractPartyCustomerCode 合同方
	 * @param confirmDataParamList 出库日期-需求方-合同方拼接列表（例：[2025-05-14-001-002, 2025-05-14-001-003]）
	 * @param planStatus 出货计划状态(null: 未登录计划, 0: 待确认, 1: 已确认)
	 * @return
	 */
	List<OrderEntryDetails> queryShipmentPlanEntryDetailList(String outboundDate, String demandCustomerCode, String contractPartyCustomerCode, List<String> confirmDataParamList, String planStatus) throws Exception;

	/**
	 * 根据出货计划番号前缀获取最大出货计划番号
	 * @param planNumberPrefix 出货计划番号前缀 (如：250423)
	 * @return
	 */
	String getMaxPlanNumberByplanNumberPrefix(String planNumberPrefix) throws Exception;

	/**
	 * 更新出货计划
	 * @param orderNoAndSerialNumsList 订单号及订单序号拼接列表 （如：[D250423-1, D250423-2]）
	 * @param planStatus 出货计划状态(0: 待确认, 1: 已确认)
	 * @param planNumber 出货计划番号
	 * @throws Exception
	 */
	void updateShipmentPlanEntryDetails(List<String> orderNoAndSerialNumsList, String planStatus, String planNumber) throws Exception;

	/**
	 * 出库数量录入列表
	 * @param pageInfo 分页信息
	 * @param outboundStartDate 出库开始日期
	 * @param outboundEndDate 出库结束日期
	 * @param customerCode 客户代码	
	 * @param userName 用户名
	 * @return
	 */
	ShipmentQuantityInfoPageDTO queryShipmentQuantityEntryListByPage(PageInfo pageInfo, String outboundStartDate, String outboundEndDate, String customerCode, String userName) throws Exception;

	/**
	 * 出库数量详情列表
	 * @param queryParamList 出库日期-产品代码-客户订单号集合拼接字符串列表（例：[2025-05-14-001-002, 2025-05-14-001-003]）
	 * @return
	 */
	List<ShipmentQuantityInfo> queryShipmentQuantityEntryDetailList(List<String> queryParamList) throws Exception;

	/**
	 * 出库数量信息详情
	 * @param id 流水号
	 * @return
	 */
	ShipmentQuantityInfo queryShipmentQuantityEntryDetail(String id) throws Exception;

	/**
	 * 查询出库信息列表（用于新增出库数量）
	 * 
	 * @param pageInfo                                      分页信息
	 * @param outboundStartDate                             出库开始日期
	 * @param outboundEndDate                               出库结束日期
	 * @param customerCode                                  客户代码
	 * @param userName                                      用户名
	 * @param outboundDateAndCustomerCodeAndProductCodeList 到货日期-客户编号-产品编号集合拼接字符串（例：2025-04-23-001-002,2025-04-23-003-006）
	 * @return
	 */
	ShipmentQuantityInfoPageDTO queryShipmentQuantityEntryListForAdd(PageInfo pageInfo, String outboundStartDate,
			String outboundEndDate, String customerCode, String userName,
			List<String> outboundDateAndCustomerCodeAndProductCodeList) throws Exception;

	/**
	 * 查询出货计划番号列表
	 * @param queryParamList 出库日期-产品代码-客户订单号拼接列表（例：[2025-05-14-001-002, 2025-05-14-001-003]）
	 * @return
	 */
	List<ShipmentQuantityInfo> queryShipmentPlanNoList(List<String> queryParamList) throws Exception;

	/**
	 * 批量新增出库数量信息
	 * @param shipmentQuantityInfoList 出库数量信息列表
	 * @return
	 */
	void batchAddShipmentQuantityEntryList(List<ShipmentQuantityInfo> shipmentQuantityInfoList) throws Exception;

	/**
	 * 更新出库数量信息
	 * @param id 流水号
	 * @param shipmentQuantity 出库数量
	 * @param shipmentPlanNo 出库计划番号
	 * @return
	 * @throws Exception
	 */
	void updateShipmentQuantityEntry(Integer id, String shipmentQuantity, String shipmentPlanNo) throws Exception;

	/**
	 * 删除出库数量信息
	 * @param id 流水号
	 * @throws Exception
	 */
	void deleteShipmentQuantityEntry(Integer id) throws Exception;

	/**
	 * 根据客户代码查询产品信息
	 * 
	 * @param customerCode 客户代码
	 * @return
	 * @throws Exception
	 */
	List<String> getProductListByCustomerCode(String customerCode) throws Exception;

	/**
	 * 根据客户代码和铜合同类别查询铜合同列表
	 * 
	 * @param customerCode       客户代码
	 * @param copperContractType 铜合同类别
	 * @return
	 * @throws Exception
	 */
	List<String> getCopperContractList(String customerCode, String copperContractType) throws Exception;

	/**
	 * 根据铜合同No查询铜合同详情
	 * 
	 * @param copperContractNo 铜合同No
	 * @return
	 * @throws Exception
	 */
	OrderEntryDetails getCopperContractDetail(String copperContractNo) throws Exception;

	/**
	 * 查询所有铜条件列表（包含货币信息）
	 *
	 * @return
	 * @throws Exception
	 */
	List<Map<String, Object>> getCopperConditionList() throws Exception;
}