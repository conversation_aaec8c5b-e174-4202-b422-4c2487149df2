package com.hongru.service.impl.businessOps;

import com.hongru.entity.businessOps.*;
import com.hongru.mapper.businessOps.AscendingWaterMapper;
import com.hongru.mapper.businessOps.ContractDetailsMapper;
import com.hongru.mapper.businessOps.CopperConditionsMapper;
import com.hongru.mapper.businessOps.CopperContractMapper;
import com.hongru.mapper.businessOps.CopperPricesMapper;
import com.hongru.mapper.businessOps.CountrySettingMapper;
import com.hongru.mapper.businessOps.CurrencyExchangeRateMapper;
import com.hongru.mapper.businessOps.CurrencySettingMapper;
import com.hongru.mapper.businessOps.CustomerClassifyMapper;
import com.hongru.mapper.businessOps.CustomerDetailsMapper;
import com.hongru.mapper.businessOps.PayConditionsMapper;
import com.hongru.mapper.businessOps.ProductsPriceMapper;
import com.hongru.mapper.businessOps.TaxRateCalculationMapper;
import com.hongru.mapper.businessOps.TradeConditionsMapper;
import com.hongru.mapper.businessOps.TransportConditionsMapper;
import com.hongru.mapper.businessOps.WareHouseMapper;
import com.hongru.pojo.dto.AscendingWaterPageDTO;
import com.hongru.pojo.dto.ContractDetailsPageDTO;
import com.hongru.pojo.dto.CopperContractPageDTO;
import com.hongru.pojo.dto.CopperPricesPageDTO;
import com.hongru.pojo.dto.CurrencyExchangeRatePageDTO;
import com.hongru.pojo.dto.CustomerDetailsPageDTO;
import com.hongru.pojo.dto.ProductsPricePageDTO;
import com.hongru.service.businessOps.ISalesCommonService;
import com.hongru.support.page.PageInfo;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;

@Service
public class SalesCommonServiceImpl implements ISalesCommonService {
	@Autowired
	private CustomerDetailsMapper customerDetailsMapper;
	@Autowired
	private CustomerClassifyMapper customerClassifyMapper;
	@Autowired
	private ProductsPriceMapper productPriceMapper;
	@Autowired
	private TradeConditionsMapper tradeConditionsMapper;
	@Autowired
	private PayConditionsMapper payConditionsMapper;
	@Autowired
	private TransportConditionsMapper transportConditionsMapper;
	@Autowired
	private WareHouseMapper wareHouseMapper;
	@Autowired
	private CountrySettingMapper countrySettingMapper;
	@Autowired
	private CurrencySettingMapper currencySettingMapper;
	@Autowired
	private CurrencyExchangeRateMapper currencyExchangeRateMapper;
	@Autowired
	private TaxRateCalculationMapper taxRateCalculationMapper;
	@Autowired
	private CopperConditionsMapper copperConditionsMapper;
	@Autowired
	private CopperPricesMapper copperPricesMapper;
	@Autowired
	private CopperContractMapper copperContractMapper;
	@Autowired
	private AscendingWaterMapper ascendingWaterMapper;
	@Autowired
	private ContractDetailsMapper contractDetailsMapper;

	/* ======================客户详情表====================== */
	/**
	 * 客户详情
	 * 
	 * @param
	 * @throws
	 */
	public CustomerDetails getCustomerDetailsById(Integer id) throws Exception {
		return customerDetailsMapper.selectCustomerDetailsById(id);
	}

	/**
	 * 客户详情列表
	 * 
	 * @param
	 * @throws
	 * @return
	 */
	public CustomerDetailsPageDTO customerDetailsListByPage(PageInfo pageInfo, String customerCode, String status)
			throws Exception {

		List<CustomerDetails> customerDetailsList = customerDetailsMapper.customerDetailsListByPage(pageInfo,
				customerCode, status);
		Integer total = customerDetailsMapper.customerDetailsListByPageCount(customerCode, status);
		pageInfo.setTotal(total);
		return new CustomerDetailsPageDTO(pageInfo, customerDetailsList);
	}

	/**
	 * 新增客户详情
	 * 
	 * @param
	 * @throws
	 * @return int
	 */
	public int insertCustomerDetails(CustomerDetails customerDetails) throws Exception {
		return customerDetailsMapper.insertCustomerDetails(customerDetails);
	}

	/**
	 * 更新客户详情
	 * 
	 * @param
	 * @throws
	 * @return
	 */
	public void updateCustomerDetails(CustomerDetails customerDetails) throws Exception {
		customerDetailsMapper.updateCustomerDetails(customerDetails);
	}

	/**
	 * 删除客户详情
	 * 
	 * @param
	 * @throws
	 * @return
	 */
	public void updateCustomerDetailsStatus(Integer id) {
		customerDetailsMapper.updateCustomerDetailsStatus(id);
	}

	/* ======================合同方详情表====================== */
	/**
	 * 取得最大合同方代码
	 * 
	 * @throws
	 * @return java.util.List<com.hongru.entity.businessOps.ContractDetails>
	 */
	public String getMaxContractCode() throws Exception {
		return contractDetailsMapper.getMaxContractCode();
	}

	/**
	 * 合同方详情list
	 * 
	 * @throws
	 * @return java.util.List<com.hongru.entity.businessOps.ContractDetails>
	 */
	public List<ContractDetails> listContractDetailsList() throws Exception {
		return contractDetailsMapper.listContractDetailsList();
	}

	/**
	 * 合同方详情
	 * 
	 * @param
	 * @throws
	 */
	public ContractDetails getContractDetailsById(Integer id) throws Exception {
		return contractDetailsMapper.selectContractDetailsById(id);
	}

	/**
	 * 合同方详情列表
	 * 
	 * @param
	 * @throws
	 * @return
	 */
	public ContractDetailsPageDTO contractDetailsListByPage(PageInfo pageInfo, String contractCode) throws Exception {

		List<ContractDetails> contractDetailsList = contractDetailsMapper.contractDetailsListByPage(pageInfo,
				contractCode);
		Integer total = contractDetailsMapper.contractDetailsListByPageCount(contractCode);
		pageInfo.setTotal(total);
		return new ContractDetailsPageDTO(pageInfo, contractDetailsList);
	}

	/**
	 * 新增合同方
	 * 
	 * @param
	 * @throws
	 * @return int
	 */
	public int insertContractDetails(ContractDetails contractDetails) throws Exception {
		return contractDetailsMapper.insertContractDetails(contractDetails);
	}

	/**
	 * 更新合同方
	 * 
	 * @param
	 * @throws
	 * @return
	 */
	public void updateContractDetails(ContractDetails contractDetails) throws Exception {
		contractDetailsMapper.updateContractDetails(contractDetails);
	}

	/**
	 * 删除合同方
	 * 
	 * @param
	 * @throws
	 * @return
	 */
	public void deleteContractDetailsById(Integer id) {
		contractDetailsMapper.deleteContractDetailsById(id);
	}

	/**
	 * 根据合同方代码获取合同方详情
	 * 
	 * @param contractCode 合同方代码
	 * @throws
	 * @return 合同方详情
	 */
	public ContractDetails getContractDetailsByCode(String contractCode) throws Exception {
		return contractDetailsMapper.selectByContractCode(contractCode);
	}

	/* ======================客户分类表====================== */
	/**
	 * 客户分类详情
	 * 
	 * @param
	 * @throws
	 */
	public CustomerClassify getCustomerClassifyById(Integer id) throws Exception {
		return customerClassifyMapper.selectCustomerClassifyById(id);
	}

	/**
	 * 客户分类列表
	 * 
	 * @param
	 * @throws
	 * @return java.util.List<com.hongru.entity.businessOps.CustomerClassify>
	 */
	public List<CustomerClassify> listCustomerClassify(String customerCategoryCode, String customerCategory)
			throws Exception {
		return customerClassifyMapper.listCustomerClassify(customerCategoryCode, customerCategory);
	}

	/**
	 * 新增客户分类表
	 * 
	 * @param
	 * @throws
	 * @return int
	 */
	public int insertCustomerClassify(CustomerClassify customerClassify) throws Exception {
		return customerClassifyMapper.insertCustomerClassify(customerClassify);
	}

	/**
	 * 更新客户分类表
	 * 
	 * @param
	 * @throws
	 * @return
	 */
	public void updateCustomerClassify(CustomerClassify customerClassify) throws Exception {
		customerClassifyMapper.updateCustomerClassify(customerClassify);
	}

	/**
	 * 删除客户分类表
	 * 
	 * @param
	 * @throws
	 * @return
	 */
	public void deleteCustomerClassify(Integer id) throws Exception {
		customerClassifyMapper.deleteCustomerClassify(id);
	}

	/* ======================产品价格表====================== */
	/**
	 * 产品价格详情
	 * 
	 * @param
	 * @throws
	 */
	public ProductsPrice getProductsPriceById(Integer id) throws Exception {
		return productPriceMapper.selectProductsPriceById(id);
	}

	/**
	 * 产品价格详情列表
	 * 
	 * @param
	 * @throws
	 * @return
	 */
	public ProductsPricePageDTO productsPriceListByPage(PageInfo pageInfo, String customerCode, String productCode,
			String applyDateStart, String applyDateEnd) throws Exception {

		List<ProductsPrice> productsPriceList = productPriceMapper.productsPriceListByPage(pageInfo, customerCode,
				productCode, applyDateStart, applyDateEnd);
		Integer total = productPriceMapper.productsPriceListByPageCount(customerCode, productCode, applyDateStart,
				applyDateEnd);
		pageInfo.setTotal(total);
		return new ProductsPricePageDTO(pageInfo, productsPriceList);
	}

	/**
	 * 新增产品价格
	 * 
	 * @param
	 * @throws
	 * @return int
	 */
	public int insertProductsPrice(ProductsPrice productsPrice) throws Exception {
		return productPriceMapper.insertProductsPrice(productsPrice);
	}

	/**
	 * 更新产品价格
	 * 
	 * @param
	 * @throws
	 * @return
	 */
	public void updateProductsPrice(ProductsPrice productsPrice) throws Exception {
		productPriceMapper.updateProductsPrice(productsPrice);
	}

	/**
	 * 删除产品价格
	 * 
	 * @param
	 * @throws
	 * @return
	 */
	public void deleteProductsPrice(Integer id) throws Exception {
		productPriceMapper.deleteProductsPrice(id);
	}

	/**
	 * 根据客户代码、产品代码、到货日期查询产品单价
	 * 
	 * @param customerCode 客户代码
	 * @param productCode  产品代码
	 * @param arrivalDate  到货日期
	 * @throws Exception
	 * @return 产品价格信息
	 */
	public ProductsPrice getProductPriceByConditions(String customerCode, String productCode, String arrivalDate)
			throws Exception {
		return productPriceMapper.getProductPriceByConditions(customerCode, productCode, arrivalDate);
	}

	/* ======================交易条件表====================== */
	/**
	 * 交易条件详情
	 * 
	 * @param
	 * @throws
	 */
	public TradeConditions getTradeConditionsById(Integer id) throws Exception {
		return tradeConditionsMapper.selectTradeConditionsById(id);
	}

	/**
	 * 交易条件列表
	 * 
	 * @param
	 * @throws
	 * @return java.util.List<com.hongru.entity.businessOps.TradeConditions>
	 */
	public List<TradeConditions> listTradeConditions(String tradeCondition, String conditionName) throws Exception {
		return tradeConditionsMapper.listTradeConditions(tradeCondition, conditionName);
	}

	/**
	 * 新增交易条件
	 * 
	 * @param
	 * @throws
	 * @return int
	 */
	public int insertTradeConditions(TradeConditions tradeConditions) throws Exception {
		return tradeConditionsMapper.insertTradeConditions(tradeConditions);
	}

	/**
	 * 更新交易条件
	 * 
	 * @param
	 * @throws
	 * @return
	 */
	public void updateTradeConditions(TradeConditions tradeConditions) throws Exception {
		tradeConditionsMapper.updateTradeConditions(tradeConditions);
	}

	/**
	 * 删除交易条件表
	 * 
	 * @param
	 * @throws
	 * @return
	 */
	public void deleteTradeConditions(Integer id) throws Exception {
		tradeConditionsMapper.deleteTradeConditions(id);
	}

	/* ======================付款条件表====================== */
	/**
	 * 付款条件详情
	 * 
	 * @param
	 * @throws
	 */
	public PayConditions getPayConditionsById(Integer id) throws Exception {
		return payConditionsMapper.selectPayConditionsById(id);
	}

	/**
	 * 付款条件列表
	 * 
	 * @param
	 * @throws
	 * @return java.util.List<com.hongru.entity.businessOps.PayConditions>
	 */
	public List<PayConditions> listPayConditions(String payCondition, String payConditionName) throws Exception {
		return payConditionsMapper.listPayConditions(payCondition, payConditionName);
	}

	/**
	 * 新增付款条件
	 * 
	 * @param
	 * @throws
	 * @return int
	 */
	public int insertPayConditions(PayConditions payConditions) throws Exception {
		return payConditionsMapper.insertPayConditions(payConditions);
	}

	/**
	 * 更新付款条件
	 * 
	 * @param
	 * @throws
	 * @return
	 */
	public void updatePayConditions(PayConditions payConditions) throws Exception {
		payConditionsMapper.updatePayConditions(payConditions);
	}

	/**
	 * 删除付款条件表
	 * 
	 * @param
	 * @throws
	 * @return
	 */
	public void deletePayConditions(Integer id) throws Exception {
		payConditionsMapper.deletePayConditions(id);
	}

	/* ======================运输条件表====================== */
	/**
	 * 运输条件详情
	 * 
	 * @param
	 * @throws
	 */
	public TransportConditions getTransportConditionsById(Integer id) throws Exception {
		return transportConditionsMapper.selectTransportConditionsById(id);
	}

	/**
	 * 运输条件列表
	 * 
	 * @param
	 * @throws
	 * @return java.util.List<com.hongru.entity.businessOps.TransportConditions>
	 */
	public List<TransportConditions> listTransportConditions(String transportCondition, String transportConditionName)
			throws Exception {
		return transportConditionsMapper.listTransportConditions(transportCondition, transportConditionName);
	}

	/**
	 * 新增运输条件
	 * 
	 * @param
	 * @throws
	 * @return int
	 */
	public int insertTransportConditions(TransportConditions transportConditions) throws Exception {
		return transportConditionsMapper.insertTransportConditions(transportConditions);
	}

	/**
	 * 更新运输条件
	 * 
	 * @param
	 * @throws
	 * @return
	 */
	public void updateTransportConditions(TransportConditions transportConditions) throws Exception {
		transportConditionsMapper.updateTransportConditions(transportConditions);
	}

	/**
	 * 删除运输条件表
	 * 
	 * @param
	 * @throws
	 * @return
	 */
	public void deleteTransportConditions(Integer id) throws Exception {
		transportConditionsMapper.deleteTransportConditions(id);
	}

	/* ======================国家设定表====================== */
	/**
	 * 国家设定详情
	 * 
	 * @param
	 * @throws
	 */
	public CountrySetting getCountrySettingById(Integer id) throws Exception {
		return countrySettingMapper.selectCountrySettingById(id);
	}

	/**
	 * 国家设定详情列表
	 * 
	 * @param
	 * @throws
	 * @return java.util.List<com.hongru.entity.businessOps.CountrySetting>
	 */
	public List<CountrySetting> listCountrySetting(String country, String countryName) throws Exception {
		return countrySettingMapper.listCountrySetting(country, countryName);
	}

	/**
	 * 新增国家
	 * 
	 * @param
	 * @throws
	 * @return int
	 */
	public int insertCountrySetting(CountrySetting countrySetting) throws Exception {
		return countrySettingMapper.insertCountrySetting(countrySetting);
	}

	/**
	 * 更新国家设定表
	 * 
	 * @param
	 * @throws
	 * @return
	 */
	public void updateCountrySetting(CountrySetting countrySetting) throws Exception {
		countrySettingMapper.updateCountrySetting(countrySetting);
	}

	/**
	 * 删除国家设定表
	 * 
	 * @param
	 * @throws
	 * @return
	 */
	public void deleteCountrySetting(Integer id) throws Exception {
		countrySettingMapper.deleteCountrySetting(id);
	}

	/* ======================仓库表====================== */
	/**
	 * 仓库详情
	 * 
	 * @param
	 * @throws
	 */
	public WareHouse getWareHouseById(Integer id) throws Exception {
		return wareHouseMapper.selectWareHouseById(id);
	}

	/**
	 * 仓库详情列表
	 * 
	 * @param
	 * @throws
	 * @return java.util.List<com.hongru.entity.businessOps.WareHouse>
	 */
	public List<WareHouse> listWareHouse(String wareHouseCode, String wareHouseName) throws Exception {
		return wareHouseMapper.listWareHouse(wareHouseCode, wareHouseName);
	}

	/**
	 * 新增仓库详情
	 * 
	 * @param
	 * @throws
	 * @return int
	 */
	public int insertWareHouse(WareHouse wareHouse) throws Exception {
		return wareHouseMapper.insertWareHouse(wareHouse);
	}

	/**
	 * 更新仓库表
	 * 
	 * @param
	 * @throws
	 * @return
	 */
	public void updateWareHouse(WareHouse wareHouse) throws Exception {
		wareHouseMapper.updateWareHouse(wareHouse);
	}

	/**
	 * 删除仓库表
	 * 
	 * @param
	 * @throws
	 * @return
	 */
	public void deleteWareHouse(Integer id) throws Exception {
		wareHouseMapper.deleteWareHouse(id);
	}

	/* ======================货币设定表====================== */
	/**
	 * 货币种类详情
	 * 
	 * @param
	 * @throws
	 */
	public CurrencySetting getCurrencySettingById(Integer id) throws Exception {
		return currencySettingMapper.selectCurrencySettingById(id);
	}

	/**
	 * 货币种类详情列表
	 * 
	 * @param
	 * @throws
	 * @return java.util.List<com.hongru.entity.businessOps.CurrencySetting>
	 */
	public List<CurrencySetting> listCurrencySetting(String currency, String currencyName) throws Exception {
		return currencySettingMapper.listCurrencySetting(currency, currencyName);
	}

	/**
	 * 新增货币详情
	 * 
	 * @param
	 * @throws
	 * @return int
	 */
	public int insertCurrencySetting(CurrencySetting currencySetting) throws Exception {
		return currencySettingMapper.insertCurrencySetting(currencySetting);
	}

	/**
	 * 更新货币设定表
	 * 
	 * @param
	 * @throws
	 * @return
	 */
	public void updateCurrencySetting(CurrencySetting currencySetting) throws Exception {
		currencySettingMapper.updateCurrencySetting(currencySetting);
	}

	/**
	 * 删除仓库表
	 * 
	 * @param
	 * @throws
	 * @return
	 */
	public void deleteCurrencySetting(Integer id) throws Exception {
		currencySettingMapper.deleteCurrencySetting(id);
	}

	/**
	 * 取得货币列表（去重）
	 * 
	 * @param
	 * @throws
	 * @return
	 */
	public List<String> selectDistinctCurrency() throws Exception {
		return currencySettingMapper.selectDistinctCurrency();
	}

	/* ======================货币汇率换算表====================== */
	/**
	 * 货币汇率换算详情
	 * 
	 * @param
	 * @throws
	 */
	public CurrencyExchangeRate getCurrencyExchangeRateById(Integer id) throws Exception {
		return currencyExchangeRateMapper.selectCurrencyExchangeRateById(id);
	}

	/**
	 * 货币汇率换算列表(分页)
	 * 
	 * @param pageInfo 分页信息
	 * @param search   搜索内容
	 * @return
	 */
	public CurrencyExchangeRatePageDTO currencyExchangeRateListByPage(PageInfo pageInfo, String originalCurrency,
			String targetCurrency, String applyDateStart, String applyDateEnd) throws Exception {

		List<CurrencyExchangeRate> currencyExchangeRateList = currencyExchangeRateMapper.currencyExchangeRateListByPage(
				pageInfo, originalCurrency, targetCurrency, applyDateStart, applyDateEnd);
		Integer total = currencyExchangeRateMapper.currencyExchangeRateListByPageCount(originalCurrency, targetCurrency,
				applyDateStart, applyDateEnd);
		pageInfo.setTotal(total);
		return new CurrencyExchangeRatePageDTO(pageInfo, currencyExchangeRateList);

	}

	/**
	 * 新增货币汇率换算详情
	 * 
	 * @param
	 * @throws
	 */
	public int insertCurrencyExchangeRate(CurrencyExchangeRate currencyExchangeRate) throws Exception {
		return currencyExchangeRateMapper.insertCurrencyExchangeRate(currencyExchangeRate);
	}

	/**
	 * 更新货币汇率换算表
	 * 
	 * @param
	 * @throws
	 * @return
	 */
	public void updateCurrencyExchangeRate(CurrencyExchangeRate currencyExchangeRate) throws Exception {
		currencyExchangeRateMapper.updateCurrencyExchangeRate(currencyExchangeRate);
	}

	/**
	 * 删除货币汇率换算
	 * 
	 * @param
	 * @throws
	 * @return
	 */
	public void deleteCurrencyExchangeRate(Integer id) throws Exception {
		currencyExchangeRateMapper.deleteCurrencyExchangeRate(id);
	}

	/**
	 * 根据换算原货币和适用日期查询汇率
	 * 
	 * @param originalCurrency 换算原货币
	 * @param arrivalDate      到货日期
	 * @return 货币换算汇率
	 * @throws Exception
	 */
	public CurrencyExchangeRate getCurrencyExchangeRateByOriginalCurrencyAndDate(String originalCurrency,
			String arrivalDate) throws Exception {
		return currencyExchangeRateMapper.getCurrencyExchangeRateByOriginalCurrencyAndDate(originalCurrency,
				arrivalDate);
	}

	/* ======================税率计算表====================== */
	/**
	 * 税率计算详情
	 * 
	 * @param
	 * @throws
	 */
	public TaxRateCalculation getTaxRateCalculationById(Integer id) throws Exception {
		return taxRateCalculationMapper.selectTaxRateCalculationById(id);
	}

	/**
	 * 税率计算详情列表
	 * 
	 * @param
	 * @throws
	 * @return java.util.List<com.hongru.entity.businessOps.TaxRateCalculation>
	 */
	public List<TaxRateCalculation> listTaxRateCalculation(String taxCalcName, BigDecimal taxRate) throws Exception {
		return taxRateCalculationMapper.listTaxRateCalculation(taxCalcName, taxRate);
	}

	/**
	 * 新增税率计算
	 * 
	 * @param
	 * @throws
	 */
	public int insertTaxRateCalculation(TaxRateCalculation taxRateCalculation) throws Exception {
		return taxRateCalculationMapper.insertTaxRateCalculation(taxRateCalculation);
	}

	/**
	 * 更新税率计算
	 * 
	 * @param
	 * @throws
	 * @return
	 */
	public void updateTaxRateCalculation(TaxRateCalculation taxRateCalculation) throws Exception {
		taxRateCalculationMapper.updateTaxRateCalculation(taxRateCalculation);
	}

	/**
	 * 删除税率计算
	 * 
	 * @param
	 * @throws
	 * @return
	 */
	public void deleteTaxRateCalculation(Integer id) throws Exception {
		taxRateCalculationMapper.deleteTaxRateCalculation(id);
	}

	/**
	 * 取得当前最大税率代码
	 * 
	 * @param
	 * @throws
	 * @return
	 */
	public int selectMaxTaxCode() throws Exception {
		return taxRateCalculationMapper.selectMaxTaxCode();
	}

	/**
	 * 根据税率代码取得税率
	 * 
	 * @param
	 * @throws
	 */
	public TaxRateCalculation getTaxRateByTaxCode(String taxRateCode) throws Exception {
		return taxRateCalculationMapper.findTaxRateByTaxCode(taxRateCode);
	}

	/* ======================铜条件表====================== */
	/**
	 * 铜条件详情
	 * 
	 * @param
	 * @throws
	 */
	public CopperConditions getCopperConditionsById(Integer id) throws Exception {
		return copperConditionsMapper.selectCopperConditionsById(id);
	}

	/**
	 * 铜条件列表
	 * 
	 * @param
	 * @throws
	 * @return java.util.List<com.hongru.entity.businessOps.CopperConditions>
	 */
	public List<CopperConditions> listCopperConditions(String copperCondition, String copperConditionName)
			throws Exception {
		return copperConditionsMapper.listCopperConditions(copperCondition, copperConditionName);
	}

	/**
	 * 新增铜条件
	 * 
	 * @param
	 * @throws
	 * @return int
	 */
	public int insertCopperConditions(CopperConditions copperConditions) throws Exception {
		return copperConditionsMapper.insertCopperConditions(copperConditions);
	}

	/**
	 * 更新铜条件
	 * 
	 * @param
	 * @throws
	 * @return
	 */
	public void updateCopperConditions(CopperConditions copperConditions) throws Exception {
		copperConditionsMapper.updateCopperConditions(copperConditions);
	}

	/**
	 * 删除铜条件表
	 * 
	 * @param
	 * @throws
	 * @return
	 */
	public void deleteCopperConditions(Integer id) throws Exception {
		copperConditionsMapper.deleteCopperConditions(id);
	}

	/**
	 * 根据铜条件获取铜条件详情
	 * 
	 * @param
	 * @throws
	 * @return
	 */
	public CopperConditions findCopperConditions(String copperCondition) throws Exception {
		return copperConditionsMapper.findCopperConditions(copperCondition);
	}

	/**
	 * 获取铜条件列表
	 * 
	 * @param
	 * @throws
	 * @return
	 */
	public List<String> selectCopperCondition() throws Exception {
		return copperConditionsMapper.selectCopperCondition();
	}

	/* ======================铜价表====================== */
	/**
	 * 铜价详情
	 * 
	 * @param
	 * @throws
	 */
	public CopperPrices getCopperPricesById(Integer id) throws Exception {
		return copperPricesMapper.selectCopperPricesById(id);
	}

	/**
	 * 铜价详情列表
	 * 
	 * @param pageInfo 分页信息
	 * @param search   搜索内容
	 * @throws
	 * @return java.util.List<com.hongru.entity.businessOps.CopperPrices>
	 */
	public CopperPricesPageDTO copperPricesListByPage(PageInfo pageInfo, String copperCondition, String applyDateStart,
			String applyDateEnd) throws Exception {

		List<CopperPrices> copperPricesList = copperPricesMapper.copperPricesListByPage(pageInfo, copperCondition,
				applyDateStart, applyDateEnd);
		Integer total = copperPricesMapper.copperPricesListByPageCount(copperCondition, applyDateStart, applyDateEnd);
		pageInfo.setTotal(total);
		return new CopperPricesPageDTO(pageInfo, copperPricesList);
	}

	/**
	 * 新增铜价详情
	 * 
	 * @param
	 * @throws
	 * @return int
	 */
	public int insertCopperPrices(CopperPrices copperPrices) throws Exception {
		return copperPricesMapper.insertCopperPrices(copperPrices);
	}

	/**
	 * 更新铜价详情
	 * 
	 * @param
	 * @throws
	 * @return
	 */
	public void updateCopperPrices(CopperPrices copperPrices) throws Exception {
		copperPricesMapper.updateCopperPrices(copperPrices);
	}

	/**
	 * 删除铜价
	 * 
	 * @param
	 * @throws
	 * @return
	 */
	public void deleteCopperPrices(Integer id) throws Exception {
		copperPricesMapper.deleteCopperPrices(id);
	}

	/**
	 * 根据铜条件和到货日期查询铜基本
	 * 
	 * @param copperCondition 铜条件
	 * @param arrivalDate     到货日期
	 * @throws Exception
	 * @return CopperPrices
	 */
	public CopperPrices getCopperBaseByConditionAndDate(String copperCondition, String arrivalDate) throws Exception {
		return copperPricesMapper.getCopperBaseByConditionAndDate(copperCondition, arrivalDate);
	}

	/* ======================铜合同表====================== */
	/**
	 * 铜合同详情
	 * 
	 * @param
	 * @throws
	 */
	public CopperContract getCopperContractById(Integer id) throws Exception {
		return copperContractMapper.selectCopperContractById(id);
	}

	/**
	 * 铜合同详情列表
	 * 
	 * @param
	 * @throws
	 * @return
	 */
	public CopperContractPageDTO copperContractListByPage(PageInfo pageInfo, String customerCode, String copperSignNo,
			String copperSignType, String copperCondition, String appointmentDate, String useMonth, String status)
			throws Exception {

		List<CopperContract> copperContractList = copperContractMapper.copperContractListByPage(pageInfo, customerCode,
				copperSignNo, copperSignType, copperCondition, appointmentDate, useMonth, status);
		Integer total = copperContractMapper.copperContractListByPageCount(customerCode, copperSignNo, copperSignType,
				copperCondition, appointmentDate, useMonth, status);
		pageInfo.setTotal(total);
		return new CopperContractPageDTO(pageInfo, copperContractList);
	}

	/**
	 * 新增铜合同详情
	 * 
	 * @param
	 * @throws
	 */
	public int insertCopperContract(CopperContract copperContract) throws Exception {
		return copperContractMapper.insertCopperContract(copperContract);
	}

	/**
	 * 更新铜合同
	 * 
	 * @param
	 * @throws
	 * @return
	 */
	public void updateCopperContract(CopperContract copperContract) throws Exception {
		copperContractMapper.updateCopperContract(copperContract);
	}

	/**
	 * 更新铜合同状态
	 * 
	 * @param
	 * @throws
	 * @return
	 */
	public void updateCopperContractStatus(Short status, Integer id) throws Exception {
		copperContractMapper.updateCopperContractStatus(status, id);
	}

	/**
	 * 根据条件获取铜合同列表（用于销售额登录）
	 * 
	 * @param customerCode       客户代码
	 * @param copperContractType 铜合同类别
	 * @param useMonth           使用月
	 * @param status             状态
	 * @throws Exception
	 * @return
	 */
	public List<CopperContract> getCopperContractList(String customerCode, String copperContractType, String useMonth,
			String status) throws Exception {
		// 使用现有的分页查询方法，但不分页（传入null）
		return copperContractMapper.copperContractListByPage(null, customerCode, null, copperContractType, null, null,
				useMonth, status);
	}

	/**
	 * 根据铜签约NO和使用月查询铜合同详情（关联铜条件表）
	 * 
	 * @param copperSignNo 铜签约NO
	 * @param useMonth     使用月
	 * @throws Exception
	 * @return CopperContract
	 */
	public CopperContract getCopperContractDetailWithCondition(String copperSignNo, String useMonth) throws Exception {
		return copperContractMapper.getCopperContractDetailWithCondition(copperSignNo, useMonth);
	}

	/**
	 * 更新铜合同的实际数量和剩余数量
	 * 
	 * @param copperSignNo      铜签约NO
	 * @param actualQuantity    实际数量
	 * @param remainingQuantity 剩余数量
	 * @throws Exception
	 */
	public void updateCopperContractQuantity(String copperSignNo, java.math.BigDecimal actualQuantity,
			java.math.BigDecimal remainingQuantity) throws Exception {
		copperContractMapper.updateCopperContractQuantity(copperSignNo, actualQuantity, remainingQuantity);
	}

	/* ======================升水表====================== */
	/**
	 * 升水价格详情
	 * 
	 * @param
	 * @throws
	 */
	public AscendingWater getAscendingWaterById(Integer id) throws Exception {
		return ascendingWaterMapper.selectAscendingWaterById(id);
	}

	/**
	 * 升水价格详情列表
	 * 
	 * @param
	 * @throws
	 * @return
	 */
	public AscendingWaterPageDTO ascendingWaterListByPage(PageInfo pageInfo, String customerCode,
			String copperCondition, String applyDateStart, String applyDateEnd) throws Exception {

		List<AscendingWater> ascendingWaterList = ascendingWaterMapper.ascendingWaterListByPage(pageInfo, customerCode,
				copperCondition, applyDateStart, applyDateEnd);
		Integer total = ascendingWaterMapper.ascendingWaterListByPageCount(customerCode, copperCondition,
				applyDateStart, applyDateEnd);
		pageInfo.setTotal(total);
		return new AscendingWaterPageDTO(pageInfo, ascendingWaterList);
	}

	/**
	 * 新增升水价格
	 * 
	 * @param
	 * @throws
	 * @return int
	 */
	public int insertAscendingWater(AscendingWater ascendingWater) throws Exception {
		return ascendingWaterMapper.insertAscendingWater(ascendingWater);
	}

	/**
	 * 更新升水价格详情
	 * 
	 * @param
	 * @throws
	 * @return
	 */
	public void updateAscendingWater(AscendingWater ascendingWater) throws Exception {
		ascendingWaterMapper.updateAscendingWater(ascendingWater);
	}

	/**
	 * 删除升水价格
	 * 
	 * @param
	 * @throws
	 * @return
	 */
	public void deleteAscendingWater(Integer id) throws Exception {
		ascendingWaterMapper.deleteAscendingWater(id);
	}

	/**
	 * 根据客户代码和到货日期查询升水表数据（用于一般铜的铜条件和升水单价获取）
	 * 
	 * @param customerCode 客户代码
	 * @param arrivalDate  到货日期
	 * @throws Exception
	 * @return 升水表数据列表
	 */
	public List<AscendingWater> getAscendingWaterByCustomerAndDate(String customerCode, String arrivalDate)
			throws Exception {
		return ascendingWaterMapper.getAscendingWaterByCustomerAndDate(customerCode, arrivalDate);
	}

}