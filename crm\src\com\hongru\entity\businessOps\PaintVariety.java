package com.hongru.entity.businessOps;

import com.baomidou.mybatisplus.annotations.TableId;
import com.baomidou.mybatisplus.annotations.TableName;
import com.baomidou.mybatisplus.enums.IdType;

@TableName("油漆品种表")//CostPrice
public class PaintVariety {
	/* 状态-正常 */
	public static final short STATE_NORMAL = 0;
	/* 状态-删除 */
	public static final short STATE_DELETED = 9;

	/* 流水号 */
	@TableId(value="LXH", type= IdType.AUTO)
	protected int lxh;
	/* 油漆名称 */
	protected String paintName;
	/* 油漆全名 */
	protected String fullPaintName;
	/* 油漆种类 */
	protected String paintType;
	/* 固含量 */
	protected String solidContent;
	/* 固含量检测条件 */
	protected String solidContentDetectionConditions;
	/* 过滤器 */
	protected String filter;
	/* 溶剂 */
	protected String solvent;
	/* 配置方法 */
	protected String configurationMethod;
	/* 生产商 */
	protected String producer;
	/* 修改理由 */
	protected String reasonForModification;
	/* 更新日期 */
	protected String updateDate;
	/* 状态 */
	protected String state;
	/* 油漆桶单重 */
	protected String paintBucketUnitWeight;
	/* 价格 */
	protected String price;
	/* 粘度 */
	protected String viscosity;
	/* 粘度检测条件 */
	protected String viscosityTestingConditions;
	/* IR */
	protected String ir;
	/* 文件名称 */
	protected String fileName;
	/* 有效期 */
	protected String validityPeriod;
	/* 固含量受入范围 */
	protected String solidContentAcceptanceRange;
	/* 漆膜比重 */
	protected String specificGravityOfPaintFilm;

	public int getLxh() {
		return lxh;
	}

	public void setLxh(int lxh) {
		this.lxh = lxh;
	}

	public String getPaintName() {
		return paintName;
	}

	public void setPaintName(String paintName) {
		this.paintName = paintName;
	}

	public String getFullPaintName() {
		return fullPaintName;
	}

	public void setFullPaintName(String fullPaintName) {
		this.fullPaintName = fullPaintName;
	}

	public String getPaintType() {
		return paintType;
	}

	public void setPaintType(String paintType) {
		this.paintType = paintType;
	}

	public String getSolidContent() {
		return solidContent;
	}

	public void setSolidContent(String solidContent) {
		this.solidContent = solidContent;
	}

	public String getSolidContentDetectionConditions() {
		return solidContentDetectionConditions;
	}

	public void setSolidContentDetectionConditions(String solidContentDetectionConditions) {
		this.solidContentDetectionConditions = solidContentDetectionConditions;
	}

	public String getFilter() {
		return filter;
	}

	public void setFilter(String filter) {
		this.filter = filter;
	}

	public String getSolvent() {
		return solvent;
	}

	public void setSolvent(String solvent) {
		this.solvent = solvent;
	}

	public String getConfigurationMethod() {
		return configurationMethod;
	}

	public void setConfigurationMethod(String configurationMethod) {
		this.configurationMethod = configurationMethod;
	}

	public String getProducer() {
		return producer;
	}

	public void setProducer(String producer) {
		this.producer = producer;
	}

	public String getReasonForModification() {
		return reasonForModification;
	}

	public void setReasonForModification(String reasonForModification) {
		this.reasonForModification = reasonForModification;
	}

	public String getUpdateDate() {
		return updateDate;
	}

	public void setUpdateDate(String updateDate) {
		this.updateDate = updateDate;
	}

	public String getState() {
		return state;
	}

	public void setState(String state) {
		this.state = state;
	}

	public String getPaintBucketUnitWeight() {
		return paintBucketUnitWeight;
	}

	public void setPaintBucketUnitWeight(String paintBucketUnitWeight) {
		this.paintBucketUnitWeight = paintBucketUnitWeight;
	}

	public String getPrice() {
		return price;
	}

	public void setPrice(String price) {
		this.price = price;
	}

	public String getViscosity() {
		return viscosity;
	}

	public void setViscosity(String viscosity) {
		this.viscosity = viscosity;
	}

	public String getViscosityTestingConditions() {
		return viscosityTestingConditions;
	}

	public void setViscosityTestingConditions(String viscosityTestingConditions) {
		this.viscosityTestingConditions = viscosityTestingConditions;
	}

	public String getIr() {
		return ir;
	}

	public void setIr(String ir) {
		this.ir = ir;
	}

	public String getFileName() {
		return fileName;
	}

	public void setFileName(String fileName) {
		this.fileName = fileName;
	}

	public String getValidityPeriod() {
		return validityPeriod;
	}

	public void setValidityPeriod(String validityPeriod) {
		this.validityPeriod = validityPeriod;
	}

	public String getSolidContentAcceptanceRange() {
		return solidContentAcceptanceRange;
	}

	public void setSolidContentAcceptanceRange(String solidContentAcceptanceRange) {
		this.solidContentAcceptanceRange = solidContentAcceptanceRange;
	}

	public String getSpecificGravityOfPaintFilm() {
		return specificGravityOfPaintFilm;
	}

	public void setSpecificGravityOfPaintFilm(String specificGravityOfPaintFilm) {
		this.specificGravityOfPaintFilm = specificGravityOfPaintFilm;
	}
}