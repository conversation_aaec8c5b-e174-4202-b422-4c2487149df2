package com.hongru.mapper.businessOps;

import com.baomidou.mybatisplus.mapper.BaseMapper;
import com.hongru.entity.businessOps.CountrySetting;

import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface CountrySettingMapper extends BaseMapper<CountrySetting> {
	
	int insertCountrySetting(@Param("countrySetting") CountrySetting countrySetting);
  
	CountrySetting selectCountrySettingById(@Param("id") int id);

    List<CountrySetting> listCountrySetting(@Param("country") String country, @Param("countryName") String countryName);

    void updateCountrySetting(@Param("countrySetting") CountrySetting countrySetting);

    void deleteCountrySetting(@Param("id") Integer id);
}
