package com.hongru.common.util;

import java.math.BigDecimal;
import java.text.DecimalFormat;

/**
 * 数值格式化工具类
 * 根据客户要求对不同类型的数值字段进行格式化
 * 
 * <AUTHOR>
 * @date 2025-07-23
 */
public class NumberFormatUtil {

    /**
     * 格式化数量类字段（3位小数）
     * 适用字段：salesQuantity, shipmentQuantity
     */
    public static String formatQuantity(BigDecimal value) {
        if (value == null) {
            return "0.000";
        }
        DecimalFormat df = new DecimalFormat("0.000");
        return df.format(value);
    }

    /**
     * 格式化金额类字段（2位小数）
     * 适用字段：salesAmount, copperAmount, zeroBaseSalesAmount, zeroBaseProfitAmount,
     * filmWeightAmount, zeroBaseDirectCostAmount
     */
    public static String formatAmount(BigDecimal value) {
        if (value == null) {
            return "0.00";
        }
        DecimalFormat df = new DecimalFormat("0.00");
        return df.format(value);
    }

    /**
     * 格式化单价类字段（4位小数）
     * 适用字段：salesUnitPrice, zeroBaseDirectCost, zeroBaseSalesUnitPrice
     */
    public static String formatUnitPrice(BigDecimal value) {
        if (value == null) {
            return "0.0000";
        }
        DecimalFormat df = new DecimalFormat("0.0000");
        return df.format(value);
    }

    /**
     * 格式化铜价类字段（4位小数）
     * 适用字段：copperPrice
     */
    public static String formatCopperPrice(BigDecimal value) {
        if (value == null) {
            return "0.0000";
        }
        DecimalFormat df = new DecimalFormat("0.0000");
        return df.format(value);
    }

    /**
     * 格式化尺寸类字段（4位小数）
     * 适用字段：size
     */
    public static String formatSize(BigDecimal value) {
        if (value == null) {
            return "0.0000";
        }
        DecimalFormat df = new DecimalFormat("0.0000");
        return df.format(value);
    }

    /**
     * 格式化运费单价类字段（3位小数）
     * 适用字段：transportationCost, premiumUnitPrice
     */
    public static String formatTransportationCost(BigDecimal value) {
        if (value == null) {
            return "0.000";
        }
        DecimalFormat df = new DecimalFormat("0.000");
        return df.format(value);
    }

    /**
     * 格式化目付量字段（2位小数）
     * 适用字段：filmWeight
     */
    public static String formatFilmWeight(BigDecimal value) {
        if (value == null) {
            return "0.00";
        }
        DecimalFormat df = new DecimalFormat("0.00");
        return df.format(value);
    }

    /**
     * 格式化0base直接成本金额字段（2位小数）
     * 适用字段：zeroBaseDirectCostAmount
     */
    public static String formatZeroBaseDirectCostAmount(BigDecimal value) {
        if (value == null) {
            return "0.00";
        }
        DecimalFormat df = new DecimalFormat("0.00");
        return df.format(value);
    }

    // 以下方法用于Excel导出时的数值处理，返回double类型

    /**
     * 获取数量类字段的double值（用于Excel导出）
     */
    public static double getQuantityValue(BigDecimal value) {
        if (value == null) {
            return 0.000;
        }
        return value.setScale(3, BigDecimal.ROUND_HALF_UP).doubleValue();
    }

    /**
     * 获取金额类字段的double值（用于Excel导出）
     */
    public static double getAmountValue(BigDecimal value) {
        if (value == null) {
            return 0.00;
        }
        return value.setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
    }

    /**
     * 获取单价类字段的double值（用于Excel导出）
     */
    public static double getUnitPriceValue(BigDecimal value) {
        if (value == null) {
            return 0.0000;
        }
        return value.setScale(4, BigDecimal.ROUND_HALF_UP).doubleValue();
    }

    /**
     * 获取铜价类字段的double值（用于Excel导出）
     */
    public static double getCopperPriceValue(BigDecimal value) {
        if (value == null) {
            return 0.0000;
        }
        return value.setScale(4, BigDecimal.ROUND_HALF_UP).doubleValue();
    }

    /**
     * 获取尺寸类字段的double值（用于Excel导出）
     */
    public static double getSizeValue(BigDecimal value) {
        if (value == null) {
            return 0.0000;
        }
        return value.setScale(4, BigDecimal.ROUND_HALF_UP).doubleValue();
    }

    /**
     * 获取运费单价类字段的double值（用于Excel导出）
     */
    public static double getTransportationCostValue(BigDecimal value) {
        if (value == null) {
            return 0.000;
        }
        return value.setScale(3, BigDecimal.ROUND_HALF_UP).doubleValue();
    }

    /**
     * 获取目付量字段的double值（用于Excel导出）
     */
    public static double getFilmWeightValue(BigDecimal value) {
        if (value == null) {
            return 0.00;
        }
        return value.setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
    }

    /**
     * 获取0base直接成本金额字段的double值（用于Excel导出）
     */
    public static double getZeroBaseDirectCostAmountValue(BigDecimal value) {
        if (value == null) {
            return 0.00;
        }
        return value.setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
    }
}
