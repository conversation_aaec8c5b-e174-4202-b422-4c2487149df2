layui.use(['laydate', 'layer', 'table', 'element', 'form'], function(){
    var laydate = layui.laydate //日期
        ,layer = layui.layer //弹层
        ,table = layui.table //表格
        ,element = layui.element //元素操作
    var form = layui.form;
    
    // 会计年月选择器
    laydate.render({
        elem: '#accountingYearMonth'
        ,type: 'month'
        ,format: 'yyyy-MM'
    });
    
    // 执行一个 table 实例
    var url = baselocation+'/salesReport/userMargin/list';
    table.render({
        elem: '#demo'
        ,height: 'full-170'
        ,url: url //数据接口
        ,cellMinWidth: 80 //全局定义常规单元格的最小宽度
        ,parseData:function(res){ //res 即为原始返回的数据
            return {
                "code": 0, //解析接口状态
                "msg": '', //解析提示文本
                "count": res.total, //解析数据长度
                "data": res.rows //解析数据列表
            };
        }
        ,method:'post'
        ,title: 'User-Margin报表'
        ,page: false //关闭分页
        ,where:$("#formSearch").serializeJsonObject()
        ,toolbar: '#toolbarDemo' //开启工具栏，此处显示默认图标，可以自定义模板，详见文档
        ,defaultToolbar: ['filter']
        ,totalRow: true //开启合计行
        ,cols: [[ //表头
            {field: 'accountingYearMonth', title: '会计年月', align:'center', fixed: 'left', width: 100}
            ,{field: 'siteCode', title: '据点C', align:'center', fixed: 'left', width: 80}
            ,{field: 'customerCode', title: '需求方C', align:'center', fixed: 'left', width: 100}
            ,{field: 'customerAlias', title: '需求方略称', align:'center', fixed: 'left', width: 120}
            ,{field: 'productCode', title: '品名', align:'center', width: 150}
            ,{field: 'productGroupName', title: '品目组名', align:'center', width: 100}
            ,{field: 'labelSizeName', title: '尺寸', align:'center', width: 80}
            ,{field: 'customerGroupName', title: '需求方组名', align:'center', width: 120}
            ,{field: 'productMiddleCategory', title: '品目中分类名', align:'center', width: 120}
            ,{field: 'productCategoryCode', title: '品目C', align:'center', width: 100}
            ,{field: 'transportationCost', title: '运费（单价・功能货币）', align:'right', width: 150, totalRow: true, templet: function(d){
                if(d.transportationCost != null){
                    return parseFloat(d.transportationCost).toFixed(3);
                }else{
                    return "0.000";
                }
            }}
            ,{field: 'zeroBaseDirectCost', title: '0base直接成本（单价・功能货币）', align:'right', width: 180, totalRow: true, templet: function(d){
                if(d.zeroBaseDirectCost != null){
                    return parseFloat(d.zeroBaseDirectCost).toFixed(4);
                }else{
                    return "0.0000";
                }
            }}
            ,{field: 'zeroBaseSalesUnitPrice', title: '销售额0base单价（不含税・功能货币）', align:'right', width: 200, totalRow: true, templet: function(d){
                if(d.zeroBaseSalesUnitPrice != null){
                    return parseFloat(d.zeroBaseSalesUnitPrice).toFixed(4);
                }else{
                    return "0.0000";
                }
            }}
            ,{field: 'copperPrice', title: '铜价', align:'right', width: 80, totalRow: true, templet: function(d){
                if(d.copperPrice != null){
                    return parseFloat(d.copperPrice).toFixed(4);
                }else{
                    return "0.0000";
                }
            }}
            ,{field: 'copperCondition', title: '销售额铜条件', align:'center', width: 120}
            ,{field: 'orderTypeCategory', title: '接单种类分类名', align:'center', width: 120}
            ,{field: 'currencyCode', title: '货币C', align:'center', width: 80}
            ,{field: 'salesQuantity', title: '销售额数', align:'right', width: 100, totalRow: true, templet: function(d){
                if(d.salesQuantity != null){
                    return parseFloat(d.salesQuantity).toFixed(3);
                }else{
                    return "0.000";
                }
            }}
            ,{field: 'zeroBaseDirectCostAmount', title: '0base直接成本（金额・功能货币）', align:'right', width: 180, totalRow: true, templet: function(d){
                if(d.zeroBaseDirectCostAmount != null){
                    return parseFloat(d.zeroBaseDirectCostAmount).toFixed(2);
                }else{
                    return "0.00";
                }
            }}
            ,{field: 'zeroBaseSalesAmount', title: '销售额0base金额（不含税・功能货币）', align:'right', width: 200, totalRow: true, templet: function(d){
                if(d.zeroBaseSalesAmount != null){
                    return parseFloat(d.zeroBaseSalesAmount).toFixed(2);
                }else{
                    return "0.00";
                }
            }}
            ,{field: 'salesAmount', title: '销售额金额（不含税）', align:'right', width: 150, totalRow: true, templet: function(d){
                if(d.salesAmount != null){
                    return parseFloat(d.salesAmount).toFixed(2);
                }else{
                    return "0.00";
                }
            }}
            ,{field: 'zeroBaseProfitAmount', title: '0B综利益额', align:'right', width: 120, totalRow: true, templet: function(d){
                if(d.zeroBaseProfitAmount != null){
                    return parseFloat(d.zeroBaseProfitAmount).toFixed(2);
                }else{
                    return "0.00";
                }
            }}
            ,{field: 'copperAmount', title: '铜金额', align:'right', width: 100, totalRow: true, templet: function(d){
                if(d.copperAmount != null){
                    return parseFloat(d.copperAmount).toFixed(2);
                }else{
                    return "0.00";
                }
            }}
            ,{field: 'filmWeight', title: '目付量', align:'right', width: 80, totalRow: true, templet: function(d){
                if(d.filmWeight != null){
                    return parseFloat(d.filmWeight).toFixed(2);
                }else{
                    return "0.00";
                }
            }}
            ,{field: 'filmWeightAmount', title: '目付金额', align:'right', width: 100, totalRow: true, templet: function(d){
                if(d.filmWeightAmount != null){
                    return parseFloat(d.filmWeightAmount).toFixed(2);
                }else{
                    return "0.00";
                }
            }}
            ,{field: 'premiumUnitPrice', title: '销售额升水单价（不含税・功能货币）', align:'right', width: 200, totalRow: true, templet: function(d){
                if(d.premiumUnitPrice != null){
                    return parseFloat(d.premiumUnitPrice).toFixed(3);
                }else{
                    return "0.000";
                }
            }}
        ]]
    });
    
    //监听头工具栏事件
    table.on('toolbar(test)', function(obj){
        var checkStatus = table.checkStatus(obj.config.id);
        switch(obj.event){
            case 'refresh':
                search();
                break;
        };
    });
});

// 检索
function search(){
    var accountingYearMonth = $("#accountingYearMonth").val();
    
    layui.table.reload('demo', {
        where: {
            accountingYearMonth: accountingYearMonth
        }
    });
}

// 导出
function exportData(){
    var accountingYearMonth = $("#accountingYearMonth").val();
    
    // 创建隐藏的表单进行POST提交
    var form = document.createElement('form');
    form.method = 'POST';
    form.action = baselocation + '/salesReport/userMargin/export';
    form.style.display = 'none';
    
    if (accountingYearMonth) {
        var input = document.createElement('input');
        input.type = 'hidden';
        input.name = 'accountingYearMonth';
        input.value = accountingYearMonth;
        form.appendChild(input);
    }
    
    document.body.appendChild(form);
    form.submit();
    document.body.removeChild(form);
}
