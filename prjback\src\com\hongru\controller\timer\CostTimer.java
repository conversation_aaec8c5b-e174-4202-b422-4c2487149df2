package com.hongru.controller.timer;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import com.hongru.entity.businessOps.PaintPrice;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.List;

//@Component
//public class CostTimer {
//	protected Logger logger = LoggerFactory.getLogger(getClass());
//
//	@Autowired
//	private ICostService costService;
//
//	/**
//	 * 每月1号0点1分 拷贝上个月的油漆单价
//	 * @param
//	 * @return void
//	 * @throws <AUTHOR>
//	 * @create 2023/5/6 16:22
//	 */
//	//@Scheduled(cron = "0 34 14 ? * *") // 测试时间 14:30
////	@Scheduled(cron = "0 1 0 1 * ?") // 每月1号0点1分触发
//	@Async
//	public void increaseAnnualLeaveTime() {
//		try {
//			SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM");
//
//			Calendar cal=Calendar.getInstance();
//			cal.add(Calendar.MONTH, -1);
//			cal.set(Calendar.DAY_OF_MONTH, 1);
//			//上个月 年月
//			String preYearMonth = sdf.format(cal.getTime());
//			//当前 年月
//			String yearMonth = sdf.format(System.currentTimeMillis());
//
//			List<PaintPrice> paintPriceList = costService.listPaintPriceListByYearMonth(preYearMonth);
//			if(paintPriceList != null && paintPriceList.size() > 0){
//				for(PaintPrice pa : paintPriceList){
//					PaintPrice paintPriceBean = new PaintPrice();
//					paintPriceBean.setState(pa.getState());
//					paintPriceBean.setCreatorId(pa.getCreatorId());
//					paintPriceBean.setCreatorName(pa.getCreatorName());
//					paintPriceBean.setCreatedTime(pa.getCreatedTime());
//					paintPriceBean.setYearMonth(yearMonth);
//					paintPriceBean.setOutPaintName(pa.getOutPaintName());
//					paintPriceBean.setPaintPrice(pa.getPaintPrice());
//					paintPriceBean.setInventoryPrice(pa.getInventoryPrice());
//					paintPriceBean.setProducer(pa.getProducer());
//					costService.addPaintPrice(paintPriceBean);
//				}
//			}
//		} catch (Exception e) {
//			e.printStackTrace();
//			logger.error("定时器复制上月油漆单价错误：" + e.getMessage());
//		}
//	}
//
//
//}