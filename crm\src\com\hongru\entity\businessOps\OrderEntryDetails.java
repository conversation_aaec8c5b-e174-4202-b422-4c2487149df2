package com.hongru.entity.businessOps;

import com.baomidou.mybatisplus.annotations.TableField;
import com.baomidou.mybatisplus.annotations.TableId;
import com.baomidou.mybatisplus.annotations.TableName;
import com.baomidou.mybatisplus.enums.IdType;

import java.math.BigDecimal;
import java.util.List;


@TableName("订单表")//CostPrice
public class OrderEntryDetails {
	/* 状态-正常 */
	public static final String STATE_NORMAL = "0";
	/* 状态-删除 */
	public static final String STATE_DELETED = "9";
	
	/* 流水号 */
	@TableId(value="流水号", type= IdType.AUTO)
	protected Integer id;
	/* 状态 */
	protected String status;
	/* 接单NO */
	protected String orderNo;
	/* 据点 */
	protected String stronghold;
	/* 接单日 */
	protected String orderDate;
	/* 客户订单号 */
	protected String customerOrderNo;
	/* 接单种类 (0: 通用, 1: 样品) */
	protected String orderType;
	/* 合同方 */
	protected String contractPartyCustomerCode;
	/* 合同方简称 */
	protected String contractPartyCustomerAlias;
	/* 需求方 */
	protected String demandCustomerCode;
	/* 需求方简称 */
	protected String demandCustomerAlias;
	/* 需求方名称 */
	protected String demandCustomerName;
	/* 金额 */
	protected BigDecimal amount;
	/* 创建人姓名 */
	protected String creatorName;
	/* 创建时间 */
	protected String createdTime;
	/* 更新人姓名 */
	protected String updaterName;
	/* 更新时间 */
	protected String updatedTime;

	/* 条码 */
	protected String barcode;
	/* 出库日期 */
	protected String outboundDate;
	/* 准备时间 */
	protected String prepareDate;
	/* 到货日期 */
	protected String arrivalDate;
	/* 型号 */
	protected String modelNumber;
	/* 尺寸 */
	protected String size;
	/* 线盘 */
	protected String wireSpool;
	/* 部品编号 */
	protected String partNumber;
	/* 订单序号 */
	protected String orderSerialNum;
	/* 订单数量 */
	protected String orderQuantity;
	/* 已送数量 */
	protected String deliveredQuantity;
	/* 剩余数量 */
	protected String remainingQuantity;
	/* 明细备注 */
	protected String detailRemark;
	/* 附件ID */
	protected String attachmentId;
	/* 是否确认订单 (0: 未确认, 1: 已确认) */
	protected String isConfirm;
	/* 出货计划 (null: 未登录计划，0: 待确认，1: 已确认) */
	protected String planStatus;
	/* 出货计划番号 */
	protected String planNumber;

	/* 铜合同类别 */
	protected String copperContractType;
	/* 铜签约No */
	protected String copperContractNo;
	/* 铜条件 */
	protected String copperCondition;
	/* 铜货币 */
	protected String copperCurrency;
	/* 换算率 */
	protected BigDecimal conversionRate;
	/* 铜基本 */
	protected BigDecimal copperBase;
	/* 升水 */
	protected BigDecimal premium;
	/* 换算后铜单价 */
	protected BigDecimal convertedCopperPrice;
	/* 0base */
	protected BigDecimal zeroBase;
	/* 接单单价 */
	protected BigDecimal orderUnitPrice;
	/* 接单金额 */
	protected BigDecimal orderAmount;

	/* 客户地址 */
	protected String customerAddress;
	/* 订单明细数量 */
	protected Integer orderCount;
	/* 出货计划状态 (查询结果) */
	protected String shipmentPlanStatus;

	/* 子订单列表 */
	protected List<OrderEntryDetails> subOrders;

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public String getOrderNo() {
		return orderNo;
	}

	public void setOrderNo(String orderNo) {
		this.orderNo = orderNo;
	}

	public String getStronghold() {
		return stronghold;
	}

	public void setStronghold(String stronghold) {
		this.stronghold = stronghold;
	}

	public String getOrderDate() {
		return orderDate;
	}

	public void setOrderDate(String orderDate) {
		this.orderDate = orderDate;
	}
	
	public String getCustomerOrderNo() {
		return customerOrderNo;
	}

	public void setCustomerOrderNo(String customerOrderNo) {
		this.customerOrderNo = customerOrderNo;
	}

	public String getOrderType() {
		return orderType;
	}

	public void setOrderType(String orderType) {
		this.orderType = orderType;
	}

	public String getContractPartyCustomerCode() {
		return contractPartyCustomerCode;
	}

	public void setContractPartyCustomerCode(String contractPartyCustomerCode) {
		this.contractPartyCustomerCode = contractPartyCustomerCode;
	}

	public String getContractPartyCustomerAlias() {
		return contractPartyCustomerAlias;
	}

	public void setContractPartyCustomerAlias(String contractPartyCustomerAlias) {
		this.contractPartyCustomerAlias = contractPartyCustomerAlias;
	}

	public String getDemandCustomerCode() {
		return demandCustomerCode;
	}

	public void setDemandCustomerCode(String demandCustomerCode) {
		this.demandCustomerCode = demandCustomerCode;
	}

	public String getDemandCustomerAlias() {
		return demandCustomerAlias;
	}

	public void setDemandCustomerAlias(String demandCustomerAlias) {
		this.demandCustomerAlias = demandCustomerAlias;
	}

	public String getDemandCustomerName() {
		return demandCustomerName;
	}

	public void setDemandCustomerName(String demandCustomerName) {
		this.demandCustomerName = demandCustomerName;
	}

	public BigDecimal getAmount() {
		return amount;
	}

	public void setAmount(BigDecimal amount) {
		this.amount = amount;
	}

	public String getCreatorName() {
		return creatorName;
	}

	public void setCreatorName(String creatorName) {
		this.creatorName = creatorName;
	}

	public String getCreatedTime() {
		return createdTime;
	}

	public void setCreatedTime(String createdTime) {
		this.createdTime = createdTime;
	}

	public String getUpdaterName() {
		return updaterName;
	}

	public void setUpdaterName(String updaterName) {
		this.updaterName = updaterName;
	}

	public String getUpdatedTime() {
		return updatedTime;
	}

	public void setUpdatedTime(String updatedTime) {
		this.updatedTime = updatedTime;
	}

	public String getOutboundDate() {
		return outboundDate;
	}

	public void setOutboundDate(String outboundDate) {
		this.outboundDate = outboundDate;
	}

	public String getArrivalDate() {
		return arrivalDate;
	}

	public void setArrivalDate(String arrivalDate) {
		this.arrivalDate = arrivalDate;
	}

	public String getModelNumber() {
		return modelNumber;
	}

	public void setModelNumber(String modelNumber) {
		this.modelNumber = modelNumber;
	}

	public String getSize() {
		return size;
	}

	public void setSize(String size) {
		this.size = size;
	}

	public String getWireSpool() {
		return wireSpool;
	}

	public void setWireSpool(String wireSpool) {
		this.wireSpool = wireSpool;
	}

	public String getPartNumber() {
		return partNumber;
	}

	public void setPartNumber(String partNumber) {
		this.partNumber = partNumber;
	}

	public String getOrderSerialNum() {
		return orderSerialNum;
	}

	public void setOrderSerialNum(String orderSerialNum) {
		this.orderSerialNum = orderSerialNum;
	}

	public String getBarcode() {
		return barcode;
	}

	public void setBarcode(String barcode) {
		this.barcode = barcode;
	}

	public String getPrepareDate() {
		return prepareDate;
	}

	public void setPrepareDate(String prepareDate) {
		this.prepareDate = prepareDate;
	}

	public String getOrderQuantity() {
		return orderQuantity;
	}

	public void setOrderQuantity(String orderQuantity) {
		this.orderQuantity = orderQuantity;
	}

	public String getDeliveredQuantity() {
		return deliveredQuantity;
	}

	public void setDeliveredQuantity(String deliveredQuantity) {
		this.deliveredQuantity = deliveredQuantity;
	}

	public String getRemainingQuantity() {
		return remainingQuantity;
	}

	public void setRemainingQuantity(String remainingQuantity) {
		this.remainingQuantity = remainingQuantity;
	}

	public String getDetailRemark() {
		return detailRemark;
	}

	public void setDetailRemark(String detailRemark) {
		this.detailRemark = detailRemark;
	}

	public String getAttachmentId() {
		return attachmentId;
	}

	public void setAttachmentId(String attachmentId) {
		this.attachmentId = attachmentId;
	}

	public List<OrderEntryDetails> getSubOrders() {
		return subOrders;
	}

	public void setSubOrders(List<OrderEntryDetails> subOrders) {
		this.subOrders = subOrders;
	}

	public String getIsConfirm() {
		return isConfirm;
	}
	
	public void setIsConfirm(String isConfirm) {
		this.isConfirm = isConfirm;
	}	

	public String getPlanStatus() {
		return planStatus;
	}

	public void setPlanStatus(String planStatus) {
		this.planStatus = planStatus;
	}
	
	public String getPlanNumber() {
		return planNumber;
	}

	public void setPlanNumber(String planNumber) {
		this.planNumber = planNumber;
	}

	public String getCustomerAddress() {
		return customerAddress;
	}

	public void setCustomerAddress(String customerAddress) {
		this.customerAddress = customerAddress;
	}

	public Integer getOrderCount() {
		return orderCount;
	}

	public void setOrderCount(Integer orderCount) {
		this.orderCount = orderCount;
	}
	
	public String getShipmentPlanStatus() {
		return shipmentPlanStatus;
	}

	public void setShipmentPlanStatus(String shipmentPlanStatus) {
		this.shipmentPlanStatus = shipmentPlanStatus;
	}

	public String getCopperContractType() {
		return copperContractType;
	}

	public void setCopperContractType(String copperContractType) {
		this.copperContractType = copperContractType;
	}

	public String getCopperContractNo() {
		return copperContractNo;
	}

	public void setCopperContractNo(String copperContractNo) {
		this.copperContractNo = copperContractNo;
	}

	public String getCopperCondition() {
		return copperCondition;
	}

	public void setCopperCondition(String copperCondition) {
		this.copperCondition = copperCondition;
	}

	public String getCopperCurrency() {
		return copperCurrency;
	}

	public void setCopperCurrency(String copperCurrency) {
		this.copperCurrency = copperCurrency;
	}

	public BigDecimal getConversionRate() {
		return conversionRate;
	}

	public void setConversionRate(BigDecimal conversionRate) {
		this.conversionRate = conversionRate;
	}

	public BigDecimal getCopperBase() {
		return copperBase;
	}

	public void setCopperBase(BigDecimal copperBase) {
		this.copperBase = copperBase;
	}

	public BigDecimal getPremium() {
		return premium;
	}

	public void setPremium(BigDecimal premium) {
		this.premium = premium;
	}

	public BigDecimal getConvertedCopperPrice() {
		return convertedCopperPrice;
	}

	public void setConvertedCopperPrice(BigDecimal convertedCopperPrice) {
		this.convertedCopperPrice = convertedCopperPrice;
	}

	public BigDecimal getZeroBase() {
		return zeroBase;
	}

	public void setZeroBase(BigDecimal zeroBase) {
		this.zeroBase = zeroBase;
	}

	public BigDecimal getOrderUnitPrice() {
		return orderUnitPrice;
	}

	public void setOrderUnitPrice(BigDecimal orderUnitPrice) {
		this.orderUnitPrice = orderUnitPrice;
	}

	public BigDecimal getOrderAmount() {
		return orderAmount;
	}

	public void setOrderAmount(BigDecimal orderAmount) {
		this.orderAmount = orderAmount;
	}
}