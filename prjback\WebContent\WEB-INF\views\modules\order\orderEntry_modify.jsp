<%@ page language="java" import="java.util.*"
         contentType="text/html; charset=utf-8"%>
<%@ include file="/WEB-INF/layouts/base.jsp"%>
<html>
<head>
    <title>订单信息修改</title>
    <style>
        /*设置数据表表头字体*/
        .layui-table th .layui-table-cell{
            text-align: center;
            font-size: 14px;
            font-weight: bold;
            padding: 5px 2px;
        }

        /* 设置表格单元格更紧凑 */
        .layui-table td, .layui-table th {
            padding: 4px 2px;
        }

        /* 设置表格行高更小 */
        .layui-table tr {
            line-height: 28px;
        }

        /* 设置输入框更紧凑 */
        .layui-table .layui-input {
            height: 28px;
            line-height: 28px;
            padding: 0 5px;
        }

        /* 紧凑的卡片样式 */
        .layui-card-body {
            padding: 8px;
        }

        /* 客户信息区域样式 */
        .customer-info-section {
            border: 1px solid #e6e6e6;
            padding: 10px 15px 5px 15px;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }

        /* 表单标签样式 */
        .layui-form-label {
            padding: 8px 10px;
            width: 80px;
        }

        /* 附件预览区域样式 */
        #attachmentPreview {
            margin-top: 5px;
        }

        #attachmentPreview .layui-card {
            box-shadow: 0 1px 2px 0 rgba(0,0,0,.05);
        }

        #attachmentPreview .layui-card-header {
            font-weight: bold;
            color: #1E9FFF;
            padding: 8px 10px;
            font-size: 14px;
        }

        /* 单行编辑样式 */
        tr.single-edit-row {
            background-color: #e8f7f9 !important;
            border: 2px solid #1E9FFF;
        }

        tr.single-edit-row td {
            padding: 4px 3px;
        }

        /* 单行编辑鼠标悬停效果 */
        tr.single-edit-row:hover {
            background-color: #d0f0f7 !important;
            cursor: pointer;
        }

        /* 单元格点击提示效果 */
        tr.single-edit-row td:hover {
            background-color: #c4eaf3;
        }

        /* 保存按钮效果 */
        .layui-btn.layui-bg-blue:active {
            background-color: #0d73a9;
            transform: scale(0.98);
        }

        /* 行保存按钮样式 */
        .save-row-btn {
            width: 100%;
        }

        /* 单行操作完成动画效果 */
        .save-success {
            animation: saveSuccess 1s ease-in-out;
        }

        @keyframes saveSuccess {
            0% { background-color: #e8f7f9; }
            50% { background-color: #d4f5c3; }
            100% { background-color: transparent; }
        }

        /* 单行编辑样式 */
        tr.single-edit-row {
            background-color: #e8f7f9 !important;
            border: 2px solid #1E9FFF;
        }

        tr.single-edit-row td {
            padding: 4px 3px;
        }

        /* 单行编辑鼠标悬停效果 */
        tr.single-edit-row:hover {
            background-color: #d0f0f7 !important;
            cursor: pointer;
        }

        /* 单元格点击提示效果 */
        tr.single-edit-row td:hover {
            background-color: #c4eaf3;
        }

        /* 单行操作按钮样式 */
        .single-row-operation .layui-btn-group {
            white-space: nowrap;
        }

        .single-row-operation .layui-btn-group .layui-btn {
            margin-right: 2px;
        }

        /* 表格最大高度和滚动样式 */
        .table-container {
            position: relative;
        }

        .table-scroll-container {
            max-height: 238px; /* 减小高度以限制显示5行 */
            overflow-y: auto;
            transition: max-height 0.3s ease;
        }

        /* 附件删除按钮悬停效果 */
        #removeAttachment:hover {
            color: #ff0000 !important;
            transform: scale(1.2);
        }

        /* 附件信息卡片样式 */
        #attachmentInfo .layui-btn-primary:hover {
            border-color: #1E9FFF;
            box-shadow: 0 2px 8px rgba(0,0,0,0.15);
        }

        /* 全屏模式 */
        .table-fullscreen {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: #fff;
            z-index: 9999;
            padding: 20px;
            box-sizing: border-box;
            overflow: auto;
        }

        /* 单行模式下全屏时的滚动容器样式 */
        .table-fullscreen .table-scroll-container.single-mode {
            max-height: calc(100vh - 160px) !important;
            overflow-y: auto;
        }

        /* 放大缩小按钮样式 */
        .table-control-buttons {
            position: absolute;
            top: 0;
            right: 10px;
            z-index: 99;
            display: inline-block;
        }

        .table-control-buttons .layui-btn-xs {
            height: 20px;
            line-height: 20px;
            padding: 0 5px;
            font-size: 12px;
            margin-left: 2px;
        }

        /* 调整表格定位，为按钮留出空间 */
        .layui-table thead th:last-child {
            position: relative;
            min-width: 100px;
        }

        .table-control-button {
            cursor: pointer;
            margin-left: 5px;
            background: #f2f2f2;
            border: 1px solid #ddd;
            border-radius: 3px;
            width: 32px;
            height: 32px;
            line-height: 32px;
            text-align: center;
        }

        .table-control-button:hover {
            background: #e6e6e6;
        }

        .single-edit-row .layui-form-select dl {
            position: fixed;
            margin-top: 190px;
        }

        /* 必填字段验证错误样式 */
        .layui-form-danger {
            border-color: #FF5722 !important;
            background-color: #fff2f0 !important;
        }

        .layui-form-danger:focus {
            border-color: #FF5722 !important;
            box-shadow: 0 0 0 2px rgba(255, 87, 34, 0.2) !important;
        }

        /* 接单金额输入框样式 */
        .order-amount-input {
            cursor: pointer !important;
            background-color: #f8f8f8 !important;
            border: 1px solid #e6e6e6 !important;
            transition: all 0.3s ease;
        }

        .order-amount-input:hover {
            background-color: #e8f4fd !important;
            border-color: #1E9FFF !important;
            box-shadow: 0 0 5px rgba(30, 159, 255, 0.3);
        }

        .order-amount-input:focus {
            background-color: #e8f4fd !important;
            border-color: #1E9FFF !important;
            box-shadow: 0 0 5px rgba(30, 159, 255, 0.5);
        }

        /* 自动填充字段样式 */
        .auto-filled-input {
            background-color: #f0f8ff !important;
            border: 1px solid #b3d9ff !important;
            color: #666 !important;
            cursor: not-allowed !important;
        }

        .auto-filled-input::placeholder {
            color: #999 !important;
        }

        /* 自动计算字段样式 */
        .auto-calculated-input {
            background-color: #f9f9f9 !important;
            border: 1px solid #d4d4d4 !important;
            color: #333 !important;
            font-weight: bold !important;
            cursor: not-allowed !important;
        }

        .auto-calculated-input::placeholder {
            color: #999 !important;
        }

        /* 铜字段弹窗中的label样式调整 */
        #copperFieldsModal .layui-form-label {
            width: 100px !important; /* 增加宽度以适应5个字 */
            padding: 8px 5px !important; /* 调整内边距 */
            font-size: 13px !important; /* 稍微减小字体 */
        }

        #copperFieldsModal .layui-input-block {
            margin-left: 110px !important; /* 对应调整左边距 */
        }
    </style>
</head>
<body>
<form class="layui-form hr-form-add" action="${ctx}/order/orderEntry/edit" method="post" id="submitForm">
    <div class="layui-fluid hr-layui-fluid">
        <div class="layui-card">
            <div class="layui-card-body">
                <div class="layui-form-item layui-row">

                    <div class="layui-form-item layui-row customer-info-section">
                        <div class="layui-inline layui-col-md3">
                            <label class="layui-form-label"><span style="color: red;">*</span>需求方:</label>
                            <div class="layui-input-block">
                                <select class="layui-select" id="demandCustomerCode" name="demandCustomerCode" lay-search="true" lay-filter="demandCustomerCodeFn" value="${orderEntryDetails.demandCustomerCode}">
                                    <option value="">请选择</option>
                                    <c:forEach items="${customersList}" var="customers">
                                        <option value="${customers.customerCode}" ${orderEntryDetails.demandCustomerCode == customers.customerCode ? 'selected="selected"' : ''}>${customers.customerCode} - ${customers.customerAlias}</option>
                                    </c:forEach>
                                </select>
                                <!-- 添加隐藏字段确保数据传递 -->
                                <input type="hidden" name="demandCustomerCodeHidden" value="${orderEntryDetails.demandCustomerCode}" />
                            </div>
                        </div>
                        <div class="layui-inline layui-col-md3">
                            <label class="layui-form-label"><span style="color: red;">*</span>合同方:</label>
                            <div class="layui-input-block">
                                <select class="layui-select" id="contractPartyCustomerCode" name="contractPartyCustomerCode" lay-search="true" value="${orderEntryDetails.contractPartyCustomerCode}">
                                    <option value="">请选择</option>
                                    <c:forEach items="${contractPartyCustomerList}" var="customers">
                                        <option value="${customers.customerCode}" ${orderEntryDetails.contractPartyCustomerCode == customers.customerCode ? 'selected="selected"' : ''}>${customers.customerCode} - ${customers.customerAlias}</option>
                                    </c:forEach>
                                </select>
                            </div>
                        </div>
                        <div class="layui-inline layui-col-md3">
                            <label class="layui-form-label">客户订单号:</label>
                            <div class="layui-input-block">
                                <input type="text" class="layui-input" id="customerOrderNo" name="customerOrderNo" onblur="checkCustomerOrderNo()" oninput="cleanCustomerOrderNo(this)" value="${orderEntryDetails.customerOrderNo}" placeholder="请输入客户订单号"/>
                            </div>
                        </div>
                        <div class="layui-inline layui-col-md2">
                            <label class="layui-form-label"><span class="star">*</span>订单种类:</label>
                            <div class="layui-input-block">
                                <select class="layui-select" id="orderType" name="orderType" value="${orderEntryDetails.orderType}">
                                    <option value="0" ${orderEntryDetails.orderType == '0' ? 'selected="selected"' : ''}>通用</option>
                                    <option value="1" ${orderEntryDetails.orderType == '1' ? 'selected="selected"' : ''}>样品</option>
                                </select>
                            </div>
                        </div>
                        <!-- 隐藏字段，保留数据但不显示 -->
                        <input type="hidden" id="id" name="id" value="${orderEntryDetails.id}" />
                        <input type="hidden" id="orderNo" name="orderNo" value="${orderEntryDetails.orderNo}" />
                        <input type="hidden" id="contractPartyCustomerAlias" name="contractPartyCustomerAlias" value="${orderEntryDetails.contractPartyCustomerAlias}" />
                        <input type="hidden" id="demandCustomerAlias" name="demandCustomerAlias" value="${orderEntryDetails.demandCustomerAlias}" />
                    </div>
                    <div class="layui-card">
                        <div class="layui-card-body">
                            <div class="layui-form-item layui-row">
                                <div class="layui-inline layui-col-md12">
                                    <div style="display: flex; align-items: center; margin-bottom: 10px;">
                                        <!-- 添加行按钮 -->
                                        <button type="button" class="layui-btn" id="addSubOrderBtn">
                                            <i class="layui-icon layui-icon-add-1"></i> 添加行
                                        </button>

                                        <!-- 附件上传按钮和显示 -->
                                        <div style="display: flex; align-items: center; margin-left: 10px;">
                                            <button type="button" class="layui-btn" id="attachmentUpload">
                                                <i class="layui-icon layui-icon-upload"></i> 添加附件
                                            </button>
                                            <button type="button" class="layui-btn" onclick="selectAttachment()" style="margin-left: 10px;">
                                                <i class="layui-icon layui-icon-upload"></i> 选择附件
                                            </button>
                                            <div style="margin-left: 10px; display: none; height: 38px;" id="attachmentInfo">
                                                <div class="layui-btn layui-btn-primary" style="height: 38px; line-height: 38px; position: relative; overflow: hidden; padding-right: 35px; cursor: default; box-shadow: 0 1px 5px rgba(0,0,0,0.1);">
                                                    <i class="layui-icon layui-icon-file" style="color: #1E9FFF;"></i>
                                                    <span id="attachmentFileName" style="color: #555;">未选择文件</span>
                                                    <i class="layui-icon layui-icon-close" id="removeAttachment" style="position: absolute; right: 10px; top: 50%; transform: translateY(-50%); cursor: pointer; color: #FF5722; transition: all 0.2s;"></i>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div id="subOrderContainer" class="table-container">
                                        <div class="table-scroll-container">
                                            <table class="layui-table">
                                                <thead>
                                                    <tr>
                                                        <th style="width: 58px;">订单序号</th>
                                                        <th>准备日期</th>
                                                        <th>出库日期</th>
                                                        <th>到货日期</th>
                                                        <th>型号</th>
                                                        <th>条码</th>
                                                        <th><span style="color: red;">*</span>尺寸</th>
                                                        <th><span style="color: red;">*</span>线盘</th>
                                                        <th>部品编号</th>
                                                        <th>订单数量(kg)</th>
                                                        <th>已送数量(kg)</th>
                                                        <th>剩余数量(kg)</th>
                                                        <th>接单金额</th>
                                                        <th>详情备注</th>
                                                        <th>操作
                                                            <div class="table-control-buttons">
                                                                <button type="button" class="layui-btn layui-btn-xs layui-btn-primary" id="fullscreenButton" title="放大">
                                                                    <i class="layui-icon layui-icon-screen-full"></i>
                                                                </button>
                                                                <button type="button" class="layui-btn layui-btn-xs layui-btn-primary" id="exitFullscreenButton" title="缩小" style="display:none;">
                                                                    <i class="layui-icon layui-icon-screen-restore"></i>
                                                                </button>
                                                            </div>
                                                        </th>
                                                    </tr>
                                                </thead>
                                                <tbody id="subOrderTableBody">
                                                    <c:if test="${not empty orderEntryDetails.subOrders}">
                                                        <c:forEach items="${orderEntryDetails.subOrders}" var="subOrder">
                                                            <tr class="sub-order-row" data-id="${subOrder.id}" data-serial="${subOrder.orderSerialNum}" data-prepare="${subOrder.prepareDate}" data-outbound="${subOrder.outboundDate}" data-arrival="${subOrder.arrivalDate}" data-model="${subOrder.modelNumber}" data-size="${subOrder.size}" data-wire="${subOrder.wireSpool}" data-part="${subOrder.partNumber}" data-order-qty="${subOrder.orderQuantity}" data-delivered-qty="${subOrder.deliveredQuantity}" data-remaining-qty="${subOrder.remainingQuantity}" data-detail-remark="${subOrder.detailRemark}" data-barcode="${subOrder.barcode}">
                                                                <td name="orderSerialNum">${subOrder.orderSerialNum}</td>
                                                                <td>${subOrder.prepareDate}</td>
                                                                <td>${subOrder.outboundDate}</td>
                                                                <td>${subOrder.arrivalDate}</td>
                                                                <td>${subOrder.modelNumber}</td>
                                                                <td>${subOrder.barcode}</td>
                                                                <td>${subOrder.size}</td>
                                                                <td>${subOrder.wireSpool}</td>
                                                                <td>${subOrder.partNumber}</td>
                                                                <td>${subOrder.orderQuantity}</td>
                                                                <td>${subOrder.deliveredQuantity}</td>
                                                                <td>${subOrder.remainingQuantity}</td>
                                                                <td>
                                                                    <input type="text" class="layui-input order-amount-input" readonly onclick="openCopperFieldsModal(this)"
                                                                           value="${subOrder.orderAmount != null ? subOrder.orderAmount : ''}"
                                                                           placeholder="编辑状态下可设置" title="请先点击编辑按钮，然后点击此处设置接单金额"
                                                                           style="cursor: pointer; background-color: #f8f8f8; color: #666;"
                                                                           data-copper-contract-type="${subOrder.copperContractType != null ? subOrder.copperContractType : ''}"
                                                                           data-copper-contract-no="${subOrder.copperContractNo != null ? subOrder.copperContractNo : ''}"
                                                                           data-copper-condition="${subOrder.copperCondition != null ? subOrder.copperCondition : ''}"
                                                                           data-copper-currency="${subOrder.copperCurrency != null ? subOrder.copperCurrency : ''}"
                                                                           data-conversion-rate="${subOrder.conversionRate != null ? subOrder.conversionRate : ''}"
                                                                           data-copper-base="${subOrder.copperBase != null ? subOrder.copperBase : ''}"
                                                                           data-premium="${subOrder.premium != null ? subOrder.premium : ''}"
                                                                           data-converted-copper-price="${subOrder.convertedCopperPrice != null ? subOrder.convertedCopperPrice : ''}"
                                                                           data-zero-base="${subOrder.zeroBase != null ? subOrder.zeroBase : ''}"
                                                                           data-order-unit-price="${subOrder.orderUnitPrice != null ? subOrder.orderUnitPrice : ''}"
                                                                           data-order-amount="${subOrder.orderAmount != null ? subOrder.orderAmount : ''}">
                                                                </td>
                                                                <td>${subOrder.detailRemark}</td>
                                                                <td class="single-row-operation">
                                                                    <div class="layui-btn-group">
                                                                        <button type="button" class="layui-btn layui-btn-primary layui-btn-sm" onclick="editSubOrderRow(this, true)" title="编辑">
                                                                            <i class="layui-icon layui-icon-edit"></i>
                                                                        </button>
                                                                        <button type="button" class="layui-btn layui-btn-primary layui-btn-sm" onclick="removeSubOrder(this, ${subOrder.orderSerialNum})" title="删除">
                                                                            <i class="layui-icon layui-icon-delete"></i>
                                                                        </button>
                                                                    </div>
                                                                </td>
                                                            </tr>
                                                        </c:forEach>
                                                    </c:if>
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                    <!-- 添加行按钮已移至顶部 -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="layui-form-item layui-row">
                    <div class="layui-inline layui-col-md12">
                        <div style="text-align: center;">
                            <input type="hidden" id="id" name="id" value="${orderEntryDetails.id}"/>
                            <input type="hidden" name="attachmentId" id="attachmentId" value="${orderEntryDetails.attachmentId}"/>
                            <button type="submit" class="layui-btn layui-btn-normal" lay-submit lay-filter="formDemo">
                                <i class="layui-icon layui-icon-ok"></i> 保存修改
                            </button>
                            <button type="button" class="layui-btn layui-btn-primary" onclick="closeAll()">
                                <i class="layui-icon layui-icon-close"></i> 取消
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 附件预览区域 - 紧凑样式 -->
                <div class="layui-card" style="margin-bottom: 5px;">
                    <div class="layui-card-header" style="display: flex; align-items: center;">
                        <i class="layui-icon layui-icon-file" style="margin-right: 5px;"></i>
                        <span id="attachmentTitle">附件预览</span>
                    </div>
                    <div class="layui-card-body" style="padding: 5px;">
                        <div id="attachmentPreview" style="display:none;" class="layui-form-item">
                            <div style="margin-top: 5px; text-align: center;"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</form>

<!-- 铜字段录入弹窗 -->
<div id="copperFieldsModal" style="display: none; padding: 20px;">
    <form class="layui-form" id="copperFieldsForm">
        <div class="layui-row">
            <div class="layui-col-md6">
                <div class="layui-form-item">
                    <label class="layui-form-label">铜合同类别:</label>
                    <div class="layui-input-block">
                        <select name="copperContractType" id="copperContractType" lay-filter="copperContractType">
                            <option value="">请选择</option>
                            <option value="1">预约铜</option>
                            <option value="2">支给铜</option>
                            <option value="3">一般铜</option>
                            <option value="4">无偿</option>
                        </select>
                    </div>
                </div>
            </div>
            <div class="layui-col-md6">
                <div class="layui-form-item">
                    <label class="layui-form-label">铜签约No:</label>
                    <div class="layui-input-block">
                        <select name="copperContractNo" id="copperContractNo" lay-filter="copperContractNo">
                            <option value="">请选择</option>
                        </select>
                    </div>
                </div>
            </div>
        </div>

        <div class="layui-row">
            <div class="layui-col-md6">
                <div class="layui-form-item">
                    <label class="layui-form-label">铜条件:</label>
                    <div class="layui-input-block">
                        <select name="copperCondition" id="copperCondition" lay-filter="copperCondition">
                            <option value="">请选择</option>
                        </select>
                    </div>
                </div>
            </div>
            <div class="layui-col-md6">
                <div class="layui-form-item">
                    <label class="layui-form-label">铜货币:</label>
                    <div class="layui-input-block">
                        <input type="text" name="copperCurrency" id="copperCurrency" class="layui-input auto-filled-input" readonly placeholder="从合同自动填充">
                    </div>
                </div>
            </div>
        </div>

        <div class="layui-row">
            <div class="layui-col-md6">
                <div class="layui-form-item">
                    <label class="layui-form-label">换算率:</label>
                    <div class="layui-input-block">
                        <input type="number" name="conversionRate" id="conversionRate" class="layui-input" step="0.0001" oninput="calculateOrderAmount()">
                    </div>
                </div>
            </div>
            <div class="layui-col-md6">
                <div class="layui-form-item">
                    <label class="layui-form-label">铜base:</label>
                    <div class="layui-input-block">
                        <input type="number" name="copperBase" id="copperBase" class="layui-input auto-filled-input" step="0.01" readonly placeholder="从合同自动填充">
                    </div>
                </div>
            </div>
        </div>

        <div class="layui-row">
            <div class="layui-col-md6">
                <div class="layui-form-item">
                    <label class="layui-form-label">升水:</label>
                    <div class="layui-input-block">
                        <input type="number" name="premium" id="premium" class="layui-input" step="0.01" oninput="calculateOrderAmount()">
                    </div>
                </div>
            </div>
            <div class="layui-col-md6">
                <div class="layui-form-item">
                    <label class="layui-form-label">换算后铜单价:</label>
                    <div class="layui-input-block">
                        <input type="number" name="convertedCopperPrice" id="convertedCopperPrice" class="layui-input auto-calculated-input" step="0.01" readonly placeholder="自动计算">
                    </div>
                </div>
            </div>
        </div>

        <div class="layui-row">
            <div class="layui-col-md6">
                <div class="layui-form-item">
                    <label class="layui-form-label">0base:</label>
                    <div class="layui-input-block">
                        <input type="number" name="zeroBase" id="zeroBase" class="layui-input" step="0.01" oninput="calculateOrderAmount()">
                    </div>
                </div>
            </div>
            <div class="layui-col-md6">
                <div class="layui-form-item">
                    <label class="layui-form-label">接单单价:</label>
                    <div class="layui-input-block">
                        <input type="number" name="orderUnitPrice" id="orderUnitPrice" class="layui-input auto-calculated-input" step="0.01" readonly placeholder="自动计算">
                    </div>
                </div>
            </div>
        </div>

        <div class="layui-row">
            <div class="layui-col-md6">
                <div class="layui-form-item">
                    <label class="layui-form-label">订单数量:</label>
                    <div class="layui-input-block">
                        <input type="text" class="layui-input" id="modalOrderQuantity" name="orderQuantity" placeholder="请输入订单数量" oninput="calculateOrderAmount()" style="background-color: #f8f8f8;">
                    </div>
                </div>
            </div>
            <div class="layui-col-md6">
                <div class="layui-form-item">
                    <label class="layui-form-label">接单金额:</label>
                    <div class="layui-input-block">
                        <input type="number" name="orderAmount" id="orderAmount" class="layui-input auto-calculated-input" step="0.01" readonly placeholder="自动计算">
                    </div>
                </div>
            </div>
        </div>

        <!-- 隐藏字段，用于存储当前行的引用 -->
        <input type="hidden" id="currentRowRef" value="">
        <input type="hidden" id="currentCustomerCode" value="">
    </form>
</div>

<myfooter>
    <input type="hidden" id="baselocation" value="${ctx}">
    <script>
        // 全局变量，供前端脚本使用
        var baselocation = "${ctx}";
    </script>
    <script src="${ctxsta}/hongru/js/order/orderEntry_modify.js?time=20"></script>
</myfooter>
</body>
</html>
