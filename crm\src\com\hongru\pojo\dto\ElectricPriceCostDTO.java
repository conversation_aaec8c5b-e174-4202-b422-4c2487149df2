package com.hongru.pojo.dto;

import com.hongru.entity.businessOps.ElectricPriceCost;
import com.hongru.support.page.PageInfo;

import java.util.List;

public class ElectricPriceCostDTO {

    private PageInfo pageInfo;

    private List<ElectricPriceCost> electricPriceCostList;

    public ElectricPriceCostDTO(PageInfo pageInfo, List<ElectricPriceCost> electricPriceCostList) {
        super();
        this.pageInfo = pageInfo;
        this.electricPriceCostList = electricPriceCostList;
    }

    public PageInfo getPageInfo() {
        return pageInfo;
    }

    public void setPageInfo(PageInfo pageInfo) {
        this.pageInfo = pageInfo;
    }

    public List<ElectricPriceCost> getElectricPriceCostList() {
        return electricPriceCostList;
    }

    public void setElectricPriceCostList(List<ElectricPriceCost> electricPriceCostList) {
        this.electricPriceCostList = electricPriceCostList;
    }
}
