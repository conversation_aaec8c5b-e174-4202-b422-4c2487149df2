package com.hongru.mapper.businessOps;

import com.baomidou.mybatisplus.mapper.BaseMapper;
import com.hongru.entity.businessOps.ProductsPrice;
import com.hongru.support.page.PageInfo;

import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ProductsPriceMapper extends BaseMapper<ProductsPrice> {
	int insertProductsPrice(@Param("productsPrice") ProductsPrice productsPrice);
  
	ProductsPrice selectProductsPriceById(@Param("id") int id);

    List<ProductsPrice> productsPriceListByPage(@Param("pageInfo") PageInfo pageInfo, @Param("customerCode") String customerCode, @Param("productCode") String productCode, @Param("applyDateStart") String applyDateStart, @Param("applyDateEnd") String applyDateEnd);
    Integer productsPriceListByPageCount(@Param("customerCode") String customerCode, @Param("productCode") String productCode, @Param("applyDateStart") String applyDateStart, @Param("applyDateEnd") String applyDateEnd);

    void updateProductsPrice(@Param("productsPrice") ProductsPrice productsPrice);

    void deleteProductsPrice(@Param("id") Integer id);

    /**
     * 根据客户代码、产品代码、到货日期查询产品单价
     * 
     * @param customerCode 客户代码
     * @param productCode  产品代码
     * @param arrivalDate  到货日期
     * @return 产品价格信息
     */
    ProductsPrice getProductPriceByConditions(@Param("customerCode") String customerCode,
            @Param("productCode") String productCode,
            @Param("arrivalDate") String arrivalDate);
}
