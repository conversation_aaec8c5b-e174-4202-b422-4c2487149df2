<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hongru.mapper.businessOps.CopperConditionsMapper">
    <sql id="copperConditions_sql">
		pc.[流水号] AS id,pc.[铜条件] AS copperCondition,pc.[铜条件名] AS copperConditionName,pc.[货币] AS currency,
		pc.[创建人姓名] AS creatorName,pc.[创建时间] AS createdTime,
		pc.[更新人姓名] AS updaterName,pc.[更新时间] AS updatedTime
	</sql>

	<insert id="insertCopperConditions" parameterType="com.hongru.entity.businessOps.CopperConditions">
		INSERT INTO [businessOps].[dbo].[铜条件表]
		(
		[铜条件],
		[铜条件名],
		[货币],
		[创建人姓名],
		[创建时间]
		)VALUES(
		#{copperConditions.copperCondition},
		#{copperConditions.copperConditionName},
		#{copperConditions.currency},
		#{copperConditions.creatorName},
		#{copperConditions.createdTime}
		)
	</insert>

	<select id="selectCopperConditionsById" resultType="com.hongru.entity.businessOps.CopperConditions">
		SELECT
		<include refid="copperConditions_sql"/>
		FROM [businessOps].[dbo].[铜条件表] pc
		<where>
			<if test="id != null">
				AND pc.[流水号] = #{id}
			</if>
		</where>
	</select>

	<select id="listCopperConditions" resultType="com.hongru.entity.businessOps.CopperConditions">
		SELECT
		<include refid="copperConditions_sql"/>
		FROM [businessOps].[dbo].[铜条件表] pc
		<where>
			<if test="copperCondition != null and copperCondition != ''">
				AND pc.[铜条件] = #{copperCondition}
			</if>
			<if test="copperConditionName != null and copperConditionName != 0">
				AND pc.[铜条件名] = #{copperConditionName}
			</if>
		</where>
		ORDER BY pc.[铜条件]
	</select>

	<update id="updateCopperConditions">
		UPDATE [businessOps].[dbo].[铜条件表]
		<set>
			<if test="copperConditions.copperCondition != null and copperConditions.copperCondition != ''">
				[铜条件] = #{copperConditions.copperCondition},
			</if>
			<if test="copperConditions.copperConditionName != null and copperConditions.copperConditionName != ''">
				[铜条件名] = #{copperConditions.copperConditionName},
			</if>
			<if test="copperConditions.currency != null and copperConditions.currency != ''">
				[货币] = #{copperConditions.currency},
			</if>
			<if test="copperConditions.updaterName != null and copperConditions.updaterName != ''">
				[更新人姓名] = #{copperConditions.updaterName},
			</if>
			<if test="copperConditions.updatedTime != null">
				[更新时间] = #{copperConditions.updatedTime},
			</if>
		</set>
		WHERE [流水号] = #{copperConditions.id}
	</update>
	
	<delete id="deleteCopperConditions">
		DELETE [businessOps].[dbo].[铜条件表] WHERE [流水号] = #{id}
	</delete>
	
	<select id="findCopperConditions" resultType="com.hongru.entity.businessOps.CopperConditions">
		SELECT
		<include refid="copperConditions_sql"/>
		FROM [businessOps].[dbo].[铜条件表] pc
		<where>
			<if test="copperCondition != null and copperCondition != ''">
				AND pc.[铜条件] = #{copperCondition}
			</if>
		</where>
	</select>
	
	<select id="selectCopperCondition" resultType="String">
		SELECT DISTINCT([铜条件])  AS copperCondition FROM [businessOps].[dbo].[铜条件表]
	</select>
</mapper>