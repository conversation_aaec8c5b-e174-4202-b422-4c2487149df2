package com.hongru.service.businessOps;

import java.util.List;

import javax.servlet.http.HttpServletResponse;

import com.hongru.pojo.dto.CuBaseDTO;
import com.hongru.pojo.dto.CustomerSalesRevenueDTO;
import com.hongru.pojo.dto.DepartmentalSalesRevenueDTO;
import com.hongru.pojo.dto.MonthlyShipmentDetailDTO;
import com.hongru.pojo.dto.MonthlyShipmentPerformanceDTO;
import com.hongru.pojo.dto.ShipmentSalesDiffDTO;
import com.hongru.pojo.dto.UserMarginDTO;
import com.hongru.pojo.dto.ZeroBaseReportDTO;

public interface ISalesReportService {

        /**
         * 查询部门销售额数据
         * 
         * @param startDate 开始日期
         * @param endDate   结束日期
         * @return 部门销售额数据
         */
        List<DepartmentalSalesRevenueDTO> listDepartmentalSalesRevenue(String startDate, String endDate);

        /**
         * 导出部门销售额数据到Excel
         * 
         * @param startDate 开始日期
         * @param endDate   结束日期
         * @param response  HTTP响应
         * @throws Exception 异常
         */
        void exportDepartmentalSalesRevenue(String startDate, String endDate, HttpServletResponse response)
                        throws Exception;

        /**
         * 查询客户销售额数据
         * 
         * @param startDate 开始日期
         * @param endDate   结束日期
         * @return 客户销售额数据
         */
        List<CustomerSalesRevenueDTO> listCustomerSalesRevenue(String startDate, String endDate);

        /**
         * 导出客户销售额数据到Excel
         * 
         * @param startDate 开始日期
         * @param endDate   结束日期
         * @param response  HTTP响应
         * @throws Exception 异常
         */
        void exportCustomerSalesRevenue(String startDate, String endDate, HttpServletResponse response)
                        throws Exception;

        /**
         * 查询所有产品中分类
         * 
         * @return 产品中分类列表
         */
        List<String> selectDistinctProductMiddleCategory();

        /**
         * 查询出货和销售额差数据
         *
         * @param startDate 开始日期
         * @param endDate   结束日期
         * @return 出货和销售额差数据
         */
        List<ShipmentSalesDiffDTO> listShipmentSalesDiff(String startDate, String endDate);

        /**
         * 根据年月查询出货和销售额差数据
         * 计算选择年月的上个月销售额登录数据与当前年月支付请求数据的差额
         *
         * @param yearMonth 年月（格式：yyyy-MM）
         * @return 出货和销售额差数据
         */
        List<ShipmentSalesDiffDTO> listShipmentSalesDiffByYearMonth(String yearMonth);

        /**
         * 导出出货和销售额差数据到Excel
         *
         * @param startDate 开始日期
         * @param endDate   结束日期
         * @param response  HTTP响应
         * @throws Exception 异常
         */
        void exportShipmentSalesDiff(String startDate, String endDate, HttpServletResponse response)
                        throws Exception;

        /**
         * 根据年月导出出货和销售额差数据到Excel
         *
         * @param yearMonth 年月（格式：yyyy-MM）
         * @param response  HTTP响应
         * @throws Exception 异常
         */
        void exportShipmentSalesDiffByYearMonth(String yearMonth, HttpServletResponse response) throws Exception;

        /**
         * 查询月出货明细数据
         * 
         * @param startDate 开始日期
         * @param endDate   结束日期
         * @return 月出货明细数据
         */
        List<MonthlyShipmentDetailDTO> listMonthlyShipmentDetail(String startDate, String endDate);

        /**
         * 导出月出货明细数据到Excel
         * 
         * @param startDate 开始日期
         * @param endDate   结束日期
         * @param response  HTTP响应
         * @throws Exception 异常
         */
        void exportMonthlyShipmentDetail(String startDate, String endDate, HttpServletResponse response)
                        throws Exception;

        /**
         * 查询月出货实绩数据
         * 
         * @param startDate 开始日期
         * @param endDate   结束日期
         * @return 月出货实绩数据
         */
        List<MonthlyShipmentPerformanceDTO> listMonthlyShipmentPerformance(String startDate, String endDate);

        /**
         * 导出月出货实绩数据到Excel
         * 
         * @param startDate 开始日期
         * @param endDate   结束日期
         * @param response  HTTP响应
         * @throws Exception 异常
         */
        void exportMonthlyShipmentPerformance(String startDate, String endDate, HttpServletResponse response)
                        throws Exception;

        /**
         * 查询Cu Base数据
         * 
         * @param startDate 开始日期
         * @param endDate   结束日期
         * @return Cu Base数据列表
         */
        List<CuBaseDTO> listCuBase(String startDate, String endDate);

        /**
         * 导出Cu Base数据到Excel
         *
         * @param startDate 开始日期
         * @param endDate   结束日期
         * @param response  HTTP响应
         * @throws Exception 异常
         */
        void exportCuBase(String startDate, String endDate, HttpServletResponse response) throws Exception;

        /**
         * 查询0-base报表数据
         *
         * @param startAccountingYearMonth 开始会计年月
         * @param endAccountingYearMonth   结束会计年月
         * @return 0-base报表数据列表
         */
        List<ZeroBaseReportDTO> listZeroBaseReport(String startAccountingYearMonth, String endAccountingYearMonth);

        /**
         * 导出0-base报表数据到Excel
         *
         * @param startAccountingYearMonth 开始会计年月
         * @param endAccountingYearMonth   结束会计年月
         * @param response                 HTTP响应
         * @throws Exception 异常
         */
        void exportZeroBaseReport(String startAccountingYearMonth, String endAccountingYearMonth,
                        HttpServletResponse response) throws Exception;

        /**
         * 查询User-Margin报表数据
         *
         * @param accountingYearMonth 会计年月
         * @return User-Margin报表数据列表
         */
        List<UserMarginDTO> listUserMargin(String accountingYearMonth);

        /**
         * 导出User-Margin报表数据到Excel
         *
         * @param accountingYearMonth 会计年月
         * @param response            HTTP响应
         * @throws Exception 异常
         */
        void exportUserMargin(String accountingYearMonth, HttpServletResponse response) throws Exception;
}