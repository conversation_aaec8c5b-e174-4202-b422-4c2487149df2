package com.hongru.entity.businessOps;

import java.math.BigDecimal;


public class WireDiscUnitWeightUfBean {

	/* 入库重量 */
	protected BigDecimal storageWeight;
	/* 线盘个数 */
	protected int wireDiscNum;

	public BigDecimal getStorageWeight() {
		return storageWeight;
	}

	public void setStorageWeight(BigDecimal storageWeight) {
		this.storageWeight = storageWeight;
	}

	public int getWireDiscNum() {
		return wireDiscNum;
	}

	public void setWireDiscNum(int wireDiscNum) {
		this.wireDiscNum = wireDiscNum;
	}

}