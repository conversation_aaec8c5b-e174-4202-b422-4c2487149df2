<%@ page language="java" import="java.util.*"
         contentType="text/html; charset=utf-8"%>
<%@ include file="/WEB-INF/layouts/base.jsp"%>
<html>
<head>
    <title>税率计算修改</title>
    <style>
        /*设置数据表表头字体*/
        .layui-table th .layui-table-cell{
            text-align: center;
        }
    </style>
</head>
<body>
<form class="layui-form hr-form-add" action="${ctx}/salesCommon/taxRateCalculation/edit" method="post" id="submitForm">
    <div class="layui-fluid hr-layui-fluid">
        <div class="layui-card">
            <div class="layui-card-body">
                <div class="layui-form-item layui-row">
                    <div class="layui-inline layui-col-md5">
                        <label class="layui-form-label">税率代码:</label>
                        <div class="layui-input-block">
                             <input type="text" class="layui-input" id="taxCode" name="taxCode" value="${taxRateCalculation.taxCode}" readonly/>
                        </div>
                    </div>
                    <div class="layui-inline layui-col-md5">
                        <label class="layui-form-label">货币:</label>
                        <div class="layui-input-block"  style="display: flex;align-items: center;align-content: center;">
                            <input type="radio" name="taxCalcName" id="taxCalcName" value="含税" title="含税" ${taxRateCalculation.taxCalcName eq '含税'?'checked':''}/>
                            <input type="radio" name="taxCalcName" id="taxCalcName" value="不含税" title="不含税" ${taxRateCalculation.taxCalcName eq '不含税'?'checked':''}>
                            <input type="radio" name="taxCalcName" id="taxCalcName" value="无税" title="无税" ${taxRateCalculation.taxCalcName eq '无税'?'checked':''}>
                        </div>
                        </div>
                    </div>
                    <div class="layui-inline layui-col-md5">
		                <label class="layui-form-label"><span class="star">*</span>税率(%):</label>
		                <div class="layui-input-block">
                            <input type="text" class="layui-input" id="taxRate" name="taxRate" value="${taxRateCalculation.taxRate}" onKeyUp="amount(this)" onBlur="overFormat(this)"  lay-verify="required" required/>
                        </div>
                    </div>
                </div>
                </div>
                <div class="layui-form-item layui-row">
                    <div class="layui-inline layui-col-md12">
                        <label class="layui-form-label"></label>
                        <div class="layui-input-block">
                            <input type="hidden" id="id" name="id" value="${taxRateCalculation.id}"/>
                            <button type="button" class="layui-btn layui-bg-blue" lay-submit lay-filter="formDemo">保存</button>
                            <button type="button" class="layui-btn layui-btn-primary" onclick="closeAll();">取消</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</form>
<myfooter>
    <script src="${ctxsta}/hongru/js/salesCommon/taxRateCalculation_modify.js?time=1"></script>
</myfooter>
</body>
</html>
