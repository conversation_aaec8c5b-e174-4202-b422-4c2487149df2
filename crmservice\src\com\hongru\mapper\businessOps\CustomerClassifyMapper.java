package com.hongru.mapper.businessOps;

import com.baomidou.mybatisplus.mapper.BaseMapper;
import com.hongru.entity.businessOps.CustomerClassify;

import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface CustomerClassifyMapper extends BaseMapper<CustomerClassify> {
	int insertCustomerClassify(@Param("customerClassify") CustomerClassify customerClassify);
  
	CustomerClassify selectCustomerClassifyById(@Param("id") int id);

    List<CustomerClassify> listCustomerClassify(@Param("customerCategoryCode") String customerCategoryCode, @Param("customerCategory") String customerCategory);

    void updateCustomerClassify(@Param("customerClassify") CustomerClassify customerClassify);

    void deleteCustomerClassify(@Param("id") Integer id);
}
