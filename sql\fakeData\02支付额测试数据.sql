-- Insert test data for payment table
INSERT INTO 支付额表 (销售额NO, 支付日期, 支付金额, 支付状态, 支付方式, 备注, 创建人, 更新人) VALUES
-- SSK1 data
('S202502001', '2025-02-15', 51520.00, '已支付', '银行转账', '全额支付', 'admin', 'admin'),

-- WINTEC2 data
('S202502002', '2025-02-20', 14521750.00, '已支付', '银行转账', '全额支付', 'admin', 'admin'),

-- Shanghai Mitsubishi data
('S202502003', '2025-02-25', 988125.00, '已支付', '银行转账', '全额支付', 'admin', 'admin'),

-- Shanghai Sanchong data
('S202502004', '2025-02-28', 549150.00, '已支付', '银行转账', '全额支付', 'admin', 'admin'),

-- Shanghai Weike data
('S202502005', '2025-03-01', 416240.00, '已支付', '银行转账', '全额支付', 'admin', 'admin'),

-- Fuji <PERSON>ko data
('S202502006', '2025-03-05', 381840.00, '已支付', '银行转账', '全额支付', 'admin', 'admin'),

-- Toyo Electric data
('S202502007', '2025-03-10', 35920.00, '已支付', '银行转账', '全额支付', 'admin', 'admin'),

-- Dongguan Lisheng data
('S202502008', '2025-03-15', 196800.00, '已支付', '银行转账', '全额支付', 'admin', 'admin'),

-- Delta Electronics data
('S202502009', '2025-03-20', 708400.00, '已支付', '银行转账', '全额支付', 'admin', 'admin'),

-- Foshan Aisan data
('S202502010', '2025-03-25', 50450.00, '已支付', '银行转账', '全额支付', 'admin', 'admin'),

-- India Denso data
('S202502011', '2025-03-30', 31046.50, '已支付', '银行转账', '全额支付', 'admin', 'admin'),

-- Pakistan Honda data
('S202502012', '2025-04-01', 124580.00, '已支付', '银行转账', '全额支付', 'admin', 'admin'),

-- Japan Citizen data
('S202502013', '2025-04-05', 13911.75, '已支付', '银行转账', '全额支付', 'admin', 'admin'),

-- Korea Denso data
('S202502014', '2025-04-10', 38628.00, '已支付', '银行转账', '全额支付', 'admin', 'admin');

-- Verify data insertion
SELECT 
    a.客户简称,
    a.结算货币,
    b.支付日期,
    b.支付金额,
    b.支付状态,
    b.支付方式
FROM 销售额表 a
LEFT JOIN 支付额表 b ON a.销售额NO = b.销售额NO
ORDER BY b.支付日期; 