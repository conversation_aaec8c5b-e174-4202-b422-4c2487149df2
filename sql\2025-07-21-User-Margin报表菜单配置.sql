-- User-Margin报表菜单配置
-- 创建日期：2025-07-21
-- 描述：为User-Margin报表添加菜单项

USE [BusinessOps]
GO

-- 添加User-Margin报表菜单项
-- 注意：parent_id需要根据实际的销售报表父菜单ID进行调整
-- 这里假设销售报表的parent_id是100308（根据之前的Menu数据.sql文件）
INSERT INTO [dbo].[hr_system_menu]
([parent_id], [menu_type], [menu_code], [menu_name], [sort], [href], [icon], [status], [permission], [create_time], [create_by], [update_time], [update_by], [remarks]) 
VALUES 
(100308, 2, NULL, N'User-Margin', 6, N'/salesReport/userMargin', N'', 1, NULL, GETDATE(), N'超级管理员', NULL, NULL, N'User-Margin报表页面');

PRINT 'User-Margin报表菜单配置完成';

-- 查询验证菜单是否添加成功
SELECT [menu_id], [parent_id], [menu_type], [menu_name], [sort], [href], [status]
FROM [dbo].[hr_system_menu]
WHERE [menu_name] = N'User-Margin'
ORDER BY [menu_id] DESC;
