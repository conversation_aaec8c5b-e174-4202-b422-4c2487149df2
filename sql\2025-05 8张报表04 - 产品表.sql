 -- 创建产品表
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[产品表]') AND type in (N'U'))
BEGIN
    CREATE TABLE [dbo].[产品表](
        [流水号] [int] IDENTITY(1,1) NOT NULL,
        [产品代码] [nvarchar](50) NOT NULL,
        [产品名称] [nvarchar](100) NULL,
        [尺寸] [nvarchar](20) NULL,
        [产品中分类] [nvarchar](50) NULL,
        [最小销售单位] [nvarchar](10) NULL,
        [创建人姓名] [nvarchar](50) NULL,
        [创建时间] [datetime] NULL,
        [更新人姓名] [nvarchar](50) NULL,
        [更新时间] [datetime] NULL,
        CONSTRAINT [PK_产品表] PRIMARY KEY CLUSTERED 
        (
            [流水号] ASC
        )
    )
END

-- alter增加标签尺寸名称字段
ALTER TABLE [dbo].[产品表]
ADD [标签尺寸名称] [nvarchar](50) NULL;

-- alter增加线盘名称字段
ALTER TABLE [dbo].[产品表]
ADD [线盘名称] [nvarchar](50) NULL;