
layui.use(['laydate', 'layer', 'table', 'element','form'], function(){
    var laydate = layui.laydate //日期
        ,layer = layui.layer //弹层
        ,table = layui.table //表格
        ,element = layui.element //元素操作
    var form = layui.form;
  //开始日期
  var insStart = layui.laydate.render({
      elem: '#applyDateStart'
//      ,min: 0
      ,done: function(value, date){
    	//更新结束日期的最小日期
    	insEnd.config.min = lay.extend({}, date, {
    	  month: date.month - 1
    	});
    	
    	//自动弹出结束日期的选择器
       insEnd.config.elem[0].focus();
      }
    });

    //结束日期
    var insEnd = layui.laydate.render({
      elem: '#applyDateEnd'
//      ,min: 0
      ,done: function(value, date){
    	//更新开始日期的最大日期
    	insStart.config.max = lay.extend({}, date, {
    	  month: date.month - 1
    	});
      }
    });   
        
    //执行一个 table 实例
    var url = baselocation+'/salesCommon/copperPrices/list';
    table.render({
        elem: '#demo'
//        ,height: 'full-185'
        ,url: url //数据接口
        ,cellMinWidth: 80 //全局定义常规单元格的最小宽度
        ,parseData:function(res){ //res 即为原始返回的数据
            return {
                "code": 0, //解析接口状态
                "msg": '', //解析提示文本
                "count": res.total, //解析数据长度
                "data": res.rows //解析数据列表
            };
        }
        ,method:'post'
        ,title: '铜价列表'
        ,page: true //开启分页
        ,where:$("#formSearch").serializeJsonObject()
        ,toolbar: '#toolbarDemo' //开启工具栏，此处显示默认图标，可以自定义模板，详见文档
        ,defaultToolbar: ['filter']
        ,totalRow: false //开启合计行
        ,cols: [[ //表头
            {field: 'copperCondition',title: '铜条件',align:'center'}
            ,{field: 'copperConditionName',title: '铜条件名',align:'center'}
            ,{field: 'currency',title: '货币',align:'center'}
            ,{field: 'copperPrice',title: '铜基本',align:'center',
            	templet: function(d){
					if(d.copperPrice != null){
						return d.copperPrice.toFixed(5);
					}else{
						return "0.00000";
					}
            	}
            }
            ,{field: 'applyDateStart',title: '适用开始日',align:'center'}
            ,{field: 'applyDateEnd',title: '适用结束日',align:'center'}
            ,{field: 'creatorName',title: '创建者',align:'center'}
            ,{field: 'createdTime',title: '创建时间',align:'center'}
            ,{field: 'updaterName',title: '更新者',align:'center'}
            ,{field: 'updatedTime',title: '更新时间',align:'center'}
            ,{title: '操作',minWidth:150, align:'left',fixed: 'right', toolbar: '#barDemo'}
        ]] 
    });

    //监听头工具栏事件
    table.on('toolbar(test)', function(obj){
        var checkStatus = table.checkStatus(obj.config.id)
            ,data = checkStatus.data; //获取选中的数据
        switch(obj.event){
            case 'toAdd':
                layer_show('添加', baselocation+"/salesCommon/copperPrices/add/view", 750, document.body.clientHeight-100)
                break;
            case 'refresh':
                var temp = $("#formSearch").serializeJsonObject();
                console.info(temp);
                layui.table.reload('demo', {
                    page: {
                        curr: 1
                    }
                    ,where: temp
                }, 'data');
                break;
        };
    });

    //监听行工具事件
    table.on('tool(test)', function(obj){ //注：tool 是工具条事件名，test 是 table 原始容器的属性 lay-filter="对应的值"
        var data = obj.data //获得当前行数据
            ,layEvent = obj.event; //获得 lay-event 对应的值
        if(layEvent === 'edit'){
            layer_show('编辑',baselocation+"/salesCommon/copperPrices/edit/view?id="+data.id,document.body.clientWidth-10, document.body.clientHeight-10);
        }else if(layEvent === 'remove'){
            layer.confirm('确认要删除吗？', {
                btn : [ '确定', '取消' ] //按钮
            }, function() {
                $.ajax({
                    type : 'post',
                    dataType : 'json',
                    data: {"id":data.id},
                    url : baselocation + '/salesCommon/copperPrices/remove',
                    success : function(result) {
                        if (result.code == 1) {
                            layer.msg("操作成功!", {
                                shade : 0.3,
                                time : 1500
                            }, function() {
                                search();
                                layer.closeAll();
                            });
                        } else {
                            layer.alert(result.message, {
                                icon : 2
                            });
                        }
                    }
                })
            });
        }
    });

});

function search() {
	var temp = $("#formSearch").serializeJsonObject();
	console.info(temp);
	layui.table.reload('demo', {
		page: {
			curr: 1
		}
		,where: temp
	}, 'data');
}
