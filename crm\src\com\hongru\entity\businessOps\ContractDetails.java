package com.hongru.entity.businessOps;

import com.baomidou.mybatisplus.annotations.TableField;
import com.baomidou.mybatisplus.annotations.TableId;
import com.baomidou.mybatisplus.annotations.TableName;
import com.baomidou.mybatisplus.enums.IdType;


@TableName("合同方详情表")//CostPrice
public class ContractDetails {
	
	/* 流水号 */
	@TableId(value="流水号", type= IdType.AUTO)
	protected int id;
	/* 合同方代码 */
	protected String contractCode;
	/* 合同方简称 */
	protected String contractAlias;
	/* 合同方名称 */
	protected String contractName;
	/* 合同方地址 */
	protected String contractAddress;
	/* 创建人姓名 */
	protected String creatorName;
	/* 创建时间 */
	protected String createdTime;
	/* 更新人姓名 */
	protected String updaterName;
	/* 更新时间 */
	protected String updatedTime;
	public int getId() {
		return id;
	}
	public void setId(int id) {
		this.id = id;
	}
	public String getContractCode() {
		return contractCode;
	}
	public void setContractCode(String contractCode) {
		this.contractCode = contractCode;
	}
	public String getContractAlias() {
		return contractAlias;
	}
	public void setContractAlias(String contractAlias) {
		this.contractAlias = contractAlias;
	}
	public String getContractAddress() {
		return contractAddress;
	}
	public String getContractName() {
		return contractName;
	}
	public void setContractName(String contractName) {
		this.contractName = contractName;
	}
	public void setContractAddress(String contractAddress) {
		this.contractAddress = contractAddress;
	}
	public String getCreatorName() {
		return creatorName;
	}
	public void setCreatorName(String creatorName) {
		this.creatorName = creatorName;
	}
	public String getCreatedTime() {
		return createdTime;
	}
	public void setCreatedTime(String createdTime) {
		this.createdTime = createdTime;
	}
	public String getUpdaterName() {
		return updaterName;
	}
	public void setUpdaterName(String updaterName) {
		this.updaterName = updaterName;
	}
	public String getUpdatedTime() {
		return updatedTime;
	}
	public void setUpdatedTime(String updatedTime) {
		this.updatedTime = updatedTime;
	}
}