package com.hongru.pojo.dto;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 月出货实绩DTO
 * 用于月出货实绩报表的数据传输
 */
public class MonthlyShipmentPerformanceDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 会计年月
     */
    private String accountingYearMonth;

    /**
     * 国内/国外
     */
    private String domesticOrForeign;

    /**
     * 需求方略称（客户简称）
     */
    private String customerAlias;

    /**
     * 品目中分类名（产品中分类）
     */
    private String productMiddleCategory;

    /**
     * 品名（产品代码）
     */
    private String productCode;

    /**
     * 出货数
     */
    private BigDecimal shipmentQuantity;

    /**
     * 合计出货数
     */
    private BigDecimal totalShipmentQuantity;

    // Getters and Setters
    public String getAccountingYearMonth() {
        return accountingYearMonth;
    }

    public void setAccountingYearMonth(String accountingYearMonth) {
        this.accountingYearMonth = accountingYearMonth;
    }

    public String getDomesticOrForeign() {
        return domesticOrForeign;
    }

    public void setDomesticOrForeign(String domesticOrForeign) {
        this.domesticOrForeign = domesticOrForeign;
    }

    public String getCustomerAlias() {
        return customerAlias;
    }

    public void setCustomerAlias(String customerAlias) {
        this.customerAlias = customerAlias;
    }

    public String getProductMiddleCategory() {
        return productMiddleCategory;
    }

    public void setProductMiddleCategory(String productMiddleCategory) {
        this.productMiddleCategory = productMiddleCategory;
    }

    public String getProductCode() {
        return productCode;
    }

    public void setProductCode(String productCode) {
        this.productCode = productCode;
    }

    public BigDecimal getShipmentQuantity() {
        return shipmentQuantity;
    }

    public void setShipmentQuantity(BigDecimal shipmentQuantity) {
        this.shipmentQuantity = shipmentQuantity;
    }

    public BigDecimal getTotalShipmentQuantity() {
        return totalShipmentQuantity;
    }

    public void setTotalShipmentQuantity(BigDecimal totalShipmentQuantity) {
        this.totalShipmentQuantity = totalShipmentQuantity;
    }
}