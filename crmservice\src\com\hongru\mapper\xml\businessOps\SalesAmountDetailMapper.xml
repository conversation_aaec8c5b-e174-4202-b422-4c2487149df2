<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hongru.mapper.businessOps.SalesAmountDetailMapper">
  <sql id="salesAmountDetail_sql">
    sad.[销售额明细ID] AS id,
    sad.[销售额NO] AS salesAmountNo,
    sad.[产品代码] AS productCode,
    sad.[出库日期] AS shipmentDate,
    sad.[到货日期] AS arrivalDate,
    sad.[客户订单号] AS customerOrderNo,
    sad.[明细备注] AS detailRemark,
    sad.[铜合同类别] AS copperContractType,
    sad.[铜合同NO] AS copperContractNo,
    sad.[铜条件] AS copperCondition,
    sad.[换算率] AS conversionRate,
    sad.[数量] AS quantity,
    sad.[铜base] AS copperBase,
    sad.[升水] AS premium,
    sad.[换算后铜单价] AS convertedCopperPrice,
    sad.[零基础] AS zeroBase,
    sad.[销售单价] AS salesUnitPrice,
    sad.[销售金额] AS salesAmount,
    sad.[产品中分类] AS productMiddleCategory,
    sad.[出货计划番号] AS shipmentPlanNo,
    sad.[创建人] AS creatorName,
    sad.[创建时间] AS createdTime,
    sad.[更新人] AS updaterName,
    sad.[更新时间] AS updatedTime,
    sad.[送货单号] AS deliveryOrderNo,
    sad.[送货单序号] AS deliveryOrderSeq,
    sad.[品目C] AS productCategoryCode,
    sad.[理由] AS reason,
    sad.[线盘] AS reelName
  </sql>

  <!-- 根据销售额NO获取销售额明细列表 -->
  <select id="getSalesAmountDetailsByNo" resultType="com.hongru.entity.businessOps.SalesAmountDetail">
    SELECT
      <include refid="salesAmountDetail_sql"/>
    FROM [销售额明细表] sad
    WHERE sad.[销售额NO] = #{salesAmountNo}
    ORDER BY sad.[销售额明细ID]
  </select>

  <!-- 更新销售额明细数据 -->
  <update id="updateSalesAmountDetail">
    UPDATE [销售额明细表] SET
      [出库日期] = #{detail.shipmentDate},
      [客户订单号] = #{detail.customerOrderNo},
      [明细备注] = #{detail.detailRemark},
      [铜合同类别] = #{detail.copperContractType},
      [铜合同NO] = #{detail.copperContractNo},
      [铜条件] = #{detail.copperCondition},
      [换算率] = #{detail.conversionRate},
      [数量] = #{detail.quantity},
      [铜base] = #{detail.copperBase},
      [升水] = #{detail.premium},
      [换算后铜单价] = #{detail.convertedCopperPrice},
      [零基础] = #{detail.zeroBase},
      [销售单价] = #{detail.salesUnitPrice},
      [销售金额] = #{detail.salesAmount},
      [产品中分类] = #{detail.productMiddleCategory},
      [出货计划番号] = #{detail.shipmentPlanNo},
      [更新人] = #{detail.updaterName},
      [更新时间] = #{detail.updatedTime},
      [送货单号] = #{detail.deliveryOrderNo},
      [送货单序号] = #{detail.deliveryOrderSeq},
      [品目C] = #{detail.productCategoryCode},
      [理由] = #{detail.reason},
      [线盘] = #{detail.reelName}
    WHERE [销售额明细ID] = #{detail.id}
  </update>

  <!-- 根据销售额NO删除销售额明细数据 -->
  <delete id="deleteBySalesAmountNo">
    DELETE FROM [销售额明细表]
    WHERE [销售额NO] = #{salesAmountNo}
  </delete>
  
  <!-- 自定义插入销售额明细表数据 -->
  <insert id="insert" parameterType="com.hongru.entity.businessOps.SalesAmountDetail">
    INSERT INTO 销售额明细表 (
      销售额NO,
      产品代码,
      出库日期,
      到货日期,
      客户订单号,
      明细备注,
      铜合同类别,
      铜合同NO,
      铜条件,
      换算率,
      数量,
      铜base,
      升水,
      换算后铜单价,
      零基础,
      销售单价,
      销售金额,
      产品中分类,
      出货计划番号,
      创建人,
      创建时间,
      更新人,
      更新时间,
      送货单号,
      送货单序号,
      品目C,
      理由,
      线盘
    ) VALUES (
      #{salesAmountNo},
      #{productCode},
      #{shipmentDate},
      #{arrivalDate},
      #{customerOrderNo},
      #{detailRemark},
      #{copperContractType},
      #{copperContractNo},
      #{copperCondition},
      #{conversionRate},
      #{quantity},
      #{copperBase},
      #{premium},
      #{convertedCopperPrice},
      #{zeroBase},
      #{salesUnitPrice},
      #{salesAmount},
      #{productMiddleCategory},
      #{shipmentPlanNo},
      #{creatorName},
      #{createdTime},
      #{updaterName},
      #{updatedTime},
      #{deliveryOrderNo},
      #{deliveryOrderSeq},
      #{productCategoryCode},
      #{reason},
      #{reelName}
    )
  </insert>
</mapper>
