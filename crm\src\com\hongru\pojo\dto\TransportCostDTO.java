package com.hongru.pojo.dto;

import com.hongru.entity.businessOps.TransportCost;
import com.hongru.support.page.PageInfo;

import java.util.List;

public class TransportCostDTO {

    private PageInfo pageInfo;

    private List<TransportCost> transportCostList;

    public TransportCostDTO(PageInfo pageInfo, List<TransportCost> transportCostList) {
        super();
        this.pageInfo = pageInfo;
        this.transportCostList = transportCostList;
    }

    public PageInfo getPageInfo() {
        return pageInfo;
    }

    public void setPageInfo(PageInfo pageInfo) {
        this.pageInfo = pageInfo;
    }

    public List<TransportCost> getTransportCostList() {
        return transportCostList;
    }

    public void setTransportCostList(List<TransportCost> transportCostList) {
        this.transportCostList = transportCostList;
    }
}
