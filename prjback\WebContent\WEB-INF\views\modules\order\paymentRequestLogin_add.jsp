<%@ page language="java" import="java.util.*"
         contentType="text/html; charset=utf-8"%>
<%@ include file="/WEB-INF/layouts/base.jsp"%>
<html>
<head>
    <title>支付请求登录</title>
    <style>
        .layui-form-label {
            width: 120px;
            padding: 8px 5px;
            text-align: right;
            box-sizing: border-box;
        }
        .layui-input-block {
            margin-left: 130px;
            box-sizing: border-box;
        }
        .layui-form-item {
            display: table;
            width: 100%;
            table-layout: fixed;
            margin-bottom: 15px;
        }
        .layui-inline {
            display: table-cell;
            width: 25%;
            padding-right: 10px;
            box-sizing: border-box;
            float: none;
        }
        .hr-line {
            margin: 15px 0;
            border-top: 1px solid #e6e6e6;
        }
        /* 明细信息部分的特殊处理 */
        .layui-block {
            width: 100%;
            display: block;
        }

        /* 表格容器样式 */
        .detail-table-container {
            width: 100%;
            padding: 0;
            margin-left: 0;
        }

        /* 确保表格宽度100% */
        .layui-table {
            width: 100% !important;
            margin: 0;
        }

        /* 底部按钮居中对齐 */
        .btn-container {
            text-align: center;
            margin-top: 20px;
        }
        .btn-container .layui-btn {
            margin: 0 10px;
        }

        /* 移动端适配 */
        @media screen and (max-width: 768px) {
            .layui-form-item {
                display: block;
            }
            .layui-inline {
                display: block;
                width: 100%;
                padding-right: 0;
                margin-bottom: 10px;
            }
        }

        /* 日期字段样式 */
        .date-cell {
            cursor: pointer;
            color: #1E9FFF;
        }
        .date-cell:hover {
            text-decoration: underline;
        }

        /* 表格中下拉框样式 */
        .layui-table-body select {
            width: 100%;
            border: none;
            height: 28px;
            background-color: transparent;
        }

        /* 确保下拉框父元素可见 */
        .layui-table-cell {
            overflow: visible !important;
            height: auto !important;
            white-space: normal;
        }

        .two-row-data td {
            text-align: center;
            vertical-align: middle;
            border: 1px solid #e6e6e6;
            padding: 3px;
            font-size: 11px;
        }
        .two-row-data input, .two-row-data select {
            width: 100%;
            height: 26px;
            line-height: 26px;
            font-size: 11px;
            border: 1px solid #ddd;
            padding: 2px 5px;
        }

        /* 原生下拉框样式 - 确保不被Layui渲染 */
        .native-select {
            width: 100% !important;
            height: 26px !important;
            line-height: 26px !important;
            font-size: 11px !important;
            border: 1px solid #ddd !important;
            padding: 2px 5px !important;
            background-color: #fff !important;
            appearance: auto !important;
            -webkit-appearance: menulist !important;
            -moz-appearance: menulist !important;
            box-sizing: border-box !important;
        }

        /* 防止Layui渲染原生下拉框 */
        .native-select + .layui-form-select {
            display: none !important;
        }

        /* 隐藏表格中被Layui渲染的下拉框，只显示原生下拉框 */
        .layui-table .layui-form-select {
            display: none !important;
        }

        /* 数量验证错误样式 */
        .quantity-error {
            border: 2px solid #ff4757 !important;
            background-color: #ffe6e6 !important;
            animation: shake 0.5s ease-in-out;
        }
        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            75% { transform: translateX(5px); }
        }

        /* 铜单价默认值设定区域简洁样式 */
        .copper-default-label {
            color: #495057 !important;
            font-weight: 500 !important;
        }

        .copper-default-select {
            background: #fff !important;
            border: 1px solid #17a2b8 !important;
            border-radius: 3px !important;
            color: #495057 !important;
        }

        .copper-default-select:focus {
            border-color: #138496 !important;
            box-shadow: 0 0 0 2px rgba(23, 162, 184, 0.1) !important;
        }

        .copper-apply-btn {
            background: #17a2b8 !important;
            border: 1px solid #17a2b8 !important;
            border-radius: 3px !important;
            padding: 6px 16px !important;
            color: #fff !important;
            font-size: 12px !important;
            display: inline-flex !important;
            align-items: center !important;
            justify-content: center !important;
            vertical-align: middle !important;
        }

        .copper-apply-btn:hover {
            background: #138496 !important;
            border-color: #117a8b !important;
        }

        /* 只隐藏表格中被Layui渲染的下拉框 */
        .layui-table .layui-form-select {
            display: none !important;
        }

        /* 确保表格中的原生select可见 */
        .layui-table select.native-select,
        select.native-select {
            display: block !important;
            position: relative !important;
            z-index: 1 !important;
            visibility: visible !important;
            background-color: #fff !important;
        }

        /* 强制隐藏native-select后面的Layui渲染元素（只在表格中） */
        .layui-table select.native-select + .layui-form-select {
            display: none !important;
        }
    </style>
</head>
<body class="gray-bg">
<div class="wrapper wrapper-content animated fadeInRight">
    <div class="row">
        <div class="col-sm-12">
            <div class="ibox float-e-margins">
                <div class="ibox-content">
                    <form class="layui-form" id="form" action="">
                        <!-- 第一行：4个字段 -->
                        <div class="layui-form-item">
                            <div class="layui-inline layui-col-md3">
                                <label class="layui-form-label">支付请求日:</label>
                                <div class="layui-input-block">
                                    <input type="text" name="paymentRequestDate" id="paymentRequestDate" required lay-verify="required" autocomplete="off" class="layui-input">
                                </div>
                            </div>
                            <div class="layui-inline layui-col-md3">
                                <label class="layui-form-label">要求支付日:</label>
                                <div class="layui-input-block">
                                    <input type="text" name="paymentDate" id="paymentDate" required lay-verify="required" autocomplete="off" class="layui-input">
                                </div>
                            </div>
                            <div class="layui-inline layui-col-md3">
                                <label class="layui-form-label">需求方:</label>
                                <div class="layui-input-block">
                                    <select class="layui-select" id="customerCode" name="customerCode" lay-filter="customerCodeFilter" lay-search="true" lay-verify="required">
                                        <option value="">请选择</option>
                                        <c:forEach items="${customersList}" var="customers">
                                            <option value="${customers.customerCode}">${customers.customerCode} - ${customers.customerAlias}</option>
                                        </c:forEach>
                                    </select>
                                </div>
                            </div>
                            <div class="layui-inline layui-col-md3">
                                <label class="layui-form-label">税率(%):</label>
                                <div class="layui-input-block">
                                    <input type="text" name="taxRate" id="taxRate" autocomplete="off" class="layui-input" readonly>
                                    <!-- 隐藏字段保存税代码 -->
                                    <input type="hidden" name="taxCode" id="taxCode">
                                </div>
                            </div>
                        </div>

                        <!-- 第二行：4个字段 -->
                        <div class="layui-form-item">
                            <div class="layui-inline layui-col-md3">
                                <label class="layui-form-label">货币:</label>
                                <div class="layui-input-block">
                                    <select class="layui-select" id="currency" name="currency" lay-verify="required">
                                        <option value="RMB">RMB</option>
                                        <option value="USD">USD</option>
                                        <option value="EUR">EUR</option>
                                        <option value="JPY">JPY</option>
                                    </select>
                                </div>
                            </div>
                            <div class="layui-inline layui-col-md3">
                                <label class="layui-form-label">总请求金额:</label>
                                <div class="layui-input-block">
                                    <input type="text" name="totalAmount" id="totalAmount" readonly class="layui-input">
                                </div>
                            </div>
                            <div class="layui-inline layui-col-md3">
                                <label class="layui-form-label">税额:</label>
                                <div class="layui-input-block">
                                    <input type="text" name="taxAmount" id="taxAmount" readonly class="layui-input">
                                </div>
                            </div>
                            <div class="layui-inline layui-col-md3">
                                <label class="layui-form-label">交易条件:</label>
                                <div class="layui-input-block">
                                    <select class="layui-select" id="tradeCondition" name="tradeCondition">
                                        <option value="">请选择</option>
                                    </select>
                                </div>
                            </div>
                            <div class="layui-inline layui-col-md3">
                                <label class="layui-form-label">付款条件:</label>
                                <div class="layui-input-block">
                                    <select class="layui-select" id="payCondition" name="payCondition">
                                        <option value="">请选择</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- 第三行：1个字段 -->
                        <div class="layui-form-item">
                            <div class="layui-inline layui-col-md3">
                                <label class="layui-form-label">运输条件:</label>
                                <div class="layui-input-block">
                                    <select class="layui-select" id="transportCondition" name="transportCondition">
                                        <option value="">请选择</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- 铜单价默认值设定区域 - 简洁样式 -->
                        <div style="background-color: #f8f9fa; padding: 15px; border-radius: 4px; margin: 15px 0; border-left: 3px solid #17a2b8;">
                            <div style="margin-bottom: 10px;">
                                <span style="color: #495057; font-size: 14px; font-weight: 500;">
                                    <i class="layui-icon layui-icon-set" style="margin-right: 5px;"></i>
                                    铜单价默认值设定
                                </span>
                                <span style="margin-left: 8px; color: #6c757d; font-size: 12px;">(批量应用)</span>
                            </div>
                            <div class="layui-form-item" style="margin-bottom: 0;">
                                <div class="layui-inline layui-col-md3">
                                    <label class="layui-form-label copper-default-label">铜合同类别:</label>
                                    <div class="layui-input-block">
                                        <select class="layui-select copper-default-select" id="defaultCopperContractType" name="defaultCopperContractType" lay-filter="defaultCopperContractTypeFilter">
                                            <option value="">请选择</option>
                                            <option value="1">预约铜</option>
                                            <option value="2">支给铜</option>
                                            <option value="3">一般铜</option>
                                            <option value="4">无偿</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="layui-inline layui-col-md3">
                                    <label class="layui-form-label copper-default-label">铜合同NO:</label>
                                    <div class="layui-input-block">
                                        <select class="layui-select copper-default-select" id="defaultCopperContractNo" name="defaultCopperContractNo" lay-filter="defaultCopperContractNoFilter">
                                            <option value="">请选择</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="layui-inline layui-col-md3">
                                    <label class="layui-form-label copper-default-label">铜条件:</label>
                                    <div class="layui-input-block">
                                        <select class="layui-select copper-default-select" id="defaultCopperCondition" name="defaultCopperCondition" lay-filter="defaultCopperConditionFilter">
                                            <option value="">请选择</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="layui-inline layui-col-md3">
                                    <div style="text-align: center; padding-top: 5px;">
                                        <button type="button" class="layui-btn copper-apply-btn" id="applyDefaultValues">
                                            <i class="layui-icon layui-icon-down"></i> 应用
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="hr-line"></div>
                        <div class="layui-form-item" style="display: block;">
                            <div class="layui-block" style="width: 100%;">
                                <div class="layui-input-block detail-table-container">
                                    <div style="margin-bottom: 10px;">
                                        <button type="button" class="layui-btn layui-btn-normal" id="addDetailByShipmentBtn">
                                            <i class="layui-icon layui-icon-add-circle"></i> 新增明细
                                        </button>
                                    </div>
                                    <table class="layui-table" id="detailTable" lay-filter="detailTable"></table>
                                </div>
                            </div>
                        </div>

                        <div class="btn-container">
                            <button class="layui-btn layui-bg-blue" lay-submit lay-filter="formDemo">保存</button>
                            <button type="reset" class="layui-btn layui-btn-primary">重置</button>
                            <button type="button" class="layui-btn layui-btn-primary" onclick="closeLayer()">关闭</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<myfooter>
    <script>
        var baselocation = '${ctx}';
    </script>
    <script src="${ctxsta}/hongru/js/order/paymentRequestLogin_add.js?v=20250725005"></script>
</myfooter>
</body>
</html>
