layui.use(['laydate','upload','element','layer','form'], function() {
    var form = layui.form;
    var $ = layui.jquery
        ,upload = layui.upload
        ,element = layui.element
        ,layer = layui.layer;
     var laydate = layui.laydate;

    form.on('submit(formDemo)', function(data) {
        var index = layer.load(2, {
            shade : [ 0.1, '#fff' ]
        });
        var actionUrl = $("#submitForm").attr('action');
        $.ajax({
            url : actionUrl,
            type : 'post',
            data : $('#submitForm').serialize(),
            success : function(result) {
                layer.closeAll();
                if (result.code == 1) {
                    parent.layer.msg("操作成功!", {
                        shade : 0.3,
                        time : 1500
                    }, function() {
                        parent.window.location.reload();
                    });
                } else {
                    layer.alert(result.message);
                }
            }
        });
        return false;
    });
});

function closeAll() {
    parent.layer.closeAll();
}