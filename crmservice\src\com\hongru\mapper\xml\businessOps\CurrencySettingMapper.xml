<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hongru.mapper.businessOps.CurrencySettingMapper">
    <sql id="currencySetting_sql">
		pc.[流水号] AS id,pc.[货币] AS currency,pc.[货币名] AS CurrencyName,
		pc.[创建人姓名] AS creatorName,pc.[创建时间] AS createdTime,
		pc.[更新人姓名] AS updaterName,pc.[更新时间] AS updatedTime
	</sql>

	<insert id="insertCurrencySetting" parameterType="com.hongru.entity.businessOps.CurrencySetting">
		INSERT INTO [businessOps].[dbo].[货币设定表]
		(
		[货币],
		[货币名],
		[创建人姓名],
		[创建时间]
		)VALUES(
		#{currencySetting.currency},
		#{currencySetting.currencyName},
		#{currencySetting.creatorName},
		#{currencySetting.createdTime}
		)
	</insert>

	<select id="selectCurrencySettingById" resultType="com.hongru.entity.businessOps.CurrencySetting">
		SELECT
		<include refid="currencySetting_sql"/>
		FROM [businessOps].[dbo].[货币设定表] pc
		<where>
			<if test="id != null">
				AND pc.[流水号] = #{id}
			</if>
		</where>
	</select>

	<select id="listCurrencySetting" resultType="com.hongru.entity.businessOps.CurrencySetting">
		SELECT
		<include refid="currencySetting_sql"/>
		FROM [businessOps].[dbo].[货币设定表] pc
		<where>
			<if test="currency != null and currency != ''">
				AND pc.[货币] = #{currency}
			</if>
			<if test="currencyName != null and currencyName != ''">
				AND pc.[货币名] = #{currencyName}
			</if>
		</where>
		ORDER BY pc.[流水号]
	</select>

	<update id="updateCurrencySetting">
		UPDATE [businessOps].[dbo].[货币设定表]
		<set>
			<if test="currencySetting.currency != null and currencySetting.currency != ''">
				[货币] = #{currencySetting.currency},
			</if>
			<if test="currencySetting.currencyName != null and currencySetting.currencyName != ''">
				[货币名] = #{currencySetting.currencyName},
			</if>
			<if test="currencySetting.updaterName != null and currencySetting.updaterName != ''">
				[更新人姓名] = #{currencySetting.updaterName},
			</if>
			<if test="currencySetting.updatedTime != null">
				[更新时间] = #{currencySetting.updatedTime},
			</if>
		</set>
		WHERE [流水号] = #{currencySetting.id}
	</update>
	
	<delete id="deleteCurrencySetting">
		DELETE [businessOps].[dbo].[货币设定表] WHERE [流水号] = #{id}
	</delete>
	
	<select id="selectDistinctCurrency" resultType="String">
		SELECT DISTINCT([货币]) FROM [businessOps].[dbo].[货币设定表]
	</select>
</mapper>