<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hongru.mapper.businessOps.SalesAmountMapper">
  <sql id="salesAmount_sql">
    sa.[销售额NO] AS salesAmountNo, sa.[据点] AS stronghold,
    sa.[销售额日] AS salesAmountDate, sa.[接单种类] AS orderType, sa.[客户代码] AS customerCode,
    sa.[客户简称] AS customerAlias, sa.[结算货币] AS settlementCurrency, sa.[税代码] AS taxCode,
    sa.[会计年月] AS accountingYearMonth, sa.[销售额确认] AS salesAmountConfirm,
    sa.[交易条件] AS tradeCondition, sa.[付款条件] AS payCondition, sa.[运输条件] AS transportCondition,
    sa.[创建人] AS creatorName, sa.[创建时间] AS createdTime,
    sa.[更新人] AS updaterName, sa.[更新时间] AS updatedTime
  </sql>
  
  <!-- 查询部门销售额数据 -->
  <select id="listDepartmentalSalesRevenue" resultType="com.hongru.pojo.dto.DepartmentalSalesRevenueDTO">
    SELECT
      a.[客户简称] AS customerAlias,
      a.[结算货币] AS settlementCurrency,
      b.[产品中分类] AS productMiddleCategory,
      SUM(b.[数量]) AS salesQuantity,
      SUM(b.[销售金额]) AS salesAmount
    FROM [销售额表] a
    LEFT JOIN [销售额明细表] b ON a.[销售额NO] = b.[销售额NO]
    <where>
      <if test="startDate != null and startDate != ''">
        AND a.[销售额日] &gt;= #{startDate}
      </if>
      <if test="endDate != null and endDate != ''">
        AND a.[销售额日] &lt;= #{endDate}
      </if>
    </where>
    GROUP BY a.[客户简称], a.[结算货币], b.[产品中分类]
    ORDER BY a.[客户简称], b.[产品中分类]
  </select>
  
  <!-- 查询客户销售额数据 -->
  <select id="listCustomerSalesRevenue" resultType="com.hongru.pojo.dto.CustomerSalesRevenueDTO">
    SELECT
      LEFT(a.[销售额日], 7) AS accountingYearMonth,
      a.[客户简称] AS customerAlias,
      b.[产品代码] AS productCode,
      c.[尺寸] AS size,
      b.[出库日期] AS shipmentDate,
      b.[到货日期] AS arrivalDate,
      SUM(b.[数量]) AS salesQuantity,
      b.[销售单价] AS salesUnitPrice,
      SUM(b.[销售金额]) AS salesAmount,
      b.[铜base] AS copperPrice,
      b.[产品中分类] AS productMiddleCategory
    FROM [销售额表] a
    LEFT JOIN [销售额明细表] b ON a.[销售额NO] = b.[销售额NO]
    LEFT JOIN [sumitomo].[dbo].[产品表] c ON b.[产品代码] = c.[产品代码] COLLATE DATABASE_DEFAULT
    <where>
      <if test="startDate != null and startDate != ''">
        AND a.[销售额日] &gt;= #{startDate}
      </if>
      <if test="endDate != null and endDate != ''">
        AND a.[销售额日] &lt;= #{endDate}
      </if>
    </where>
    GROUP BY LEFT(a.[销售额日], 7), a.[客户简称], b.[产品代码], c.[尺寸], b.[出库日期], b.[到货日期], b.[销售单价], b.[铜base], b.[产品中分类]
    ORDER BY a.[客户简称], b.[产品中分类]
  </select>
  
  <!-- 查询所有产品中分类 -->
  <select id="selectDistinctProductMiddleCategory" resultType="java.lang.String">
    SELECT DISTINCT [产品中分类]
    FROM [销售额明细表]
    ORDER BY [产品中分类]
  </select>
  
  <!-- 查询出货和销售额差数据 -->
  <select id="listShipmentSalesDiff" resultType="com.hongru.pojo.dto.ShipmentSalesDiffDTO">
    SELECT
      LEFT(a.[销售额日], 7) AS accountingYearMonth,
      a.[客户简称] AS customerAlias,
      b.[产品代码] AS productCode,
      b.[产品中分类] AS productMiddleCategory,
      SUM(b.[数量]) - SUM(c.[数量]) AS salesQuantity,
      SUM(b.[数量] * b.[销售单价]) - SUM(c.[数量] * c.[销售单价]) AS salesAmount,
      SUM(b.[铜base] * b.[数量]) - SUM(c.[铜base] * c.[数量]) AS copperAmount
    FROM [销售额表] a
    LEFT JOIN [销售额明细表] b ON a.[销售额NO] = b.[销售额NO]
    LEFT JOIN [支付额明细表] c ON a.[销售额NO] = c.[销售额NO]
    <where>
      <if test="startDate != null and startDate != ''">
        AND a.[销售额日] &gt;= #{startDate}
      </if>
      <if test="endDate != null and endDate != ''">
        AND a.[销售额日] &lt;= #{endDate}
      </if>
    </where>
    GROUP BY a.[销售额日], a.[客户简称], b.[产品代码], b.[产品中分类]
    ORDER BY a.[客户简称], b.[产品中分类]
  </select>

  <!-- 根据年月查询出货和销售额差数据 -->
  <select id="listShipmentSalesDiffByYearMonth" resultType="com.hongru.pojo.dto.ShipmentSalesDiffDTO">
    WITH PreviousMonthSales AS (
      -- 上个月的销售额登录数据
      SELECT
        a.[客户简称] AS customerAlias,
        b.[产品代码] AS productCode,
        b.[产品中分类] AS productMiddleCategory,
        SUM(b.[数量]) AS salesQuantity,
        SUM(b.[数量] * b.[销售单价]) AS salesAmount,
        SUM(b.[铜base] * b.[数量]) AS copperAmount
      FROM [销售额表] a
      LEFT JOIN [销售额明细表] b ON a.[销售额NO] = b.[销售额NO]
      WHERE LEFT(a.[销售额日], 7) = FORMAT(DATEADD(MONTH, -1, CAST(#{yearMonth} + '-01' AS DATE)), 'yyyy-MM')
      GROUP BY a.[客户简称], b.[产品代码], b.[产品中分类]
    ),
    CurrentMonthPayments AS (
      -- 当前月的支付请求数据
      SELECT
        p.[客户简称] AS customerAlias,
        pd.[产品代码] AS productCode,
        pd.[产品中分类] AS productMiddleCategory,
        SUM(pd.[数量]) AS paymentQuantity,
        SUM(pd.[数量] * pd.[支付单价]) AS paymentAmount,
        SUM(pd.[铜base] * pd.[数量]) AS copperAmount
      FROM [支付额表] p
      LEFT JOIN [支付额明细表] pd ON p.[支付请求NO] = pd.[支付请求NO]
      WHERE LEFT(p.[支付请求日], 7) = #{yearMonth}
      GROUP BY p.[客户简称], pd.[产品代码], pd.[产品中分类]
    )
    SELECT
      #{yearMonth} AS accountingYearMonth,
      COALESCE(s.customerAlias, p.customerAlias) AS customerAlias,
      COALESCE(s.productCode, p.productCode) AS productCode,
      COALESCE(s.productMiddleCategory, p.productMiddleCategory) AS productMiddleCategory,
      COALESCE(s.salesQuantity, 0) - COALESCE(p.paymentQuantity, 0) AS salesQuantity,
      COALESCE(s.salesAmount, 0) - COALESCE(p.paymentAmount, 0) AS salesAmount,
      COALESCE(s.copperAmount, 0) - COALESCE(p.copperAmount, 0) AS copperAmount
    FROM PreviousMonthSales s
    FULL OUTER JOIN CurrentMonthPayments p
      ON s.customerAlias = p.customerAlias
      AND s.productCode = p.productCode
      AND s.productMiddleCategory = p.productMiddleCategory
    WHERE (COALESCE(s.salesQuantity, 0) - COALESCE(p.paymentQuantity, 0)) != 0
       OR (COALESCE(s.salesAmount, 0) - COALESCE(p.paymentAmount, 0)) != 0
       OR (COALESCE(s.copperAmount, 0) - COALESCE(p.copperAmount, 0)) != 0
    ORDER BY COALESCE(s.customerAlias, p.customerAlias), COALESCE(s.productMiddleCategory, p.productMiddleCategory)
  </select>

  <!-- 查询月出货明细数据 -->
  <select id="listMonthlyShipmentDetail" resultType="com.hongru.pojo.dto.MonthlyShipmentDetailDTO">
    SELECT
      LEFT(a.[销售额日], 7) AS accountingYearMonth,
      a.[客户简称] AS customerAlias,
      b.[产品代码] AS productCode,
      c.[标签尺寸名称] AS size,
      b.[产品中分类] AS productMiddleCategory,
      SUM(b.[数量]) AS shipmentQuantity,
      c.[线盘名称] AS wireReelName
    FROM [销售额表] a
    LEFT JOIN [销售额明细表] b ON a.[销售额NO] = b.[销售额NO]
    LEFT JOIN [sumitomo].[dbo].[产品表] c ON b.[产品代码] = c.[产品代码] COLLATE DATABASE_DEFAULT
    <where>
      <if test="startDate != null">
        AND a.[销售额日] &gt;= #{startDate}
      </if>
      <if test="endDate != null">
        AND a.[销售额日] &lt;= #{endDate}
      </if>
    </where>
    GROUP BY LEFT(a.[销售额日], 7), a.[客户简称], b.[产品代码], c.[标签尺寸名称], b.[产品中分类], c.[线盘名称]
    ORDER BY a.[客户简称], b.[产品中分类]
  </select>
  
  <!-- 查询月出货实绩数据 -->
  <select id="listMonthlyShipmentPerformance" resultType="com.hongru.pojo.dto.MonthlyShipmentPerformanceDTO">
    SELECT
      LEFT(a.[销售额日], 7) AS accountingYearMonth,
      CASE 
        WHEN a.[结算货币] = 'RMB' THEN N'国内'
        ELSE N'国外'
      END AS domesticOrForeign,
      a.[客户简称] AS customerAlias,
      b.[产品中分类] AS productMiddleCategory,
      b.[产品代码] AS productCode,
      SUM(b.[数量]) AS shipmentQuantity
    FROM [销售额表] a
    LEFT JOIN [销售额明细表] b ON a.[销售额NO] = b.[销售额NO]
    <where>
      <if test="startDate != null and startDate != ''">
        AND a.[销售额日] &gt;= #{startDate}
      </if>
      <if test="endDate != null and endDate != ''">
        AND a.[销售额日] &lt;= #{endDate}
      </if>
    </where>
    GROUP BY LEFT(a.[销售额日], 7), a.[结算货币], a.[客户简称], b.[产品中分类], b.[产品代码]
    ORDER BY domesticOrForeign, a.[客户简称], b.[产品中分类], b.[产品代码]
  </select>

  <!-- 分页查询销售额登录列表 -->
  <select id="listSalesLoginByPage" resultType="com.hongru.pojo.dto.SalesLoginListDTO">
    SELECT
      a.[销售额NO] AS salesNo,
      a.[销售额日] AS salesDate,
      a.[客户代码] AS customerCode,
      a.[客户简称] AS customerAlias,
      a.[结算货币] AS currency,
      SUM(b.[销售金额]) AS salesAmount,
      ISNULL(c.[税率], a.[税代码]) AS taxRate,
      a.[创建人] AS creatorName,
      a.[创建时间] AS createdTime,
      a.[更新人] AS updaterName,
      a.[更新时间] AS updatedTime,
      a.[销售额确认] AS salesAmountConfirm,
      CASE WHEN COUNT(CASE WHEN b.[支付请求确认] = '1' THEN 1 END) > 0 THEN '1' ELSE '0' END AS paymentRequestConfirm
    FROM [销售额表] a
    LEFT JOIN [销售额明细表] b ON a.[销售额NO] = b.[销售额NO]
    LEFT JOIN [税率计算表] c ON a.[税代码] = c.[税率代码]
    <where>
      1=1
      <if test="customerCode != null and customerCode != ''">
        AND a.[客户代码] = #{customerCode}
      </if>
      <if test="salesAmountDate != null and salesAmountDate != ''">
        AND a.[销售额日] = #{salesAmountDate}
      </if>
    </where>
    GROUP BY a.[销售额NO], a.[销售额日], a.[客户代码], a.[客户简称], a.[结算货币], a.[税代码], c.[税率], a.[创建人], a.[创建时间], a.[更新人], a.[更新时间], a.[销售额确认]
    ORDER BY a.[创建时间] DESC
    <if test="pageInfo != null">
      OFFSET #{pageInfo.offset} ROWS FETCH NEXT #{pageInfo.limit} ROWS ONLY
    </if>
  </select>

  <!-- 查询销售额登录列表总数 -->
  <select id="listSalesLoginByPageCount" resultType="java.lang.Integer">
    SELECT COUNT(DISTINCT a.[销售额NO])
    FROM [销售额表] a
    LEFT JOIN [销售额明细表] b ON a.[销售额NO] = b.[销售额NO]
    <where>
      1=1
      <if test="customerCode != null and customerCode != ''">
        AND a.[客户代码] = #{customerCode}
      </if>
      <if test="salesAmountDate != null and salesAmountDate != ''">
        AND a.[销售额日] = #{salesAmountDate}
      </if>
    </where>
  </select>

  <!-- 分页查询销售额登录列表（按用户过滤） -->
  <select id="listSalesLoginByPageWithUser" resultType="com.hongru.pojo.dto.SalesLoginListDTO">
    SELECT
      a.[销售额NO] AS salesNo,
      a.[销售额日] AS salesDate,
      a.[客户代码] AS customerCode,
      a.[客户简称] AS customerAlias,
      a.[结算货币] AS currency,
      SUM(b.[销售金额]) AS salesAmount,
      ISNULL(c.[税率], a.[税代码]) AS taxRate,
      a.[创建人] AS creatorName,
      a.[创建时间] AS createdTime,
      a.[更新人] AS updaterName,
      a.[更新时间] AS updatedTime,
      a.[销售额确认] AS salesAmountConfirm,
      CASE WHEN COUNT(CASE WHEN b.[支付请求确认] = '1' THEN 1 END) > 0 THEN '1' ELSE '0' END AS paymentRequestConfirm
    FROM [销售额表] a
    LEFT JOIN [销售额明细表] b ON a.[销售额NO] = b.[销售额NO]
    LEFT JOIN [税率计算表] c ON a.[税代码] = c.[税率代码]
    <where>
      1=1
      <if test="customerCode != null and customerCode != ''">
        AND a.[客户代码] = #{customerCode}
      </if>
      <if test="startDate != null and startDate != ''">
        AND a.[销售额日] &gt;= #{startDate}
      </if>
      <if test="endDate != null and endDate != ''">
        AND a.[销售额日] &lt;= #{endDate}
      </if>
      <if test="creatorName != null and creatorName != ''">
        AND a.[创建人] = #{creatorName}
      </if>
    </where>
    GROUP BY a.[销售额NO], a.[销售额日], a.[客户代码], a.[客户简称], a.[结算货币], a.[税代码], c.[税率], a.[创建人], a.[创建时间], a.[更新人], a.[更新时间], a.[销售额确认]
    ORDER BY a.[创建时间] DESC
    <if test="pageInfo != null">
      OFFSET #{pageInfo.offset} ROWS FETCH NEXT #{pageInfo.limit} ROWS ONLY
    </if>
  </select>

  <!-- 查询销售额登录列表总数（按用户过滤） -->
  <select id="listSalesLoginByPageCountWithUser" resultType="java.lang.Integer">
    SELECT COUNT(DISTINCT a.[销售额NO])
    FROM [销售额表] a
    LEFT JOIN [销售额明细表] b ON a.[销售额NO] = b.[销售额NO]
    <where>
      1=1
      <if test="customerCode != null and customerCode != ''">
        AND a.[客户代码] = #{customerCode}
      </if>
      <if test="startDate != null and startDate != ''">
        AND a.[销售额日] &gt;= #{startDate}
      </if>
      <if test="endDate != null and endDate != ''">
        AND a.[销售额日] &lt;= #{endDate}
      </if>
      <if test="creatorName != null and creatorName != ''">
        AND a.[创建人] = #{creatorName}
      </if>
    </where>
  </select>

  <!-- 根据客户代码获取客户详情信息 -->
  <select id="getCustomerDetailsInfo" resultType="java.util.Map">
    SELECT
      a.[国家],
      a.[货币] AS 结算货币,
      a.[税率代码] AS 税代码,
      b.[税率],
      a.[交易条件],
      c.[交易条件名],
      a.[付款条件],
      d.[付款条件名],
      a.[运输条件],
      e.[运输条件名],
      b.[税率计算名]
    FROM [客户详情表] a
    LEFT JOIN [税率计算表] b ON a.[税率代码] = b.[税率代码]
    LEFT JOIN [交易条件表] c ON a.[交易条件] = c.[交易条件]
    LEFT JOIN [付款条件表] d ON a.[付款条件] = d.[付款条件]
    LEFT JOIN [运输条件表] e ON a.[运输条件] = e.[运输条件]
    WHERE a.[客户代码] = #{customerCode}
  </select>

  <!-- 获取交易条件列表 -->
  <select id="getTradeConditions" resultType="java.util.Map">
    SELECT [交易条件], [交易条件名]
    FROM [交易条件表]
    ORDER BY [交易条件]
  </select>

  <!-- 获取付款条件列表 -->
  <select id="getPayConditions" resultType="java.util.Map">
    SELECT [付款条件], [付款条件名]
    FROM [付款条件表]
    ORDER BY [付款条件]
  </select>

  <!-- 获取运输条件列表 -->
  <select id="getTransportConditions" resultType="java.util.Map">
    SELECT [运输条件], [运输条件名]
    FROM [运输条件表]
    ORDER BY [运输条件]
  </select>

  <!-- 根据客户代码获取出库数据用于生成销售额 -->
  <select id="getOutboundDataForSalesAmount" resultType="java.util.Map">
    SELECT
      c.[接单种类],
      b.[条码],
      b.[产品代码],
      a.[到货日期],
      b.[明细备注],
      b.[铜合同类别],
      b.[铜签约No],
      b.[铜条件],
      b.[换算率],
      a.[出库数量] AS quantity,
      b.[铜基本] AS copperBase,
      b.[升水],
      b.[零基础],
      a.[产品中分类],
      a.[出库计划番号] AS shipmentPlanNo,
      a.[送货单号] AS deliveryNoteNo,
      a.[送货单序号] AS deliveryNoteSeq
    FROM [BusinessOps].[dbo].[出库数量表] a
    LEFT JOIN [订单明细表] b ON a.[出库计划番号] = b.[出货计划番号] COLLATE DATABASE_DEFAULT
    LEFT JOIN [订单表] c ON b.[订单NO] = c.[订单NO]
    WHERE a.[客户代码] = #{customerCode}
      AND a.[到货日期] IS NOT NULL
  </select>
  
  <!-- 根据客户代码和到货日期获取出库数据用于生成销售额 -->
  <select id="getOutboundDataForSalesAmountWithArrivalDate" resultType="java.util.Map">
    SELECT
      c.[接单种类],
      b.[条码],
      b.[产品代码],
      b.[产品代码] AS 品名,
      b.[尺寸],
      b.[线盘名称] AS 线盘,
      a.[客户代码] AS 客户品目C,
      b.[出库日期] AS 出货日,
      a.[到货日期],
      a.[客户订单号] AS 客户订单No,
--       '常规出货' AS 理由,
      b.[明细备注] AS 明细内部备注,
      b.[铜合同类别],
--       b.[铜签约No] AS 铜合同No,
--       b.[铜条件],
--       b.[铜货币],
      b.[换算率],
      a.[出库数量] AS quantity,
      a.[出库数量] AS 数量,
      b.[铜基本] AS copperBase,
      b.[铜基本] AS 铜base,
      b.[升水],
      b.[零基础],
      a.[产品中分类],
      sp.[条码] AS 品目C,
      a.[出库计划番号] AS shipmentPlanNo,
      a.[出库计划番号] AS 出货计划番号,
      a.[送货单号] AS deliveryNoteNo,
      a.[送货单号] AS 出货单No,
      a.[送货单序号] AS deliveryNoteSeq,
      a.[送货单序号]
    FROM [BusinessOps].[dbo].[出库数量表] a
    LEFT JOIN [订单明细表] b ON a.[出库计划番号] = b.[出货计划番号] COLLATE DATABASE_DEFAULT
    LEFT JOIN [订单表] c ON b.[订单NO] = c.[订单NO]
    LEFT JOIN [sumitomo].[dbo].[产品表] sp ON b.[产品代码] = sp.[产品代码] COLLATE DATABASE_DEFAULT
    WHERE a.[客户代码] = #{customerCode}
      AND a.[到货日期] IS NOT NULL
      <if test="arrivalDate != null and arrivalDate != ''">
        AND a.[到货日期] = #{arrivalDate}
      </if>
  </select>

  <!-- 根据客户代码和日期范围获取出库数据用于生成销售额 -->
  <select id="getOutboundDataForSalesAmountWithDateRange" resultType="java.util.Map">
    SELECT
      c.[接单种类],
      b.[条码],
      b.[产品代码],
      b.[产品代码] AS 品名,
      b.[尺寸],
      b.[线盘名称] AS 线盘,
      a.[客户代码] AS 客户品目C,
      b.[出库日期] AS 出货日,
      a.[到货日期],
      a.[客户订单号] AS 客户订单No,
      b.[明细备注] AS 明细内部备注,
      b.[铜合同类别],
      b.[换算率],
      a.[出库数量] AS quantity,
      a.[出库数量] AS 数量,
      b.[铜基本] AS copperBase,
      b.[铜基本] AS 铜base,
      b.[升水],
      b.[零基础],
      a.[产品中分类],
      sp.[条码] AS 品目C,
      a.[出库计划番号] AS shipmentPlanNo,
      a.[出库计划番号] AS 出货计划番号,
      a.[送货单号] AS deliveryNoteNo,
      a.[送货单号] AS 出货单No,
      a.[送货单序号] AS deliveryNoteSeq,
      a.[送货单序号]
    FROM [BusinessOps].[dbo].[出库数量表] a
    LEFT JOIN [订单明细表] b ON a.[出库计划番号] = b.[出货计划番号] COLLATE DATABASE_DEFAULT
    LEFT JOIN [订单表] c ON b.[订单NO] = c.[订单NO]
    LEFT JOIN [sumitomo].[dbo].[产品表] sp ON b.[产品代码] = sp.[产品代码] COLLATE DATABASE_DEFAULT
    WHERE a.[客户代码] = #{customerCode}
      AND a.[到货日期] IS NOT NULL
      <if test="startDate != null and startDate != ''">
        AND a.[到货日期] &gt;= #{startDate}
      </if>
      <if test="endDate != null and endDate != ''">
        AND a.[到货日期] &lt;= #{endDate}
      </if>
  </select>

  <!-- 根据前缀获取最大销售额NO -->
  <select id="getMaxSalesAmountNoByPrefix" resultType="java.lang.String">
    SELECT TOP 1 [销售额NO]
    FROM [销售额表]
    WHERE [销售额NO] LIKE CONCAT(#{prefix}, '%')
    ORDER BY [销售额NO] DESC
  </select>

  <!-- 根据销售额NO获取销售额主表数据 -->
  <select id="getSalesAmountByNo" resultType="com.hongru.entity.businessOps.SalesAmount">
    SELECT
      sa.[销售额NO] AS salesAmountNo,
      sa.[据点] AS stronghold,
      sa.[销售额日] AS salesAmountDate,
      sa.[接单种类] AS orderType,
      sa.[客户代码] AS customerCode,
      sa.[客户简称] AS customerAlias,
      sa.[结算货币] AS settlementCurrency,
      sa.[税代码] AS taxCode,
      sa.[会计年月] AS accountingYearMonth,
      sa.[销售额确认] AS salesAmountConfirm,
      sa.[交易条件] AS tradeCondition,
      sa.[付款条件] AS payCondition,
      sa.[运输条件] AS transportCondition,
      sa.[创建人] AS creatorName,
      sa.[创建时间] AS createdTime,
      sa.[更新人] AS updaterName,
      sa.[更新时间] AS updatedTime
    FROM [销售额表] sa
    WHERE sa.[销售额NO] = #{salesAmountNo}
  </select>
  
  <!-- 根据销售额NO删除销售额数据 -->
  <delete id="deleteBySalesAmountNo">
    DELETE FROM [销售额表]
    WHERE [销售额NO] = #{salesAmountNo}
  </delete>

  <!-- 检查指定客户在指定到货日是否已登录 -->
  <select id="checkCustomerExists" resultType="java.lang.Integer">
    SELECT COUNT(1)
    FROM [销售额表] sa
    INNER JOIN [销售额明细表] sad ON sa.[销售额NO] = sad.[销售额NO]
    WHERE sa.[客户代码] = #{customerCode}
      AND sad.[到货日期] = #{arrivalDate}
  </select>

  <!-- 自定义插入销售额表数据 -->
  <insert id="insert" parameterType="com.hongru.entity.businessOps.SalesAmount">
    INSERT INTO 销售额表 (
      销售额NO,
      据点,
      销售额日,
      接单种类,
      客户代码,
      客户简称,
      结算货币,
      税代码,
      会计年月,
      销售额确认,
      交易条件,
      付款条件,
      运输条件,
      创建人,
      创建时间,
      更新人,
      更新时间
    ) VALUES (
      #{salesAmountNo},
      #{stronghold},
      #{salesAmountDate},
      #{orderType},
      #{customerCode},
      #{customerAlias},
      #{settlementCurrency},
      #{taxCode},
      #{accountingYearMonth},
      #{salesAmountConfirm},
      #{tradeCondition},
      #{payCondition},
      #{transportCondition},
      #{creatorName},
      #{createdTime},
      #{updaterName},
      #{updatedTime}
    )
  </insert>

  <!-- 根据客户代码查询销售额数据用于支付请求 -->
  <select id="getSalesDataForPaymentRequest" resultType="com.hongru.entity.businessOps.PaymentAmountDetail">
    SELECT
      a.[销售额NO] AS salesAmountNo,
      b.[销售额明细ID] AS salesAmountSeq,  -- 使用明细ID，用于精确更新
      a.[客户代码] AS customerCode,
      b.[品目C] AS barcode,
      b.[产品代码] AS productCode,
      b.[出库日期] AS shipmentDate,
      b.[到货日期] AS arrivalDate,
      b.[客户订单号] AS customerOrderNo,
      b.[明细备注] AS detailRemark,
      b.[铜合同类别] AS copperContractType,
      b.[铜合同NO] AS copperContractNo,
      b.[铜条件] AS copperCondition,
      b.[换算率] AS conversionRate,
      b.[数量] AS quantity,
      b.[铜base] AS copperBase,
      b.[升水] AS premium,
      b.[换算后铜单价] AS convertedCopperPrice,
      b.[零基础] AS zeroBase,
      b.[销售单价] AS paymentUnitPrice,
      b.[销售金额] AS requestAmount,
      b.[产品中分类] AS productMiddleCategory,
      -- 新增字段，从销售额明细表获取实际值
      b.[送货单号] AS deliveryNoteNo,  -- 出货单No.
      b.[送货单序号] AS deliveryNoteSeq,  -- 送货单序号
      b.[线盘] AS reelName,  -- 线盘
      b.[出货计划番号] AS shipmentPlanNo,  -- 出货计划番号
      b.[支付请求确认] AS paymentRequestConfirm,  -- 支付请求确认状态
      '' AS currency  -- 铜货币（通过前端级联操作获取）
    FROM [销售额表] a
    LEFT JOIN [销售额明细表] b ON a.[销售额NO] = b.[销售额NO]
    WHERE a.[客户代码] = #{customerCode}
      AND (b.[支付请求确认] IS NULL OR b.[支付请求确认] != '1')
    ORDER BY b.[创建时间] DESC
  </select>

  <!-- 更新销售额明细表的支付请求确认状态 -->
  <update id="updatePaymentRequestConfirm">
    UPDATE [销售额明细表] SET
      [支付请求确认] = '1'
    WHERE [销售额NO] = #{salesAmountNo}
  </update>

  <!-- 更新指定明细行的支付请求确认状态 -->
  <update id="updatePaymentRequestConfirmByDetail">
    UPDATE [销售额明细表] SET
      [支付请求确认] = #{confirmStatus}
    WHERE [销售额明细ID] = #{detailSeq}
  </update>

  <!-- 检查销售额是否已被支付请求登录使用 -->
  <select id="isUsedByPaymentRequest" resultType="boolean">
    SELECT CASE WHEN COUNT(*) > 0 THEN 1 ELSE 0 END
    FROM [销售额明细表]
    WHERE [销售额NO] = #{salesAmountNo}
      AND [支付请求确认] = '1'
  </select>

  <!-- 查询0-base报表数据 -->
  <select id="listZeroBaseReport" resultType="com.hongru.pojo.dto.ZeroBaseReportDTO">
    SELECT
      b.[产品中分类] AS productMiddleCategory,
      a.[客户简称] AS customerAlias,
      b.[产品代码] AS productCode,
      c.[标签尺寸名称] AS labelSizeName,
      a.[结算货币] AS currency,
      b.[零基础] AS zeroBaseSalesUnitPrice,
      SUM(b.[数量]) AS salesQuantity,
      a.[会计年月] AS accountingYearMonth,
      -- TODO: 需要确认0base直接成本的计算逻辑
      -- 通过条码关联产品成本表获取，按年度+成本键获取唯一数据
      -- 会计年度按当年4月到次年3月计算
      0.00 AS zeroBaseDirectCost
    FROM [销售额表] a
    LEFT JOIN [销售额明细表] b ON a.[销售额NO] = b.[销售额NO]
    LEFT JOIN [sumitomo].[dbo].[产品表] c ON b.[产品代码] = c.[产品代码] COLLATE DATABASE_DEFAULT
    <where>
      <if test="startAccountingYearMonth != null and startAccountingYearMonth != ''">
        AND a.[会计年月] &gt;= #{startAccountingYearMonth}
      </if>
      <if test="endAccountingYearMonth != null and endAccountingYearMonth != ''">
        AND a.[会计年月] &lt;= #{endAccountingYearMonth}
      </if>
    </where>
    GROUP BY b.[产品中分类], a.[客户简称], b.[产品代码], c.[标签尺寸名称], a.[结算货币], b.[零基础], a.[会计年月]
    ORDER BY a.[客户简称], b.[产品中分类], b.[产品代码]
  </select>

  <!-- 查询User-Margin报表数据 -->
  <select id="listUserMargin" resultType="com.hongru.pojo.dto.UserMarginDTO">
    SELECT
      a.[会计年月] AS accountingYearMonth,
      a.[据点] AS siteCode,
      a.[客户简称] AS customerCode,
      a.[客户简称] AS customerAlias,
      b.[产品代码] AS productCode,
      c.[上行] AS productGroupName,
      c.[标签尺寸名称] AS labelSizeName,
      e.[客户类别名] AS customerGroupName,
      b.[产品中分类] AS productMiddleCategory,
      b.[品目C] AS productCategoryCode,
      a.[接单种类] AS orderTypeCategory,
      a.[结算货币] AS currencyCode,

      -- 成本相关字段（从CostPrice库的产品成本表获取）
      -- TODO: 需要确认运费和0base直接成本的获取方式
      ISNULL(f.[运输费], 0) AS transportationCost,
      ISNULL(f.[到捆包为止0Base], 0) AS zeroBaseDirectCost,

      -- 销售相关字段
      b.[零基础] AS zeroBaseSalesUnitPrice,
      b.[铜base] AS copperPrice,
      b.[铜条件] AS copperCondition,
      b.[升水] AS premiumUnitPrice,
      SUM(b.[数量]) AS salesQuantity,

      -- 计算字段
      ISNULL(f.[到捆包为止0Base], 0) * SUM(b.[数量]) AS zeroBaseDirectCostAmount,
      b.[零基础] * SUM(b.[数量]) AS zeroBaseSalesAmount,
      (b.[铜base] + b.[零基础]) * SUM(b.[数量]) AS salesAmount,
      (b.[零基础] - ISNULL(f.[到捆包为止0Base], 0)) * SUM(b.[数量]) AS zeroBaseProfitAmount,
      b.[铜base] * SUM(b.[数量]) AS copperAmount,
      -- TODO: 需要确认产品设计数据表中皮膜重的计算逻辑
      0.0 AS filmWeight,
      0.0 * SUM(b.[数量]) AS filmWeightAmount

    FROM [销售额表] a
    LEFT JOIN [销售额明细表] b ON a.[销售额NO] = b.[销售额NO]
    LEFT JOIN [sumitomo].[dbo].[产品表] c ON b.[产品代码] = c.[产品代码] COLLATE DATABASE_DEFAULT
    LEFT JOIN [businessOps].[dbo].[客户详情表] d ON a.[客户代码] = d.[客户代码] COLLATE DATABASE_DEFAULT
    LEFT JOIN [businessOps].[dbo].[客户分类表] e ON d.[客户类别代码] = e.[客户类别代码] COLLATE DATABASE_DEFAULT
    -- TODO: 需要确认产品设计数据表的关联方式
    -- LEFT JOIN [产品设计数据表] f ON [关联字段]
    -- 关联CostPrice库的产品成本表（使用会计年度+条码作为key）
    -- 会计年度计算：当年4月到次年3月为一个会计年度
    LEFT JOIN [CostPrice].[dbo].[产品成本表] f ON
      CASE
        WHEN SUBSTRING(a.[会计年月], 5, 2) >= '04'
        THEN SUBSTRING(a.[会计年月], 1, 4)  -- 4月及以后，使用当年
        ELSE CAST(CAST(SUBSTRING(a.[会计年月], 1, 4) AS INT) - 1 AS CHAR(4))  -- 1-3月，使用前一年
      END = f.[年度] COLLATE DATABASE_DEFAULT
      AND b.[品目C] = f.[成本键] COLLATE DATABASE_DEFAULT
    <where>
      <if test="accountingYearMonth != null and accountingYearMonth != ''">
        AND a.[会计年月] = #{accountingYearMonth}
      </if>
    </where>
    GROUP BY a.[会计年月], a.[据点], a.[客户简称], b.[产品代码], c.[上行], c.[标签尺寸名称],
             e.[客户类别名], b.[产品中分类], b.[品目C], a.[接单种类], a.[结算货币],
             b.[零基础], b.[铜base], b.[铜条件], b.[升水], f.[运输费], f.[到捆包为止0Base]
    ORDER BY a.[客户简称], b.[产品代码]
  </select>

  <!-- 根据出货单No.获取明细数据 -->
  <select id="getDetailsByShipmentNo" resultType="java.util.Map">
    SELECT
      sq.[送货单号] AS deliveryNoteNo,
      sq.[送货单序号] AS deliveryNoteSeq,
      sp.[条码] AS productCategoryCode,
      sq.[产品代码] AS productName,
      od.[线盘名称] AS reelName,
      sq.[客户代码] AS customerProductCode,
      CONVERT(VARCHAR, sq.[到货日期], 23) AS outboundDate,
      CONVERT(VARCHAR, sq.[到货日期], 23) AS arrivalDate,
      sq.[客户订单号] AS customerOrderNo,
      '' AS remark,
      ISNULL(od.[铜合同类别], '3') AS copperContractType,
      ISNULL(od.[铜签约No], '') AS copperContractNo,
      ISNULL(od.[铜条件], '') AS copperCondition,
      ISNULL(od.[铜货币], '') AS currency,
      ISNULL(od.[换算率], '1.0') AS conversionRate,
      sq.[出库数量] AS quantity,
      ISNULL(od.[铜基本], 0) AS copperBase,
      ISNULL(od.[升水], 0) AS premium,
      ISNULL(od.[换算后铜单价], 0) AS convertedCopperPrice,
      ISNULL(od.[零基础], 0) AS zeroBase,
      ISNULL(od.[接单单价], 0) AS salesUnitPrice,
      ISNULL(od.[接单金额], 0) AS salesAmount,
      sq.[产品中分类] AS productMiddleCategory,
      sq.[出库计划番号] AS shipmentPlanNo,
      sq.[出库数量] AS originalQuantity
    FROM [BusinessOps].[dbo].[出库数量表] sq
    LEFT JOIN [BusinessOps].[dbo].[订单明细表] od ON sq.[出库计划番号] = od.[出货计划番号] COLLATE DATABASE_DEFAULT
    LEFT JOIN [sumitomo].[dbo].[产品表] sp ON sq.[产品代码] = sp.[产品代码] COLLATE DATABASE_DEFAULT
    WHERE sq.[送货单号] = #{shipmentNo}
    ORDER BY sq.[送货单序号]
  </select>
</mapper>
