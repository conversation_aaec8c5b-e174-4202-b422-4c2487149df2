<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hongru.mapper.businessOps.CustomerClassifyMapper">
    <sql id="customerClassify_sql">
		pc.[流水号] AS id,pc.[客户类别代码] AS customerCategoryCode,pc.[客户类别名] AS customerCategory,pc.[备注] AS remark,
		pc.[创建人姓名] AS creatorName,pc.[创建时间] AS createdTime,
		pc.[更新人姓名] AS updaterName,pc.[更新时间] AS updatedTime
	</sql>

	<insert id="insertCustomerClassify" parameterType="com.hongru.entity.businessOps.CustomerClassify">
		INSERT INTO [businessOps].[dbo].[客户分类表]
		(
		[客户类别代码],
		[客户类别名],
		[备注],
		[创建人姓名],
		[创建时间]
		)VALUES(
		#{customerClassify.customerCategoryCode},
		#{customerClassify.customerCategory},
		#{customerClassify.remark},
		#{customerClassify.creatorName},
		#{customerClassify.createdTime}
		)
	</insert>

	<select id="selectCustomerClassifyById" resultType="com.hongru.entity.businessOps.CustomerClassify">
		SELECT
		<include refid="customerClassify_sql"/>
		FROM [businessOps].[dbo].[客户分类表] pc
		<where>
			<if test="id != null">
				AND pc.[流水号] = #{id}
			</if>
		</where>
	</select>

	<select id="listCustomerClassify" resultType="com.hongru.entity.businessOps.CustomerClassify">
		SELECT
		<include refid="customerClassify_sql"/>
		FROM [businessOps].[dbo].[客户分类表] pc
		<where>
			<if test="customerCategoryCode != null and customerCategoryCode != ''">
				AND pc.[客户类别代码] = #{customerCategoryCode}
			</if>
			<if test="customerCategory != null and customerCategory != 0">
				AND pc.[客户类别名] = #{customerCategory}
			</if>
		</where>
		ORDER BY pc.[客户类别代码]
	</select>

	<update id="updateCustomerClassify">
		UPDATE [businessOps].[dbo].[客户分类表]
		<set>
			<if test="customerClassify.customerCategoryCode != null and customerClassify.customerCategoryCode != ''">
				[客户类别代码] = #{customerClassify.customerCategoryCode},
			</if>
			<if test="customerClassify.customerCategory != null and customerClassify.customerCategory != ''">
				[客户类别名] = #{customerClassify.customerCategory},
			</if>
			<if test="customerClassify.remark != null">
				[备注] = #{customerClassify.remark},
			</if>
			<if test="customerClassify.updaterName != null and customerClassify.updaterName != ''">
				[更新人姓名] = #{customerClassify.updaterName},
			</if>
			<if test="customerClassify.updatedTime != null">
				[更新时间] = #{customerClassify.updatedTime},
			</if>
		</set>
		WHERE [流水号] = #{customerClassify.id}
	</update>
	
	<delete id="deleteCustomerClassify">
		DELETE [businessOps].[dbo].[客户分类表] WHERE [流水号] = #{id}
	</delete>
</mapper>