package com.hongru.pojo.dto;

import com.hongru.entity.businessOps.GasPriceCost;
import com.hongru.support.page.PageInfo;

import java.util.List;

public class GasPriceCostDTO {

    private PageInfo pageInfo;

    private List<GasPriceCost> gasPriceCostList;

    public GasPriceCostDTO(PageInfo pageInfo, List<GasPriceCost> gasPriceCostList) {
        super();
        this.pageInfo = pageInfo;
        this.gasPriceCostList = gasPriceCostList;
    }

    public PageInfo getPageInfo() {
        return pageInfo;
    }

    public void setPageInfo(PageInfo pageInfo) {
        this.pageInfo = pageInfo;
    }

    public List<GasPriceCost> getGasPriceCostList() {
        return gasPriceCostList;
    }

    public void setGasPriceCostList(List<GasPriceCost> gasPriceCostList) {
        this.gasPriceCostList = gasPriceCostList;
    }
}
