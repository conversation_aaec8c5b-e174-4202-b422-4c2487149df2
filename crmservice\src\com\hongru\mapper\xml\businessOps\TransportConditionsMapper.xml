<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hongru.mapper.businessOps.TransportConditionsMapper">
    <sql id="transportConditions_sql">
		pc.[流水号] AS id,pc.[运输条件] AS transportCondition,pc.[运输条件名] AS transportConditionName,pc.[备注] AS remark,
		pc.[创建人姓名] AS creatorName,pc.[创建时间] AS createdTime,
		pc.[更新人姓名] AS updaterName,pc.[更新时间] AS updatedTime
	</sql>

	<insert id="insertTransportConditions" parameterType="com.hongru.entity.businessOps.TransportConditions">
		INSERT INTO [businessOps].[dbo].[运输条件表]
		(
		[运输条件],
		[运输条件名],
		[备注],
		[创建人姓名],
		[创建时间]
		)VALUES(
		#{transportConditions.transportCondition},
		#{transportConditions.transportConditionName},
		#{transportConditions.remark},
		#{transportConditions.creatorName},
		#{transportConditions.createdTime}
		)
	</insert>

	<select id="selectTransportConditionsById" resultType="com.hongru.entity.businessOps.TransportConditions">
		SELECT
		<include refid="transportConditions_sql"/>
		FROM [businessOps].[dbo].[运输条件表] pc
		<where>
			<if test="id != null">
				AND pc.[流水号] = #{id}
			</if>
		</where>
	</select>

	<select id="listTransportConditions" resultType="com.hongru.entity.businessOps.TransportConditions">
		SELECT
		<include refid="transportConditions_sql"/>
		FROM [businessOps].[dbo].[运输条件表] pc
		<where>
			<if test="transportCondition != null and transportCondition != ''">
				AND pc.[运输条件] = #{transportCondition}
			</if>
			<if test="transportConditionName != null and transportConditionName != 0">
				AND pc.[运输条件名] = #{transportConditionName}
			</if>
		</where>
		ORDER BY pc.[运输条件]
	</select>

	<update id="updateTransportConditions">
		UPDATE [businessOps].[dbo].[运输条件表]
		<set>
			<if test="transportConditions.transportCondition != null and transportConditions.transportCondition != ''">
				[运输条件] = #{transportConditions.transportCondition},
			</if>
			<if test="transportConditions.transportConditionName != null and transportConditions.transportConditionName != ''">
				[运输条件名] = #{transportConditions.transportConditionName},
			</if>
			<if test="transportConditions.remark != null">
				[备注] = #{transportConditions.remark},
			</if>
			<if test="transportConditions.updaterName != null and transportConditions.updaterName != ''">
				[更新人姓名] = #{transportConditions.updaterName},
			</if>
			<if test="transportConditions.updatedTime != null">
				[更新时间] = #{transportConditions.updatedTime},
			</if>
		</set>
		WHERE [流水号] = #{transportConditions.id}
	</update>
	
	<delete id="deleteTransportConditions">
		DELETE [businessOps].[dbo].[运输条件表] WHERE [流水号] = #{id}
	</delete>
</mapper>