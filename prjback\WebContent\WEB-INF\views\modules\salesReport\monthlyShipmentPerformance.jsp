<%@ page language="java" import="java.util.*" contentType="text/html; charset=utf-8"%>
<%@ include file="/WEB-INF/layouts/base.jsp"%>
<!DOCTYPE HTML>
<html>
<head>
  <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0" />
  <title>月出货实绩</title>
</head>
<body>
<div class="ibox float-e-margins">
  <div class="ibox-content">
    <div class="layui-card">
      <div class="layui-card-header">检索条件</div>
      <div class="layui-card-body">
        <div class="layui-form">
          <form id="formSearch" class="layui-form layui-form-pane">
            <div class="layui-form-item">
              <div class="layui-inline layui-col-md3">
                <label class="layui-form-label">销售额日期</label>
                <div class="layui-input-block">
                  <input type="text" name="salesDateRange" id="salesDateRange" placeholder="请选择日期范围" autocomplete="off" class="layui-input">
                </div>
              </div>
              <div class="layui-inline layui-col-md1 hr-div-btn">
                <button type="button" id="btn_query" onclick="search();" class="layui-btn layui-bg-blue"><i class="layui-icon layui-icon-search"></i>检索</button>
              </div>
              <div class="layui-inline layui-col-md1 hr-div-btn">
                <button type="button" id="btn_export" onclick="exportData();" class="layui-btn layui-bg-green"><i class="layui-icon layui-icon-export"></i>导出</button>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>
    <%--初始化table--%>
    <table class="layui-hide" id="demo" lay-filter="test"></table>
    <!-- 自定义头部工具栏 -->
    <script type="text/html" id="toolbarDemo">
      <div class="layui-btn-container">
        <button class="layui-btn layui-btn-primary layui-btn-sm" lay-event="refresh"><i class="layui-icon layui-icon-refresh"></i>刷新</button>
      </div>
    </script>
  </div>
</div>
<myfooter>
  <script src="${ctxsta}/hongru/js/salesReport/monthlyShipmentPerformance.js?time=<%=System.currentTimeMillis()%>"></script>
</myfooter>
</body>
</html> 