package com.hongru.pojo.dto;

import java.util.List;

import com.hongru.entity.businessOps.CustomerDetails;
import com.hongru.support.page.PageInfo;

public class CustomerDetailsPageDTO{

	/**
	 * 客户详情信息
	 */
	private List<CustomerDetails> customerDetailsList;
	
	/**
	 * 分页信息
	 */
	private PageInfo pageInfo;
	
	public CustomerDetailsPageDTO(PageInfo pageInfo, List<CustomerDetails> customerDetailsList) {
		super();
		this.customerDetailsList = customerDetailsList;
		this.pageInfo = pageInfo;
	}

	public List<CustomerDetails> getCustomerDetailsList() {
		return customerDetailsList;
	}

	public void setCustomerDetailsList(List<CustomerDetails> customerDetailsList) {
		this.customerDetailsList = customerDetailsList;
	}

	public PageInfo getPageInfo() {
		return pageInfo;
	}

	public void setPageInfo(PageInfo pageInfo) {
		this.pageInfo = pageInfo;
	}

}
