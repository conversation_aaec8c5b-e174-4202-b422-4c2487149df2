$(function(){
	  $.fn.serializeJsonObject = function() {  
		  var json = {};  
		  var form = this.serializeArray();  
		  $.each(form, function() {  
			  if (json[this.name]) {  
				  if (!json[this.name].push) {  
					  json[this.name] = [ json[this.name] ];  
				  }  
				  json[this.name].push(this.value || '');
			  } else {  
				  json[this.name] = this.value || '';  
			  }  
		  });  
		  return json;  
	  }
})