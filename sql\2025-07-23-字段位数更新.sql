/*
 * 字段位数更新脚本
 * 根据客户要求更新各报表页面数值字段的小数位数
 * 创建日期: 2025-07-23
 * 说明: 更新销售报表相关表的字段精度，以满足客户对小数位数的要求
 */

PRINT '开始执行字段位数更新脚本...';

-- ========================================
-- 1. 销售额明细表字段位数更新
-- ========================================
PRINT '正在更新销售额明细表字段位数...';

-- 更新销售单价字段：decimal(18,2) → decimal(18,4)
IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[销售额明细表]') AND name = N'销售单价')
BEGIN
    ALTER TABLE [dbo].[销售额明细表] ALTER COLUMN [销售单价] decimal(18,4) NOT NULL;
    PRINT '✓ 销售额明细表.[销售单价] 已更新为 decimal(18,4)';
END

-- 更新铜base字段：decimal(18,2) → decimal(18,4)
IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[销售额明细表]') AND name = N'铜base')
BEGIN
    ALTER TABLE [dbo].[销售额明细表] ALTER COLUMN [铜base] decimal(18,4) NOT NULL;
    PRINT '✓ 销售额明细表.[铜base] 已更新为 decimal(18,4)';
END

-- 更新升水字段：decimal(18,2) → decimal(18,3)
IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[销售额明细表]') AND name = N'升水')
BEGIN
    ALTER TABLE [dbo].[销售额明细表] ALTER COLUMN [升水] decimal(18,3) NULL;
    PRINT '✓ 销售额明细表.[升水] 已更新为 decimal(18,3)';
END

-- 更新零基础字段：decimal(18,2) → decimal(18,4)
IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[销售额明细表]') AND name = N'零基础')
BEGIN
    ALTER TABLE [dbo].[销售额明细表] ALTER COLUMN [零基础] decimal(18,4) NULL;
    PRINT '✓ 销售额明细表.[零基础] 已更新为 decimal(18,4)';
END

-- ========================================
-- 2. 支付额明细表字段位数更新
-- ========================================
PRINT '正在更新支付额明细表字段位数...';

-- 更新销售单价字段：decimal(18,2) → decimal(18,4)
IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[支付额明细表]') AND name = N'销售单价')
BEGIN
    ALTER TABLE [dbo].[支付额明细表] ALTER COLUMN [销售单价] decimal(18,4) NULL;
    PRINT '✓ 支付额明细表.[销售单价] 已更新为 decimal(18,4)';
END

-- 更新铜base字段：decimal(18,2) → decimal(18,4)
IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[支付额明细表]') AND name = N'铜base')
BEGIN
    ALTER TABLE [dbo].[支付额明细表] ALTER COLUMN [铜base] decimal(18,4) NOT NULL;
    PRINT '✓ 支付额明细表.[铜base] 已更新为 decimal(18,4)';
END

-- 更新升水字段：decimal(18,2) → decimal(18,3)
IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[支付额明细表]') AND name = N'升水')
BEGIN
    ALTER TABLE [dbo].[支付额明细表] ALTER COLUMN [升水] decimal(18,3) NULL;
    PRINT '✓ 支付额明细表.[升水] 已更新为 decimal(18,3)';
END

-- 更新零基础字段：decimal(18,2) → decimal(18,4)
IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[支付额明细表]') AND name = N'零基础')
BEGIN
    ALTER TABLE [dbo].[支付额明细表] ALTER COLUMN [零基础] decimal(18,4) NULL;
    PRINT '✓ 支付额明细表.[零基础] 已更新为 decimal(18,4)';
END


-- 更新升水字段：decimal(7,5) → decimal(18,3)
IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[订单明细表]') AND name = N'升水')
BEGIN
    ALTER TABLE [dbo].[订单明细表] ALTER COLUMN [升水] decimal(18,3) NULL;
    PRINT '✓ 订单明细表.[升水] 已更新为 decimal(18,3)';
END

-- 更新铜基本字段：decimal(7,5) → decimal(18,4)
IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[订单明细表]') AND name = N'铜基本')
BEGIN
    ALTER TABLE [dbo].[订单明细表] ALTER COLUMN [铜基本] decimal(18,4) NULL;
    PRINT '✓ 订单明细表.[铜基本] 已更新为 decimal(18,4)';
END


-- ========================================
-- 5. 升水表字段位数更新
-- ========================================
PRINT '正在更新升水表字段位数...';

-- 更新升水单价字段：decimal(5,4) → decimal(18,3)
IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[升水表]') AND name = N'升水单价')
BEGIN
    ALTER TABLE [dbo].[升水表] ALTER COLUMN [升水单价] decimal(18,3) NOT NULL;
    PRINT '✓ 升水表.[升水单价] 已更新为 decimal(18,3)';
END

-- ========================================
-- 6. 铜价表字段位数更新
-- ========================================
PRINT '正在更新铜价表字段位数...';

-- 更新铜基本字段：decimal(7,5) → decimal(18,4)
IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[铜价表]') AND name = N'铜基本')
BEGIN
    ALTER TABLE [dbo].[铜价表] ALTER COLUMN [铜基本] decimal(18,4) NULL;
    PRINT '✓ 铜价表.[铜基本] 已更新为 decimal(18,4)';
END

-- ========================================
-- 7. 铜合同表字段位数更新
-- ========================================
PRINT '正在更新铜合同表字段位数...';

-- 更新签约铜价字段：decimal(7,5) → decimal(18,4)
IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[铜合同表]') AND name = N'签约铜价')
BEGIN
    ALTER TABLE [dbo].[铜合同表] ALTER COLUMN [签约铜价] decimal(18,4) NOT NULL;
    PRINT '✓ 铜合同表.[签约铜价] 已更新为 decimal(18,4)';
END

-- 更新免税签约铜价字段：decimal(7,5) → decimal(18,4)
IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[铜合同表]') AND name = N'免税签约铜价')
BEGIN
    ALTER TABLE [dbo].[铜合同表] ALTER COLUMN [免税签约铜价] decimal(18,4) NOT NULL;
    PRINT '✓ 铜合同表.[免税签约铜价] 已更新为 decimal(18,4)';
END

-- ========================================
-- 8. 出库数量表字段位数更新
-- ========================================
PRINT '正在更新出库数量表字段位数...';

-- 出库数量字段已经是 numeric(8,3)，符合3位小数要求，无需修改
-- 尺寸字段已经是 numeric(5,4)，符合4位小数要求，无需修改
PRINT '✓ 出库数量表字段位数已符合要求，无需修改';

PRINT '字段位数更新脚本执行完成！';
PRINT '========================================';
PRINT '更新摘要:';
PRINT '- 销售额明细表: 4个字段已更新';
PRINT '- 支付额明细表: 4个字段已更新';
PRINT '- 订单明细表: 3个字段已更新';
PRINT '- 产品表: 1个字段已更新';
PRINT '- 升水表: 1个字段已更新';
PRINT '- 铜价表: 1个字段已更新';
PRINT '- 铜合同表: 2个字段已更新';
PRINT '- 出库数量表: 字段已符合要求';
PRINT '========================================';
PRINT '注意事项:';
PRINT '1. 字符串类型转数值类型的字段需要确保数据兼容性';
PRINT '2. 建议在生产环境执行前先在测试环境验证';
PRINT '3. 执行前建议备份相关表数据';
PRINT '4. 相关应用程序代码可能需要同步更新';
PRINT '5. 对于User-Margin报表中的计算字段(如transportationCost、filmWeight等)';
PRINT '   需要在应用层确保计算精度符合要求';
