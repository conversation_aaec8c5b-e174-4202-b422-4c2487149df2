package com.hongru.mapper.businessOps;

import com.baomidou.mybatisplus.mapper.BaseMapper;
import com.hongru.entity.businessOps.CurrencySetting;

import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface CurrencySettingMapper extends BaseMapper<CurrencySetting> {
	
	int insertCurrencySetting(@Param("currencySetting") CurrencySetting currencySetting);
  
	CurrencySetting selectCurrencySettingById(@Param("id") int id);

    List<CurrencySetting> listCurrencySetting(@Param("currency") String currency, @Param("currencyName") String currencyName);

    void updateCurrencySetting(@Param("currencySetting") CurrencySetting currencySetting);

    void deleteCurrencySetting(@Param("id") Integer id);
    
    List<String> selectDistinctCurrency();
}