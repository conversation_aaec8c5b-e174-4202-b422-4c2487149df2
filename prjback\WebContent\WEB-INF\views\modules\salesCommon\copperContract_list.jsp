<%@ page language="java" import="java.util.*"
         contentType="text/html; charset=utf-8"%>
<%@ include file="/WEB-INF/layouts/base.jsp"%>
<html>
<head>
    <title>铜合同表</title>
    <style>
        /*设置数据表表头字体*/
        .layui-table th .layui-table-cell{
            text-align: center;
        }
    </style>
</head>
<body class="gray-bg">
<div class="ibox float-e-margins">
    <div class="ibox-content">
        <div class="layui-collapse">
            <div class="layui-colla-item">
                <div class="layui-colla-content layui-show">
                    <form id="formSearch" class="layui-form hr-form-search toolbar table-tool-mini" method="post" action="">
                        <div class="layui-form-item">
		                    <div class="layui-inline layui-col-md3">
		                        <label class="layui-form-label">客户简称:</label>
		                        <div class="layui-input-block">
		                            <select class="layui-select" id="customerCode" name="customerCode" lay-search="true">
		                                <option value="">请选择</option>
		                                <c:forEach items="${customersList}" var="customers">
		                                    <option value="${customers.customerCode}" >${customers.customerCode} - ${customers.customerAlias}</option>
		                                </c:forEach>
		                            </select>
		                        </div>
		                    </div>
		                    <div class="layui-inline layui-col-md3">
		                        <label class="layui-form-label">铜签约No:</label>
		                        <div class="layui-input-block">
									<input type="text" class="layui-input" id="copperSignNo" name="copperSignNo" value="" />
		                        </div>
		                    </div>
		                    <div class="layui-inline layui-col-md3">
                                <label class="layui-form-label">铜签约类别:</label>
                                <div class="layui-input-block"  style="display: flex;align-items: center;align-content: center;">
                                    <input type="radio" name="copperSignType" id="copperSignType" value="1" title="预约铜" checked="checked"/>
                                    <input type="radio" name="copperSignType" id="copperSignType" value="2" title="支付铜" >
                                </div>
                            </div>
		                    <div class="layui-inline layui-col-md3">
		                        <label class="layui-form-label">铜条件:</label>
		                        <div class="layui-input-block">
		                            <select class="layui-select" id="copperCondition" name="copperCondition" lay-verify="required" required>
		                                <option value="">请选择</option>
		                                <c:forEach items="${copperConditionsList}" var="copperConditions">
		                                    <option value="${copperConditions.copperCondition}" >${copperConditions.copperCondition}</option>
		                                </c:forEach>
		                            </select>
		                        </div>
		                    </div>
		                 </div>
		                 <div class="layui-form-item">
		                    <div class="layui-inline layui-col-md3">
								<label class="layui-form-label">预约日:</label>
								<div class="layui-input-block" style="display: flex;justify-content: space-between;align-items: center;align-content: center">
									<input type="text" class="layui-input" id="appointmentDate" name="appointmentDate" placeholder="yyyy-MM-dd">
								</div>
		                    </div>
                            <div class="layui-inline layui-col-md3">
                                 <label class="layui-form-label">使用月:</label>
                                <div class="layui-input-block">
                                    <input type="text" class="layui-input" id="useMonth" name="useMonth" placeholder="yyyy-MM"/>
                                </div>
                            </div>
                           <div class="layui-inline layui-col-md3">
                                <label class="layui-form-label">状态:</label>
                                <div class="layui-input-block"  style="display: flex;align-items: center;align-content: center;">
                                    <input type="radio" name="status" id="status" value="0" title="有效" checked="checked"/>
                                    <input type="radio" name="status" id="status" value="9" title="删除" >
                                </div>
                            </div>
                            <div class="layui-inline layui-col-md1 hr-div-btn">
                                <button type="button" id="btn_query" onclick="search();" class="layui-btn layui-bg-blue"><i class="layui-icon layui-icon-search"></i>检索</button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        <%--初始化table--%>
        <table class="layui-hide" id="demo" lay-filter="test"></table>
        <!-- 自定义表中工具栏 -->
        <script type="text/html" id="barDemo">
            <a class="layui-btn layui-btn-xs layui-bg-green" lay-event="edit"><i class="layui-icon layui-icon-edit"></i>编辑</a>
            <a class="layui-btn layui-btn-xs layui-bg-red" lay-event="remove"><i class="layui-icon layui-icon-delete"></i>删除</a>
        </script>
        <!-- 自定义头部工具栏 -->
        <script type="text/html" id="toolbarDemo">
            <div class="layui-btn-container">
                <button class="layui-btn layui-btn-sm layui-bg-blue" lay-event="toAdd"><i class="layui-icon layui-icon-add-1"></i>新增</button>
                <button class="layui-btn layui-btn-primary layui-btn-sm" lay-event="refresh"><i class="layui-icon layui-icon-refresh"></i>刷新</button>
            </div>
        </script>
    </div>
</div>
<myfooter>
    <script src="${ctxsta}/hongru/js/salesCommon/copperContract_list.js?time=1"></script>
</myfooter>
</body>
</html>
