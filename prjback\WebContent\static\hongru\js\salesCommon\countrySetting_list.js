
layui.use(['laydate', 'layer', 'table', 'element','form'], function(){
    var laydate = layui.laydate //日期
        ,layer = layui.layer //弹层
        ,table = layui.table //表格
        ,element = layui.element //元素操作

    //执行一个 table 实例
    var url = baselocation+'/salesCommon/countrySetting/list';
    table.render({
        elem: '#demo'
        ,height: 'full-185'
        ,url: url //数据接口
        ,parseData:function(res){ //res 即为原始返回的数据
            return {
                "code": 0, //解析接口状态
                "msg": '', //解析提示文本
                "count": res.total, //解析数据长度
                "data": res.rows //解析数据列表
            };
        }
        ,method:'post'
        ,title: '国家列表'
        ,page: false //开启分页
        ,where:$("#formSearch").serializeJsonObject()
        ,toolbar: '#toolbarDemo' //开启工具栏，此处显示默认图标，可以自定义模板，详见文档
        ,defaultToolbar: ['filter']
        ,totalRow: false //开启合计行
        ,cols: [[ //表头
            {field: 'country',title: '国家',align:'center', width:100}
            ,{field: 'countryName',title: '国家名',align:'center', width:150}
            ,{field: 'creatorName',title: '创建者',align:'center', width:150}
            ,{field: 'createdTime',title: '创建时间',align:'center', width:180}
            ,{field: 'updaterName',title: '更新者',align:'center', width:150}
            ,{field: 'updatedTime',title: '更新时间',align:'center', width:180}
            ,{title: '操作',minWidth:150, align:'left',fixed: 'right', toolbar: '#barDemo',width: 150}
        ]] 
    });

    //监听头工具栏事件
    table.on('toolbar(test)', function(obj){
        var checkStatus = table.checkStatus(obj.config.id)
            ,data = checkStatus.data; //获取选中的数据
        switch(obj.event){
            case 'toAdd':
                layer_show('添加', baselocation+"/salesCommon/countrySetting/add/view", 750, document.body.clientHeight-100)
                break;
            case 'refresh':
                var temp = $("#formSearch").serializeJsonObject();
                console.info(temp);
                layui.table.reload('demo', {
                    page: {
                        curr: 1
                    }
                    ,where: temp
                }, 'data');
                break;
        };
    });

    //监听行工具事件
    table.on('tool(test)', function(obj){ //注：tool 是工具条事件名，test 是 table 原始容器的属性 lay-filter="对应的值"
        var data = obj.data //获得当前行数据
            ,layEvent = obj.event; //获得 lay-event 对应的值
        if(layEvent === 'edit'){
            layer_show('编辑',baselocation+"/salesCommon/countrySetting/edit/view?id="+data.id,document.body.clientWidth-10, document.body.clientHeight-10);
        }else if(layEvent === 'remove'){
            layer.confirm('确认要删除吗？', {
                btn : [ '确定', '取消' ] //按钮
            }, function() {
                $.ajax({
                    type : 'post',
                    dataType : 'json',
                    data: {"id":data.id},
                    url : baselocation + '/salesCommon/countrySetting/remove',
                    success : function(result) {
                        if (result.code == 1) {
                            layer.msg("操作成功!", {
                                shade : 0.3,
                                time : 1500
                            }, function() {
                                search();
                                layer.closeAll();
                            });
                        } else {
                            layer.alert(result.message, {
                                icon : 2
                            });
                        }
                    }
                })
            });
        }
    });

});

function search() {
    var temp = $("#formSearch").serializeJsonObject();
    console.info(temp);
    //执行重载
    layui.table.reload('demo', {
    	where: temp
    }, 'data');
}
