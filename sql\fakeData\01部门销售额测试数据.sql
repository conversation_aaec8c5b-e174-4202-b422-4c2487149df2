

-- Insert test data for sales table
INSERT INTO 销售额表 (销售额NO, 客户简称, 结算货币, 销售额日, 创建人, 更新人) VALUES
('S202502001', 'SSK1', 'RMB', '2025-02-01', 'admin', 'admin'),
('S202502002', 'WINTEC2', 'RMB', '2025-02-01', 'admin', 'admin'),
('S202502003', 'Shanghai Mitsubishi', 'RMB', '2025-02-01', 'admin', 'admin'),
('S202502004', 'Shanghai Sanchong', 'RMB', '2025-02-01', 'admin', 'admin'),
('S202502005', 'Shanghai Weike', 'RMB', '2025-02-01', 'admin', 'admin'),
('S202502006', '<PERSON> Kiko', 'RMB', '2025-02-01', 'admin', 'admin'),
('S202502007', 'Toyo Electric', 'RMB', '2025-02-01', 'admin', 'admin'),
('S202502008', 'Dongguan Li<PERSON>ng', 'RMB', '2025-02-01', 'admin', 'admin'),
('S202502009', 'Delta Electronics', 'RMB', '2025-02-01', 'admin', 'admin'),
('S202502010', 'Foshan Aisan', 'RMB', '2025-02-01', 'admin', 'admin'),
('S202502011', 'India Denso', 'USD', '2025-02-01', 'admin', 'admin'),
('S202502012', 'Pakistan Honda', 'USD', '2025-02-01', 'admin', 'admin'),
('S202502013', 'Japan Citizen', 'USD', '2025-02-01', 'admin', 'admin'),
('S202502014', 'Korea Denso', 'USD', '2025-02-01', 'admin', 'admin');

-- Insert test data for sales details table
INSERT INTO 销售额明细表 (销售额NO, 产品中分类, 数量, 销售金额, 创建人, 更新人) VALUES
-- SSK1 data
('S202502001', 'Round Wire MW EF', 427.36, 55048.59, 'admin', 'admin'),

-- WINTEC2 data
('S202502002', 'Flat Wire MW', 146193.6, 14641626.03, 'admin', 'admin'),

-- Shanghai Mitsubishi data
('S202502003', 'Round Wire MW EF', 12741.6, 1007741.67, 'admin', 'admin'),

-- Shanghai Sanchong data
('S202502004', 'Round Wire MW EF', 7150.02, 561089.15, 'admin', 'admin'),

-- Shanghai Weike data
('S202502005', 'Round Wire MW EF', 4416.36, 427422.3, 'admin', 'admin'),

-- Fuji Kiko data
('S202502006', 'Round Wire MW EF', 4885, 388406.35, 'admin', 'admin'),

-- Toyo Electric data
('S202502007', 'Ultra Fine Wire', 102.503, 36836.68, 'admin', 'admin'),

-- Dongguan Lisheng data
('S202502008', 'Round Wire MW EF', 2419.98, 198414.16, 'admin', 'admin'),

-- Delta Electronics data
('S202502009', 'Flat Wire MW', 8065.4, 714191.17, 'admin', 'admin'),

-- Foshan Aisan data
('S202502010', 'Round Wire MW EF', 522.8, 52766.2, 'admin', 'admin'),

-- India Denso data
('S202502011', 'Ultra Fine Wire', 1570.17, 31450.51, 'admin', 'admin'),

-- Pakistan Honda data (multiple lines)
('S202502012', 'Round Wire MW EF', 7200.88, 126289.03, 'admin', 'admin'),
('S202502012', 'Ultra Fine Wire', 393.262, 10707.74, 'admin', 'admin'),

-- Japan Citizen data
('S202502013', 'Ultra Fine Wire', 137.469, 14171.02, 'admin', 'admin'),

-- Korea Denso data
('S202502014', 'Ultra Fine Wire', 1887.72, 39396.72, 'admin', 'admin');

-- Verify data insertion
SELECT 
    a.客户简称,
    a.结算货币,
    b.产品中分类,
    SUM(b.数量) as 销售额数,
    SUM(b.销售金额) as 销售额金额
FROM 销售额表 a
LEFT JOIN 销售额明细表 b ON a.销售额NO = b.销售额NO
GROUP BY a.客户简称, a.结算货币, b.产品中分类
ORDER BY a.客户简称, b.产品中分类;