package com.hongru.service.businessOps;

import com.hongru.entity.businessOps.PaymentAmount;
import com.hongru.entity.businessOps.PaymentAmountDetail;
import com.hongru.pojo.dto.SalesConfirmListDTO;
import com.hongru.support.page.PageInfo;

import java.util.List;

/**
 * 销售额确认服务接口
 */
public interface ISalesConfirmService {

    /**
     * 分页查询销售额确认列表
     * 
     * @param pageInfo 分页信息
     * @param customerCode 客户代码
     * @param paymentRequestDate 支付请求日
     * @return 销售额确认列表
     * @throws Exception
     */
    List<SalesConfirmListDTO> listSalesConfirmByPage(PageInfo pageInfo, String customerCode, String paymentRequestDate) throws Exception;

    /**
     * 查询销售额确认列表总数
     * 
     * @param customerCode 客户代码
     * @param paymentRequestDate 支付请求日
     * @return 总数
     * @throws Exception
     */
    Integer listSalesConfirmByPageCount(String customerCode, String paymentRequestDate) throws Exception;

    /**
     * 根据支付请求NO获取支付额数据
     * 
     * @param paymentRequestNo 支付请求NO
     * @return 支付额数据
     * @throws Exception
     */
    PaymentAmount getPaymentAmountByNo(String paymentRequestNo) throws Exception;

    /**
     * 根据支付请求NO获取支付额明细数据
     * 
     * @param paymentRequestNo 支付请求NO
     * @return 支付额明细数据列表
     * @throws Exception
     */
    List<PaymentAmountDetail> getPaymentAmountDetailsByNo(String paymentRequestNo) throws Exception;

    /**
     * 确认销售额
     * 
     * @param paymentRequestNo 支付请求NO
     * @param confirmer 确认者
     * @return 更新行数
     * @throws Exception
     */
    int confirmSalesAmount(String paymentRequestNo, String confirmer) throws Exception;

    /**
     * 取消确认销售额
     * 
     * @param paymentRequestNo 支付请求NO
     * @param confirmer 确认者
     * @return 更新行数
     * @throws Exception
     */
    int cancelConfirmSalesAmount(String paymentRequestNo, String confirmer) throws Exception;
}
