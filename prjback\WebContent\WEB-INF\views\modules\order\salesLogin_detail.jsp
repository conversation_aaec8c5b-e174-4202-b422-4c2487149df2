<%@ page language="java" import="java.util.*"
         contentType="text/html; charset=utf-8"%>
<%@ include file="/WEB-INF/layouts/base.jsp"%>
<html>
<head>
    <title>销售额登录详情</title>
    <style>
        .layui-form-label {
            width: 90px;
            padding: 8px 5px;
            text-align: right;
            box-sizing: border-box;
        }
        .layui-input-block {
            margin-left: 100px;
            box-sizing: border-box;
        }
        .layui-form-item {
            display: table;
            width: 100%;
            table-layout: fixed;
            margin-bottom: 15px;
        }
        .layui-inline {
            display: table-cell;
            width: 33.33%;
            padding-right: 10px;
            box-sizing: border-box;
            float: none;
        }
        .hr-line {
            margin: 15px 0;
            border-top: 1px solid #e6e6e6;
        }
        .detail-text {
            line-height: 38px;
            padding-left: 5px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        /* 明细信息部分的特殊处理 */
        .layui-block {
            width: 100%;
            display: block;
        }
        
        /* 表格容器样式 */
        .detail-table-container {
            width: 100%;
            padding: 0;
            margin-left: 0;
        }
        
        /* 确保表格宽度100% */
        .layui-table {
            width: 100% !important;
            margin: 0;
        }
        
        /* 底部按钮居中对齐 */
        .btn-container {
            text-align: center;
            margin-top: 20px;
        }
        .btn-container .layui-btn {
            margin: 0 10px;
        }
        
        /* 移动端适配 */
        @media screen and (max-width: 768px) {
            .layui-form-item {
                display: block;
            }
            .layui-inline {
                display: block;
                width: 100%;
                padding-right: 0;
                margin-bottom: 10px;
            }
        }
    </style>
</head>
<body class="gray-bg">
<div class="wrapper wrapper-content animated fadeInRight">
    <div class="row">
        <div class="col-sm-12">
            <div class="ibox float-e-margins">
                <div class="ibox-content">
                    <form class="layui-form" id="form" action="">
                        <input type="hidden" name="id" id="id" value="${id}">
                        <input type="hidden" name="salesAmountNo" id="salesAmountNo" value="${salesAmountNo}">
                        <div class="layui-form-item">
                            <div class="layui-inline layui-col-md4">
                                <label class="layui-form-label">销售额NO:</label>
                                <div class="layui-input-block detail-text" id="salesNo"></div>
                            </div>
                            <div class="layui-inline layui-col-md4">
                                <label class="layui-form-label">需求方:</label>
                                <div class="layui-input-block detail-text" id="customerAlias"></div>
                            </div>
                            <div class="layui-inline layui-col-md4">
                                <label class="layui-form-label">销售额日:</label>
                                <div class="layui-input-block detail-text" id="salesDate"></div>
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <div class="layui-inline layui-col-md4">
                                <label class="layui-form-label">货币:</label>
                                <div class="layui-input-block detail-text" id="currency"></div>
                            </div>
                            <div class="layui-inline layui-col-md4">
                                <label class="layui-form-label">税率(%):</label>
                                <div class="layui-input-block detail-text" id="taxRate"></div>
                            </div>
                            <div class="layui-inline layui-col-md4">
                                <label class="layui-form-label">总金额:</label>
                                <div class="layui-input-block detail-text" id="totalAmount"></div>
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <div class="layui-inline layui-col-md4">
                                <label class="layui-form-label">税额:</label>
                                <div class="layui-input-block detail-text" id="taxAmount"></div>
                            </div>
                        </div>

                        <!-- 客户相关条件字段 -->
                        <div class="layui-form-item">
                            <div class="layui-inline layui-col-md4">
                                <label class="layui-form-label">交易条件:</label>
                                <div class="layui-input-block detail-text" id="tradeCondition"></div>
                            </div>
                            <div class="layui-inline layui-col-md4">
                                <label class="layui-form-label">付款条件:</label>
                                <div class="layui-input-block detail-text" id="payCondition"></div>
                            </div>
                            <div class="layui-inline layui-col-md4">
                                <label class="layui-form-label">运输条件:</label>
                                <div class="layui-input-block detail-text" id="transportCondition"></div>
                            </div>
                        </div>
                        <!-- <div class="layui-form-item">
                            <div class="layui-inline layui-col-md4">
                                <label class="layui-form-label">税率代码:</label>
                                <div class="layui-input-block detail-text" id="taxRateCode"></div>
                            </div>
                        </div> -->
<%--                        <div class="layui-form-item">--%>
<%--                            <div class="layui-inline layui-col-md4">--%>
<%--                                <label class="layui-form-label">创建者:</label>--%>
<%--                                <div class="layui-input-block detail-text" id="creatorName"></div>--%>
<%--                            </div>--%>
<%--                            <div class="layui-inline layui-col-md4">--%>
<%--                                <label class="layui-form-label">创建时间:</label>--%>
<%--                                <div class="layui-input-block detail-text" id="createdTime"></div>--%>
<%--                            </div>--%>
<%--                            <div class="layui-inline layui-col-md4">--%>
<%--                                <label class="layui-form-label">更新者:</label>--%>
<%--                                <div class="layui-input-block detail-text" id="updaterName"></div>--%>
<%--                            </div>--%>
<%--                        </div>--%>
<%--                        <div class="layui-form-item">--%>
<%--                            <div class="layui-inline layui-col-md4">--%>
<%--                                <label class="layui-form-label">更新时间:</label>--%>
<%--                                <div class="layui-input-block detail-text" id="updatedTime"></div>--%>
<%--                            </div>--%>
<%--                            <div class="layui-inline layui-col-md4">--%>
<%--                                <!-- 占位元素，保持布局一致性 -->--%>
<%--                            </div>--%>
<%--                            <div class="layui-inline layui-col-md4">--%>
<%--                                <!-- 占位元素，保持布局一致性 -->--%>
<%--                            </div>--%>
<%--                        </div>--%>

                        <div class="hr-line"></div>
                        <div class="layui-form-item" style="display: block;">
                            <div class="layui-block" style="width: 100%;">
                                <div class="layui-input-block detail-table-container">
                                    <table class="layui-table" id="detailTable" lay-filter="detailTable"></table>
                                </div>
                            </div>
                        </div>

                        <div class="btn-container">
                            <button type="button" class="layui-btn layui-btn-primary" onclick="closeLayer()">关闭</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
<myfooter>
    <script>
        var baselocation = '${ctx}';
    </script>
    <script src="${ctxsta}/hongru/js/order/salesLogin_detail.js?v=96"></script>
</myfooter>
</body>
</html> 