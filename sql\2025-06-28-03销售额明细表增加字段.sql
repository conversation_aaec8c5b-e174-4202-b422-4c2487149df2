-- 销售额明细表增加字段：品目C、理由和线盘
-- 创建日期：2025-06-28

USE [BusinessOps]
GO

-- 检查字段是否已存在，如果不存在则添加
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[销售额明细表]') AND name = N'品目C')
BEGIN
    ALTER TABLE [dbo].[销售额明细表] ADD [品目C] NVARCHAR(50) NULL;
    PRINT '已添加 [品目C] 字段';
END
ELSE
BEGIN
    PRINT '[品目C] 字段已存在';
END

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[销售额明细表]') AND name = N'理由')
BEGIN
    ALTER TABLE [dbo].[销售额明细表] ADD [理由] NVARCHAR(100) NULL;
    PRINT '已添加 [理由] 字段';
END
ELSE
BEGIN
    PRINT '[理由] 字段已存在';
END

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[销售额明细表]') AND name = N'线盘')
BEGIN
    ALTER TABLE [dbo].[销售额明细表] ADD [线盘] NVARCHAR(100) NULL;
    PRINT '已添加 [线盘] 字段';
END
ELSE
BEGIN
    PRINT '[线盘] 字段已存在';
END

-- 添加注释
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'品目C编码', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'销售额明细表', @level2type = N'COLUMN', @level2name = N'品目C';
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'出货理由', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'销售额明细表', @level2type = N'COLUMN', @level2name = N'理由';
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'线盘名称', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'销售额明细表', @level2type = N'COLUMN', @level2name = N'线盘';

PRINT '销售额明细表字段添加完成';
GO 